{"version": 3, "file": "PopupClient.js", "sources": ["../../src/interaction_client/PopupClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, CommonAuthorizationCodeRequest, AuthorizationCodeClient, ThrottlingUtils, CommonEndSessionRequest, UrlString, AuthError, OIDC_DEFAULT_SCOPES, Constants, ProtocolUtils, ServerAuthorizationCodeResponse, PerformanceEvents, StringUtils, IPerformanceClient, Logger, ICrypto } from \"@azure/msal-common\";\r\nimport { StandardInteractionClient } from \"./StandardInteractionClient\";\r\nimport { EventType } from \"../event/EventType\";\r\nimport { InteractionType, ApiId, BrowserConstants } from \"../utils/BrowserConstants\";\r\nimport { EndSessionPopupRequest } from \"../request/EndSessionPopupRequest\";\r\nimport { NavigationOptions } from \"../navigation/NavigationOptions\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { NativeInteractionClient } from \"./NativeInteractionClient\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { InteractionHandler, InteractionParams } from \"../interaction_handler/InteractionHandler\";\r\nimport { PopupWindowAttributes } from \"../request/PopupWindowAttributes\";\r\n\r\nexport type PopupParams = InteractionParams & {\r\n    popup?: Window|null;\r\n    popupName: string;\r\n    popupWindowAttributes: PopupWindowAttributes\r\n};\r\n\r\nexport class PopupClient extends StandardInteractionClient {\r\n    private currentWindow: Window | undefined;\r\n    protected nativeStorage: BrowserCacheManager;\r\n\r\n    constructor(config: BrowserConfiguration, storageImpl: BrowserCacheManager, browserCrypto: ICrypto, logger: Logger, eventHandler: EventHandler, navigationClient: INavigationClient, performanceClient: IPerformanceClient, nativeStorageImpl: BrowserCacheManager, nativeMessageHandler?: NativeMessageHandler, correlationId?: string) {\r\n        super(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient, nativeMessageHandler, correlationId);\r\n        // Properly sets this reference for the unload event.\r\n        this.unloadWindow = this.unloadWindow.bind(this);\r\n        this.nativeStorage = nativeStorageImpl;\r\n    }\r\n\r\n    /**\r\n     * Acquires tokens by opening a popup window to the /authorize endpoint of the authority\r\n     * @param request\r\n     */\r\n    acquireToken(request: PopupRequest): Promise<AuthenticationResult> {\r\n        try {\r\n            const popupName = this.generatePopupName(request.scopes || OIDC_DEFAULT_SCOPES, request.authority || this.config.auth.authority);\r\n            const popupWindowAttributes = request.popupWindowAttributes || {};\r\n\r\n            // asyncPopups flag is true. Acquires token without first opening popup. Popup will be opened later asynchronously.\r\n            if (this.config.system.asyncPopups) {\r\n                this.logger.verbose(\"asyncPopups set to true, acquiring token\");\r\n                // Passes on popup position and dimensions if in request\r\n                return this.acquireTokenPopupAsync(request, popupName, popupWindowAttributes);\r\n            } else {\r\n                // asyncPopups flag is set to false. Opens popup before acquiring token.\r\n                this.logger.verbose(\"asyncPopup set to false, opening popup before acquiring token\");\r\n                const popup = this.openSizedPopup(\"about:blank\", popupName, popupWindowAttributes);\r\n                return this.acquireTokenPopupAsync(request, popupName, popupWindowAttributes, popup);\r\n            }\r\n        } catch (e) {\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clears local cache for the current user then opens a popup window prompting the user to sign-out of the server\r\n     * @param logoutRequest\r\n     */\r\n    logout(logoutRequest?: EndSessionPopupRequest): Promise<void> {\r\n        try {\r\n            this.logger.verbose(\"logoutPopup called\");\r\n            const validLogoutRequest = this.initializeLogoutRequest(logoutRequest);\r\n\r\n            const popupName = this.generateLogoutPopupName(validLogoutRequest);\r\n            const authority = logoutRequest && logoutRequest.authority;\r\n            const mainWindowRedirectUri = logoutRequest && logoutRequest.mainWindowRedirectUri;\r\n            const popupWindowAttributes = logoutRequest?.popupWindowAttributes || {};\r\n\r\n            // asyncPopups flag is true. Acquires token without first opening popup. Popup will be opened later asynchronously.\r\n            if (this.config.system.asyncPopups) {\r\n                this.logger.verbose(\"asyncPopups set to true\");\r\n                // Passes on popup position and dimensions if in request\r\n                return this.logoutPopupAsync(validLogoutRequest, popupName, popupWindowAttributes, authority, undefined, mainWindowRedirectUri);\r\n            } else {\r\n                // asyncPopups flag is set to false. Opens popup before logging out.\r\n                this.logger.verbose(\"asyncPopup set to false, opening popup\");\r\n                const popup = this.openSizedPopup(\"about:blank\", popupName, popupWindowAttributes);\r\n                return this.logoutPopupAsync(validLogoutRequest, popupName, popupWindowAttributes, authority, popup, mainWindowRedirectUri);\r\n            }\r\n        } catch (e) {\r\n            // Since this function is synchronous we need to reject\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper which obtains an access_token for your API via opening a popup window in the user's browser\r\n     * @param validRequest\r\n     * @param popupName\r\n     * @param popup\r\n     * @param popupWindowAttributes\r\n     *\r\n     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.\r\n     */\r\n    protected async acquireTokenPopupAsync(request: PopupRequest, popupName: string, popupWindowAttributes: PopupWindowAttributes, popup?: Window|null): Promise<AuthenticationResult> {\r\n        this.logger.verbose(\"acquireTokenPopupAsync called\");\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenPopup);\r\n\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest, request.correlationId);\r\n        const validRequest = await this.initializeAuthorizationRequest(request, InteractionType.Popup);\r\n        this.browserStorage.updateCacheEntries(validRequest.state, validRequest.nonce, validRequest.authority, validRequest.loginHint || Constants.EMPTY_STRING, validRequest.account || null);\r\n\r\n        try {\r\n            // Create auth code request and generate PKCE params\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest, request.correlationId);\r\n            const authCodeRequest: CommonAuthorizationCodeRequest = await this.initializeAuthorizationCodeRequest(validRequest);\r\n\r\n            // Initialize the client\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, request.correlationId);\r\n            const authClient: AuthorizationCodeClient = await this.createAuthCodeClient(serverTelemetryManager, validRequest.authority, validRequest.azureCloudOptions);\r\n            this.logger.verbose(\"Auth code client created\");\r\n\r\n            const isNativeBroker = NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeMessageHandler, request.authenticationScheme);\r\n            // Start measurement for server calls with native brokering enabled\r\n            let fetchNativeAccountIdMeasurement;\r\n            if (isNativeBroker) {\r\n                fetchNativeAccountIdMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.FetchAccountIdWithNativeBroker, request.correlationId);\r\n            }\r\n\r\n            // Create acquire token url.\r\n            const navigateUrl = await authClient.getAuthCodeUrl({\r\n                ...validRequest,\r\n                nativeBroker: isNativeBroker\r\n            });\r\n\r\n            // Create popup interaction handler.\r\n            const interactionHandler = new InteractionHandler(authClient, this.browserStorage, authCodeRequest, this.logger, this.performanceClient);\r\n\r\n            // Show the UI once the url has been created. Get the window handle for the popup.\r\n            const popupParameters: PopupParams = {\r\n                popup,\r\n                popupName,\r\n                popupWindowAttributes\r\n            };\r\n            const popupWindow: Window = this.initiateAuthRequest(navigateUrl, popupParameters);\r\n            this.eventHandler.emitEvent(EventType.POPUP_OPENED, InteractionType.Popup, {popupWindow}, null);\r\n\r\n            // Monitor the window for the hash. Return the string value and close the popup when the hash is received. Default timeout is 60 seconds.\r\n            const hash = await this.monitorPopupForHash(popupWindow);\r\n            // Deserialize hash fragment response parameters.\r\n            const serverParams: ServerAuthorizationCodeResponse = UrlString.getDeserializedHash(hash);\r\n            const state = this.validateAndExtractStateFromHash(serverParams, InteractionType.Popup, validRequest.correlationId);\r\n            // Remove throttle if it exists\r\n            ThrottlingUtils.removeThrottle(this.browserStorage, this.config.auth.clientId, authCodeRequest);\r\n\r\n            if (serverParams.accountId) {\r\n                this.logger.verbose(\"Account id found in hash, calling WAM for token\");\r\n                // end measurement for server call with native brokering enabled\r\n                if (fetchNativeAccountIdMeasurement) {\r\n                    fetchNativeAccountIdMeasurement.endMeasurement({\r\n                        success: true,\r\n                        isNativeBroker: true\r\n                    });\r\n                }\r\n\r\n                if (!this.nativeMessageHandler) {\r\n                    throw BrowserAuthError.createNativeConnectionNotEstablishedError();\r\n                }\r\n                const nativeInteractionClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.acquireTokenPopup, this.performanceClient, this.nativeMessageHandler, serverParams.accountId, this.nativeStorage, validRequest.correlationId);\r\n                const { userRequestState } = ProtocolUtils.parseRequestState(this.browserCrypto, state);\r\n                return nativeInteractionClient.acquireToken({\r\n                    ...validRequest,\r\n                    state: userRequestState,\r\n                    prompt: undefined // Server should handle the prompt, ideally native broker can do this part silently\r\n                }).finally(() => {\r\n                    this.browserStorage.cleanRequestByState(state);\r\n                });\r\n            }\r\n\r\n            // Handle response from hash string.\r\n            const result = await interactionHandler.handleCodeResponseFromHash(hash, state, authClient.authority, this.networkClient);\r\n\r\n            return result;\r\n        } catch (e) {\r\n            if (popup) {\r\n                // Close the synchronous popup if an error is thrown before the window unload event is registered\r\n                popup.close();\r\n            }\r\n\r\n            if (e instanceof AuthError) {\r\n                (e as AuthError).setCorrelationId(this.correlationId);\r\n            }\r\n\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            this.browserStorage.cleanRequestByState(validRequest.state);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @param validRequest\r\n     * @param popupName\r\n     * @param requestAuthority\r\n     * @param popup\r\n     * @param mainWindowRedirectUri\r\n     * @param popupWindowAttributes\r\n     */\r\n    protected async logoutPopupAsync(validRequest: CommonEndSessionRequest, popupName: string, popupWindowAttributes: PopupWindowAttributes, requestAuthority?: string, popup?: Window|null, mainWindowRedirectUri?: string): Promise<void> {\r\n        this.logger.verbose(\"logoutPopupAsync called\");\r\n        this.eventHandler.emitEvent(EventType.LOGOUT_START, InteractionType.Popup, validRequest);\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.logoutPopup);\r\n\r\n        try {\r\n            // Clear cache on logout\r\n            await this.clearCacheOnLogout(validRequest.account);\r\n\r\n            // Initialize the client\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, validRequest.correlationId);\r\n            const authClient = await this.createAuthCodeClient(serverTelemetryManager, requestAuthority);\r\n            this.logger.verbose(\"Auth code client created\");\r\n\r\n            // Create logout string and navigate user window to logout.\r\n            const logoutUri: string = authClient.getLogoutUri(validRequest);\r\n\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_SUCCESS, InteractionType.Popup, validRequest);\r\n\r\n            // Open the popup window to requestUrl.\r\n            const popupWindow = this.openPopup(logoutUri, {popupName, popupWindowAttributes, popup});\r\n            this.eventHandler.emitEvent(EventType.POPUP_OPENED, InteractionType.Popup, {popupWindow}, null);\r\n\r\n            await this.waitForLogoutPopup(popupWindow);\r\n\r\n            if (mainWindowRedirectUri) {\r\n                const navigationOptions: NavigationOptions = {\r\n                    apiId: ApiId.logoutPopup,\r\n                    timeout: this.config.system.redirectNavigationTimeout,\r\n                    noHistory: false\r\n                };\r\n                const absoluteUrl = UrlString.getAbsoluteUrl(mainWindowRedirectUri, BrowserUtils.getCurrentUri());\r\n\r\n                this.logger.verbose(\"Redirecting main window to url specified in the request\");\r\n                this.logger.verbosePii(`Redirecting main window to: ${absoluteUrl}`);\r\n                this.navigationClient.navigateInternal(absoluteUrl, navigationOptions);\r\n            } else {\r\n                this.logger.verbose(\"No main window navigation requested\");\r\n            }\r\n        } catch (e) {\r\n            if (popup) {\r\n                // Close the synchronous popup if an error is thrown before the window unload event is registered\r\n                popup.close();\r\n            }\r\n\r\n            if (e instanceof AuthError) {\r\n                (e as AuthError).setCorrelationId(this.correlationId);\r\n            }\r\n\r\n            this.browserStorage.setInteractionInProgress(false);\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_FAILURE, InteractionType.Popup, null, e);\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_END, InteractionType.Popup);\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n\r\n        this.eventHandler.emitEvent(EventType.LOGOUT_END, InteractionType.Popup);\r\n    }\r\n\r\n    /**\r\n     * Opens a popup window with given request Url.\r\n     * @param requestUrl\r\n     */\r\n    initiateAuthRequest(requestUrl: string, params: PopupParams): Window {\r\n        // Check that request url is not empty.\r\n        if (!StringUtils.isEmpty(requestUrl)) {\r\n            this.logger.infoPii(`Navigate to: ${requestUrl}`);\r\n            // Open the popup window to requestUrl.\r\n            return this.openPopup(requestUrl, params);\r\n        } else {\r\n            // Throw error if request URL is empty.\r\n            this.logger.error(\"Navigate url is empty\");\r\n            throw BrowserAuthError.createEmptyNavigationUriError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Monitors a window until it loads a url with the same origin.\r\n     * @param popupWindow - window that is being monitored\r\n     * @param timeout - timeout for processing hash once popup is redirected back to application\r\n     */\r\n    monitorPopupForHash(popupWindow: Window): Promise<string> {\r\n        return new Promise((resolve, reject) => {\r\n            /*\r\n             * Polling for popups needs to be tick-based,\r\n             * since a non-trivial amount of time can be spent on interaction (which should not count against the timeout).\r\n             */\r\n            const maxTicks = this.config.system.windowHashTimeout / this.config.system.pollIntervalMilliseconds;\r\n            let ticks = 0;\r\n\r\n            this.logger.verbose(\"PopupHandler.monitorPopupForHash - polling started\");\r\n\r\n            const intervalId = setInterval(() => {\r\n                // Window is closed\r\n                if (popupWindow.closed) {\r\n                    this.logger.error(\"PopupHandler.monitorPopupForHash - window closed\");\r\n                    this.cleanPopup();\r\n                    clearInterval(intervalId);\r\n                    reject(BrowserAuthError.createUserCancelledError());\r\n                    return;\r\n                }\r\n\r\n                let href: string = Constants.EMPTY_STRING;\r\n                let hash: string = Constants.EMPTY_STRING;\r\n                try {\r\n                    /*\r\n                     * Will throw if cross origin,\r\n                     * which should be caught and ignored\r\n                     * since we need the interval to keep running while on STS UI.\r\n                     */\r\n                    href = popupWindow.location.href;\r\n                    hash = popupWindow.location.hash;\r\n                } catch (e) {}\r\n\r\n                // Don't process blank pages or cross domain\r\n                if (StringUtils.isEmpty(href) || href === \"about:blank\") {\r\n                    return;\r\n                }\r\n\r\n                this.logger.verbose(\"PopupHandler.monitorPopupForHash - popup window is on same origin as caller\");\r\n\r\n                /*\r\n                 * Only run clock when we are on same domain for popups\r\n                 * as popup operations can take a long time.\r\n                 */\r\n                ticks++;\r\n\r\n                if (hash) {\r\n                    this.logger.verbose(\"PopupHandler.monitorPopupForHash - found hash in url\");\r\n                    clearInterval(intervalId);\r\n                    this.cleanPopup(popupWindow);\r\n\r\n                    if (UrlString.hashContainsKnownProperties(hash)) {\r\n                        this.logger.verbose(\"PopupHandler.monitorPopupForHash - hash contains known properties, returning.\");\r\n                        resolve(hash);\r\n                    } else {\r\n                        this.logger.error(\"PopupHandler.monitorPopupForHash - found hash in url but it does not contain known properties. Check that your router is not changing the hash prematurely.\");\r\n                        this.logger.errorPii(`PopupHandler.monitorPopupForHash - hash found: ${hash}`);\r\n                        reject(BrowserAuthError.createHashDoesNotContainKnownPropertiesError());\r\n                    }\r\n                } else if (ticks > maxTicks) {\r\n                    this.logger.error(\"PopupHandler.monitorPopupForHash - unable to find hash in url, timing out\");\r\n                    clearInterval(intervalId);\r\n                    reject(BrowserAuthError.createMonitorPopupTimeoutError());\r\n                }\r\n            }, this.config.system.pollIntervalMilliseconds);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Waits for user interaction in logout popup window\r\n     * @param popupWindow\r\n     * @returns\r\n     */\r\n    waitForLogoutPopup(popupWindow: Window): Promise<void> {\r\n        return new Promise((resolve) => {\r\n            this.logger.verbose(\"PopupHandler.waitForLogoutPopup - polling started\");\r\n\r\n            const intervalId = setInterval(() => {\r\n                // Window is closed\r\n                if (popupWindow.closed) {\r\n                    this.logger.error(\"PopupHandler.waitForLogoutPopup - window closed\");\r\n                    this.cleanPopup();\r\n                    clearInterval(intervalId);\r\n                    resolve();\r\n                }\r\n\r\n                let href: string = Constants.EMPTY_STRING;\r\n                try {\r\n                    /*\r\n                     * Will throw if cross origin,\r\n                     * which should be caught and ignored\r\n                     * since we need the interval to keep running while on STS UI.\r\n                     */\r\n                    href = popupWindow.location.href;\r\n                } catch (e) {}\r\n\r\n                // Don't process blank pages or cross domain\r\n                if (StringUtils.isEmpty(href) || href === \"about:blank\") {\r\n                    return;\r\n                }\r\n\r\n                this.logger.verbose(\"PopupHandler.waitForLogoutPopup - popup window is on same origin as caller, closing.\");\r\n\r\n                clearInterval(intervalId);\r\n                this.cleanPopup(popupWindow);\r\n                resolve();\r\n            }, this.config.system.pollIntervalMilliseconds);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     *\r\n     * Configures popup window for login.\r\n     *\r\n     * @param urlNavigate\r\n     * @param title\r\n     * @param popUpWidth\r\n     * @param popUpHeight\r\n     * @param popupWindowAttributes\r\n     * @ignore\r\n     * @hidden\r\n     */\r\n    openPopup(urlNavigate: string, popupParams: PopupParams): Window {\r\n        try {\r\n            let popupWindow;\r\n            // Popup window passed in, setting url to navigate to\r\n            if (popupParams.popup) {\r\n                popupWindow = popupParams.popup;\r\n                this.logger.verbosePii(`Navigating popup window to: ${urlNavigate}`);\r\n                popupWindow.location.assign(urlNavigate);\r\n            } else if (typeof popupParams.popup === \"undefined\") {\r\n                // Popup will be undefined if it was not passed in\r\n                this.logger.verbosePii(`Opening popup window to: ${urlNavigate}`);\r\n                popupWindow = this.openSizedPopup(urlNavigate, popupParams.popupName, popupParams.popupWindowAttributes);\r\n            }\r\n\r\n            // Popup will be null if popups are blocked\r\n            if (!popupWindow) {\r\n                throw BrowserAuthError.createEmptyWindowCreatedError();\r\n            }\r\n            if (popupWindow.focus) {\r\n                popupWindow.focus();\r\n            }\r\n            this.currentWindow = popupWindow;\r\n            window.addEventListener(\"beforeunload\", this.unloadWindow);\r\n\r\n            return popupWindow;\r\n        } catch (e) {\r\n            this.logger.error(\"error opening popup \" + (e as AuthError).message);\r\n            this.browserStorage.setInteractionInProgress(false);\r\n            throw BrowserAuthError.createPopupWindowError((e as AuthError).toString());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper function to set popup window dimensions and position\r\n     * @param urlNavigate\r\n     * @param popupName\r\n     * @param popupWindowAttributes\r\n     * @returns\r\n     */\r\n    openSizedPopup(urlNavigate: string, popupName: string, popupWindowAttributes: PopupWindowAttributes): Window|null {\r\n        /**\r\n         * adding winLeft and winTop to account for dual monitor\r\n         * using screenLeft and screenTop for IE8 and earlier\r\n         */\r\n        const winLeft = window.screenLeft ? window.screenLeft : window.screenX;\r\n        const winTop = window.screenTop ? window.screenTop : window.screenY;\r\n        /**\r\n         * window.innerWidth displays browser window\"s height and width excluding toolbars\r\n         * using document.documentElement.clientWidth for IE8 and earlier\r\n         */\r\n        const winWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\r\n        const winHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;\r\n\r\n        let width = popupWindowAttributes.popupSize?.width;\r\n        let height = popupWindowAttributes.popupSize?.height;\r\n        let top = popupWindowAttributes.popupPosition?.top;\r\n        let left = popupWindowAttributes.popupPosition?.left;\r\n\r\n        if (!width || width < 0 || width > winWidth) {\r\n            this.logger.verbose(\"Default popup window width used. Window width not configured or invalid.\");\r\n            width = BrowserConstants.POPUP_WIDTH;\r\n        }\r\n\r\n        if (!height || height < 0 || height > winHeight) {\r\n            this.logger.verbose(\"Default popup window height used. Window height not configured or invalid.\");\r\n            height = BrowserConstants.POPUP_HEIGHT;\r\n        }\r\n\r\n        if (!top || top < 0 || top > winHeight) {\r\n            this.logger.verbose(\"Default popup window top position used. Window top not configured or invalid.\");\r\n            top = Math.max(0, ((winHeight / 2) - (BrowserConstants.POPUP_HEIGHT / 2)) + winTop);\r\n        }\r\n\r\n        if (!left || left < 0 || left > winWidth) {\r\n            this.logger.verbose(\"Default popup window left position used. Window left not configured or invalid.\");\r\n            left = Math.max(0, ((winWidth / 2) - (BrowserConstants.POPUP_WIDTH / 2)) + winLeft);\r\n        }\r\n\r\n        return window.open(urlNavigate, popupName, `width=${width}, height=${height}, top=${top}, left=${left}, scrollbars=yes`);\r\n    }\r\n\r\n    /**\r\n     * Event callback to unload main window.\r\n     */\r\n    unloadWindow(e: Event): void {\r\n        this.browserStorage.cleanRequestByInteractionType(InteractionType.Popup);\r\n        if (this.currentWindow) {\r\n            this.currentWindow.close();\r\n        }\r\n        // Guarantees browser unload will happen, so no other errors will be thrown.\r\n        e.preventDefault();\r\n    }\r\n\r\n    /**\r\n     * Closes popup, removes any state vars created during popup calls.\r\n     * @param popupWindow\r\n     */\r\n    cleanPopup(popupWindow?: Window): void {\r\n        if (popupWindow) {\r\n            // Close window.\r\n            popupWindow.close();\r\n        }\r\n        // Remove window unload function\r\n        window.removeEventListener(\"beforeunload\", this.unloadWindow);\r\n\r\n        // Interaction is completed - remove interaction status.\r\n        this.browserStorage.setInteractionInProgress(false);\r\n    }\r\n\r\n    /**\r\n     * Generates the name for the popup based on the client id and request\r\n     * @param clientId\r\n     * @param request\r\n     */\r\n    generatePopupName(scopes: Array<string>, authority: string): string {\r\n        return `${BrowserConstants.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${scopes.join(\"-\")}.${authority}.${this.correlationId}`;\r\n    }\r\n\r\n    /**\r\n     * Generates the name for the popup based on the client id and request for logouts\r\n     * @param clientId\r\n     * @param request\r\n     */\r\n    generateLogoutPopupName(request: CommonEndSessionRequest): string {\r\n        const homeAccountId = request.account && request.account.homeAccountId;\r\n        return `${BrowserConstants.POPUP_NAME_PREFIX}.${this.config.auth.clientId}.${homeAccountId}.${this.correlationId}`;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AA0BH,IAAA,WAAA,kBAAA,UAAA,MAAA,EAAA;IAAiC,SAAyB,CAAA,WAAA,EAAA,MAAA,CAAA,CAAA;AAItD,IAAA,SAAA,WAAA,CAAY,MAA4B,EAAE,WAAgC,EAAE,aAAsB,EAAE,MAAc,EAAE,YAA0B,EAAE,gBAAmC,EAAE,iBAAqC,EAAE,iBAAsC,EAAE,oBAA2C,EAAE,aAAsB,EAAA;AAAvU,QAAA,IAAA,KAAA,GACI,kBAAM,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,CAAC,IAI5I,IAAA,CAAA;;QAFG,KAAI,CAAC,YAAY,GAAG,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;AACjD,QAAA,KAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;;KAC1C;AAED;;;AAGG;IACH,WAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,OAAqB,EAAA;QAC9B,IAAI;YACA,IAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,IAAI,mBAAmB,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjI,YAAA,IAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,IAAI,EAAE,CAAC;;AAGlE,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;;gBAEhE,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC;AACjF,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;AACrF,gBAAA,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC;AACnF,gBAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,SAAS,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAC;AACxF,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,WAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,aAAsC,EAAA;QACzC,IAAI;AACA,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC1C,IAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAEvE,IAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;AACnE,YAAA,IAAM,SAAS,GAAG,aAAa,IAAI,aAAa,CAAC,SAAS,CAAC;AAC3D,YAAA,IAAM,qBAAqB,GAAG,aAAa,IAAI,aAAa,CAAC,qBAAqB,CAAC;AACnF,YAAA,IAAM,qBAAqB,GAAG,CAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,qBAAqB,KAAI,EAAE,CAAC;;AAGzE,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;;AAE/C,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,EAAE,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC;AACnI,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;AAC9D,gBAAA,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC;AACnF,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,EAAE,qBAAqB,EAAE,SAAS,EAAE,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAC/H,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;IACa,WAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtC,UAAuC,OAAqB,EAAE,SAAiB,EAAE,qBAA4C,EAAE,KAAmB,EAAA;;;;;;;AAC9I,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;wBAC/C,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAE9F,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,uDAAuD,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBACpH,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC,CAAA,CAAA;;AAAxF,wBAAA,YAAY,GAAG,EAAyE,CAAA,IAAA,EAAA,CAAA;AAC9F,wBAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;;;;;AAInL,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,2DAA2D,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACrF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,kCAAkC,CAAC,YAAY,CAAC,CAAA,CAAA;;AAA7G,wBAAA,eAAe,GAAmC,EAA2D,CAAA,IAAA,EAAA,CAAA;;AAGnH,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACnF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAA,CAAA;;AAArJ,wBAAA,UAAU,GAA4B,EAA+G,CAAA,IAAA,EAAA,CAAA;AAC3J,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;wBAE1C,cAAc,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE7I,wBAAA,+BAA+B,SAAA,CAAC;AACpC,wBAAA,IAAI,cAAc,EAAE;AAChB,4BAAA,+BAA+B,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACtJ,yBAAA;wBAGmB,OAAM,CAAA,CAAA,YAAA,UAAU,CAAC,cAAc,CAC5C,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,YAAY,KACf,YAAY,EAAE,cAAc,EAAA,CAAA,CAC9B,CAAA,CAAA;;AAHI,wBAAA,WAAW,GAAG,EAGlB,CAAA,IAAA,EAAA,CAAA;wBAGI,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAGnI,wBAAA,eAAe,GAAgB;AACjC,4BAAA,KAAK,EAAA,KAAA;AACL,4BAAA,SAAS,EAAA,SAAA;AACT,4BAAA,qBAAqB,EAAA,qBAAA;yBACxB,CAAC;wBACI,WAAW,GAAW,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;wBACnF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,CAAC,KAAK,EAAE,EAAC,WAAW,EAAA,WAAA,EAAC,EAAE,IAAI,CAAC,CAAC;AAGnF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA,CAAA;;AAAlD,wBAAA,IAAI,GAAG,EAA2C,CAAA,IAAA,EAAA,CAAA;AAElD,wBAAA,YAAY,GAAoC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACpF,wBAAA,OAAA,GAAQ,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,eAAe,CAAC,KAAK,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;;AAEpH,wBAAA,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;wBAEhG,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;;AAEvE,4BAAA,IAAI,+BAA+B,EAAE;gCACjC,+BAA+B,CAAC,cAAc,CAAC;AAC3C,oCAAA,OAAO,EAAE,IAAI;AACb,oCAAA,cAAc,EAAE,IAAI;AACvB,iCAAA,CAAC,CAAC;AACN,6BAAA;AAED,4BAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gCAAA,MAAM,gBAAgB,CAAC,yCAAyC,EAAE,CAAC;AACtE,6BAAA;4BACK,uBAAuB,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;AACrT,4BAAA,gBAAgB,GAAK,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,OAAK,CAAC,CAAA,gBAA/D,CAAgE;AACxF,4BAAA,OAAA,CAAA,CAAA,aAAO,uBAAuB,CAAC,YAAY,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACpC,YAAY,CACf,EAAA,EAAA,KAAK,EAAE,gBAAgB,EACvB,MAAM,EAAE,SAAS;AACnB,kCAAA,CAAA,CAAA,CAAC,OAAO,CAAC,YAAA;AACP,oCAAA,KAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,OAAK,CAAC,CAAC;AACnD,iCAAC,CAAC,CAAC,CAAA;AACN,yBAAA;AAGc,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,0BAA0B,CAAC,IAAI,EAAE,OAAK,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA,CAAA;;AAAnH,wBAAA,MAAM,GAAG,EAA0G,CAAA,IAAA,EAAA,CAAA;AAEzH,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;AAEd,wBAAA,IAAI,KAAK,EAAE;;4BAEP,KAAK,CAAC,KAAK,EAAE,CAAC;AACjB,yBAAA;wBAED,IAAI,GAAC,YAAY,SAAS,EAAE;AACvB,4BAAA,GAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,yBAAA;AAED,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5D,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;;;;;;;AAQG;AACa,IAAA,WAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhC,UAAiC,YAAqC,EAAE,SAAiB,EAAE,qBAA4C,EAAE,gBAAyB,EAAE,KAAmB,EAAE,qBAA8B,EAAA;;;;;;AACnN,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC/C,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;wBAEnF,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;;;;;wBAIpF,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA,CAAA;;;AAAnD,wBAAA,EAAA,CAAA,IAAA,EAAmD,CAAC;;AAGpD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;wBACjH,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAA,CAAA;;AAAtF,wBAAA,UAAU,GAAG,EAAyE,CAAA,IAAA,EAAA,CAAA;AAC5F,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAG1C,wBAAA,SAAS,GAAW,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAEhE,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAGrF,wBAAA,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,EAAC,SAAS,EAAA,SAAA,EAAE,qBAAqB,EAAA,qBAAA,EAAE,KAAK,EAAA,KAAA,EAAC,CAAC,CAAC;wBACzF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,CAAC,KAAK,EAAE,EAAC,WAAW,EAAA,WAAA,EAAC,EAAE,IAAI,CAAC,CAAC;AAEhG,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAA,CAAA;;AAA1C,wBAAA,EAAA,CAAA,IAAA,EAA0C,CAAC;AAE3C,wBAAA,IAAI,qBAAqB,EAAE;AACjB,4BAAA,iBAAiB,GAAsB;gCACzC,KAAK,EAAE,KAAK,CAAC,WAAW;AACxB,gCAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,gCAAA,SAAS,EAAE,KAAK;6BACnB,CAAC;AACI,4BAAA,WAAW,GAAG,SAAS,CAAC,cAAc,CAAC,qBAAqB,EAAE,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;AAElG,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yDAAyD,CAAC,CAAC;4BAC/E,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,8BAA+B,GAAA,WAAa,CAAC,CAAC;4BACrE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC1E,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAC9D,yBAAA;;;;AAED,wBAAA,IAAI,KAAK,EAAE;;4BAEP,KAAK,CAAC,KAAK,EAAE,CAAC;AACjB,yBAAA;wBAED,IAAI,GAAC,YAAY,SAAS,EAAE;AACvB,4BAAA,GAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,yBAAA;AAED,wBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,GAAC,CAAC,CAAC;AACtF,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;AACzE,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;AAC7C,wBAAA,MAAM,GAAC,CAAC;;AAGZ,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;;;;;AAC5E,KAAA,CAAA;AAED;;;AAGG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,UAAoB,UAAkB,EAAE,MAAmB,EAAA;;AAEvD,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,eAAgB,GAAA,UAAY,CAAC,CAAC;;YAElD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAC7C,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC3C,YAAA,MAAM,gBAAgB,CAAC,6BAA6B,EAAE,CAAC;AAC1D,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACH,WAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,WAAmB,EAAA;QAAvC,IAkEC,KAAA,GAAA,IAAA,CAAA;AAjEG,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AAC/B;;;AAGG;AACH,YAAA,IAAM,QAAQ,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC;YACpG,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,YAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;YAE1E,IAAM,UAAU,GAAG,WAAW,CAAC,YAAA;;gBAE3B,IAAI,WAAW,CAAC,MAAM,EAAE;AACpB,oBAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;oBACtE,KAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,oBAAA,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAC,CAAC;oBACpD,OAAO;AACV,iBAAA;AAED,gBAAA,IAAI,IAAI,GAAW,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAA,IAAI,IAAI,GAAW,SAAS,CAAC,YAAY,CAAC;gBAC1C,IAAI;AACA;;;;AAIG;AACH,oBAAA,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjC,oBAAA,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;AACpC,iBAAA;gBAAC,OAAO,CAAC,EAAE,GAAE;;gBAGd,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,EAAE;oBACrD,OAAO;AACV,iBAAA;AAED,gBAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6EAA6E,CAAC,CAAC;AAEnG;;;AAGG;AACH,gBAAA,KAAK,EAAE,CAAC;AAER,gBAAA,IAAI,IAAI,EAAE;AACN,oBAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;oBAC5E,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,oBAAA,KAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAE7B,oBAAA,IAAI,SAAS,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;AAC7C,wBAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+EAA+E,CAAC,CAAC;wBACrG,OAAO,CAAC,IAAI,CAAC,CAAC;AACjB,qBAAA;AAAM,yBAAA;AACH,wBAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6JAA6J,CAAC,CAAC;wBACjL,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iDAAkD,GAAA,IAAM,CAAC,CAAC;AAC/E,wBAAA,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,EAAE,CAAC,CAAC;AAC3E,qBAAA;AACJ,iBAAA;qBAAM,IAAI,KAAK,GAAG,QAAQ,EAAE;AACzB,oBAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2EAA2E,CAAC,CAAC;oBAC/F,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,oBAAA,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,EAAE,CAAC,CAAC;AAC7D,iBAAA;aACJ,EAAE,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;AACpD,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;IACH,WAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,WAAmB,EAAA;QAAtC,IAmCC,KAAA,GAAA,IAAA,CAAA;AAlCG,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAA;AACvB,YAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;YAEzE,IAAM,UAAU,GAAG,WAAW,CAAC,YAAA;;gBAE3B,IAAI,WAAW,CAAC,MAAM,EAAE;AACpB,oBAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;oBACrE,KAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA;AAED,gBAAA,IAAI,IAAI,GAAW,SAAS,CAAC,YAAY,CAAC;gBAC1C,IAAI;AACA;;;;AAIG;AACH,oBAAA,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;AACpC,iBAAA;gBAAC,OAAO,CAAC,EAAE,GAAE;;gBAGd,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,EAAE;oBACrD,OAAO;AACV,iBAAA;AAED,gBAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sFAAsF,CAAC,CAAC;gBAE5G,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,gBAAA,KAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC7B,gBAAA,OAAO,EAAE,CAAC;aACb,EAAE,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;AACpD,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,WAAmB,EAAE,WAAwB,EAAA;QACnD,IAAI;YACA,IAAI,WAAW,SAAA,CAAC;;YAEhB,IAAI,WAAW,CAAC,KAAK,EAAE;AACnB,gBAAA,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,8BAA+B,GAAA,WAAa,CAAC,CAAC;AACrE,gBAAA,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC5C,aAAA;AAAM,iBAAA,IAAI,OAAO,WAAW,CAAC,KAAK,KAAK,WAAW,EAAE;;gBAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,2BAA4B,GAAA,WAAa,CAAC,CAAC;AAClE,gBAAA,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAC5G,aAAA;;YAGD,IAAI,CAAC,WAAW,EAAE;AACd,gBAAA,MAAM,gBAAgB,CAAC,6BAA6B,EAAE,CAAC;AAC1D,aAAA;YACD,IAAI,WAAW,CAAC,KAAK,EAAE;gBACnB,WAAW,CAAC,KAAK,EAAE,CAAC;AACvB,aAAA;AACD,YAAA,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;YACjC,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAE3D,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,GAAI,CAAe,CAAC,OAAO,CAAC,CAAC;AACrE,YAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,gBAAgB,CAAC,sBAAsB,CAAE,CAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9E,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,WAAmB,EAAE,SAAiB,EAAE,qBAA4C,EAAA;;AAC/F;;;AAGG;AACH,QAAA,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC;AACvE,QAAA,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;AACpE;;;AAGG;AACH,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC,eAAe,CAAC,WAAW,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;AACxG,QAAA,IAAM,SAAS,GAAG,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;AAE5G,QAAA,IAAI,KAAK,GAAG,CAAA,EAAA,GAAA,qBAAqB,CAAC,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;AACnD,QAAA,IAAI,MAAM,GAAG,CAAA,EAAA,GAAA,qBAAqB,CAAC,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAC;AACrD,QAAA,IAAI,GAAG,GAAG,CAAA,EAAA,GAAA,qBAAqB,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,GAAG,CAAC;AACnD,QAAA,IAAI,IAAI,GAAG,CAAA,EAAA,GAAA,qBAAqB,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAC;QAErD,IAAI,CAAC,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,QAAQ,EAAE;AACzC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0EAA0E,CAAC,CAAC;AAChG,YAAA,KAAK,GAAG,gBAAgB,CAAC,WAAW,CAAC;AACxC,SAAA;QAED,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,SAAS,EAAE;AAC7C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4EAA4E,CAAC,CAAC;AAClG,YAAA,MAAM,GAAG,gBAAgB,CAAC,YAAY,CAAC;AAC1C,SAAA;QAED,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,SAAS,EAAE;AACpC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+EAA+E,CAAC,CAAC;YACrG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,gBAAgB,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AACvF,SAAA;QAED,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iFAAiF,CAAC,CAAC;YACvG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,KAAK,gBAAgB,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC;AACvF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,QAAA,GAAS,KAAK,GAAA,WAAA,GAAY,MAAM,GAAS,QAAA,GAAA,GAAG,eAAU,IAAI,GAAA,kBAAkB,CAAC,CAAC;KAC5H,CAAA;AAED;;AAEG;IACH,WAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,CAAQ,EAAA;QACjB,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC9B,SAAA;;QAED,CAAC,CAAC,cAAc,EAAE,CAAC;KACtB,CAAA;AAED;;;AAGG;IACH,WAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,WAAoB,EAAA;AAC3B,QAAA,IAAI,WAAW,EAAE;;YAEb,WAAW,CAAC,KAAK,EAAE,CAAC;AACvB,SAAA;;QAED,MAAM,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;;AAG9D,QAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;KACvD,CAAA;AAED;;;;AAIG;AACH,IAAA,WAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UAAkB,MAAqB,EAAE,SAAiB,EAAA;QACtD,OAAU,gBAAgB,CAAC,iBAAiB,GAAI,GAAA,GAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAA,GAAA,GAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAA,GAAA,GAAI,SAAS,GAAI,GAAA,GAAA,IAAI,CAAC,aAAe,CAAC;KACtI,CAAA;AAED;;;;AAIG;IACH,WAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,OAAgC,EAAA;QACpD,IAAM,aAAa,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;AACvE,QAAA,OAAU,gBAAgB,CAAC,iBAAiB,GAAI,GAAA,GAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAI,GAAA,GAAA,aAAa,SAAI,IAAI,CAAC,aAAe,CAAC;KACtH,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAhgBA,CAAiC,yBAAyB,CAggBzD;;;;"}