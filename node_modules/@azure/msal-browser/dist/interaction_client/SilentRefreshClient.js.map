{"version": 3, "file": "SilentRefreshClient.js", "sources": ["../../src/interaction_client/SilentRefreshClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { StandardInteractionClient } from \"./StandardInteractionClient\";\r\nimport { CommonSilentFlowRequest, AuthenticationResult, ServerTelemetryManager, RefreshTokenClient, AuthError, AzureCloudOptions, PerformanceEvents } from \"@azure/msal-common\";\r\nimport { ApiId } from \"../utils/BrowserConstants\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\n\r\nexport class SilentRefreshClient extends StandardInteractionClient {\r\n    /**\r\n     * Exchanges the refresh token for new tokens\r\n     * @param request\r\n     */\r\n    async acquireToken(request: CommonSilentFlowRequest): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentRefreshClientAcquireToken, request.correlationId);\r\n\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.InitializeBaseRequest, request.correlationId);\r\n        const silentRequest: CommonSilentFlowRequest = {\r\n            ...request,\r\n            ...await this.initializeBaseRequest(request, request.account)\r\n        };\r\n        const acquireTokenMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SilentRefreshClientAcquireToken, silentRequest.correlationId);\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenSilent_silentFlow);\r\n\r\n        const refreshTokenClient = await this.createRefreshTokenClient(serverTelemetryManager, silentRequest.authority, silentRequest.azureCloudOptions);\r\n        this.logger.verbose(\"Refresh token client created\");\r\n        // Send request to renew token. Auth module will throw errors if token cannot be renewed.\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.RefreshTokenClientAcquireTokenByRefreshToken, request.correlationId);\r\n        return refreshTokenClient.acquireTokenByRefreshToken(silentRequest)\r\n            .then((result: AuthenticationResult) => {\r\n                acquireTokenMeasurement.endMeasurement({\r\n                    success: true,\r\n                    fromCache: result.fromCache,\r\n                    requestId: result.requestId\r\n                });\r\n\r\n                return result;\r\n            })\r\n            .catch((e: AuthError) => {\r\n                if (e instanceof AuthError) {\r\n                    (e as AuthError).setCorrelationId(this.correlationId);\r\n                }\r\n                serverTelemetryManager.cacheFailedRequest(e);\r\n                acquireTokenMeasurement.endMeasurement({\r\n                    errorCode: e.errorCode,\r\n                    subErrorCode: e.subError,\r\n                    success: false\r\n                });\r\n                throw e;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Currently Unsupported\r\n     */\r\n    logout(): Promise<void> {\r\n        // Synchronous so we must reject\r\n        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());\r\n    }\r\n\r\n    /**\r\n     * Creates a Refresh Client with the given authority, or the default authority.\r\n     * @param serverTelemetryManager\r\n     * @param authorityUrl\r\n     */\r\n    protected async createRefreshTokenClient(serverTelemetryManager: ServerTelemetryManager, authorityUrl?: string, azureCloudOptions?: AzureCloudOptions): Promise<RefreshTokenClient> {\r\n        // Create auth module.\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, this.correlationId);\r\n        const clientConfig = await this.getClientConfiguration(serverTelemetryManager, authorityUrl, azureCloudOptions);\r\n        return new RefreshTokenClient(clientConfig, this.performanceClient);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAOH,IAAA,mBAAA,kBAAA,UAAA,MAAA,EAAA;IAAyC,SAAyB,CAAA,mBAAA,EAAA,MAAA,CAAA,CAAA;AAAlE,IAAA,SAAA,mBAAA,GAAA;;KA+DC;AA9DG;;;AAGG;IACG,mBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAgC,EAAA;;;;;;;AAC/C,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAErH,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;2CAEhG,OAAO,CAAA,CAAA,CAAA;wBACP,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA,CAAA;;wBAF3D,aAAa,GAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,CAAA,MAAA,CAAA,CAEZ,SAA0D,CAChE,CAAA,CAAA,CAAA;AACK,wBAAA,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;wBAClJ,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;AAE/E,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAA,CAAA;;AAA1I,wBAAA,kBAAkB,GAAG,EAAqH,CAAA,IAAA,EAAA,CAAA;AAChJ,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;;AAEpD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,4CAA4C,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9H,wBAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,0BAA0B,CAAC,aAAa,CAAC;iCAC9D,IAAI,CAAC,UAAC,MAA4B,EAAA;gCAC/B,uBAAuB,CAAC,cAAc,CAAC;AACnC,oCAAA,OAAO,EAAE,IAAI;oCACb,SAAS,EAAE,MAAM,CAAC,SAAS;oCAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,iCAAA,CAAC,CAAC;AAEH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACD,KAAK,CAAC,UAAC,CAAY,EAAA;gCAChB,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,oCAAA,CAAe,CAAC,gBAAgB,CAAC,KAAI,CAAC,aAAa,CAAC,CAAC;AACzD,iCAAA;AACD,gCAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gCAC7C,uBAAuB,CAAC,cAAc,CAAC;oCACnC,SAAS,EAAE,CAAC,CAAC,SAAS;oCACtB,YAAY,EAAE,CAAC,CAAC,QAAQ;AACxB,oCAAA,OAAO,EAAE,KAAK;AACjB,iCAAA,CAAC,CAAC;AACH,gCAAA,MAAM,CAAC,CAAC;AACZ,6BAAC,CAAC,CAAC,CAAA;;;;AACV,KAAA,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;;QAEI,OAAO,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,EAAE,CAAC,CAAC;KAChF,CAAA;AAED;;;;AAIG;AACa,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxC,UAAyC,sBAA8C,EAAE,YAAqB,EAAE,iBAAqC,EAAA;;;;;;;AAEjJ,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBACzG,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAA,CAAA;;AAAzG,wBAAA,YAAY,GAAG,EAA0F,CAAA,IAAA,EAAA,CAAA;wBAC/G,OAAO,CAAA,CAAA,aAAA,IAAI,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA;;;;AACvE,KAAA,CAAA;IACL,OAAC,mBAAA,CAAA;AAAD,CA/DA,CAAyC,yBAAyB,CA+DjE;;;;"}