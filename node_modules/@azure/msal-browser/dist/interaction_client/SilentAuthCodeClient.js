/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __extends, __awaiter, __generator, __assign } from '../_virtual/_tslib.js';
import { AuthError, PerformanceEvents, Constants } from '@azure/msal-common';
import { StandardInteractionClient } from './StandardInteractionClient.js';
import { BrowserAuthError } from '../error/BrowserAuthError.js';
import { InteractionType } from '../utils/BrowserConstants.js';
import { SilentHandler } from '../interaction_handler/SilentHandler.js';
import { HybridSpaAuthorizationCodeClient } from './HybridSpaAuthorizationCodeClient.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var SilentAuthCodeClient = /** @class */ (function (_super) {
    __extends(SilentAuthCodeClient, _super);
    function SilentAuthCodeClient(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, apiId, performanceClient, nativeMessageHandler, correlationId) {
        var _this = _super.call(this, config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient, nativeMessageHandler, correlationId) || this;
        _this.apiId = apiId;
        return _this;
    }
    /**
     * Acquires a token silently by redeeming an authorization code against the /token endpoint
     * @param request
     */
    SilentAuthCodeClient.prototype.acquireToken = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var silentRequest, serverTelemetryManager, authCodeRequest, clientConfig, authClient, silentHandler, e_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.logger.trace("SilentAuthCodeClient.acquireToken called");
                        // Auth code payload is required
                        if (!request.code) {
                            throw BrowserAuthError.createAuthCodeRequiredError();
                        }
                        // Create silent request
                        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest, request.correlationId);
                        return [4 /*yield*/, this.initializeAuthorizationRequest(request, InteractionType.Silent)];
                    case 1:
                        silentRequest = _a.sent();
                        this.browserStorage.updateCacheEntries(silentRequest.state, silentRequest.nonce, silentRequest.authority, silentRequest.loginHint || Constants.EMPTY_STRING, silentRequest.account || null);
                        serverTelemetryManager = this.initializeServerTelemetryManager(this.apiId);
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        authCodeRequest = __assign(__assign({}, silentRequest), { code: request.code });
                        // Initialize the client
                        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, request.correlationId);
                        return [4 /*yield*/, this.getClientConfiguration(serverTelemetryManager, silentRequest.authority)];
                    case 3:
                        clientConfig = _a.sent();
                        authClient = new HybridSpaAuthorizationCodeClient(clientConfig);
                        this.logger.verbose("Auth code client created");
                        silentHandler = new SilentHandler(authClient, this.browserStorage, authCodeRequest, this.logger, this.config.system, this.performanceClient);
                        // Handle auth code parameters from request
                        return [2 /*return*/, silentHandler.handleCodeResponseFromServer({
                                code: request.code,
                                msgraph_host: request.msGraphHost,
                                cloud_graph_host_name: request.cloudGraphHostName,
                                cloud_instance_host_name: request.cloudInstanceHostName
                            }, silentRequest.state, authClient.authority, this.networkClient, false)];
                    case 4:
                        e_1 = _a.sent();
                        if (e_1 instanceof AuthError) {
                            e_1.setCorrelationId(this.correlationId);
                        }
                        serverTelemetryManager.cacheFailedRequest(e_1);
                        this.browserStorage.cleanRequestByState(silentRequest.state);
                        throw e_1;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Currently Unsupported
     */
    SilentAuthCodeClient.prototype.logout = function () {
        // Synchronous so we must reject
        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());
    };
    return SilentAuthCodeClient;
}(StandardInteractionClient));

export { SilentAuthCodeClient };
//# sourceMappingURL=SilentAuthCodeClient.js.map
