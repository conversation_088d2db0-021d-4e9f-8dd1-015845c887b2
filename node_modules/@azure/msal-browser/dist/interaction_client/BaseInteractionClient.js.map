{"version": 3, "file": "BaseInteractionClient.js", "sources": ["../../src/interaction_client/BaseInteractionClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ICrypto, INetworkModule, Logger, AuthenticationResult, AccountInfo, AccountEntity, BaseAuthRequest, AuthenticationScheme, UrlString, ServerTelemetryManager, ServerTelemetryRequest, ClientConfigurationError, StringUtils, Authority, AuthorityOptions, AuthorityFactory, IPerformanceClient, PerformanceEvents } from \"@azure/msal-common\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { EndSessionRequest } from \"../request/EndSessionRequest\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\nimport { version } from \"../packageMetadata\";\r\nimport { BrowserConstants } from \"../utils/BrowserConstants\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\n\r\nexport abstract class BaseInteractionClient {\r\n\r\n    protected config: BrowserConfiguration;\r\n    protected browserStorage: BrowserCacheManager;\r\n    protected browserCrypto: ICrypto;\r\n    protected networkClient: INetworkModule;\r\n    protected logger: Logger;\r\n    protected eventHandler: EventHandler;\r\n    protected navigationClient: INavigationClient;\r\n    protected nativeMessageHandler: NativeMessageHandler | undefined;\r\n    protected correlationId: string;\r\n    protected performanceClient: IPerformanceClient;\r\n\r\n    constructor(config: BrowserConfiguration, storageImpl: BrowserCacheManager, browserCrypto: ICrypto, logger: Logger, eventHandler: EventHandler, navigationClient: INavigationClient, performanceClient: IPerformanceClient, nativeMessageHandler?: NativeMessageHandler, correlationId?: string) {\r\n        this.config = config;\r\n        this.browserStorage = storageImpl;\r\n        this.browserCrypto = browserCrypto;\r\n        this.networkClient = this.config.system.networkClient;\r\n        this.eventHandler = eventHandler;\r\n        this.navigationClient = navigationClient;\r\n        this.nativeMessageHandler = nativeMessageHandler;\r\n        this.correlationId = correlationId || this.browserCrypto.createNewGuid();\r\n        this.logger = logger.clone(BrowserConstants.MSAL_SKU, version, this.correlationId);\r\n        this.performanceClient = performanceClient;\r\n    }\r\n\r\n    abstract acquireToken(request: RedirectRequest|PopupRequest|SsoSilentRequest): Promise<AuthenticationResult|void>;\r\n\r\n    abstract logout(request: EndSessionRequest): Promise<void>;\r\n\r\n    protected async clearCacheOnLogout(account?: AccountInfo| null): Promise<void> {\r\n        if (account) {\r\n            if (AccountEntity.accountInfoIsEqual(account, this.browserStorage.getActiveAccount(), false)) {\r\n                this.logger.verbose(\"Setting active account to null\");\r\n                this.browserStorage.setActiveAccount(null);\r\n            }\r\n            // Clear given account.\r\n            try {\r\n                await this.browserStorage.removeAccount(AccountEntity.generateAccountCacheKey(account));\r\n                this.logger.verbose(\"Cleared cache items belonging to the account provided in the logout request.\");\r\n            } catch (error) {\r\n                this.logger.error(\"Account provided in logout request was not found. Local cache unchanged.\");\r\n            }\r\n        } else {\r\n            try {\r\n                this.logger.verbose(\"No account provided in logout request, clearing all cache items.\", this.correlationId);\r\n                // Clear all accounts and tokens\r\n                await this.browserStorage.clear();\r\n                // Clear any stray keys from IndexedDB\r\n                await this.browserCrypto.clearKeystore();\r\n            } catch(e) {\r\n                this.logger.error(\"Attempted to clear all MSAL cache items and failed. Local cache unchanged.\");\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Initializer function for all request APIs\r\n     * @param request\r\n     */\r\n    protected async initializeBaseRequest(request: Partial<BaseAuthRequest>, account?: AccountInfo): Promise<BaseAuthRequest> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.InitializeBaseRequest, request.correlationId);\r\n        this.logger.verbose(\"Initializing BaseAuthRequest\");\r\n        const authority = request.authority || this.config.auth.authority;\r\n\r\n        if (account) {\r\n            await this.validateRequestAuthority(authority, account);\r\n        }\r\n\r\n        const scopes = [...((request && request.scopes) || [])];\r\n\r\n        const validatedRequest: BaseAuthRequest = {\r\n            ...request,\r\n            correlationId: this.correlationId,\r\n            authority,\r\n            scopes\r\n        };\r\n\r\n        // Set authenticationScheme to BEARER if not explicitly set in the request\r\n        if (!validatedRequest.authenticationScheme) {\r\n            validatedRequest.authenticationScheme = AuthenticationScheme.BEARER;\r\n            this.logger.verbose(\"Authentication Scheme wasn't explicitly set in request, defaulting to \\\"Bearer\\\" request\");\r\n        } else {\r\n            if (validatedRequest.authenticationScheme === AuthenticationScheme.SSH) {\r\n                if (!request.sshJwk) {\r\n                    throw ClientConfigurationError.createMissingSshJwkError();\r\n                }\r\n                if(!request.sshKid) {\r\n                    throw ClientConfigurationError.createMissingSshKidError();\r\n                }\r\n            }\r\n            this.logger.verbose(`Authentication Scheme set to \"${validatedRequest.authenticationScheme}\" as configured in Auth request`);\r\n        }\r\n\r\n        // Set requested claims hash if claims were requested and claims-based caching is enabled\r\n        if (this.config.cache.claimsBasedCachingEnabled && request.claims && !StringUtils.isEmptyObj(request.claims)) {\r\n            validatedRequest.requestedClaimsHash = await this.browserCrypto.hashString(request.claims);\r\n        }\r\n\r\n        return validatedRequest;\r\n    }\r\n\r\n    /**\r\n     *\r\n     * Use to get the redirect uri configured in MSAL or null.\r\n     * @param requestRedirectUri\r\n     * @returns Redirect URL\r\n     *\r\n     */\r\n    getRedirectUri(requestRedirectUri?: string): string {\r\n        this.logger.verbose(\"getRedirectUri called\");\r\n        const redirectUri = requestRedirectUri || this.config.auth.redirectUri || BrowserUtils.getCurrentUri();\r\n        return UrlString.getAbsoluteUrl(redirectUri, BrowserUtils.getCurrentUri());\r\n    }\r\n\r\n    /*\r\n     * If authority provided in the request does not match environment/authority specified \r\n     * in the account or MSAL config, we throw an error.\r\n     */\r\n    async validateRequestAuthority(authority: string, account: AccountInfo): Promise<void> {\r\n        const discoveredAuthority = await this.getDiscoveredAuthority(authority);\r\n        \r\n        if(!discoveredAuthority.isAlias(account.environment)) {\r\n            throw ClientConfigurationError.createAuthorityMismatchError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @param apiId\r\n     * @param correlationId\r\n     * @param forceRefresh\r\n     */\r\n    protected initializeServerTelemetryManager(apiId: number, forceRefresh?: boolean): ServerTelemetryManager {\r\n        this.logger.verbose(\"initializeServerTelemetryManager called\");\r\n        const telemetryPayload: ServerTelemetryRequest = {\r\n            clientId: this.config.auth.clientId,\r\n            correlationId: this.correlationId,\r\n            apiId: apiId,\r\n            forceRefresh: forceRefresh || false,\r\n            wrapperSKU: this.browserStorage.getWrapperMetadata()[0],\r\n            wrapperVer: this.browserStorage.getWrapperMetadata()[1]\r\n        };\r\n\r\n        return new ServerTelemetryManager(telemetryPayload, this.browserStorage);\r\n    }\r\n\r\n    /**\r\n     * Used to get a discovered version of the default authority.\r\n     * @param requestAuthority\r\n     * @param requestCorrelationId\r\n     */\r\n    protected async getDiscoveredAuthority(requestAuthority?: string): Promise<Authority> {\r\n        this.logger.verbose(\"getDiscoveredAuthority called\");\r\n        const authorityOptions: AuthorityOptions = {\r\n            protocolMode: this.config.auth.protocolMode,\r\n            knownAuthorities: this.config.auth.knownAuthorities,\r\n            cloudDiscoveryMetadata: this.config.auth.cloudDiscoveryMetadata,\r\n            authorityMetadata: this.config.auth.authorityMetadata\r\n        };\r\n\r\n        if (requestAuthority) {\r\n            this.logger.verbose(\"Creating discovered authority with request authority\");\r\n            return await AuthorityFactory.createDiscoveredInstance(requestAuthority, this.config.system.networkClient, this.browserStorage, authorityOptions, this.logger);\r\n        }\r\n\r\n        this.logger.verbose(\"Creating discovered authority with configured authority\");\r\n        return await AuthorityFactory.createDiscoveredInstance(this.config.auth.authority, this.config.system.networkClient, this.browserStorage, authorityOptions, this.logger);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAgBH,IAAA,qBAAA,kBAAA,YAAA;AAaI,IAAA,SAAA,qBAAA,CAAY,MAA4B,EAAE,WAAgC,EAAE,aAAsB,EAAE,MAAc,EAAE,YAA0B,EAAE,gBAAmC,EAAE,iBAAqC,EAAE,oBAA2C,EAAE,aAAsB,EAAA;AAC3R,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AACtD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACzE,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACnF,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;IAMe,qBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlC,UAAmC,OAA2B,EAAA;;;;;AACtD,wBAAA,IAAA,CAAA,OAAO,EAAP,OAAO,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACP,wBAAA,IAAI,aAAa,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,EAAE;AAC1F,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACtD,4BAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC9C,yBAAA;;;;AAGG,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAA,CAAA;;AAAvF,wBAAA,EAAA,CAAA,IAAA,EAAuF,CAAC;AACxF,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8EAA8E,CAAC,CAAC;;;;AAEpG,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC;;;;;wBAI9F,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kEAAkE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;AAE5G,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAA,CAAA;;;AAAjC,wBAAA,EAAA,CAAA,IAAA,EAAiC,CAAC;;AAElC,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAA,CAAA;;;AAAxC,wBAAA,EAAA,CAAA,IAAA,EAAwC,CAAC;;;;AAEzC,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;;;;;;AAG3G,KAAA,CAAA;AAED;;;AAGG;AACa,IAAA,qBAAA,CAAA,SAAA,CAAA,qBAAqB,GAArC,UAAsC,OAAiC,EAAE,OAAqB,EAAA;;;;;;AAC1F,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC3G,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAC9C,wBAAA,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAE9D,wBAAA,IAAA,CAAA,OAAO,EAAP,OAAO,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACP,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAvD,wBAAA,EAAA,CAAA,IAAA,EAAuD,CAAC;;;AAGtD,wBAAA,MAAM,GAAO,QAAA,EAAC,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;wBAElD,gBAAgB,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACf,OAAO,CAAA,EAAA,EACV,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,SAAS,EAAA,SAAA;4BACT,MAAM,EAAA,MAAA,GACT,CAAC;;AAGF,wBAAA,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE;AACxC,4BAAA,gBAAgB,CAAC,oBAAoB,GAAG,oBAAoB,CAAC,MAAM,CAAC;AACpE,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0FAA0F,CAAC,CAAC;AACnH,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,gBAAgB,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;AACpE,gCAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,oCAAA,MAAM,wBAAwB,CAAC,wBAAwB,EAAE,CAAC;AAC7D,iCAAA;AACD,gCAAA,IAAG,CAAC,OAAO,CAAC,MAAM,EAAE;AAChB,oCAAA,MAAM,wBAAwB,CAAC,wBAAwB,EAAE,CAAC;AAC7D,iCAAA;AACJ,6BAAA;4BACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,GAAA,gBAAgB,CAAC,oBAAoB,GAAiC,kCAAA,CAAC,CAAC;AAChI,yBAAA;8BAGG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,EAAxG,OAAwG,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACxG,wBAAA,EAAA,GAAA,gBAAgB,CAAA;wBAAuB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,CAAA;;wBAA1F,EAAiB,CAAA,mBAAmB,GAAG,EAAA,CAAA,IAAA,EAAmD,CAAC;;AAG/F,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,CAAA;;;;AAC3B,KAAA,CAAA;AAED;;;;;;AAMG;IACH,qBAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,kBAA2B,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC7C,QAAA,IAAM,WAAW,GAAG,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;QACvG,OAAO,SAAS,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9E,CAAA;AAED;;;AAGG;AACG,IAAA,qBAAA,CAAA,SAAA,CAAA,wBAAwB,GAA9B,UAA+B,SAAiB,EAAE,OAAoB,EAAA;;;;;AACtC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAlE,wBAAA,mBAAmB,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;wBAExE,IAAG,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAClD,4BAAA,MAAM,wBAAwB,CAAC,4BAA4B,EAAE,CAAC;AACjE,yBAAA;;;;;AACJ,KAAA,CAAA;AAED;;;;;AAKG;AACO,IAAA,qBAAA,CAAA,SAAA,CAAA,gCAAgC,GAA1C,UAA2C,KAAa,EAAE,YAAsB,EAAA;AAC5E,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAC/D,QAAA,IAAM,gBAAgB,GAA2B;AAC7C,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACnC,aAAa,EAAE,IAAI,CAAC,aAAa;AACjC,YAAA,KAAK,EAAE,KAAK;YACZ,YAAY,EAAE,YAAY,IAAI,KAAK;YACnC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACvD,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;SAC1D,CAAC;QAEF,OAAO,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KAC5E,CAAA;AAED;;;;AAIG;IACa,qBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtC,UAAuC,gBAAyB,EAAA;;;;;;AAC5D,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC/C,wBAAA,gBAAgB,GAAqB;AACvC,4BAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;AAC3C,4BAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;AACnD,4BAAA,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAC/D,4BAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;yBACxD,CAAC;AAEE,wBAAA,IAAA,CAAA,gBAAgB,EAAhB,OAAgB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAChB,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sDAAsD,CAAC,CAAC;wBACrE,OAAM,CAAA,CAAA,YAAA,gBAAgB,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA,CAAA;AAA9J,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAuJ,CAAC,CAAA;;AAGnK,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yDAAyD,CAAC,CAAC;AACxE,wBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA,CAAA;AAAxK,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAiK,CAAC,CAAA;;;;AAC5K,KAAA,CAAA;IACL,OAAC,qBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}