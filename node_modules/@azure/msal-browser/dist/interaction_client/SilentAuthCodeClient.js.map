{"version": 3, "file": "SilentAuthCodeClient.js", "sources": ["../../src/interaction_client/SilentAuthCodeClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, ICrypto, Logger, CommonAuthorizationCodeRequest, AuthError, Constants, IPerformanceClient, PerformanceEvents } from \"@azure/msal-common\";\r\nimport { StandardInteractionClient } from \"./StandardInteractionClient\";\r\nimport { AuthorizationUrlRequest } from \"../request/AuthorizationUrlRequest\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { InteractionType, ApiId } from \"../utils/BrowserConstants\";\r\nimport { SilentHandler } from \"../interaction_handler/SilentHandler\";\r\nimport { AuthorizationCodeRequest } from \"../request/AuthorizationCodeRequest\";\r\nimport { HybridSpaAuthorizationCodeClient } from \"./HybridSpaAuthorizationCodeClient\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\n\r\nexport class SilentAuthCodeClient extends StandardInteractionClient {\r\n    private apiId: ApiId;\r\n\r\n    constructor(config: BrowserConfiguration, storageImpl: BrowserCacheManager, browserCrypto: ICrypto, logger: Logger, eventHandler: EventHandler, navigationClient: INavigationClient, apiId: ApiId, performanceClient: IPerformanceClient, nativeMessageHandler?: NativeMessageHandler, correlationId?: string) {\r\n        super(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient, nativeMessageHandler, correlationId);\r\n        this.apiId = apiId;\r\n    }\r\n\r\n    /**\r\n     * Acquires a token silently by redeeming an authorization code against the /token endpoint\r\n     * @param request\r\n     */\r\n    async acquireToken(request: AuthorizationCodeRequest): Promise<AuthenticationResult> {\r\n        this.logger.trace(\"SilentAuthCodeClient.acquireToken called\");\r\n\r\n        // Auth code payload is required\r\n        if (!request.code) {\r\n            throw BrowserAuthError.createAuthCodeRequiredError();\r\n\r\n        }\r\n\r\n        // Create silent request\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest, request.correlationId);\r\n        const silentRequest: AuthorizationUrlRequest = await this.initializeAuthorizationRequest(request, InteractionType.Silent);\r\n        this.browserStorage.updateCacheEntries(silentRequest.state, silentRequest.nonce, silentRequest.authority, silentRequest.loginHint || Constants.EMPTY_STRING, silentRequest.account || null);\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(this.apiId);\r\n\r\n        try {\r\n\r\n            // Create auth code request (PKCE not needed)\r\n            const authCodeRequest: CommonAuthorizationCodeRequest = {\r\n                ...silentRequest,\r\n                code: request.code\r\n            };\r\n\r\n            // Initialize the client\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, request.correlationId);\r\n            const clientConfig = await this.getClientConfiguration(serverTelemetryManager, silentRequest.authority);\r\n            const authClient: HybridSpaAuthorizationCodeClient = new HybridSpaAuthorizationCodeClient(clientConfig);\r\n            this.logger.verbose(\"Auth code client created\");\r\n\r\n            // Create silent handler\r\n            const silentHandler = new SilentHandler(authClient, this.browserStorage, authCodeRequest, this.logger, this.config.system, this.performanceClient);\r\n\r\n            // Handle auth code parameters from request\r\n            return silentHandler.handleCodeResponseFromServer(\r\n                {\r\n                    code: request.code,\r\n                    msgraph_host: request.msGraphHost,\r\n                    cloud_graph_host_name: request.cloudGraphHostName,\r\n                    cloud_instance_host_name: request.cloudInstanceHostName\r\n                },\r\n                silentRequest.state,\r\n                authClient.authority,\r\n                this.networkClient,\r\n                false\r\n            );\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                (e as AuthError).setCorrelationId(this.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            this.browserStorage.cleanRequestByState(silentRequest.state);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Currently Unsupported\r\n     */\r\n    logout(): Promise<void> {\r\n        // Synchronous so we must reject\r\n        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAgBH,IAAA,oBAAA,kBAAA,UAAA,MAAA,EAAA;IAA0C,SAAyB,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;AAG/D,IAAA,SAAA,oBAAA,CAAY,MAA4B,EAAE,WAAgC,EAAE,aAAsB,EAAE,MAAc,EAAE,YAA0B,EAAE,gBAAmC,EAAE,KAAY,EAAE,iBAAqC,EAAE,oBAA2C,EAAE,aAAsB,EAAA;AAA7S,QAAA,IAAA,KAAA,GACI,kBAAM,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,CAAC,IAE5I,IAAA,CAAA;AADG,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;;KACtB;AAED;;;AAGG;IACG,oBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAiC,EAAA;;;;;;AAChD,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;;AAG9D,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACf,4BAAA,MAAM,gBAAgB,CAAC,2BAA2B,EAAE,CAAC;AAExD,yBAAA;;AAGD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,uDAAuD,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC1F,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,eAAe,CAAC,MAAM,CAAC,CAAA,CAAA;;AAAnH,wBAAA,aAAa,GAA4B,EAA0E,CAAA,IAAA,EAAA,CAAA;AACzH,wBAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EAAE,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;wBAEtL,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;wBAKvE,eAAe,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACd,aAAa,CAChB,EAAA,EAAA,IAAI,EAAE,OAAO,CAAC,IAAI,EAAA,CACrB,CAAC;;AAGF,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC5G,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,aAAa,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAjG,wBAAA,YAAY,GAAG,EAAkF,CAAA,IAAA,EAAA,CAAA;AACjG,wBAAA,UAAU,GAAqC,IAAI,gCAAgC,CAAC,YAAY,CAAC,CAAC;AACxG,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;wBAG1C,aAAa,GAAG,IAAI,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;;wBAGnJ,OAAO,CAAA,CAAA,aAAA,aAAa,CAAC,4BAA4B,CAC7C;gCACI,IAAI,EAAE,OAAO,CAAC,IAAI;gCAClB,YAAY,EAAE,OAAO,CAAC,WAAW;gCACjC,qBAAqB,EAAE,OAAO,CAAC,kBAAkB;gCACjD,wBAAwB,EAAE,OAAO,CAAC,qBAAqB;AAC1D,6BAAA,EACD,aAAa,CAAC,KAAK,EACnB,UAAU,CAAC,SAAS,EACpB,IAAI,CAAC,aAAa,EAClB,KAAK,CACR,CAAC,CAAA;;;wBAEF,IAAI,GAAC,YAAY,SAAS,EAAE;AACvB,4BAAA,GAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,yBAAA;AACD,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7D,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;AAEG;AACH,IAAA,oBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;;QAEI,OAAO,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,EAAE,CAAC,CAAC;KAChF,CAAA;IACL,OAAC,oBAAA,CAAA;AAAD,CA3EA,CAA0C,yBAAyB,CA2ElE;;;;"}