{"version": 3, "file": "RedirectClient.js", "sources": ["../../src/interaction_client/RedirectClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, CommonAuthorizationCodeRequest, AuthorizationCodeClient, UrlString, AuthError, ServerTelemetryManager, Constants, ProtocolUtils, ServerAuthorizationCodeResponse, ThrottlingUtils, ICrypto, Logger, IPerformanceClient, PerformanceEvents } from \"@azure/msal-common\";\r\nimport { StandardInteractionClient } from \"./StandardInteractionClient\";\r\nimport { ApiId, InteractionType, TemporaryCacheKeys } from \"../utils/BrowserConstants\";\r\nimport { RedirectHandler } from \"../interaction_handler/RedirectHandler\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { EndSessionRequest } from \"../request/EndSessionRequest\";\r\nimport { EventType } from \"../event/EventType\";\r\nimport { NavigationOptions } from \"../navigation/NavigationOptions\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { NativeInteractionClient } from \"./NativeInteractionClient\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\n\r\nexport class RedirectClient extends StandardInteractionClient {\r\n    protected nativeStorage: BrowserCacheManager;\r\n\r\n    constructor(config: BrowserConfiguration, storageImpl: BrowserCacheManager, browserCrypto: ICrypto, logger: Logger, eventHandler: EventHandler, navigationClient: INavigationClient, performanceClient: IPerformanceClient, nativeStorageImpl: BrowserCacheManager, nativeMessageHandler?: NativeMessageHandler, correlationId?: string) {\r\n        super(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient, nativeMessageHandler, correlationId);\r\n        this.nativeStorage = nativeStorageImpl;\r\n    }\r\n\r\n    /**\r\n     * Redirects the page to the /authorize endpoint of the IDP\r\n     * @param request\r\n     */\r\n    async acquireToken(request: RedirectRequest): Promise<void> {\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest, request.correlationId);\r\n        const validRequest = await this.initializeAuthorizationRequest(request, InteractionType.Redirect);\r\n        this.browserStorage.updateCacheEntries(validRequest.state, validRequest.nonce, validRequest.authority, validRequest.loginHint || Constants.EMPTY_STRING, validRequest.account || null);\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenRedirect);\r\n\r\n        const handleBackButton = (event: PageTransitionEvent) => {\r\n            // Clear temporary cache if the back button is clicked during the redirect flow.\r\n            if (event.persisted) {\r\n                this.logger.verbose(\"Page was restored from back/forward cache. Clearing temporary cache.\");\r\n                this.browserStorage.cleanRequestByState(validRequest.state);\r\n                this.eventHandler.emitEvent(EventType.RESTORE_FROM_BFCACHE, InteractionType.Redirect);\r\n            }\r\n        };\r\n\r\n        try {\r\n            // Create auth code request and generate PKCE params\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest, request.correlationId);\r\n            const authCodeRequest: CommonAuthorizationCodeRequest = await this.initializeAuthorizationCodeRequest(validRequest);\r\n\r\n            // Initialize the client\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, request.correlationId);\r\n            const authClient: AuthorizationCodeClient = await this.createAuthCodeClient(serverTelemetryManager, validRequest.authority, validRequest.azureCloudOptions);\r\n            this.logger.verbose(\"Auth code client created\");\r\n\r\n            // Create redirect interaction handler.\r\n            const interactionHandler = new RedirectHandler(authClient, this.browserStorage, authCodeRequest, this.logger, this.browserCrypto, this.performanceClient);\r\n\r\n            // Create acquire token url.\r\n            const navigateUrl = await authClient.getAuthCodeUrl({\r\n                ...validRequest,\r\n                nativeBroker: NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeMessageHandler, request.authenticationScheme)\r\n            });\r\n\r\n            const redirectStartPage = this.getRedirectStartPage(request.redirectStartPage);\r\n            this.logger.verbosePii(`Redirect start page: ${redirectStartPage}`);\r\n\r\n            // Clear temporary cache if the back button is clicked during the redirect flow.\r\n            window.addEventListener(\"pageshow\", handleBackButton);\r\n\r\n            // Show the UI once the url has been created. Response will come back in the hash, which will be handled in the handleRedirectCallback function.\r\n            return await interactionHandler.initiateAuthRequest(navigateUrl, {\r\n                navigationClient: this.navigationClient,\r\n                redirectTimeout: this.config.system.redirectNavigationTimeout,\r\n                redirectStartPage: redirectStartPage,\r\n                onRedirectNavigate: request.onRedirectNavigate\r\n            });\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(this.correlationId);\r\n            }\r\n            window.removeEventListener(\"pageshow\", handleBackButton);\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            this.browserStorage.cleanRequestByState(validRequest.state);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks if navigateToLoginRequestUrl is set, and:\r\n     * - if true, performs logic to cache and navigate\r\n     * - if false, handles hash string and parses response\r\n     * @param hash\r\n     */\r\n    async handleRedirectPromise(hash?: string): Promise<AuthenticationResult | null> {\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.handleRedirectPromise);\r\n        try {\r\n            if (!this.browserStorage.isInteractionInProgress(true)) {\r\n                this.logger.info(\"handleRedirectPromise called but there is no interaction in progress, returning null.\");\r\n                return null;\r\n            }\r\n\r\n            const responseHash = this.getRedirectResponseHash(hash || window.location.hash);\r\n            if (!responseHash) {\r\n                // Not a recognized server response hash or hash not associated with a redirect request\r\n                this.logger.info(\"handleRedirectPromise did not detect a response hash as a result of a redirect. Cleaning temporary cache.\");\r\n                this.browserStorage.cleanRequestByInteractionType(InteractionType.Redirect);\r\n                return null;\r\n            }\r\n\r\n            let state: string;\r\n            try {\r\n                // Deserialize hash fragment response parameters.\r\n                const serverParams: ServerAuthorizationCodeResponse = UrlString.getDeserializedHash(responseHash);\r\n                state = this.validateAndExtractStateFromHash(serverParams, InteractionType.Redirect);\r\n                this.logger.verbose(\"State extracted from hash\");\r\n            } catch (e) {\r\n                this.logger.info(`handleRedirectPromise was unable to extract state due to: ${e}`);\r\n                this.browserStorage.cleanRequestByInteractionType(InteractionType.Redirect);\r\n                return null;\r\n            }\r\n\r\n            // If navigateToLoginRequestUrl is true, get the url where the redirect request was initiated\r\n            const loginRequestUrl = this.browserStorage.getTemporaryCache(TemporaryCacheKeys.ORIGIN_URI, true) || Constants.EMPTY_STRING;\r\n            const loginRequestUrlNormalized = UrlString.removeHashFromUrl(loginRequestUrl);\r\n            const currentUrlNormalized = UrlString.removeHashFromUrl(window.location.href);\r\n\r\n            if (loginRequestUrlNormalized === currentUrlNormalized && this.config.auth.navigateToLoginRequestUrl) {\r\n                // We are on the page we need to navigate to - handle hash\r\n                this.logger.verbose(\"Current page is loginRequestUrl, handling hash\");\r\n                const handleHashResult = await this.handleHash(responseHash, state, serverTelemetryManager);\r\n\r\n                if (loginRequestUrl.indexOf(\"#\") > -1) {\r\n                    // Replace current hash with non-msal hash, if present\r\n                    BrowserUtils.replaceHash(loginRequestUrl);\r\n                }\r\n\r\n                return handleHashResult;\r\n            } else if (!this.config.auth.navigateToLoginRequestUrl) {\r\n                this.logger.verbose(\"NavigateToLoginRequestUrl set to false, handling hash\");\r\n                return this.handleHash(responseHash, state, serverTelemetryManager);\r\n            } else if (!BrowserUtils.isInIframe() || this.config.system.allowRedirectInIframe) {\r\n                /*\r\n                 * Returned from authority using redirect - need to perform navigation before processing response\r\n                 * Cache the hash to be retrieved after the next redirect\r\n                 */\r\n                this.browserStorage.setTemporaryCache(TemporaryCacheKeys.URL_HASH, responseHash, true);\r\n                const navigationOptions: NavigationOptions = {\r\n                    apiId: ApiId.handleRedirectPromise,\r\n                    timeout: this.config.system.redirectNavigationTimeout,\r\n                    noHistory: true\r\n                };\r\n\r\n                /**\r\n                 * Default behavior is to redirect to the start page and not process the hash now.\r\n                 * The start page is expected to also call handleRedirectPromise which will process the hash in one of the checks above.\r\n                 */\r\n                let processHashOnRedirect: boolean = true;\r\n                if (!loginRequestUrl || loginRequestUrl === \"null\") {\r\n                    // Redirect to home page if login request url is null (real null or the string null)\r\n                    const homepage = BrowserUtils.getHomepage();\r\n                    // Cache the homepage under ORIGIN_URI to ensure cached hash is processed on homepage\r\n                    this.browserStorage.setTemporaryCache(TemporaryCacheKeys.ORIGIN_URI, homepage, true);\r\n                    this.logger.warning(\"Unable to get valid login request url from cache, redirecting to home page\");\r\n                    processHashOnRedirect = await this.navigationClient.navigateInternal(homepage, navigationOptions);\r\n                } else {\r\n                    // Navigate to page that initiated the redirect request\r\n                    this.logger.verbose(`Navigating to loginRequestUrl: ${loginRequestUrl}`);\r\n                    processHashOnRedirect = await this.navigationClient.navigateInternal(loginRequestUrl, navigationOptions);\r\n                }\r\n\r\n                // If navigateInternal implementation returns false, handle the hash now\r\n                if (!processHashOnRedirect) {\r\n                    return this.handleHash(responseHash, state, serverTelemetryManager);\r\n                }\r\n            }\r\n\r\n            return null;\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                (e as AuthError).setCorrelationId(this.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            this.browserStorage.cleanRequestByInteractionType(InteractionType.Redirect);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the response hash for a redirect request\r\n     * Returns null if interactionType in the state value is not \"redirect\" or the hash does not contain known properties\r\n     * @param hash\r\n     */\r\n    protected getRedirectResponseHash(hash: string): string | null {\r\n        this.logger.verbose(\"getRedirectResponseHash called\");\r\n        // Get current location hash from window or cache.\r\n        const isResponseHash: boolean = UrlString.hashContainsKnownProperties(hash);\r\n\r\n        if (isResponseHash) {\r\n            BrowserUtils.clearHash(window);\r\n            this.logger.verbose(\"Hash contains known properties, returning response hash\");\r\n            return hash;\r\n        }\r\n\r\n        const cachedHash = this.browserStorage.getTemporaryCache(TemporaryCacheKeys.URL_HASH, true);\r\n        this.browserStorage.removeItem(this.browserStorage.generateCacheKey(TemporaryCacheKeys.URL_HASH));\r\n\r\n        this.logger.verbose(\"Hash does not contain known properties, returning cached hash\");\r\n        return cachedHash;\r\n    }\r\n\r\n    /**\r\n     * Checks if hash exists and handles in window.\r\n     * @param hash\r\n     * @param state\r\n     */\r\n    protected async handleHash(hash: string, state: string, serverTelemetryManager: ServerTelemetryManager): Promise<AuthenticationResult> {\r\n        const cachedRequest = this.browserStorage.getCachedRequest(state, this.browserCrypto);\r\n        this.logger.verbose(\"handleHash called, retrieved cached request\");\r\n\r\n        const serverParams: ServerAuthorizationCodeResponse = UrlString.getDeserializedHash(hash);\r\n\r\n        if (serverParams.accountId) {\r\n            this.logger.verbose(\"Account id found in hash, calling WAM for token\");\r\n            if (!this.nativeMessageHandler) {\r\n                throw BrowserAuthError.createNativeConnectionNotEstablishedError();\r\n            }\r\n            const nativeInteractionClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.acquireTokenPopup, this.performanceClient, this.nativeMessageHandler, serverParams.accountId, this.nativeStorage, cachedRequest.correlationId);\r\n            const { userRequestState } = ProtocolUtils.parseRequestState(this.browserCrypto, state);\r\n            return nativeInteractionClient.acquireToken({\r\n                ...cachedRequest,\r\n                state: userRequestState,\r\n                prompt: undefined // Server should handle the prompt, ideally native broker can do this part silently\r\n            }).finally(() => {\r\n                this.browserStorage.cleanRequestByState(state);\r\n            });\r\n        }\r\n\r\n        // Hash contains known properties - handle and return in callback\r\n        const currentAuthority = this.browserStorage.getCachedAuthority(state);\r\n        if (!currentAuthority) {\r\n            throw BrowserAuthError.createNoCachedAuthorityError();\r\n        }\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, cachedRequest.correlationId);\r\n        const authClient = await this.createAuthCodeClient(serverTelemetryManager, currentAuthority);\r\n        this.logger.verbose(\"Auth code client created\");\r\n        ThrottlingUtils.removeThrottle(this.browserStorage, this.config.auth.clientId, cachedRequest);\r\n        const interactionHandler = new RedirectHandler(authClient, this.browserStorage, cachedRequest, this.logger, this.browserCrypto, this.performanceClient);\r\n        return await interactionHandler.handleCodeResponseFromHash(hash, state, authClient.authority, this.networkClient);\r\n    }\r\n\r\n    /**\r\n     * Use to log out the current user, and redirect the user to the postLogoutRedirectUri.\r\n     * Default behaviour is to redirect the user to `window.location.href`.\r\n     * @param logoutRequest\r\n     */\r\n    async logout(logoutRequest?: EndSessionRequest): Promise<void> {\r\n        this.logger.verbose(\"logoutRedirect called\");\r\n        const validLogoutRequest = this.initializeLogoutRequest(logoutRequest);\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.logout);\r\n\r\n        try {\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_START, InteractionType.Redirect, logoutRequest);\r\n\r\n            // Clear cache on logout\r\n            await this.clearCacheOnLogout(validLogoutRequest.account);\r\n\r\n            const navigationOptions: NavigationOptions = {\r\n                apiId: ApiId.logout,\r\n                timeout: this.config.system.redirectNavigationTimeout,\r\n                noHistory: false\r\n            };\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, validLogoutRequest.correlationId);\r\n            const authClient = await this.createAuthCodeClient(serverTelemetryManager, logoutRequest && logoutRequest.authority);\r\n            this.logger.verbose(\"Auth code client created\");\r\n\r\n            // Create logout string and navigate user window to logout.\r\n            const logoutUri: string = authClient.getLogoutUri(validLogoutRequest);\r\n\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_SUCCESS, InteractionType.Redirect, validLogoutRequest);\r\n            // Check if onRedirectNavigate is implemented, and invoke it if so\r\n            if (logoutRequest && typeof logoutRequest.onRedirectNavigate === \"function\") {\r\n                const navigate = logoutRequest.onRedirectNavigate(logoutUri);\r\n\r\n                if (navigate !== false) {\r\n                    this.logger.verbose(\"Logout onRedirectNavigate did not return false, navigating\");\r\n                    // Ensure interaction is in progress\r\n                    if (!this.browserStorage.getInteractionInProgress()) {\r\n                        this.browserStorage.setInteractionInProgress(true);\r\n                    }\r\n                    await this.navigationClient.navigateExternal(logoutUri, navigationOptions);\r\n                    return;\r\n                } else {\r\n                    // Ensure interaction is not in progress\r\n                    this.browserStorage.setInteractionInProgress(false);\r\n                    this.logger.verbose(\"Logout onRedirectNavigate returned false, stopping navigation\");\r\n                }\r\n            } else {\r\n                // Ensure interaction is in progress\r\n                if (!this.browserStorage.getInteractionInProgress()) {\r\n                    this.browserStorage.setInteractionInProgress(true);\r\n                }\r\n                await this.navigationClient.navigateExternal(logoutUri, navigationOptions);\r\n                return;\r\n            }\r\n        } catch(e) {\r\n            if (e instanceof AuthError) {\r\n                (e as AuthError).setCorrelationId(this.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_FAILURE, InteractionType.Redirect, null, e);\r\n            this.eventHandler.emitEvent(EventType.LOGOUT_END, InteractionType.Redirect);\r\n            throw e;\r\n        }\r\n\r\n        this.eventHandler.emitEvent(EventType.LOGOUT_END, InteractionType.Redirect);\r\n    }\r\n\r\n    /**\r\n     * Use to get the redirectStartPage either from request or use current window\r\n     * @param requestStartPage\r\n     */\r\n    protected getRedirectStartPage(requestStartPage?: string): string {\r\n        const redirectStartPage = requestStartPage || window.location.href;\r\n        return UrlString.getAbsoluteUrl(redirectStartPage, BrowserUtils.getCurrentUri());\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAmBH,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;IAAoC,SAAyB,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;AAGzD,IAAA,SAAA,cAAA,CAAY,MAA4B,EAAE,WAAgC,EAAE,aAAsB,EAAE,MAAc,EAAE,YAA0B,EAAE,gBAAmC,EAAE,iBAAqC,EAAE,iBAAsC,EAAE,oBAA2C,EAAE,aAAsB,EAAA;AAAvU,QAAA,IAAA,KAAA,GACI,kBAAM,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,CAAC,IAE5I,IAAA,CAAA;AADG,QAAA,KAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;;KAC1C;AAED;;;AAGG;IACG,cAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAwB,EAAA;;;;;;;AACvC,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,uDAAuD,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBACpH,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAA,CAAA;;AAA3F,wBAAA,YAAY,GAAG,EAA4E,CAAA,IAAA,EAAA,CAAA;AACjG,wBAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;wBACjL,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;wBAE3F,gBAAgB,GAAG,UAAC,KAA0B,EAAA;;4BAEhD,IAAI,KAAK,CAAC,SAAS,EAAE;AACjB,gCAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sEAAsE,CAAC,CAAC;gCAC5F,KAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5D,gCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,oBAAoB,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AACzF,6BAAA;AACL,yBAAC,CAAC;;;;;AAIE,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,2DAA2D,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACrF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,kCAAkC,CAAC,YAAY,CAAC,CAAA,CAAA;;AAA7G,wBAAA,eAAe,GAAmC,EAA2D,CAAA,IAAA,EAAA,CAAA;;AAGnH,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACnF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAA,CAAA;;AAArJ,wBAAA,UAAU,GAA4B,EAA+G,CAAA,IAAA,EAAA,CAAA;AAC3J,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;wBAG1C,kBAAkB,GAAG,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAGtI,wBAAA,OAAA,CAAA,CAAA,YAAM,UAAU,CAAC,cAAc,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EAC5C,YAAY,CAAA,EAAA,EACf,YAAY,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,CAAC,IACzI,CAAA,CAAA;;AAHI,wBAAA,WAAW,GAAG,EAGlB,CAAA,IAAA,EAAA,CAAA;wBAEI,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;wBAC/E,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,uBAAwB,GAAA,iBAAmB,CAAC,CAAC;;AAGpE,wBAAA,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AAG/C,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,mBAAmB,CAAC,WAAW,EAAE;gCAC7D,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AACvC,gCAAA,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AAC7D,gCAAA,iBAAiB,EAAE,iBAAiB;gCACpC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;AACjD,6BAAA,CAAC,CAAA,CAAA;;;AALF,oBAAA,OAAA,CAAA,CAAA,aAAO,SAKL,CAAC,CAAA;;;wBAEH,IAAI,GAAC,YAAY,SAAS,EAAE;AACxB,4BAAA,GAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1C,yBAAA;AACD,wBAAA,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AACzD,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5D,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;;;;AAKG;IACG,cAAqB,CAAA,SAAA,CAAA,qBAAA,GAA3B,UAA4B,IAAa,EAAA;;;;;;wBAC/B,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;;;;wBAE9F,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;AACpD,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;AAC1G,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAEK,wBAAA,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAChF,IAAI,CAAC,YAAY,EAAE;;AAEf,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2GAA2G,CAAC,CAAC;4BAC9H,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AAC5E,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAEG,wBAAA,KAAK,SAAQ,CAAC;wBAClB,IAAI;AAEM,4BAAA,YAAY,GAAoC,SAAS,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;4BAClG,KAAK,GAAG,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AACrF,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACpD,yBAAA;AAAC,wBAAA,OAAO,CAAC,EAAE;4BACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4DAA6D,GAAA,CAAG,CAAC,CAAC;4BACnF,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AAC5E,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAGK,wBAAA,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;AACvH,wBAAA,yBAAyB,GAAG,SAAS,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;wBACzE,oBAAoB,GAAG,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAE3E,wBAAA,IAAA,EAAA,yBAAyB,KAAK,oBAAoB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAA,EAAhG,OAAgG,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;AAEhG,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;wBAC7C,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,sBAAsB,CAAC,CAAA,CAAA;;AAArF,wBAAA,gBAAgB,GAAG,EAAkE,CAAA,IAAA,EAAA,CAAA;wBAE3F,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;;AAEnC,4BAAA,YAAY,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAC7C,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,CAAA;;6BACjB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAA3C,OAA2C,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAClD,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;wBAC7E,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,sBAAsB,CAAC,CAAC,CAAA;;AAC7D,wBAAA,IAAA,EAAA,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAA,EAAtE,OAAsE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC7E;;;AAGG;AACH,wBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;AACjF,wBAAA,iBAAiB,GAAsB;4BACzC,KAAK,EAAE,KAAK,CAAC,qBAAqB;AAClC,4BAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,4BAAA,SAAS,EAAE,IAAI;yBAClB,CAAC;wBAME,qBAAqB,GAAY,IAAI,CAAC;8BACtC,CAAC,eAAe,IAAI,eAAe,KAAK,MAAM,CAAA,EAA9C,OAA8C,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAExC,wBAAA,QAAQ,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;;AAE5C,wBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AACrF,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4EAA4E,CAAC,CAAC;wBAC1E,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAA,CAAA;;wBAAjG,qBAAqB,GAAG,SAAyE,CAAC;;;;wBAGlG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAkC,GAAA,eAAiB,CAAC,CAAC;wBACjD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAA,CAAA;;wBAAxG,qBAAqB,GAAG,SAAgF,CAAC;;;;wBAI7G,IAAI,CAAC,qBAAqB,EAAE;4BACxB,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,sBAAsB,CAAC,CAAC,CAAA;AACvE,yBAAA;;AAGL,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;wBAEZ,IAAI,GAAC,YAAY,SAAS,EAAE;AACvB,4BAAA,GAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,yBAAA;AACD,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AAC5E,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;;;AAIG;IACO,cAAuB,CAAA,SAAA,CAAA,uBAAA,GAAjC,UAAkC,IAAY,EAAA;AAC1C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;;QAEtD,IAAM,cAAc,GAAY,SAAS,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;AAE5E,QAAA,IAAI,cAAc,EAAE;AAChB,YAAA,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yDAAyD,CAAC,CAAC;AAC/E,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC5F,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;AAElG,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;AACrF,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;AAED;;;;AAIG;AACa,IAAA,cAAA,CAAA,SAAA,CAAA,UAAU,GAA1B,UAA2B,IAAY,EAAE,KAAa,EAAE,sBAA8C,EAAA;;;;;;;AAC5F,wBAAA,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACtF,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;AAE7D,wBAAA,YAAY,GAAoC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;wBAE1F,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;AACvE,4BAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gCAAA,MAAM,gBAAgB,CAAC,yCAAyC,EAAE,CAAC;AACtE,6BAAA;4BACK,uBAAuB,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AACtT,4BAAA,gBAAgB,GAAK,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA,gBAA/D,CAAgE;AACxF,4BAAA,OAAA,CAAA,CAAA,aAAO,uBAAuB,CAAC,YAAY,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACpC,aAAa,CAChB,EAAA,EAAA,KAAK,EAAE,gBAAgB,EACvB,MAAM,EAAE,SAAS;AACnB,kCAAA,CAAA,CAAA,CAAC,OAAO,CAAC,YAAA;AACP,oCAAA,KAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACnD,iCAAC,CAAC,CAAC,CAAA;AACN,yBAAA;wBAGK,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;wBACvE,IAAI,CAAC,gBAAgB,EAAE;AACnB,4BAAA,MAAM,gBAAgB,CAAC,4BAA4B,EAAE,CAAC;AACzD,yBAAA;AACD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;wBAClH,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAA,CAAA;;AAAtF,wBAAA,UAAU,GAAG,EAAyE,CAAA,IAAA,EAAA,CAAA;AAC5F,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChD,wBAAA,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;wBACxF,kBAAkB,GAAG,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACjJ,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,0BAA0B,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA,CAAA;AAAjH,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA0G,CAAC,CAAA;;;;AACrH,KAAA,CAAA;AAED;;;;AAIG;IACG,cAAM,CAAA,SAAA,CAAA,MAAA,GAAZ,UAAa,aAAiC,EAAA;;;;;;AAC1C,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACvC,wBAAA,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;wBACjE,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;;;AAG/E,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;;wBAG7F,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA,CAAA;;;AAAzD,wBAAA,EAAA,CAAA,IAAA,EAAyD,CAAC;AAEpD,wBAAA,iBAAiB,GAAsB;4BACzC,KAAK,EAAE,KAAK,CAAC,MAAM;AACnB,4BAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,4BAAA,SAAS,EAAE,KAAK;yBACnB,CAAC;AACF,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACvH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,aAAa,IAAI,aAAa,CAAC,SAAS,CAAC,CAAA,CAAA;;AAA9G,wBAAA,UAAU,GAAG,EAAiG,CAAA,IAAA,EAAA,CAAA;AACpH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAG1C,wBAAA,SAAS,GAAW,UAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;AAEtE,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,EAAE,eAAe,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;8BAEhG,aAAa,IAAI,OAAO,aAAa,CAAC,kBAAkB,KAAK,UAAU,CAAA,EAAvE,OAAuE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACjE,wBAAA,QAAQ,GAAG,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAEzD,wBAAA,IAAA,EAAA,QAAQ,KAAK,KAAK,CAAA,EAAlB,OAAkB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAClB,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4DAA4D,CAAC,CAAC;;AAElF,wBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,EAAE;AACjD,4BAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AACtD,yBAAA;wBACD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAA,CAAA;;AAA1E,wBAAA,EAAA,CAAA,IAAA,EAA0E,CAAC;wBAC3E,OAAO,CAAA,CAAA,YAAA,CAAA;;;AAGP,wBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;;;;;AAIzF,wBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,EAAE;AACjD,4BAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AACtD,yBAAA;wBACD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAA,CAAA;;AAA1E,wBAAA,EAAA,CAAA,IAAA,EAA0E,CAAC;wBAC3E,OAAO,CAAA,CAAA,YAAA,CAAA;;;;wBAGX,IAAI,GAAC,YAAY,SAAS,EAAE;AACvB,4BAAA,GAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,yBAAA;AACD,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;AAC7C,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,EAAE,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAC,CAAC,CAAC;AACzF,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AAC5E,wBAAA,MAAM,GAAC,CAAC;;AAGZ,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;;;;;AAC/E,KAAA,CAAA;AAED;;;AAGG;IACO,cAAoB,CAAA,SAAA,CAAA,oBAAA,GAA9B,UAA+B,gBAAyB,EAAA;QACpD,IAAM,iBAAiB,GAAG,gBAAgB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnE,OAAO,SAAS,CAAC,cAAc,CAAC,iBAAiB,EAAE,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;KACpF,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CApTA,CAAoC,yBAAyB,CAoT5D;;;;"}