{"version": 3, "file": "SilentIframeClient.js", "sources": ["../../src/interaction_client/SilentIframeClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, ICrypto, Logger, StringUtils, PromptValue, CommonAuthorizationCodeRequest, AuthorizationCodeClient, AuthError, Constants, UrlString, ServerAuthorizationCodeResponse, ProtocolUtils, IPerformanceClient, PerformanceEvents } from \"@azure/msal-common\";\r\nimport { StandardInteractionClient } from \"./StandardInteractionClient\";\r\nimport { AuthorizationUrlRequest } from \"../request/AuthorizationUrlRequest\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { InteractionType, ApiId } from \"../utils/BrowserConstants\";\r\nimport { SilentHandler } from \"../interaction_handler/SilentHandler\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\nimport { NativeInteractionClient } from \"./NativeInteractionClient\";\r\n\r\nexport class SilentIframeClient extends StandardInteractionClient {\r\n    protected apiId: ApiId;\r\n    protected nativeStorage: BrowserCacheManager;\r\n\r\n    constructor(config: BrowserConfiguration, storageImpl: BrowserCacheManager, browserCrypto: ICrypto, logger: Logger, eventHandler: EventHandler, navigationClient: INavigationClient, apiId: ApiId, performanceClient: IPerformanceClient, nativeStorageImpl: BrowserCacheManager, nativeMessageHandler?: NativeMessageHandler, correlationId?: string) {\r\n        super(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient, nativeMessageHandler, correlationId);\r\n        this.apiId = apiId;\r\n        this.nativeStorage = nativeStorageImpl;\r\n    }\r\n\r\n    /**\r\n     * Acquires a token silently by opening a hidden iframe to the /authorize endpoint with prompt=none or prompt=no_session\r\n     * @param request\r\n     */\r\n    async acquireToken(request: SsoSilentRequest): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentIframeClientAcquireToken, request.correlationId);\r\n        this.logger.verbose(\"acquireTokenByIframe called\");\r\n        const acquireTokenMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SilentIframeClientAcquireToken, request.correlationId);\r\n        // Check that we have some SSO data\r\n        if (StringUtils.isEmpty(request.loginHint) && StringUtils.isEmpty(request.sid) && (!request.account || StringUtils.isEmpty(request.account.username))) {\r\n            this.logger.warning(\"No user hint provided. The authorization server may need more information to complete this request.\");\r\n        }\r\n\r\n        // Check that prompt is set to none or no_session, throw error if it is set to anything else.\r\n        if (request.prompt && (request.prompt !== PromptValue.NONE) && (request.prompt !== PromptValue.NO_SESSION)) {\r\n            acquireTokenMeasurement.endMeasurement({\r\n                success: false\r\n            });\r\n            throw BrowserAuthError.createSilentPromptValueError(request.prompt);\r\n        }\r\n\r\n        // Create silent request\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest, request.correlationId);\r\n        const silentRequest: AuthorizationUrlRequest = await this.initializeAuthorizationRequest({\r\n            ...request,\r\n            prompt: request.prompt || PromptValue.NONE\r\n        }, InteractionType.Silent);\r\n        this.browserStorage.updateCacheEntries(silentRequest.state, silentRequest.nonce, silentRequest.authority, silentRequest.loginHint || Constants.EMPTY_STRING, silentRequest.account || null);\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(this.apiId);\r\n\r\n        try {\r\n            // Initialize the client\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, request.correlationId);\r\n            const authClient: AuthorizationCodeClient = await this.createAuthCodeClient(serverTelemetryManager, silentRequest.authority, silentRequest.azureCloudOptions);\r\n            this.logger.verbose(\"Auth code client created\");\r\n\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.SilentIframeClientTokenHelper, request.correlationId);\r\n            return await this.silentTokenHelper(authClient, silentRequest).then((result: AuthenticationResult) => {\r\n                acquireTokenMeasurement.endMeasurement({\r\n                    success: true,\r\n                    fromCache: false,\r\n                    requestId: result.requestId\r\n                });\r\n                return result;\r\n            });\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                (e as AuthError).setCorrelationId(this.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            this.browserStorage.cleanRequestByState(silentRequest.state);\r\n            acquireTokenMeasurement.endMeasurement({\r\n                errorCode: e instanceof AuthError && e.errorCode || undefined,\r\n                subErrorCode: e instanceof AuthError && e.subError || undefined,\r\n                success: false\r\n            });\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Currently Unsupported\r\n     */\r\n    logout(): Promise<void> {\r\n        // Synchronous so we must reject\r\n        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());\r\n    }\r\n\r\n    /**\r\n     * Helper which acquires an authorization code silently using a hidden iframe from given url\r\n     * using the scopes requested as part of the id, and exchanges the code for a set of OAuth tokens.\r\n     * @param navigateUrl\r\n     * @param userRequestScopes\r\n     */\r\n    protected async silentTokenHelper(authClient: AuthorizationCodeClient, silentRequest: AuthorizationUrlRequest): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentIframeClientTokenHelper, silentRequest.correlationId);\r\n\r\n        // Create auth code request and generate PKCE params\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest, silentRequest.correlationId);\r\n        const authCodeRequest: CommonAuthorizationCodeRequest = await this.initializeAuthorizationCodeRequest(silentRequest);\r\n        // Create authorize request url\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.GetAuthCodeUrl, silentRequest.correlationId);\r\n        const navigateUrl = await authClient.getAuthCodeUrl({\r\n            ...silentRequest,\r\n            nativeBroker: NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeMessageHandler, silentRequest.authenticationScheme)\r\n        });\r\n\r\n        // Create silent handler\r\n        const silentHandler = new SilentHandler(authClient, this.browserStorage, authCodeRequest, this.logger, this.config.system, this.performanceClient);\r\n        // Get the frame handle for the silent request\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.SilentHandlerInitiateAuthRequest, silentRequest.correlationId);\r\n        const msalFrame = await silentHandler.initiateAuthRequest(navigateUrl);\r\n        // Monitor the window for the hash. Return the string value and close the popup when the hash is received. Default timeout is 60 seconds.\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.SilentHandlerMonitorIframeForHash, silentRequest.correlationId);\r\n        const hash = await silentHandler.monitorIframeForHash(msalFrame, this.config.system.iframeHashTimeout);\r\n        // Deserialize hash fragment response parameters.\r\n        const serverParams: ServerAuthorizationCodeResponse = UrlString.getDeserializedHash(hash);\r\n        const state = this.validateAndExtractStateFromHash(serverParams, InteractionType.Silent, authCodeRequest.correlationId);\r\n\r\n        if (serverParams.accountId) {\r\n            this.logger.verbose(\"Account id found in hash, calling WAM for token\");\r\n            if (!this.nativeMessageHandler) {\r\n                throw BrowserAuthError.createNativeConnectionNotEstablishedError();\r\n            }\r\n            const nativeInteractionClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.apiId, this.performanceClient, this.nativeMessageHandler, serverParams.accountId, this.browserStorage, this.correlationId);\r\n            const { userRequestState } = ProtocolUtils.parseRequestState(this.browserCrypto, state);\r\n            return nativeInteractionClient.acquireToken({\r\n                ...silentRequest,\r\n                state: userRequestState,\r\n                prompt: silentRequest.prompt || PromptValue.NONE\r\n            }).finally(() => {\r\n                this.browserStorage.cleanRequestByState(state);\r\n            });\r\n        }\r\n\r\n        // Handle response from hash string\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.HandleCodeResponseFromHash, silentRequest.correlationId);\r\n        return silentHandler.handleCodeResponseFromHash(hash, state, authClient.authority, this.networkClient);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAgBH,IAAA,kBAAA,kBAAA,UAAA,MAAA,EAAA;IAAwC,SAAyB,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;IAI7D,SAAY,kBAAA,CAAA,MAA4B,EAAE,WAAgC,EAAE,aAAsB,EAAE,MAAc,EAAE,YAA0B,EAAE,gBAAmC,EAAE,KAAY,EAAE,iBAAqC,EAAE,iBAAsC,EAAE,oBAA2C,EAAE,aAAsB,EAAA;AAArV,QAAA,IAAA,KAAA,GACI,kBAAM,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,CAAC,IAG5I,IAAA,CAAA;AAFG,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,KAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;;KAC1C;AAED;;;AAGG;IACG,kBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAyB,EAAA;;;;;;AACxC,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACpH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAC7C,wBAAA,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;;AAEjJ,wBAAA,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE;AACnJ,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qGAAqG,CAAC,CAAC;AAC9H,yBAAA;;wBAGD,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,UAAU,CAAC,EAAE;4BACxG,uBAAuB,CAAC,cAAc,CAAC;AACnC,gCAAA,OAAO,EAAE,KAAK;AACjB,6BAAA,CAAC,CAAC;4BACH,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACvE,yBAAA;;AAGD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,uDAAuD,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC1F,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,8BAA8B,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACjF,OAAO,CACV,EAAA,EAAA,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,EAAA,CAAA,EAC3C,eAAe,CAAC,MAAM,CAAC,CAAA,CAAA;;AAHpB,wBAAA,aAAa,GAA4B,EAGrB,CAAA,IAAA,EAAA,CAAA;AAC1B,wBAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EAAE,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;wBAEtL,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;;AAI7E,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACnF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAA,CAAA;;AAAvJ,wBAAA,UAAU,GAA4B,EAAiH,CAAA,IAAA,EAAA,CAAA;AAC7J,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEhD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACxG,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,UAAC,MAA4B,EAAA;gCAC7F,uBAAuB,CAAC,cAAc,CAAC;AACnC,oCAAA,OAAO,EAAE,IAAI;AACb,oCAAA,SAAS,EAAE,KAAK;oCAChB,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,iCAAA,CAAC,CAAC;AACH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC,CAAA,CAAA;AAPF,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAOL,CAAC,CAAA;;;wBAEH,IAAI,GAAC,YAAY,SAAS,EAAE;AACvB,4BAAA,GAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,yBAAA;AACD,wBAAA,sBAAsB,CAAC,kBAAkB,CAAC,GAAC,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBAC7D,uBAAuB,CAAC,cAAc,CAAC;4BACnC,SAAS,EAAE,GAAC,YAAY,SAAS,IAAI,GAAC,CAAC,SAAS,IAAI,SAAS;4BAC7D,YAAY,EAAE,GAAC,YAAY,SAAS,IAAI,GAAC,CAAC,QAAQ,IAAI,SAAS;AAC/D,4BAAA,OAAO,EAAE,KAAK;AACjB,yBAAA,CAAC,CAAC;AACH,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;AAEG;AACH,IAAA,kBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;;QAEI,OAAO,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,EAAE,CAAC,CAAC;KAChF,CAAA;AAED;;;;;AAKG;AACa,IAAA,kBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjC,UAAkC,UAAmC,EAAE,aAAsC,EAAA;;;;;;;AACzG,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;;AAGzH,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,2DAA2D,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAC3F,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,kCAAkC,CAAC,aAAa,CAAC,CAAA,CAAA;;AAA9G,wBAAA,eAAe,GAAmC,EAA4D,CAAA,IAAA,EAAA,CAAA;;AAEpH,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,cAAc,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAClF,wBAAA,OAAA,CAAA,CAAA,YAAM,UAAU,CAAC,cAAc,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EAC5C,aAAa,CAAA,EAAA,EAChB,YAAY,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,aAAa,CAAC,oBAAoB,CAAC,IAC/I,CAAA,CAAA;;AAHI,wBAAA,WAAW,GAAG,EAGlB,CAAA,IAAA,EAAA,CAAA;wBAGI,aAAa,GAAG,IAAI,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;;AAEnJ,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AACtG,wBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA,CAAA;;AAAhE,wBAAA,SAAS,GAAG,EAAoD,CAAA,IAAA,EAAA,CAAA;;AAEtE,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,iCAAiC,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5G,wBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAA,CAAA;;AAAhG,wBAAA,IAAI,GAAG,EAAyF,CAAA,IAAA,EAAA,CAAA;AAEhG,wBAAA,YAAY,GAAoC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACpF,wBAAA,KAAK,GAAG,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC;wBAExH,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;AACvE,4BAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gCAAA,MAAM,gBAAgB,CAAC,yCAAyC,EAAE,CAAC;AACtE,6BAAA;4BACK,uBAAuB,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACjS,4BAAA,gBAAgB,GAAK,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA,gBAA/D,CAAgE;4BACxF,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,YAAY,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACpC,aAAa,CAChB,EAAA,EAAA,KAAK,EAAE,gBAAgB,EACvB,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,EAAA,CAAA,CAClD,CAAC,OAAO,CAAC,YAAA;AACP,oCAAA,KAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACnD,iCAAC,CAAC,CAAC,CAAA;AACN,yBAAA;;AAGD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAClH,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,0BAA0B,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;;;;AAC1G,KAAA,CAAA;IACL,OAAC,kBAAA,CAAA;AAAD,CAlIA,CAAwC,yBAAyB,CAkIhE;;;;"}