/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __extends } from '../_virtual/_tslib.js';
import { AuthorizationCodeClient } from '@azure/msal-common';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var HybridSpaAuthorizationCodeClient = /** @class */ (function (_super) {
    __extends(HybridSpaAuthorizationCodeClient, _super);
    function HybridSpaAuthorizationCodeClient(config) {
        var _this = _super.call(this, config) || this;
        _this.includeRedirectUri = false;
        return _this;
    }
    return HybridSpaAuthorizationCodeClient;
}(AuthorizationCodeClient));

export { HybridSpaAuthorizationCodeClient };
//# sourceMappingURL=HybridSpaAuthorizationCodeClient.js.map
