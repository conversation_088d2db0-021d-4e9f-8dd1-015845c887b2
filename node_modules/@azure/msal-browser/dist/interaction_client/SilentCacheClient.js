/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __extends, __awaiter, __generator, __assign } from '../_virtual/_tslib.js';
import { StandardInteractionClient } from './StandardInteractionClient.js';
import { AuthError, PerformanceEvents, SilentFlowClient } from '@azure/msal-common';
import { ApiId } from '../utils/BrowserConstants.js';
import { BrowserAuthError, BrowserAuthErrorMessage } from '../error/BrowserAuthError.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var SilentCacheClient = /** @class */ (function (_super) {
    __extends(SilentCacheClient, _super);
    function SilentCacheClient() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * Returns unexpired tokens from the cache, if available
     * @param silentRequest
     */
    SilentCacheClient.prototype.acquireToken = function (silentRequest) {
        return __awaiter(this, void 0, void 0, function () {
            var acquireTokenMeasurement, serverTelemetryManager, silentAuthClient, cachedToken, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        acquireTokenMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SilentCacheClientAcquireToken, silentRequest.correlationId);
                        serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenSilent_silentFlow);
                        return [4 /*yield*/, this.createSilentFlowClient(serverTelemetryManager, silentRequest.authority, silentRequest.azureCloudOptions)];
                    case 1:
                        silentAuthClient = _a.sent();
                        this.logger.verbose("Silent auth client created");
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, silentAuthClient.acquireCachedToken(silentRequest)];
                    case 3:
                        cachedToken = _a.sent();
                        acquireTokenMeasurement.endMeasurement({
                            success: true,
                            fromCache: true
                        });
                        return [2 /*return*/, cachedToken];
                    case 4:
                        error_1 = _a.sent();
                        if (error_1 instanceof BrowserAuthError && error_1.errorCode === BrowserAuthErrorMessage.signingKeyNotFoundInStorage.code) {
                            this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair.");
                        }
                        acquireTokenMeasurement.endMeasurement({
                            errorCode: error_1 instanceof AuthError && error_1.errorCode || undefined,
                            subErrorCode: error_1 instanceof AuthError && error_1.subError || undefined,
                            success: false
                        });
                        throw error_1;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Currently Unsupported
     */
    SilentCacheClient.prototype.logout = function () {
        // Synchronous so we must reject
        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());
    };
    /**
     * Creates an Silent Flow Client with the given authority, or the default authority.
     * @param serverTelemetryManager
     * @param authorityUrl
     */
    SilentCacheClient.prototype.createSilentFlowClient = function (serverTelemetryManager, authorityUrl, azureCloudOptions) {
        return __awaiter(this, void 0, void 0, function () {
            var clientConfig;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Create auth module.
                        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, this.correlationId);
                        return [4 /*yield*/, this.getClientConfiguration(serverTelemetryManager, authorityUrl, azureCloudOptions)];
                    case 1:
                        clientConfig = _a.sent();
                        return [2 /*return*/, new SilentFlowClient(clientConfig, this.performanceClient)];
                }
            });
        });
    };
    SilentCacheClient.prototype.initializeSilentRequest = function (request, account) {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.performanceClient.addQueueMeasurement(PerformanceEvents.InitializeSilentRequest, this.correlationId);
                        this.performanceClient.setPreQueueTime(PerformanceEvents.InitializeBaseRequest, this.correlationId);
                        _a = [__assign({}, request)];
                        return [4 /*yield*/, this.initializeBaseRequest(request, account)];
                    case 1: return [2 /*return*/, __assign.apply(void 0, [__assign.apply(void 0, _a.concat([_b.sent()])), { account: account, forceRefresh: request.forceRefresh || false }])];
                }
            });
        });
    };
    return SilentCacheClient;
}(StandardInteractionClient));

export { SilentCacheClient };
//# sourceMappingURL=SilentCacheClient.js.map
