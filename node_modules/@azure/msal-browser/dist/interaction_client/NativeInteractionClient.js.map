{"version": 3, "file": "NativeInteractionClient.js", "sources": ["../../src/interaction_client/NativeInteractionClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, Logger, ICrypto, PromptValue, AuthToken, Constants, AccountEntity, AuthorityType, ScopeSet, TimeUtils, AuthenticationScheme, UrlString, OIDC_DEFAULT_SCOPES, PopTokenGenerator, SignedHttpRequestParameters, IPerformanceClient, PerformanceEvents, IdTokenEntity, AccessTokenEntity, ClientAuthError, AuthError, CommonSilentFlowRequest, AccountInfo, CacheRecord, TokenClaims } from \"@azure/msal-common\";\r\nimport { BaseInteractionClient } from \"./BaseInteractionClient\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\nimport { NativeExtensionMethod, ApiId, TemporaryCacheKeys, NativeConstants } from \"../utils/BrowserConstants\";\r\nimport { NativeExtensionRequestBody, NativeTokenRequest } from \"../broker/nativeBroker/NativeRequest\";\r\nimport { MATS, NativeResponse } from \"../broker/nativeBroker/NativeResponse\";\r\nimport { NativeAuthError } from \"../error/NativeAuthError\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { NavigationOptions } from \"../navigation/NavigationOptions\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { SilentCacheClient } from \"./SilentCacheClient\";\r\n\r\nexport class NativeInteractionClient extends BaseInteractionClient {\r\n    protected apiId: ApiId;\r\n    protected accountId: string;\r\n    protected nativeMessageHandler: NativeMessageHandler;\r\n    protected silentCacheClient: SilentCacheClient;\r\n    protected nativeStorageManager: BrowserCacheManager;\r\n\r\n    constructor(config: BrowserConfiguration, browserStorage: BrowserCacheManager, browserCrypto: ICrypto, logger: Logger, eventHandler: EventHandler, navigationClient: INavigationClient, apiId: ApiId, performanceClient: IPerformanceClient, provider: NativeMessageHandler, accountId: string, nativeStorageImpl: BrowserCacheManager, correlationId?: string) {\r\n        super(config, browserStorage, browserCrypto, logger, eventHandler, navigationClient, performanceClient, provider, correlationId);\r\n        this.apiId = apiId;\r\n        this.accountId = accountId;\r\n        this.nativeMessageHandler = provider;\r\n        this.nativeStorageManager = nativeStorageImpl;\r\n        this.silentCacheClient = new SilentCacheClient(config, this.nativeStorageManager, browserCrypto, logger, eventHandler, navigationClient, performanceClient, provider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Acquire token from native platform via browser extension\r\n     * @param request\r\n     */\r\n    async acquireToken(request: PopupRequest|SilentRequest|SsoSilentRequest): Promise<AuthenticationResult> {\r\n        this.logger.trace(\"NativeInteractionClient - acquireToken called.\");\r\n\r\n        // start the perf measurement\r\n        const nativeATMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.NativeInteractionClientAcquireToken, request.correlationId);\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n\r\n        // initialize native request\r\n        const nativeRequest = await this.initializeNativeRequest(request);\r\n        \r\n        // check if the tokens can be retrieved from internal cache\r\n        try {\r\n            const result = await this.acquireTokensFromCache(this.accountId, nativeRequest);\r\n            nativeATMeasurement.endMeasurement({\r\n                success: true,\r\n                isNativeBroker: false, // Should be true only when the result is coming directly from the broker\r\n                fromCache: true\r\n            });\r\n            return result;\r\n        } catch (e) {\r\n            // continue with a native call for any and all errors\r\n            this.logger.info(\"MSAL internal Cache does not contain tokens, proceed to make a native call\");\r\n        }\r\n\r\n        // fall back to native calls\r\n        const messageBody: NativeExtensionRequestBody = {\r\n            method: NativeExtensionMethod.GetToken,\r\n            request: nativeRequest\r\n        };\r\n\r\n        const response: object = await this.nativeMessageHandler.sendMessage(messageBody);\r\n        const validatedResponse: NativeResponse = this.validateNativeResponse(response);\r\n\r\n        return this.handleNativeResponse(validatedResponse, nativeRequest, reqTimestamp)\r\n            .then((result: AuthenticationResult) => {\r\n                nativeATMeasurement.endMeasurement({\r\n                    success: true,\r\n                    isNativeBroker: true,\r\n                    requestId: result.requestId\r\n                });\r\n                return result;\r\n            })\r\n            .catch((error: AuthError) => {\r\n                nativeATMeasurement.endMeasurement({\r\n                    success: false,\r\n                    errorCode: error.errorCode,\r\n                    subErrorCode: error.subError,\r\n                    isNativeBroker: true\r\n                });\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Creates silent flow request\r\n     * @param request\r\n     * @param cachedAccount\r\n     * @returns CommonSilentFlowRequest\r\n     */\r\n    private createSilentCacheRequest(request: NativeTokenRequest, cachedAccount: AccountInfo): CommonSilentFlowRequest {\r\n        return {\r\n            authority: request.authority,\r\n            correlationId: this.correlationId,\r\n            scopes: ScopeSet.fromString(request.scope).asArray(),\r\n            account: cachedAccount,\r\n            forceRefresh: false,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Fetches the tokens from the cache if un-expired\r\n     * @param nativeAccountId\r\n     * @param request\r\n     * @returns authenticationResult\r\n     */\r\n    protected async acquireTokensFromCache(nativeAccountId: string, request: NativeTokenRequest): Promise<AuthenticationResult> {\r\n        if (!nativeAccountId) {\r\n            this.logger.warning(\"NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided\");\r\n            throw ClientAuthError.createNoAccountFoundError();\r\n        }\r\n        // fetch the account from browser cache\r\n        const account = this.browserStorage.getAccountInfoFilteredBy({nativeAccountId});\r\n        if (!account) {\r\n            throw ClientAuthError.createNoAccountFoundError();\r\n        }\r\n\r\n        // leverage silent flow for cached tokens retrieval\r\n        try {\r\n            const silentRequest = this.createSilentCacheRequest(request, account);\r\n            const result = await this.silentCacheClient.acquireToken(silentRequest);\r\n\r\n            const fullAccount = {\r\n                ...account,\r\n                idTokenClaims: result.idTokenClaims as TokenClaims\r\n            };\r\n            return {\r\n                ...result, \r\n                account: fullAccount\r\n            };\r\n        } catch (e) {\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires a token from native platform then redirects to the redirectUri instead of returning the response\r\n     * @param request\r\n     */\r\n    async acquireTokenRedirect(request: RedirectRequest): Promise<void> {\r\n        this.logger.trace(\"NativeInteractionClient - acquireTokenRedirect called.\");\r\n        const nativeRequest = await this.initializeNativeRequest(request);\r\n\r\n        const messageBody: NativeExtensionRequestBody = {\r\n            method: NativeExtensionMethod.GetToken,\r\n            request: nativeRequest\r\n        };\r\n\r\n        try {\r\n            const response: object = await this.nativeMessageHandler.sendMessage(messageBody);\r\n            this.validateNativeResponse(response);\r\n        } catch (e) {\r\n            // Only throw fatal errors here to allow application to fallback to regular redirect. Otherwise proceed and the error will be thrown in handleRedirectPromise\r\n            if (e instanceof NativeAuthError && e.isFatal()) {\r\n                throw e;\r\n            }\r\n        }\r\n        this.browserStorage.setTemporaryCache(TemporaryCacheKeys.NATIVE_REQUEST, JSON.stringify(nativeRequest), true);\r\n\r\n        const navigationOptions: NavigationOptions = {\r\n            apiId: ApiId.acquireTokenRedirect,\r\n            timeout: this.config.system.redirectNavigationTimeout,\r\n            noHistory: false\r\n        };\r\n        const redirectUri = this.config.auth.navigateToLoginRequestUrl ? window.location.href : this.getRedirectUri(request.redirectUri);\r\n        await this.navigationClient.navigateExternal(redirectUri, navigationOptions); // Need to treat this as external to ensure handleRedirectPromise is run again\r\n    }\r\n\r\n    /**\r\n     * If the previous page called native platform for a token using redirect APIs, send the same request again and return the response\r\n     */\r\n    async handleRedirectPromise(): Promise<AuthenticationResult | null> {\r\n        this.logger.trace(\"NativeInteractionClient - handleRedirectPromise called.\");\r\n        if (!this.browserStorage.isInteractionInProgress(true)) {\r\n            this.logger.info(\"handleRedirectPromise called but there is no interaction in progress, returning null.\");\r\n            return null;\r\n        }\r\n\r\n        // remove prompt from the request to prevent WAM from prompting twice\r\n        const cachedRequest = this.browserStorage.getCachedNativeRequest();\r\n        if (!cachedRequest) {\r\n            this.logger.verbose(\"NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null.\");\r\n            return null;\r\n        }\r\n\r\n        const { prompt, ...request} = cachedRequest;\r\n        if (prompt) {\r\n            this.logger.verbose(\"NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window.\");\r\n        }\r\n\r\n        this.browserStorage.removeItem(this.browserStorage.generateCacheKey(TemporaryCacheKeys.NATIVE_REQUEST));\r\n\r\n        const messageBody: NativeExtensionRequestBody = {\r\n            method: NativeExtensionMethod.GetToken,\r\n            request: request\r\n        };\r\n\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n\r\n        try {\r\n            this.logger.verbose(\"NativeInteractionClient - handleRedirectPromise sending message to native broker.\");\r\n            const response: object = await this.nativeMessageHandler.sendMessage(messageBody);\r\n            this.validateNativeResponse(response);\r\n            const result = this.handleNativeResponse(response as NativeResponse, request, reqTimestamp);\r\n            this.browserStorage.setInteractionInProgress(false);\r\n            return result;\r\n        } catch (e) {\r\n            this.browserStorage.setInteractionInProgress(false);    \r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Logout from native platform via browser extension\r\n     * @param request\r\n     */\r\n    logout(): Promise<void> {\r\n        this.logger.trace(\"NativeInteractionClient - logout called.\");\r\n        return Promise.reject(\"Logout not implemented yet\");\r\n    }\r\n\r\n    /**\r\n     * Transform response from native platform into AuthenticationResult object which will be returned to the end user\r\n     * @param response\r\n     * @param request\r\n     * @param reqTimestamp\r\n     */\r\n    protected async handleNativeResponse(response: NativeResponse, request: NativeTokenRequest, reqTimestamp: number): Promise<AuthenticationResult> {\r\n        this.logger.trace(\"NativeInteractionClient - handleNativeResponse called.\");\r\n\r\n        if (response.account.id !== request.accountId) {\r\n            // User switch in native broker prompt is not supported. All users must first sign in through web flow to ensure server state is in sync\r\n            throw NativeAuthError.createUserSwitchError();\r\n        }\r\n\r\n        // Get the preferred_cache domain for the given authority\r\n        const authority = await this.getDiscoveredAuthority(request.authority);\r\n\r\n        // generate identifiers\r\n        const idTokenObj = this.createIdTokenObj(response);\r\n        const homeAccountIdentifier = this.createHomeAccountIdentifier(response, idTokenObj);\r\n        const accountEntity = AccountEntity.createAccount(\r\n            {\r\n                homeAccountId: homeAccountIdentifier,\r\n                idTokenClaims: idTokenObj.claims,\r\n                clientInfo: response.client_info,\r\n                nativeAccountId: response.account.id,\r\n            },\r\n            authority\r\n        );\r\n        // generate authenticationResult\r\n        const result = await this.generateAuthenticationResult(response, request, idTokenObj, accountEntity, authority.canonicalAuthority, reqTimestamp);\r\n\r\n        // cache accounts and tokens in the appropriate storage\r\n        this.cacheAccount(accountEntity);\r\n        this.cacheNativeTokens(response, request, homeAccountIdentifier, idTokenObj, result.accessToken, result.tenantId, reqTimestamp);\r\n        \r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Create an idToken Object (not entity)\r\n     * @param response \r\n     * @returns \r\n     */\r\n    protected createIdTokenObj(response: NativeResponse): AuthToken {\r\n        return new AuthToken(response.id_token || Constants.EMPTY_STRING, this.browserCrypto);\r\n    }\r\n\r\n    /**\r\n     * creates an homeAccountIdentifier for the account\r\n     * @param response \r\n     * @param idTokenObj \r\n     * @returns \r\n     */\r\n    protected createHomeAccountIdentifier(response: NativeResponse, idTokenObj: AuthToken): string {\r\n        // Save account in browser storage\r\n        const homeAccountIdentifier = AccountEntity.generateHomeAccountId(response.client_info || Constants.EMPTY_STRING, AuthorityType.Default, this.logger, this.browserCrypto, idTokenObj.claims);\r\n\r\n        return homeAccountIdentifier;\r\n    }\r\n\r\n    /**\r\n     * Helper to generate scopes\r\n     * @param response \r\n     * @param request \r\n     * @returns \r\n     */\r\n    generateScopes(response: NativeResponse, request: NativeTokenRequest): ScopeSet {\r\n        return response.scope ? ScopeSet.fromString(response.scope) : ScopeSet.fromString(request.scope);\r\n    }\r\n\r\n    /**\r\n     * If PoP token is requesred, records the PoP token if returned from the WAM, else generates one in the browser\r\n     * @param request \r\n     * @param response \r\n     */\r\n    async generatePopAccessToken(response: NativeResponse, request: NativeTokenRequest): Promise<string> {\r\n        \r\n        if(request.tokenType === AuthenticationScheme.POP) {\r\n            /** \r\n             * This code prioritizes SHR returned from the native layer. In case of error/SHR not calculated from WAM and the AT \r\n             * is still received, SHR is calculated locally\r\n             */\r\n        \r\n            // Check if native layer returned an SHR token\r\n            if (response.shr) {\r\n                this.logger.trace(\"handleNativeServerResponse: SHR is enabled in native layer\");\r\n                return response.shr;\r\n            }\r\n\r\n            // Generate SHR in msal js if WAM does not compute it when POP is enabled\r\n            const popTokenGenerator: PopTokenGenerator = new PopTokenGenerator(this.browserCrypto);\r\n            const shrParameters: SignedHttpRequestParameters = {\r\n                resourceRequestMethod: request.resourceRequestMethod,\r\n                resourceRequestUri: request.resourceRequestUri,\r\n                shrClaims: request.shrClaims,\r\n                shrNonce: request.shrNonce\r\n            };\r\n\r\n            /**\r\n             * KeyID must be present in the native request from when the PoP key was generated in order for\r\n             * PopTokenGenerator to query the full key for signing\r\n             */\r\n            if (!request.keyId) {\r\n                throw ClientAuthError.createKeyIdMissingError();\r\n            }\r\n            return await popTokenGenerator.signPopToken(response.access_token, request.keyId, shrParameters);\r\n        } else {\r\n            return response.access_token;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Generates authentication result\r\n     * @param response \r\n     * @param request \r\n     * @param idTokenObj \r\n     * @param accountEntity \r\n     * @param authority \r\n     * @param reqTimestamp \r\n     * @returns \r\n     */\r\n    protected async generateAuthenticationResult(response: NativeResponse, request: NativeTokenRequest, idTokenObj: AuthToken, accountEntity: AccountEntity, authority: string, reqTimestamp: number): Promise<AuthenticationResult> {\r\n\r\n        // Add Native Broker fields to Telemetry\r\n        const mats = this.addTelemetryFromNativeResponse(response);\r\n\r\n        // If scopes not returned in server response, use request scopes\r\n        const responseScopes = response.scope ? ScopeSet.fromString(response.scope) : ScopeSet.fromString(request.scope);\r\n\r\n        const accountProperties = response.account.properties || {};\r\n        const uid = accountProperties[\"UID\"] || idTokenObj.claims.oid || idTokenObj.claims.sub || Constants.EMPTY_STRING;\r\n        const tid = accountProperties[\"TenantId\"] || idTokenObj.claims.tid || Constants.EMPTY_STRING;\r\n\r\n        // generate PoP token as needed\r\n        const responseAccessToken = await this.generatePopAccessToken(response, request);\r\n        const tokenType = (request.tokenType === AuthenticationScheme.POP) ? AuthenticationScheme.POP : AuthenticationScheme.BEARER;\r\n\r\n        const result: AuthenticationResult = {\r\n            authority: authority,\r\n            uniqueId: uid,\r\n            tenantId: tid,\r\n            scopes: responseScopes.asArray(),\r\n            account: accountEntity.getAccountInfo(),\r\n            idToken: response.id_token,\r\n            idTokenClaims: idTokenObj.claims,\r\n            accessToken: responseAccessToken,\r\n            fromCache: mats ? this.isResponseFromCache(mats) : false,\r\n            expiresOn: new Date(Number(reqTimestamp + response.expires_in) * 1000),\r\n            tokenType: tokenType,\r\n            correlationId: this.correlationId,\r\n            state: response.state,\r\n            fromNativeBroker: true\r\n        };\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * cache the account entity in browser storage\r\n     * @param accountEntity \r\n     */\r\n    cacheAccount(accountEntity: AccountEntity): void{\r\n        // Store the account info and hence `nativeAccountId` in browser cache\r\n        this.browserStorage.setAccount(accountEntity);\r\n\r\n        // Remove any existing cached tokens for this account in browser storage\r\n        this.browserStorage.removeAccountContext(accountEntity).catch((e) => {\r\n            this.logger.error(`Error occurred while removing account context from browser storage. ${e}`);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Stores the access_token and id_token in inmemory storage\r\n     * @param response \r\n     * @param request \r\n     * @param homeAccountIdentifier \r\n     * @param idTokenObj \r\n     * @param responseAccessToken \r\n     * @param tenantId \r\n     * @param reqTimestamp \r\n     */\r\n    cacheNativeTokens(response: NativeResponse, request: NativeTokenRequest, homeAccountIdentifier: string, idTokenObj: AuthToken, responseAccessToken: string, tenantId: string, reqTimestamp: number): void {\r\n\r\n        const cachedIdToken: IdTokenEntity | null =\r\n            IdTokenEntity.createIdTokenEntity(\r\n                homeAccountIdentifier,\r\n                request.authority,\r\n                response.id_token || Constants.EMPTY_STRING,\r\n                request.clientId,\r\n                idTokenObj.claims.tid || Constants.EMPTY_STRING\r\n            );\r\n\r\n        // cache accessToken in inmemory storage\r\n        const expiresIn: number = (request.tokenType === AuthenticationScheme.POP)\r\n            ? Constants.SHR_NONCE_VALIDITY\r\n            : (\r\n                typeof response.expires_in === \"string\"\r\n                    ? parseInt(response.expires_in, 10)\r\n                    : response.expires_in\r\n            ) || 0;\r\n        const tokenExpirationSeconds = reqTimestamp + expiresIn;\r\n        const responseScopes = this.generateScopes(response, request);\r\n        const cachedAccessToken: AccessTokenEntity | null =\r\n            AccessTokenEntity.createAccessTokenEntity(\r\n                homeAccountIdentifier,\r\n                request.authority,\r\n                responseAccessToken,\r\n                request.clientId,\r\n                idTokenObj\r\n                    ? idTokenObj.claims.tid || Constants.EMPTY_STRING\r\n                    : tenantId,\r\n                responseScopes.printScopes(),\r\n                tokenExpirationSeconds,\r\n                0,\r\n                this.browserCrypto\r\n            );\r\n\r\n        const nativeCacheRecord = new CacheRecord(\r\n            undefined,\r\n            cachedIdToken,\r\n            cachedAccessToken\r\n        );\r\n\r\n        this.nativeStorageManager.saveCacheRecord(nativeCacheRecord);\r\n    }\r\n\r\n    protected addTelemetryFromNativeResponse(response: NativeResponse): MATS | null {\r\n\r\n        const mats = this.getMATSFromResponse(response);\r\n\r\n        if (!mats){\r\n            return null;\r\n        }\r\n        \r\n        this.performanceClient.addStaticFields({\r\n            extensionId: this.nativeMessageHandler.getExtensionId(),\r\n            extensionVersion: this.nativeMessageHandler.getExtensionVersion(),\r\n            matsBrokerVersion: mats.broker_version,\r\n            matsAccountJoinOnStart: mats.account_join_on_start,\r\n            matsAccountJoinOnEnd: mats.account_join_on_end,\r\n            matsDeviceJoin: mats.device_join,\r\n            matsPromptBehavior: mats.prompt_behavior,\r\n            matsApiErrorCode: mats.api_error_code,\r\n            matsUiVisible: mats.ui_visible,\r\n            matsSilentCode: mats.silent_code,\r\n            matsSilentBiSubCode: mats.silent_bi_sub_code,\r\n            matsSilentMessage: mats.silent_message,\r\n            matsSilentStatus: mats.silent_status,\r\n            matsHttpStatus: mats.http_status,\r\n            matsHttpEventCount: mats.http_event_count\r\n        }, this.correlationId);\r\n\r\n        return mats;\r\n    }\r\n\r\n    /**\r\n     * Validates native platform response before processing\r\n     * @param response\r\n     */\r\n    private validateNativeResponse(response: object): NativeResponse {\r\n        if (\r\n            response.hasOwnProperty(\"access_token\") &&\r\n            response.hasOwnProperty(\"id_token\") &&\r\n            response.hasOwnProperty(\"client_info\") &&\r\n            response.hasOwnProperty(\"account\") &&\r\n            response.hasOwnProperty(\"scope\") &&\r\n            response.hasOwnProperty(\"expires_in\")\r\n        ) {\r\n            return response as NativeResponse;\r\n        } else {\r\n            throw NativeAuthError.createUnexpectedError(\"Response missing expected properties.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets MATS telemetry from native response\r\n     * @param response\r\n     * @returns\r\n     */\r\n    private getMATSFromResponse(response: NativeResponse): MATS|null {\r\n        if (response.properties.MATS) {\r\n            try {\r\n                return JSON.parse(response.properties.MATS);\r\n            } catch (e) {\r\n                this.logger.error(\"NativeInteractionClient - Error parsing MATS telemetry, returning null instead\");\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not response came from native cache\r\n     * @param response\r\n     * @returns\r\n     */\r\n    protected isResponseFromCache(mats: MATS): boolean {\r\n        if (typeof mats.is_cached === \"undefined\") {\r\n            this.logger.verbose(\"NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false.\");\r\n            return false;\r\n        }\r\n\r\n        return !!mats.is_cached;\r\n    }\r\n\r\n    /**\r\n     * Translates developer provided request object into NativeRequest object\r\n     * @param request\r\n     */\r\n    protected async initializeNativeRequest(request: PopupRequest|SsoSilentRequest): Promise<NativeTokenRequest> {\r\n        this.logger.trace(\"NativeInteractionClient - initializeNativeRequest called\");\r\n\r\n        const authority = request.authority || this.config.auth.authority;\r\n\r\n        if (request.account) {\r\n            await this.validateRequestAuthority(authority, request.account);\r\n        }\r\n\r\n        const canonicalAuthority = new UrlString(authority);\r\n        canonicalAuthority.validateAsUri();\r\n\r\n        // scopes are expected to be received by the native broker as \"scope\" and will be added to the request below. Other properties that should be dropped from the request to the native broker can be included in the object destructuring here.\r\n        const { scopes, ...remainingProperties } = request; \r\n        const scopeSet = new ScopeSet(scopes || []);\r\n        scopeSet.appendScopes(OIDC_DEFAULT_SCOPES);\r\n\r\n        const getPrompt = () => {\r\n            // If request is silent, prompt is always none\r\n            switch (this.apiId) {\r\n                case ApiId.ssoSilent:\r\n                case ApiId.acquireTokenSilent_silentFlow:\r\n                    this.logger.trace(\"initializeNativeRequest: silent request sets prompt to none\");\r\n                    return PromptValue.NONE;\r\n                default:\r\n                    break;\r\n            }\r\n\r\n            // Prompt not provided, request may proceed and native broker decides if it needs to prompt\r\n            if (!request.prompt) {\r\n                this.logger.trace(\"initializeNativeRequest: prompt was not provided\");\r\n                return undefined;\r\n            }\r\n\r\n            // If request is interactive, check if prompt provided is allowed to go directly to native broker\r\n            switch (request.prompt) {\r\n                case PromptValue.NONE:\r\n                case PromptValue.CONSENT:\r\n                case PromptValue.LOGIN:\r\n                    this.logger.trace(\"initializeNativeRequest: prompt is compatible with native flow\");\r\n                    return request.prompt;\r\n                default:\r\n                    this.logger.trace(`initializeNativeRequest: prompt = ${request.prompt} is not compatible with native flow`);\r\n                    throw BrowserAuthError.createNativePromptParameterNotSupportedError();\r\n            }\r\n        };\r\n        \r\n        const validatedRequest: NativeTokenRequest = {\r\n            ...remainingProperties,\r\n            accountId: this.accountId,\r\n            clientId: this.config.auth.clientId,\r\n            authority: canonicalAuthority.urlString,\r\n            scope: scopeSet.printScopes(),\r\n            redirectUri: this.getRedirectUri(request.redirectUri),\r\n            prompt: getPrompt(),\r\n            correlationId: this.correlationId,\r\n            tokenType: request.authenticationScheme,\r\n            windowTitleSubstring: document.title,\r\n            extraParameters: {\r\n                ...request.extraQueryParameters,\r\n                ...request.tokenQueryParameters,\r\n                telemetry: NativeConstants.MATS_TELEMETRY\r\n            },\r\n            extendedExpiryToken: false // Make this configurable?\r\n        };\r\n\r\n        if (request.authenticationScheme === AuthenticationScheme.POP) {\r\n\r\n            // add POP request type\r\n            const shrParameters: SignedHttpRequestParameters = {\r\n                resourceRequestUri: request.resourceRequestUri,\r\n                resourceRequestMethod: request.resourceRequestMethod,\r\n                shrClaims: request.shrClaims,\r\n                shrNonce: request.shrNonce\r\n            };\r\n\r\n            const popTokenGenerator = new PopTokenGenerator(this.browserCrypto);\r\n            const reqCnfData = await popTokenGenerator.generateCnf(shrParameters);\r\n\r\n            // to reduce the URL length, it is recommended to send the short form of the req_cnf \r\n            validatedRequest.reqCnf = reqCnfData.reqCnfString;\r\n            validatedRequest.keyId = reqCnfData.kid;\r\n        }\r\n\r\n        return validatedRequest;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAqBH,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IAA6C,SAAqB,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;IAO9D,SAAY,uBAAA,CAAA,MAA4B,EAAE,cAAmC,EAAE,aAAsB,EAAE,MAAc,EAAE,YAA0B,EAAE,gBAAmC,EAAE,KAAY,EAAE,iBAAqC,EAAE,QAA8B,EAAE,SAAiB,EAAE,iBAAsC,EAAE,aAAsB,EAAA;AAA9V,QAAA,IAAA,KAAA,GACI,kBAAM,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,aAAa,CAAC,IAMnI,IAAA,CAAA;AALG,QAAA,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,KAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AACrC,QAAA,KAAI,CAAC,oBAAoB,GAAG,iBAAiB,CAAC;QAC9C,KAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,KAAI,CAAC,oBAAoB,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;;KACxL;AAED;;;AAGG;IACG,uBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAoD,EAAA;;;;;;AACnE,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;AAG9D,wBAAA,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,mCAAmC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC5I,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAGtB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA3D,wBAAA,aAAa,GAAG,EAA2C,CAAA,IAAA,EAAA,CAAA;;;;wBAI9C,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA,CAAA;;AAAzE,wBAAA,MAAM,GAAG,EAAgE,CAAA,IAAA,EAAA,CAAA;wBAC/E,mBAAmB,CAAC,cAAc,CAAC;AAC/B,4BAAA,OAAO,EAAE,IAAI;AACb,4BAAA,cAAc,EAAE,KAAK;AACrB,4BAAA,SAAS,EAAE,IAAI;AAClB,yBAAA,CAAC,CAAC;AACH,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AAGd,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;;;AAI7F,wBAAA,WAAW,GAA+B;4BAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,4BAAA,OAAO,EAAE,aAAa;yBACzB,CAAC;wBAEuB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA,CAAA;;AAA3E,wBAAA,QAAQ,GAAW,EAAwD,CAAA,IAAA,EAAA,CAAA;AAC3E,wBAAA,iBAAiB,GAAmB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;wBAEhF,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,EAAE,aAAa,EAAE,YAAY,CAAC;iCAC3E,IAAI,CAAC,UAAC,MAA4B,EAAA;gCAC/B,mBAAmB,CAAC,cAAc,CAAC;AAC/B,oCAAA,OAAO,EAAE,IAAI;AACb,oCAAA,cAAc,EAAE,IAAI;oCACpB,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,iCAAA,CAAC,CAAC;AACH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACD,KAAK,CAAC,UAAC,KAAgB,EAAA;gCACpB,mBAAmB,CAAC,cAAc,CAAC;AAC/B,oCAAA,OAAO,EAAE,KAAK;oCACd,SAAS,EAAE,KAAK,CAAC,SAAS;oCAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oCAAA,cAAc,EAAE,IAAI;AACvB,iCAAA,CAAC,CAAC;AACH,gCAAA,MAAM,KAAK,CAAC;AAChB,6BAAC,CAAC,CAAC,CAAA;;;;AACV,KAAA,CAAA;AAED;;;;;AAKG;AACK,IAAA,uBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAhC,UAAiC,OAA2B,EAAE,aAA0B,EAAA;QACpF,OAAO;YACH,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE;AACpD,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,YAAY,EAAE,KAAK;SACtB,CAAC;KACL,CAAA;AAED;;;;;AAKG;AACa,IAAA,uBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtC,UAAuC,eAAuB,EAAE,OAA2B,EAAA;;;;;;wBACvF,IAAI,CAAC,eAAe,EAAE;AAClB,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8EAA8E,CAAC,CAAC;AACpG,4BAAA,MAAM,eAAe,CAAC,yBAAyB,EAAE,CAAC;AACrD,yBAAA;AAEK,wBAAA,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAC,eAAe,EAAA,eAAA,EAAC,CAAC,CAAC;wBAChF,IAAI,CAAC,OAAO,EAAE;AACV,4BAAA,MAAM,eAAe,CAAC,yBAAyB,EAAE,CAAC;AACrD,yBAAA;;;;wBAIS,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;wBACvD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA,CAAA;;AAAjE,wBAAA,MAAM,GAAG,EAAwD,CAAA,IAAA,EAAA,CAAA;wBAEjE,WAAW,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACV,OAAO,CACV,EAAA,EAAA,aAAa,EAAE,MAAM,CAAC,aAA4B,EAAA,CACrD,CAAC;AACF,wBAAA,OAAA,CAAA,CAAA,aAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACO,MAAM,CAAA,EAAA,EACT,OAAO,EAAE,WAAW,EACtB,CAAA,CAAA,CAAA;;;AAEF,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;;AAGG;IACG,uBAAoB,CAAA,SAAA,CAAA,oBAAA,GAA1B,UAA2B,OAAwB,EAAA;;;;;;AAC/C,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;AACtD,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA3D,wBAAA,aAAa,GAAG,EAA2C,CAAA,IAAA,EAAA,CAAA;AAE3D,wBAAA,WAAW,GAA+B;4BAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,4BAAA,OAAO,EAAE,aAAa;yBACzB,CAAC;;;;wBAG2B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA,CAAA;;AAA3E,wBAAA,QAAQ,GAAW,EAAwD,CAAA,IAAA,EAAA,CAAA;AACjF,wBAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;;;;;wBAGtC,IAAI,GAAC,YAAY,eAAe,IAAI,GAAC,CAAC,OAAO,EAAE,EAAE;AAC7C,4BAAA,MAAM,GAAC,CAAC;AACX,yBAAA;;;AAEL,wBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC;AAExG,wBAAA,iBAAiB,GAAsB;4BACzC,KAAK,EAAE,KAAK,CAAC,oBAAoB;AACjC,4BAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,4BAAA,SAAS,EAAE,KAAK;yBACnB,CAAC;wBACI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBACjI,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA,CAAA;;wBAA5E,EAA4E,CAAA,IAAA,EAAA,CAAC;;;;;AAChF,KAAA,CAAA;AAED;;AAEG;AACG,IAAA,uBAAA,CAAA,SAAA,CAAA,qBAAqB,GAA3B,YAAA;;;;;;AACI,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;wBAC7E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;AACpD,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;AAC1G,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAGK,wBAAA,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;wBACnE,IAAI,CAAC,aAAa,EAAE;AAChB,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wGAAwG,CAAC,CAAC;AAC9H,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;wBAEO,MAAM,GAAgB,aAAa,CAAA,MAA7B,EAAK,OAAO,UAAI,aAAa,EAArC,CAAqB,QAAA,CAAA,CAAD,CAAkB;AAC5C,wBAAA,IAAI,MAAM,EAAE;AACR,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sMAAsM,CAAC,CAAC;AAC/N,yBAAA;AAED,wBAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;AAElG,wBAAA,WAAW,GAA+B;4BAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,4BAAA,OAAO,EAAE,OAAO;yBACnB,CAAC;AAEI,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;;;;AAGxC,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mFAAmF,CAAC,CAAC;wBAChF,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA,CAAA;;AAA3E,wBAAA,QAAQ,GAAW,EAAwD,CAAA,IAAA,EAAA,CAAA;AACjF,wBAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;wBAChC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAA0B,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;AAC5F,wBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;AAEd,wBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,wBAAA,MAAM,GAAC,CAAC;;;;;AAEf,KAAA,CAAA;AAED;;;AAGG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC9D,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;KACvD,CAAA;AAED;;;;;AAKG;AACa,IAAA,uBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApC,UAAqC,QAAwB,EAAE,OAA2B,EAAE,YAAoB,EAAA;;;;;;AAC5G,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;wBAE5E,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,SAAS,EAAE;;AAE3C,4BAAA,MAAM,eAAe,CAAC,qBAAqB,EAAE,CAAC;AACjD,yBAAA;wBAGiB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAhE,wBAAA,SAAS,GAAG,EAAoD,CAAA,IAAA,EAAA,CAAA;AAGhE,wBAAA,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;wBAC7C,qBAAqB,GAAG,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC/E,wBAAA,aAAa,GAAG,aAAa,CAAC,aAAa,CAC7C;AACI,4BAAA,aAAa,EAAE,qBAAqB;4BACpC,aAAa,EAAE,UAAU,CAAC,MAAM;4BAChC,UAAU,EAAE,QAAQ,CAAC,WAAW;AAChC,4BAAA,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;yBACvC,EACD,SAAS,CACZ,CAAC;AAEa,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAA,CAAA;;AAA1I,wBAAA,MAAM,GAAG,EAAiI,CAAA,IAAA,EAAA,CAAA;;AAGhJ,wBAAA,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;wBACjC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAEhI,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACjB,KAAA,CAAA;AAED;;;;AAIG;IACO,uBAAgB,CAAA,SAAA,CAAA,gBAAA,GAA1B,UAA2B,QAAwB,EAAA;AAC/C,QAAA,OAAO,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;KACzF,CAAA;AAED;;;;;AAKG;AACO,IAAA,uBAAA,CAAA,SAAA,CAAA,2BAA2B,GAArC,UAAsC,QAAwB,EAAE,UAAqB,EAAA;;AAEjF,QAAA,IAAM,qBAAqB,GAAG,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,EAAE,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;AAE7L,QAAA,OAAO,qBAAqB,CAAC;KAChC,CAAA;AAED;;;;;AAKG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,QAAwB,EAAE,OAA2B,EAAA;QAChE,OAAO,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACpG,CAAA;AAED;;;;AAIG;AACG,IAAA,uBAAA,CAAA,SAAA,CAAA,sBAAsB,GAA5B,UAA6B,QAAwB,EAAE,OAA2B,EAAA;;;;;;8BAE3E,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAA9C,OAA8C,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC7C;;;AAGG;;wBAGH,IAAI,QAAQ,CAAC,GAAG,EAAE;AACd,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;4BAChF,OAAO,CAAA,CAAA,aAAA,QAAQ,CAAC,GAAG,CAAC,CAAA;AACvB,yBAAA;wBAGK,iBAAiB,GAAsB,IAAI,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACjF,wBAAA,aAAa,GAAgC;4BAC/C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;yBAC7B,CAAC;AAEF;;;AAGG;AACH,wBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAChB,4BAAA,MAAM,eAAe,CAAC,uBAAuB,EAAE,CAAC;AACnD,yBAAA;AACM,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA,CAAA;AAAhG,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAyF,CAAC,CAAA;4BAEjG,OAAO,CAAA,CAAA,aAAA,QAAQ,CAAC,YAAY,CAAC,CAAA;;;;AAEpC,KAAA,CAAA;AAED;;;;;;;;;AASG;AACa,IAAA,uBAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5C,UAA6C,QAAwB,EAAE,OAA2B,EAAE,UAAqB,EAAE,aAA4B,EAAE,SAAiB,EAAE,YAAoB,EAAA;;;;;;AAGtL,wBAAA,IAAI,GAAG,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC;wBAGrD,cAAc,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAE3G,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;wBACtD,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC;AAC3G,wBAAA,GAAG,GAAG,iBAAiB,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC;wBAGjE,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA,CAAA;;AAA1E,wBAAA,mBAAmB,GAAG,EAAoD,CAAA,IAAA,EAAA,CAAA;wBAC1E,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG,IAAI,oBAAoB,CAAC,GAAG,GAAG,oBAAoB,CAAC,MAAM,CAAC;AAEtH,wBAAA,MAAM,GAAyB;AACjC,4BAAA,SAAS,EAAE,SAAS;AACpB,4BAAA,QAAQ,EAAE,GAAG;AACb,4BAAA,QAAQ,EAAE,GAAG;AACb,4BAAA,MAAM,EAAE,cAAc,CAAC,OAAO,EAAE;AAChC,4BAAA,OAAO,EAAE,aAAa,CAAC,cAAc,EAAE;4BACvC,OAAO,EAAE,QAAQ,CAAC,QAAQ;4BAC1B,aAAa,EAAE,UAAU,CAAC,MAAM;AAChC,4BAAA,WAAW,EAAE,mBAAmB;AAChC,4BAAA,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,KAAK;AACxD,4BAAA,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AACtE,4BAAA,SAAS,EAAE,SAAS;4BACpB,aAAa,EAAE,IAAI,CAAC,aAAa;4BACjC,KAAK,EAAE,QAAQ,CAAC,KAAK;AACrB,4BAAA,gBAAgB,EAAE,IAAI;yBACzB,CAAC;AAEF,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACjB,KAAA,CAAA;AAED;;;AAGG;IACH,uBAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,aAA4B,EAAA;QAAzC,IAQC,KAAA,GAAA,IAAA,CAAA;;AANG,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;;QAG9C,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,UAAC,CAAC,EAAA;YAC5D,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sEAAuE,GAAA,CAAG,CAAC,CAAC;AAClG,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UAAkB,QAAwB,EAAE,OAA2B,EAAE,qBAA6B,EAAE,UAAqB,EAAE,mBAA2B,EAAE,QAAgB,EAAE,YAAoB,EAAA;AAE9L,QAAA,IAAM,aAAa,GACf,aAAa,CAAC,mBAAmB,CAC7B,qBAAqB,EACrB,OAAO,CAAC,SAAS,EACjB,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EAC3C,OAAO,CAAC,QAAQ,EAChB,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,CAClD,CAAC;;QAGN,IAAM,SAAS,GAAW,CAAC,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;cACnE,SAAS,CAAC,kBAAkB;AAC9B,cAAE,CACE,OAAO,QAAQ,CAAC,UAAU,KAAK,QAAQ;kBACjC,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC;AACnC,kBAAE,QAAQ,CAAC,UAAU,KACxB,CAAC,CAAC;AACX,QAAA,IAAM,sBAAsB,GAAG,YAAY,GAAG,SAAS,CAAC;QACxD,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC9D,QAAA,IAAM,iBAAiB,GACnB,iBAAiB,CAAC,uBAAuB,CACrC,qBAAqB,EACrB,OAAO,CAAC,SAAS,EACjB,mBAAmB,EACnB,OAAO,CAAC,QAAQ,EAChB,UAAU;cACJ,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY;AACjD,cAAE,QAAQ,EACd,cAAc,CAAC,WAAW,EAAE,EAC5B,sBAAsB,EACtB,CAAC,EACD,IAAI,CAAC,aAAa,CACrB,CAAC;QAEN,IAAM,iBAAiB,GAAG,IAAI,WAAW,CACrC,SAAS,EACT,aAAa,EACb,iBAAiB,CACpB,CAAC;AAEF,QAAA,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;KAChE,CAAA;IAES,uBAA8B,CAAA,SAAA,CAAA,8BAAA,GAAxC,UAAyC,QAAwB,EAAA;QAE7D,IAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,EAAC;AACN,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;AACnC,YAAA,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE;AACvD,YAAA,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE;YACjE,iBAAiB,EAAE,IAAI,CAAC,cAAc;YACtC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB;YAClD,oBAAoB,EAAE,IAAI,CAAC,mBAAmB;YAC9C,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,kBAAkB,EAAE,IAAI,CAAC,eAAe;YACxC,gBAAgB,EAAE,IAAI,CAAC,cAAc;YACrC,aAAa,EAAE,IAAI,CAAC,UAAU;YAC9B,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB;YAC5C,iBAAiB,EAAE,IAAI,CAAC,cAAc;YACtC,gBAAgB,EAAE,IAAI,CAAC,aAAa;YACpC,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB;AAC5C,SAAA,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAEvB,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACK,uBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,QAAgB,EAAA;AAC3C,QAAA,IACI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC;AACvC,YAAA,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC;AACnC,YAAA,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;AACtC,YAAA,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAClC,YAAA,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC;AAChC,YAAA,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,EACvC;AACE,YAAA,OAAO,QAA0B,CAAC;AACrC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,eAAe,CAAC,qBAAqB,CAAC,uCAAuC,CAAC,CAAC;AACxF,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACK,uBAAmB,CAAA,SAAA,CAAA,mBAAA,GAA3B,UAA4B,QAAwB,EAAA;AAChD,QAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE;YAC1B,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC/C,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gFAAgF,CAAC,CAAC;AACvG,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACO,uBAAmB,CAAA,SAAA,CAAA,mBAAA,GAA7B,UAA8B,IAAU,EAAA;AACpC,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,WAAW,EAAE;AACvC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gIAAgI,CAAC,CAAC;AACtJ,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;KAC3B,CAAA;AAED;;;AAGG;IACa,uBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvC,UAAwC,OAAsC,EAAA;;;;;;;AAC1E,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;AAExE,wBAAA,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;6BAE9D,OAAO,CAAC,OAAO,EAAf,OAAe,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACf,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA/D,wBAAA,EAAA,CAAA,IAAA,EAA+D,CAAC;;;AAG9D,wBAAA,kBAAkB,GAAG,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;wBACpD,kBAAkB,CAAC,aAAa,EAAE,CAAC;wBAG3B,MAAM,GAA6B,OAAO,CAAA,MAApC,EAAK,mBAAmB,UAAK,OAAO,EAA5C,CAAkC,QAAA,CAAA,CAAF,CAAa;wBAC7C,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;AAC5C,wBAAA,QAAQ,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAErC,wBAAA,SAAS,GAAG,YAAA;;4BAEd,QAAQ,KAAI,CAAC,KAAK;gCACd,KAAK,KAAK,CAAC,SAAS,CAAC;gCACrB,KAAK,KAAK,CAAC,6BAA6B;AACpC,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;oCACjF,OAAO,WAAW,CAAC,IAAI,CAAC;AAG/B,6BAAA;;AAGD,4BAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACtE,gCAAA,OAAO,SAAS,CAAC;AACpB,6BAAA;;4BAGD,QAAQ,OAAO,CAAC,MAAM;gCAClB,KAAK,WAAW,CAAC,IAAI,CAAC;gCACtB,KAAK,WAAW,CAAC,OAAO,CAAC;gCACzB,KAAK,WAAW,CAAC,KAAK;AAClB,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;oCACpF,OAAO,OAAO,CAAC,MAAM,CAAC;AAC1B,gCAAA;oCACI,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAqC,GAAA,OAAO,CAAC,MAAM,GAAqC,qCAAA,CAAC,CAAC;AAC5G,oCAAA,MAAM,gBAAgB,CAAC,4CAA4C,EAAE,CAAC;AAC7E,6BAAA;AACL,yBAAC,CAAC;AAEI,wBAAA,gBAAgB,yBACf,mBAAmB,CAAA,EAAA,EACtB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACnC,SAAS,EAAE,kBAAkB,CAAC,SAAS,EACvC,KAAK,EAAE,QAAQ,CAAC,WAAW,EAAE,EAC7B,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EACrD,MAAM,EAAE,SAAS,EAAE,EACnB,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,SAAS,EAAE,OAAO,CAAC,oBAAoB,EACvC,oBAAoB,EAAE,QAAQ,CAAC,KAAK,EACpC,eAAe,iCACR,OAAO,CAAC,oBAAoB,CAC5B,EAAA,OAAO,CAAC,oBAAoB,CAAA,EAAA,EAC/B,SAAS,EAAE,eAAe,CAAC,cAAc,EAAA,CAAA,EAE7C,mBAAmB,EAAE,KAAK;2BAC7B,CAAC;8BAEE,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAAzD,OAAyD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAGnD,wBAAA,aAAa,GAAgC;4BAC/C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;yBAC7B,CAAC;wBAEI,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACjD,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA,CAAA;;AAA/D,wBAAA,UAAU,GAAG,EAAkD,CAAA,IAAA,EAAA,CAAA;;AAGrE,wBAAA,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC;AAClD,wBAAA,gBAAgB,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC;;AAG5C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,CAAA;;;;AAC3B,KAAA,CAAA;IACL,OAAC,uBAAA,CAAA;AAAD,CA9lBA,CAA6C,qBAAqB,CA8lBjE;;;;"}