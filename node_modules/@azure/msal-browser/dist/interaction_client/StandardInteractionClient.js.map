{"version": 3, "file": "StandardInteractionClient.js", "sources": ["../../src/interaction_client/StandardInteractionClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ServerTelemetryManager, CommonAuthorizationCodeRequest, Constants, AuthorizationCodeClient, ClientConfiguration, AuthorityOptions, Authority, AuthorityFactory, ServerAuthorizationCodeResponse, UrlString, CommonEndSessionRequest, ProtocolUtils, ResponseMode, StringUtils, IdTokenClaims, AccountInfo, AzureCloudOptions, PerformanceEvents, AuthError} from \"@azure/msal-common\";\r\nimport { BaseInteractionClient } from \"./BaseInteractionClient\";\r\nimport { AuthorizationUrlRequest } from \"../request/AuthorizationUrlRequest\";\r\nimport { BrowserConstants, InteractionType } from \"../utils/BrowserConstants\";\r\nimport { version } from \"../packageMetadata\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { BrowserProtocolUtils, BrowserStateObject } from \"../utils/BrowserProtocolUtils\";\r\nimport { EndSessionRequest } from \"../request/EndSessionRequest\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\n\r\n/**\r\n * Defines the class structure and helper functions used by the \"standard\", non-brokered auth flows (popup, redirect, silent (RT), silent (iframe))\r\n */\r\nexport abstract class StandardInteractionClient extends BaseInteractionClient {\r\n    /**\r\n     * Generates an auth code request tied to the url request.\r\n     * @param request\r\n     */\r\n    protected async initializeAuthorizationCodeRequest(request: AuthorizationUrlRequest): Promise<CommonAuthorizationCodeRequest> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest, request.correlationId);\r\n        this.logger.verbose(\"initializeAuthorizationRequest called\", request.correlationId);\r\n        const generatedPkceParams = await this.browserCrypto.generatePkceCodes();\r\n\r\n        const authCodeRequest: CommonAuthorizationCodeRequest = {\r\n            ...request,\r\n            redirectUri: request.redirectUri,\r\n            code: Constants.EMPTY_STRING,\r\n            codeVerifier: generatedPkceParams.verifier\r\n        };\r\n\r\n        request.codeChallenge = generatedPkceParams.challenge;\r\n        request.codeChallengeMethod = Constants.S256_CODE_CHALLENGE_METHOD;\r\n\r\n        return authCodeRequest;\r\n    }\r\n\r\n    /**\r\n     * Initializer for the logout request.\r\n     * @param logoutRequest\r\n     */\r\n    protected initializeLogoutRequest(logoutRequest?: EndSessionRequest): CommonEndSessionRequest {\r\n        this.logger.verbose(\"initializeLogoutRequest called\", logoutRequest?.correlationId);\r\n\r\n        const validLogoutRequest: CommonEndSessionRequest = {\r\n            correlationId: this.correlationId || this.browserCrypto.createNewGuid(),\r\n            ...logoutRequest\r\n        };\r\n\r\n        /**\r\n         * Set logout_hint to be login_hint from ID Token Claims if present\r\n         * and logoutHint attribute wasn't manually set in logout request\r\n         */\r\n        if (logoutRequest) {\r\n            // If logoutHint isn't set and an account was passed in, try to extract logoutHint from ID Token Claims\r\n            if (!logoutRequest.logoutHint) {\r\n                if(logoutRequest.account) {\r\n                    const logoutHint = this.getLogoutHintFromIdTokenClaims(logoutRequest.account);\r\n                    if (logoutHint) {\r\n                        this.logger.verbose(\"Setting logoutHint to login_hint ID Token Claim value for the account provided\");\r\n                        validLogoutRequest.logoutHint = logoutHint;\r\n                    }\r\n                } else {\r\n                    this.logger.verbose(\"logoutHint was not set and account was not passed into logout request, logoutHint will not be set\");\r\n                }\r\n            } else {\r\n                this.logger.verbose(\"logoutHint has already been set in logoutRequest\");\r\n            }\r\n        } else {\r\n            this.logger.verbose(\"logoutHint will not be set since no logout request was configured\");\r\n        }\r\n\r\n        /*\r\n         * Only set redirect uri if logout request isn't provided or the set uri isn't null.\r\n         * Otherwise, use passed uri, config, or current page.\r\n         */\r\n        if (!logoutRequest || logoutRequest.postLogoutRedirectUri !== null) {\r\n            if (logoutRequest && logoutRequest.postLogoutRedirectUri) {\r\n                this.logger.verbose(\"Setting postLogoutRedirectUri to uri set on logout request\", validLogoutRequest.correlationId);\r\n                validLogoutRequest.postLogoutRedirectUri = UrlString.getAbsoluteUrl(logoutRequest.postLogoutRedirectUri, BrowserUtils.getCurrentUri());\r\n            } else if (this.config.auth.postLogoutRedirectUri === null) {\r\n                this.logger.verbose(\"postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect\", validLogoutRequest.correlationId);\r\n            } else if (this.config.auth.postLogoutRedirectUri) {\r\n                this.logger.verbose(\"Setting postLogoutRedirectUri to configured uri\", validLogoutRequest.correlationId);\r\n                validLogoutRequest.postLogoutRedirectUri = UrlString.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri, BrowserUtils.getCurrentUri());\r\n            } else {\r\n                this.logger.verbose(\"Setting postLogoutRedirectUri to current page\", validLogoutRequest.correlationId);\r\n                validLogoutRequest.postLogoutRedirectUri = UrlString.getAbsoluteUrl(BrowserUtils.getCurrentUri(), BrowserUtils.getCurrentUri());\r\n            }\r\n        } else {\r\n            this.logger.verbose(\"postLogoutRedirectUri passed as null, not setting post logout redirect uri\", validLogoutRequest.correlationId);\r\n        }\r\n\r\n        return validLogoutRequest;\r\n    }\r\n\r\n    /**\r\n     * Parses login_hint ID Token Claim out of AccountInfo object to be used as\r\n     * logout_hint in end session request.\r\n     * @param account\r\n     */\r\n    protected getLogoutHintFromIdTokenClaims(account: AccountInfo): string | null {\r\n        const idTokenClaims: IdTokenClaims | undefined = account.idTokenClaims;\r\n        if (idTokenClaims) {\r\n            if (idTokenClaims.login_hint) {\r\n                return idTokenClaims.login_hint;\r\n            } else {\r\n                this.logger.verbose(\"The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request\");\r\n            }\r\n        } else {\r\n            this.logger.verbose(\"The provided account does not contain ID Token Claims, logoutHint will not be added to logout request\");\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Creates an Authorization Code Client with the given authority, or the default authority.\r\n     * @param serverTelemetryManager\r\n     * @param authorityUrl\r\n     */\r\n    protected async createAuthCodeClient(serverTelemetryManager: ServerTelemetryManager, authorityUrl?: string, requestAzureCloudOptions?: AzureCloudOptions): Promise<AuthorizationCodeClient> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.StandardInteractionClientCreateAuthCodeClient, this.correlationId);\r\n        // Create auth module.\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, this.correlationId);\r\n        const clientConfig = await this.getClientConfiguration(serverTelemetryManager, authorityUrl, requestAzureCloudOptions);\r\n        return new AuthorizationCodeClient(clientConfig, this.performanceClient);\r\n    }\r\n\r\n    /**\r\n     * Creates a Client Configuration object with the given request authority, or the default authority.\r\n     * @param serverTelemetryManager\r\n     * @param requestAuthority\r\n     * @param requestCorrelationId\r\n     */\r\n    protected async getClientConfiguration(serverTelemetryManager: ServerTelemetryManager, requestAuthority?: string, requestAzureCloudOptions?: AzureCloudOptions): Promise<ClientConfiguration> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.StandardInteractionClientGetClientConfiguration, this.correlationId);\r\n        this.logger.verbose(\"getClientConfiguration called\", this.correlationId);\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetDiscoveredAuthority, this.correlationId);\r\n        const discoveredAuthority = await this.getDiscoveredAuthority(requestAuthority, requestAzureCloudOptions);\r\n        const logger= this.config.system.loggerOptions;\r\n\r\n        return {\r\n            authOptions: {\r\n                clientId: this.config.auth.clientId,\r\n                authority: discoveredAuthority,\r\n                clientCapabilities: this.config.auth.clientCapabilities\r\n            },\r\n            systemOptions: {\r\n                tokenRenewalOffsetSeconds: this.config.system.tokenRenewalOffsetSeconds,\r\n                preventCorsPreflight: true\r\n            },\r\n            loggerOptions: {\r\n                loggerCallback: logger.loggerCallback,\r\n                piiLoggingEnabled: logger.piiLoggingEnabled,\r\n                logLevel: logger.logLevel,\r\n                correlationId: this.correlationId\r\n            },\r\n            cacheOptions: {\r\n                claimsBasedCachingEnabled: this.config.cache.claimsBasedCachingEnabled\r\n            },\r\n            cryptoInterface: this.browserCrypto,\r\n            networkInterface: this.networkClient,\r\n            storageInterface: this.browserStorage,\r\n            serverTelemetryManager: serverTelemetryManager,\r\n            libraryInfo: {\r\n                sku: BrowserConstants.MSAL_SKU,\r\n                version: version,\r\n                cpu: Constants.EMPTY_STRING,\r\n                os: Constants.EMPTY_STRING\r\n            },\r\n            telemetry: this.config.telemetry\r\n        };\r\n    }\r\n    \r\n    /**\r\n     * @param hash\r\n     * @param interactionType\r\n     */\r\n    protected validateAndExtractStateFromHash(serverParams: ServerAuthorizationCodeResponse, interactionType: InteractionType, requestCorrelationId?: string): string {\r\n        this.logger.verbose(\"validateAndExtractStateFromHash called\", requestCorrelationId);\r\n        if (!serverParams.state) {\r\n            throw BrowserAuthError.createHashDoesNotContainStateError();\r\n        }\r\n\r\n        const platformStateObj = BrowserProtocolUtils.extractBrowserRequestState(this.browserCrypto, serverParams.state);\r\n        if (!platformStateObj) {\r\n            throw BrowserAuthError.createUnableToParseStateError();\r\n        }\r\n\r\n        if (platformStateObj.interactionType !== interactionType) {\r\n            throw BrowserAuthError.createStateInteractionTypeMismatchError();\r\n        }\r\n\r\n        this.logger.verbose(\"Returning state from hash\", requestCorrelationId);\r\n        return serverParams.state;\r\n    }\r\n\r\n    /**\r\n     * Used to get a discovered version of the default authority.\r\n     * @param requestAuthority\r\n     * @param requestCorrelationId\r\n     */\r\n    protected async getDiscoveredAuthority(requestAuthority?: string, requestAzureCloudOptions?: AzureCloudOptions): Promise<Authority> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.StandardInteractionClientGetDiscoveredAuthority, this.correlationId);\r\n        this.logger.verbose(\"getDiscoveredAuthority called\", this.correlationId);\r\n        const getAuthorityMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.StandardInteractionClientGetDiscoveredAuthority, this.correlationId);\r\n        const authorityOptions: AuthorityOptions = {\r\n            protocolMode: this.config.auth.protocolMode,\r\n            knownAuthorities: this.config.auth.knownAuthorities,\r\n            cloudDiscoveryMetadata: this.config.auth.cloudDiscoveryMetadata,\r\n            authorityMetadata: this.config.auth.authorityMetadata,\r\n            skipAuthorityMetadataCache: this.config.auth.skipAuthorityMetadataCache\r\n        };\r\n\r\n        // build authority string based on auth params, precedence - azureCloudInstance + tenant >> authority\r\n        const userAuthority = requestAuthority ? requestAuthority : this.config.auth.authority;\r\n\r\n        // fall back to the authority from config\r\n        const builtAuthority = Authority.generateAuthority(userAuthority, requestAzureCloudOptions || this.config.auth.azureCloudOptions);\r\n        this.logger.verbose(\"Creating discovered authority with configured authority\", this.correlationId);\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.AuthorityFactoryCreateDiscoveredInstance, this.correlationId);\r\n        return await AuthorityFactory.createDiscoveredInstance(builtAuthority, this.config.system.networkClient, this.browserStorage, authorityOptions, this.logger, this.performanceClient, this.correlationId)\r\n            .then((result: Authority) => {\r\n                getAuthorityMeasurement.endMeasurement({\r\n                    success: true,\r\n                });\r\n\r\n                return result;\r\n            })\r\n            .catch((error:AuthError) => {\r\n                getAuthorityMeasurement.endMeasurement({\r\n                    errorCode: error.errorCode,\r\n                    subErrorCode: error.subError,\r\n                    success: false\r\n                });\r\n\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Helper to initialize required request parameters for interactive APIs and ssoSilent()\r\n     * @param request\r\n     * @param interactionType\r\n     */\r\n    protected async initializeAuthorizationRequest(request: RedirectRequest|PopupRequest|SsoSilentRequest, interactionType: InteractionType): Promise<AuthorizationUrlRequest> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest, this.correlationId);\r\n        this.logger.verbose(\"initializeAuthorizationRequest called\", this.correlationId);\r\n        const redirectUri = this.getRedirectUri(request.redirectUri);\r\n        const browserState: BrowserStateObject = {\r\n            interactionType: interactionType\r\n        };\r\n        const state = ProtocolUtils.setRequestState(\r\n            this.browserCrypto,\r\n            (request && request.state)|| Constants.EMPTY_STRING,\r\n            browserState\r\n        );\r\n\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.InitializeBaseRequest, this.correlationId);\r\n        const validatedRequest: AuthorizationUrlRequest = {\r\n            ...await this.initializeBaseRequest(request),\r\n            redirectUri: redirectUri,\r\n            state: state,\r\n            nonce: request.nonce || this.browserCrypto.createNewGuid(),\r\n            responseMode: ResponseMode.FRAGMENT\r\n        };\r\n\r\n        const account = request.account || this.browserStorage.getActiveAccount();\r\n        if (account) {\r\n            this.logger.verbose(\"Setting validated request account\", this.correlationId);\r\n            this.logger.verbosePii(`Setting validated request account: ${account.homeAccountId}`, this.correlationId);\r\n            validatedRequest.account = account;\r\n        }\r\n\r\n        // Check for ADAL/MSAL v1 SSO\r\n        if (StringUtils.isEmpty(validatedRequest.loginHint) && !account) {\r\n            const legacyLoginHint = this.browserStorage.getLegacyLoginHint();\r\n            if (legacyLoginHint) {\r\n                validatedRequest.loginHint = legacyLoginHint;\r\n            }\r\n        }\r\n\r\n        return validatedRequest;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAeH;;AAEG;AACH,IAAA,yBAAA,kBAAA,UAAA,MAAA,EAAA;IAAwD,SAAqB,CAAA,yBAAA,EAAA,MAAA,CAAA,CAAA;AAA7E,IAAA,SAAA,yBAAA,GAAA;;KA+QC;AA9QG;;;AAGG;IACa,yBAAkC,CAAA,SAAA,CAAA,kCAAA,GAAlD,UAAmD,OAAgC,EAAA;;;;;;AAC/E,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,2DAA2D,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBACjJ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uCAAuC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACxD,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAA,CAAA;;AAAlE,wBAAA,mBAAmB,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;wBAElE,eAAe,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACd,OAAO,CACV,EAAA,EAAA,WAAW,EAAE,OAAO,CAAC,WAAW,EAChC,IAAI,EAAE,SAAS,CAAC,YAAY,EAC5B,YAAY,EAAE,mBAAmB,CAAC,QAAQ,EAAA,CAC7C,CAAC;AAEF,wBAAA,OAAO,CAAC,aAAa,GAAG,mBAAmB,CAAC,SAAS,CAAC;AACtD,wBAAA,OAAO,CAAC,mBAAmB,GAAG,SAAS,CAAC,0BAA0B,CAAC;AAEnE,wBAAA,OAAA,CAAA,CAAA,aAAO,eAAe,CAAC,CAAA;;;;AAC1B,KAAA,CAAA;AAED;;;AAGG;IACO,yBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAjC,UAAkC,aAAiC,EAAA;AAC/D,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,EAAE,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,aAAa,CAAC,CAAC;AAEpF,QAAA,IAAM,kBAAkB,GACpB,QAAA,CAAA,EAAA,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,EACpE,EAAA,aAAa,CACnB,CAAC;AAEF;;;AAGG;AACH,QAAA,IAAI,aAAa,EAAE;;AAEf,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC3B,IAAG,aAAa,CAAC,OAAO,EAAE;oBACtB,IAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC9E,oBAAA,IAAI,UAAU,EAAE;AACZ,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gFAAgF,CAAC,CAAC;AACtG,wBAAA,kBAAkB,CAAC,UAAU,GAAG,UAAU,CAAC;AAC9C,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mGAAmG,CAAC,CAAC;AAC5H,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;AAC3E,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mEAAmE,CAAC,CAAC;AAC5F,SAAA;AAED;;;AAGG;QACH,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,qBAAqB,KAAK,IAAI,EAAE;AAChE,YAAA,IAAI,aAAa,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBACtD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4DAA4D,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACpH,gBAAA,kBAAkB,CAAC,qBAAqB,GAAG,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,qBAAqB,EAAE,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;AAC1I,aAAA;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBACxD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sGAAsG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACjK,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC/C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;gBACzG,kBAAkB,CAAC,qBAAqB,GAAG,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;AAC7I,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACvG,gBAAA,kBAAkB,CAAC,qBAAqB,GAAG,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;AACnI,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4EAA4E,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACvI,SAAA;AAED,QAAA,OAAO,kBAAkB,CAAC;KAC7B,CAAA;AAED;;;;AAIG;IACO,yBAA8B,CAAA,SAAA,CAAA,8BAAA,GAAxC,UAAyC,OAAoB,EAAA;AACzD,QAAA,IAAM,aAAa,GAA8B,OAAO,CAAC,aAAa,CAAC;AACvE,QAAA,IAAI,aAAa,EAAE;YACf,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC1B,OAAO,aAAa,CAAC,UAAU,CAAC;AACnC,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oIAAoI,CAAC,CAAC;AAC7J,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uGAAuG,CAAC,CAAC;AAChI,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACa,IAAA,yBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApC,UAAqC,sBAA8C,EAAE,YAAqB,EAAE,wBAA4C,EAAA;;;;;;AACpJ,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;AAEhI,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBACzG,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,YAAY,EAAE,wBAAwB,CAAC,CAAA,CAAA;;AAAhH,wBAAA,YAAY,GAAG,EAAiG,CAAA,IAAA,EAAA,CAAA;wBACtH,OAAO,CAAA,CAAA,aAAA,IAAI,uBAAuB,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA;;;;AAC5E,KAAA,CAAA;AAED;;;;;AAKG;AACa,IAAA,yBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtC,UAAuC,sBAA8C,EAAE,gBAAyB,EAAE,wBAA4C,EAAA;;;;;;AAC1J,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAClI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACzE,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAClG,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,wBAAwB,CAAC,CAAA,CAAA;;AAAnG,wBAAA,mBAAmB,GAAG,EAA6E,CAAA,IAAA,EAAA,CAAA;wBACnG,MAAM,GAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;wBAE/C,OAAO,CAAA,CAAA,aAAA;AACH,gCAAA,WAAW,EAAE;AACT,oCAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,oCAAA,SAAS,EAAE,mBAAmB;AAC9B,oCAAA,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;AAC1D,iCAAA;AACD,gCAAA,aAAa,EAAE;AACX,oCAAA,yBAAyB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACvE,oCAAA,oBAAoB,EAAE,IAAI;AAC7B,iCAAA;AACD,gCAAA,aAAa,EAAE;oCACX,cAAc,EAAE,MAAM,CAAC,cAAc;oCACrC,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;oCAC3C,QAAQ,EAAE,MAAM,CAAC,QAAQ;oCACzB,aAAa,EAAE,IAAI,CAAC,aAAa;AACpC,iCAAA;AACD,gCAAA,YAAY,EAAE;AACV,oCAAA,yBAAyB,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB;AACzE,iCAAA;gCACD,eAAe,EAAE,IAAI,CAAC,aAAa;gCACnC,gBAAgB,EAAE,IAAI,CAAC,aAAa;gCACpC,gBAAgB,EAAE,IAAI,CAAC,cAAc;AACrC,gCAAA,sBAAsB,EAAE,sBAAsB;AAC9C,gCAAA,WAAW,EAAE;oCACT,GAAG,EAAE,gBAAgB,CAAC,QAAQ;AAC9B,oCAAA,OAAO,EAAE,OAAO;oCAChB,GAAG,EAAE,SAAS,CAAC,YAAY;oCAC3B,EAAE,EAAE,SAAS,CAAC,YAAY;AAC7B,iCAAA;AACD,gCAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;6BACnC,CAAC,CAAA;;;;AACL,KAAA,CAAA;AAED;;;AAGG;AACO,IAAA,yBAAA,CAAA,SAAA,CAAA,+BAA+B,GAAzC,UAA0C,YAA6C,EAAE,eAAgC,EAAE,oBAA6B,EAAA;QACpJ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wCAAwC,EAAE,oBAAoB,CAAC,CAAC;AACpF,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACrB,YAAA,MAAM,gBAAgB,CAAC,kCAAkC,EAAE,CAAC;AAC/D,SAAA;AAED,QAAA,IAAM,gBAAgB,GAAG,oBAAoB,CAAC,0BAA0B,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;QACjH,IAAI,CAAC,gBAAgB,EAAE;AACnB,YAAA,MAAM,gBAAgB,CAAC,6BAA6B,EAAE,CAAC;AAC1D,SAAA;AAED,QAAA,IAAI,gBAAgB,CAAC,eAAe,KAAK,eAAe,EAAE;AACtD,YAAA,MAAM,gBAAgB,CAAC,uCAAuC,EAAE,CAAC;AACpE,SAAA;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2BAA2B,EAAE,oBAAoB,CAAC,CAAC;QACvE,OAAO,YAAY,CAAC,KAAK,CAAC;KAC7B,CAAA;AAED;;;;AAIG;AACa,IAAA,yBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtC,UAAuC,gBAAyB,EAAE,wBAA4C,EAAA;;;;;;;AAC1G,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAClI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACnE,wBAAA,uBAAuB,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1J,wBAAA,gBAAgB,GAAqB;AACvC,4BAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;AAC3C,4BAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;AACnD,4BAAA,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAC/D,4BAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;AACrD,4BAAA,0BAA0B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B;yBAC1E,CAAC;AAGI,wBAAA,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAGjF,wBAAA,cAAc,GAAG,SAAS,CAAC,iBAAiB,CAAC,aAAa,EAAE,wBAAwB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBAClI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yDAAyD,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACnG,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,wCAAwC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAChH,wBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC;iCACnM,IAAI,CAAC,UAAC,MAAiB,EAAA;gCACpB,uBAAuB,CAAC,cAAc,CAAC;AACnC,oCAAA,OAAO,EAAE,IAAI;AAChB,iCAAA,CAAC,CAAC;AAEH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACD,KAAK,CAAC,UAAC,KAAe,EAAA;gCACnB,uBAAuB,CAAC,cAAc,CAAC;oCACnC,SAAS,EAAE,KAAK,CAAC,SAAS;oCAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oCAAA,OAAO,EAAE,KAAK;AACjB,iCAAA,CAAC,CAAC;AAEH,gCAAA,MAAM,KAAK,CAAC;AAChB,6BAAC,CAAC,CAAA,CAAA;AAhBN,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAgBD,CAAC,CAAA;;;;AACV,KAAA,CAAA;AAED;;;;AAIG;AACa,IAAA,yBAAA,CAAA,SAAA,CAAA,8BAA8B,GAA9C,UAA+C,OAAsD,EAAE,eAAgC,EAAA;;;;;;AACnI,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,uDAAuD,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC1I,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC3E,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACvD,wBAAA,YAAY,GAAuB;AACrC,4BAAA,eAAe,EAAE,eAAe;yBACnC,CAAC;wBACI,KAAK,GAAG,aAAa,CAAC,eAAe,CACvC,IAAI,CAAC,aAAa,EAClB,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,KAAI,SAAS,CAAC,YAAY,EACnD,YAAY,CACf,CAAC;AAEF,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;AAE7F,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAD1C,wBAAA,gBAAgB,GACf,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAyC,CAC5C,CAAA,CAAA,EAAA,EAAA,WAAW,EAAE,WAAW,EACxB,KAAK,EAAE,KAAK,EACZ,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,EAC1D,YAAY,EAAE,YAAY,CAAC,QAAQ,EACtC,CAAA,CAAA,CAAA;wBAEK,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;AAC1E,wBAAA,IAAI,OAAO,EAAE;4BACT,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mCAAmC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7E,4BAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,qCAAsC,GAAA,OAAO,CAAC,aAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1G,4BAAA,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;AACtC,yBAAA;;wBAGD,IAAI,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE;AACvD,4BAAA,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;AACjE,4BAAA,IAAI,eAAe,EAAE;AACjB,gCAAA,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC;AAChD,6BAAA;AACJ,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,CAAA;;;;AAC3B,KAAA,CAAA;IACL,OAAC,yBAAA,CAAA;AAAD,CA/QA,CAAwD,qBAAqB,CA+Q5E;;;;"}