/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __extends, __awaiter, __generator, __assign } from '../_virtual/_tslib.js';
import { StandardInteractionClient } from './StandardInteractionClient.js';
import { PerformanceEvents, AuthError, RefreshTokenClient } from '@azure/msal-common';
import { ApiId } from '../utils/BrowserConstants.js';
import { BrowserAuthError } from '../error/BrowserAuthError.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var SilentRefreshClient = /** @class */ (function (_super) {
    __extends(SilentRefreshClient, _super);
    function SilentRefreshClient() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * Exchanges the refresh token for new tokens
     * @param request
     */
    SilentRefreshClient.prototype.acquireToken = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var silentRequest, _a, acquireTokenMeasurement, serverTelemetryManager, refreshTokenClient;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentRefreshClientAcquireToken, request.correlationId);
                        this.performanceClient.setPreQueueTime(PerformanceEvents.InitializeBaseRequest, request.correlationId);
                        _a = [__assign({}, request)];
                        return [4 /*yield*/, this.initializeBaseRequest(request, request.account)];
                    case 1:
                        silentRequest = __assign.apply(void 0, _a.concat([_b.sent()]));
                        acquireTokenMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SilentRefreshClientAcquireToken, silentRequest.correlationId);
                        serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenSilent_silentFlow);
                        return [4 /*yield*/, this.createRefreshTokenClient(serverTelemetryManager, silentRequest.authority, silentRequest.azureCloudOptions)];
                    case 2:
                        refreshTokenClient = _b.sent();
                        this.logger.verbose("Refresh token client created");
                        // Send request to renew token. Auth module will throw errors if token cannot be renewed.
                        this.performanceClient.setPreQueueTime(PerformanceEvents.RefreshTokenClientAcquireTokenByRefreshToken, request.correlationId);
                        return [2 /*return*/, refreshTokenClient.acquireTokenByRefreshToken(silentRequest)
                                .then(function (result) {
                                acquireTokenMeasurement.endMeasurement({
                                    success: true,
                                    fromCache: result.fromCache,
                                    requestId: result.requestId
                                });
                                return result;
                            })
                                .catch(function (e) {
                                if (e instanceof AuthError) {
                                    e.setCorrelationId(_this.correlationId);
                                }
                                serverTelemetryManager.cacheFailedRequest(e);
                                acquireTokenMeasurement.endMeasurement({
                                    errorCode: e.errorCode,
                                    subErrorCode: e.subError,
                                    success: false
                                });
                                throw e;
                            })];
                }
            });
        });
    };
    /**
     * Currently Unsupported
     */
    SilentRefreshClient.prototype.logout = function () {
        // Synchronous so we must reject
        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());
    };
    /**
     * Creates a Refresh Client with the given authority, or the default authority.
     * @param serverTelemetryManager
     * @param authorityUrl
     */
    SilentRefreshClient.prototype.createRefreshTokenClient = function (serverTelemetryManager, authorityUrl, azureCloudOptions) {
        return __awaiter(this, void 0, void 0, function () {
            var clientConfig;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Create auth module.
                        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, this.correlationId);
                        return [4 /*yield*/, this.getClientConfiguration(serverTelemetryManager, authorityUrl, azureCloudOptions)];
                    case 1:
                        clientConfig = _a.sent();
                        return [2 /*return*/, new RefreshTokenClient(clientConfig, this.performanceClient)];
                }
            });
        });
    };
    return SilentRefreshClient;
}(StandardInteractionClient));

export { SilentRefreshClient };
//# sourceMappingURL=SilentRefreshClient.js.map
