{"version": 3, "file": "HybridSpaAuthorizationCodeClient.js", "sources": ["../../src/interaction_client/HybridSpaAuthorizationCodeClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthorizationCodeClient, ClientConfiguration } from \"@azure/msal-common\";\r\n\r\nexport class HybridSpaAuthorizationCodeClient extends AuthorizationCodeClient {\r\n    constructor(config: ClientConfiguration) {\r\n        super(config);\r\n        this.includeRedirectUri = false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH,IAAA,gCAAA,kBAAA,UAAA,MAAA,EAAA;IAAsD,SAAuB,CAAA,gCAAA,EAAA,MAAA,CAAA,CAAA;AACzE,IAAA,SAAA,gCAAA,CAAY,MAA2B,EAAA;QAAvC,IACI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,MAAM,CAAC,IAEhB,IAAA,CAAA;AADG,QAAA,KAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;;KACnC;IACL,OAAC,gCAAA,CAAA;AAAD,CALA,CAAsD,uBAAuB,CAK5E;;;;"}