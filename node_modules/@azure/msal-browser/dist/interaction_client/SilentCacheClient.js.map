{"version": 3, "file": "SilentCacheClient.js", "sources": ["../../src/interaction_client/SilentCacheClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { StandardInteractionClient } from \"./StandardInteractionClient\";\r\nimport { CommonSilentFlowRequest, AuthenticationResult, SilentFlowClient, ServerTelemetryManager, AccountInfo, AzureCloudOptions, PerformanceEvents, AuthError } from \"@azure/msal-common\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { ApiId } from \"../utils/BrowserConstants\";\r\nimport { BrowserAuthError, BrowserAuthErrorMessage } from \"../error/BrowserAuthError\";\r\n\r\nexport class SilentCacheClient extends StandardInteractionClient {\r\n    /**\r\n     * Returns unexpired tokens from the cache, if available\r\n     * @param silentRequest\r\n     */\r\n    async acquireToken(silentRequest: CommonSilentFlowRequest): Promise<AuthenticationResult> {\r\n        const acquireTokenMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SilentCacheClientAcquireToken, silentRequest.correlationId);\r\n        // Telemetry manager only used to increment cacheHits here\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenSilent_silentFlow);\r\n\r\n        const silentAuthClient = await this.createSilentFlowClient(serverTelemetryManager, silentRequest.authority, silentRequest.azureCloudOptions);\r\n        this.logger.verbose(\"Silent auth client created\");\r\n\r\n        try {\r\n            const cachedToken = await silentAuthClient.acquireCachedToken(silentRequest);\r\n\r\n            acquireTokenMeasurement.endMeasurement({\r\n                success: true,\r\n                fromCache: true\r\n            });\r\n            return cachedToken;\r\n        } catch (error) {\r\n            if (error instanceof BrowserAuthError && error.errorCode === BrowserAuthErrorMessage.signingKeyNotFoundInStorage.code) {\r\n                this.logger.verbose(\"Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair.\");\r\n            }\r\n            acquireTokenMeasurement.endMeasurement({\r\n                errorCode: error instanceof AuthError && error.errorCode || undefined,\r\n                subErrorCode: error instanceof AuthError && error.subError || undefined,\r\n                success: false\r\n            });\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Currently Unsupported\r\n     */\r\n    logout(): Promise<void> {\r\n        // Synchronous so we must reject\r\n        return Promise.reject(BrowserAuthError.createSilentLogoutUnsupportedError());\r\n    }\r\n\r\n    /**\r\n     * Creates an Silent Flow Client with the given authority, or the default authority.\r\n     * @param serverTelemetryManager\r\n     * @param authorityUrl\r\n     */\r\n    protected async createSilentFlowClient(serverTelemetryManager: ServerTelemetryManager, authorityUrl?: string, azureCloudOptions?: AzureCloudOptions): Promise<SilentFlowClient> {\r\n        // Create auth module.\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.StandardInteractionClientGetClientConfiguration, this.correlationId);\r\n        const clientConfig = await this.getClientConfiguration(serverTelemetryManager, authorityUrl, azureCloudOptions);\r\n        return new SilentFlowClient(clientConfig, this.performanceClient);\r\n    }\r\n\r\n    async initializeSilentRequest(request: SilentRequest, account: AccountInfo): Promise<CommonSilentFlowRequest> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.InitializeSilentRequest, this.correlationId);\r\n\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.InitializeBaseRequest, this.correlationId);\r\n        return {\r\n            ...request,\r\n            ...await this.initializeBaseRequest(request, account),\r\n            account: account,\r\n            forceRefresh: request.forceRefresh || false\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAQH,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;IAAuC,SAAyB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;AAAhE,IAAA,SAAA,iBAAA,GAAA;;KAiEC;AAhEG;;;AAGG;IACG,iBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,aAAsC,EAAA;;;;;;AAC/C,wBAAA,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;wBAEhJ,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;AAEjF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAA,CAAA;;AAAtI,wBAAA,gBAAgB,GAAG,EAAmH,CAAA,IAAA,EAAA,CAAA;AAC5I,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;;;;AAG1B,wBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA,CAAA;;AAAtE,wBAAA,WAAW,GAAG,EAAwD,CAAA,IAAA,EAAA,CAAA;wBAE5E,uBAAuB,CAAC,cAAc,CAAC;AACnC,4BAAA,OAAO,EAAE,IAAI;AACb,4BAAA,SAAS,EAAE,IAAI;AAClB,yBAAA,CAAC,CAAC;AACH,wBAAA,OAAA,CAAA,CAAA,aAAO,WAAW,CAAC,CAAA;;;AAEnB,wBAAA,IAAI,OAAK,YAAY,gBAAgB,IAAI,OAAK,CAAC,SAAS,KAAK,uBAAuB,CAAC,2BAA2B,CAAC,IAAI,EAAE;AACnH,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sHAAsH,CAAC,CAAC;AAC/I,yBAAA;wBACD,uBAAuB,CAAC,cAAc,CAAC;4BACnC,SAAS,EAAE,OAAK,YAAY,SAAS,IAAI,OAAK,CAAC,SAAS,IAAI,SAAS;4BACrE,YAAY,EAAE,OAAK,YAAY,SAAS,IAAI,OAAK,CAAC,QAAQ,IAAI,SAAS;AACvE,4BAAA,OAAO,EAAE,KAAK;AACjB,yBAAA,CAAC,CAAC;AACH,wBAAA,MAAM,OAAK,CAAC;;;;;AAEnB,KAAA,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;;QAEI,OAAO,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,EAAE,CAAC,CAAC;KAChF,CAAA;AAED;;;;AAIG;AACa,IAAA,iBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtC,UAAuC,sBAA8C,EAAE,YAAqB,EAAE,iBAAqC,EAAA;;;;;;;AAE/I,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,+CAA+C,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBACzG,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAA,CAAA;;AAAzG,wBAAA,YAAY,GAAG,EAA0F,CAAA,IAAA,EAAA,CAAA;wBAC/G,OAAO,CAAA,CAAA,aAAA,IAAI,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA;;;;AACrE,KAAA,CAAA;AAEK,IAAA,iBAAA,CAAA,SAAA,CAAA,uBAAuB,GAA7B,UAA8B,OAAsB,EAAE,OAAoB,EAAA;;;;;;AACtE,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAE1G,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;2CAE7F,OAAO,CAAA,CAAA,CAAA;wBACP,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA,CAAA;AAFzD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,CAAA,MAAA,CAAA,CAEO,EAAkD,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EACrD,OAAO,EAAE,OAAO,EAChB,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,KAAK,EAC7C,CAAA,CAAA,CAAA,CAAA;;;;AACL,KAAA,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAjEA,CAAuC,yBAAyB,CAiE/D;;;;"}