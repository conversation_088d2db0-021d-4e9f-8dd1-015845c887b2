{"version": 3, "file": "NativeInteractionClient.d.ts", "sourceRoot": "", "sources": ["../../src/interaction_client/NativeInteractionClient.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAe,SAAS,EAAa,aAAa,EAAiB,QAAQ,EAAmH,kBAAkB,EAAmJ,MAAM,oBAAoB,CAAC;AAC5a,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAC/D,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAC/D,OAAO,EAAE,oBAAoB,EAAE,MAAM,6CAA6C,CAAC;AACnF,OAAO,EAAyB,KAAK,EAAuC,MAAM,2BAA2B,CAAC;AAC9G,OAAO,EAA8B,kBAAkB,EAAE,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,uCAAuC,CAAC;AAE7E,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAE7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AAEpE,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,qBAAa,uBAAwB,SAAQ,qBAAqB;IAC9D,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;IACvB,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IACrD,SAAS,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IAC/C,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;gBAExC,MAAM,EAAE,oBAAoB,EAAE,cAAc,EAAE,mBAAmB,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,oBAAoB,EAAE,SAAS,EAAE,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,aAAa,CAAC,EAAE,MAAM;IAS9V;;;OAGG;IACG,YAAY,CAAC,OAAO,EAAE,YAAY,GAAC,aAAa,GAAC,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAqDvG;;;;;OAKG;IACH,OAAO,CAAC,wBAAwB;IAUhC;;;;;OAKG;cACa,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IA6B3H;;;OAGG;IACG,oBAAoB,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IA6BnE;;OAEG;IACG,qBAAqB,IAAI,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAyCnE;;;OAGG;IACH,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAKvB;;;;;OAKG;cACa,oBAAoB,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,kBAAkB,EAAE,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAiChJ;;;;OAIG;IACH,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAc,GAAG,SAAS;IAI/D;;;;;OAKG;IACH,SAAS,CAAC,2BAA2B,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,GAAG,MAAM;IAO9F;;;;;OAKG;IACH,cAAc,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,kBAAkB,GAAG,QAAQ;IAI/E;;;;OAIG;IACG,sBAAsB,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAoCpG;;;;;;;;;OASG;cACa,4BAA4B,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAoChO;;;OAGG;IACH,YAAY,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI;IAUhD;;;;;;;;;OASG;IACH,iBAAiB,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,IAAI;IA6CzM,SAAS,CAAC,8BAA8B,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,GAAG,IAAI;IA6B/E;;;OAGG;IACH,OAAO,CAAC,sBAAsB;IAe9B;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAY3B;;;;OAIG;IACH,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IASlD;;;OAGG;cACa,uBAAuB,CAAC,OAAO,EAAE,YAAY,GAAC,gBAAgB,GAAG,OAAO,CAAC,kBAAkB,CAAC;CAsF/G"}