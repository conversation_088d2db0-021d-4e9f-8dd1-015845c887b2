{"version": 3, "file": "NavigationClient.js", "sources": ["../../src/navigation/NavigationClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { INavigationClient } from \"./INavigationClient\";\r\nimport { NavigationOptions } from \"./NavigationOptions\";\r\n\r\nexport class NavigationClient implements INavigationClient {\r\n    /**\r\n     * Navigates to other pages within the same web application\r\n     * @param url \r\n     * @param options \r\n     */\r\n    navigateInternal(url: string, options: NavigationOptions): Promise<boolean>{\r\n        return NavigationClient.defaultNavigateWindow(url, options);\r\n    }\r\n\r\n    /**\r\n     * Navigates to other pages outside the web application i.e. the Identity Provider\r\n     * @param url \r\n     * @param options \r\n     */\r\n    navigateExternal(url: string, options: NavigationOptions): Promise<boolean> {\r\n        return NavigationClient.defaultNavigateWindow(url, options);\r\n    }\r\n\r\n    /**\r\n     * Default navigation implementation invoked by the internal and external functions\r\n     * @param url \r\n     * @param options \r\n     */\r\n    private static defaultNavigateWindow(url: string, options: NavigationOptions): Promise<boolean> {\r\n        if (options.noHistory) {\r\n            window.location.replace(url);\r\n        } else {\r\n            window.location.assign(url);\r\n        }\r\n\r\n        return new Promise((resolve) => {\r\n            setTimeout(() => {\r\n                resolve(true);\r\n            }, options.timeout);\r\n        });\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAKH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KAqCC;AApCG;;;;AAIG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,GAAW,EAAE,OAA0B,EAAA;QACpD,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KAC/D,CAAA;AAED;;;;AAIG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,GAAW,EAAE,OAA0B,EAAA;QACpD,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KAC/D,CAAA;AAED;;;;AAIG;AACY,IAAA,gBAAA,CAAA,qBAAqB,GAApC,UAAqC,GAAW,EAAE,OAA0B,EAAA;QACxE,IAAI,OAAO,CAAC,SAAS,EAAE;AACnB,YAAA,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAA;AACvB,YAAA,UAAU,CAAC,YAAA;gBACP,OAAO,CAAC,IAAI,CAAC,CAAC;AAClB,aAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACxB,SAAC,CAAC,CAAC;KACN,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}