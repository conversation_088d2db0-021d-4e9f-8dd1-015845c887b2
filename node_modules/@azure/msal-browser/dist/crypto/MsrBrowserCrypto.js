/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __spread, __awaiter, __generator } from '../_virtual/_tslib.js';
import { KEY_FORMAT_JWK } from '../utils/BrowserConstants.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var MsrBrowserCrypto = /** @class */ (function () {
    function MsrBrowserCrypto() {
    }
    MsrBrowserCrypto.prototype.initPrng = function (entropy) {
        // Turn into array, as initPrng seems to not always like Uint8Array (even though it should support both)
        return window.msrCrypto.initPrng(__spread(entropy));
    };
    MsrBrowserCrypto.prototype.getRandomValues = function (dataBuffer) {
        return window.msrCrypto.getRandomValues(dataBuffer);
    };
    MsrBrowserCrypto.prototype.generateKey = function (algorithm, extractable, keyUsages) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.msrCrypto.subtle.generateKey(algorithm, extractable, keyUsages)];
            });
        });
    };
    MsrBrowserCrypto.prototype.exportKey = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.msrCrypto.subtle.exportKey(KEY_FORMAT_JWK, key)];
            });
        });
    };
    MsrBrowserCrypto.prototype.importKey = function (keyData, algorithm, extractable, keyUsages) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.msrCrypto.subtle.importKey(KEY_FORMAT_JWK, keyData, algorithm, extractable, keyUsages)];
            });
        });
    };
    MsrBrowserCrypto.prototype.sign = function (algorithm, key, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.msrCrypto.subtle.sign(algorithm, key, data)];
            });
        });
    };
    MsrBrowserCrypto.prototype.digest = function (algorithm, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.msrCrypto.subtle.digest(algorithm, data)];
            });
        });
    };
    return MsrBrowserCrypto;
}());

export { MsrBrowserCrypto };
//# sourceMappingURL=MsrBrowserCrypto.js.map
