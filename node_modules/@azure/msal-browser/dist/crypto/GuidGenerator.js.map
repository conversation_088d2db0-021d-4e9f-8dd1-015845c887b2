{"version": 3, "file": "GuidGenerator.js", "sources": ["../../src/crypto/GuidGenerator.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants , IGuidGenerator } from \"@azure/msal-common\";\r\nimport { MathUtils } from \"../utils/MathUtils\";\r\nimport { BrowserCrypto } from \"./BrowserCrypto\";\r\nexport class GuidGenerator implements IGuidGenerator {\r\n\r\n    // browser crypto object used to generate random values\r\n    private cryptoObj: BrowserCrypto;\r\n\r\n    constructor(cryptoObj: BrowserCrypto) {\r\n        this.cryptoObj = cryptoObj;\r\n    }\r\n\r\n    /*\r\n     * RFC4122: The version 4 UUID is meant for generating UUIDs from truly-random or\r\n     * pseudo-random numbers.\r\n     * The algorithm is as follows:\r\n     *     Set the two most significant bits (bits 6 and 7) of the\r\n     *        clock_seq_hi_and_reserved to zero and one, respectively.\r\n     *     Set the four most significant bits (bits 12 through 15) of the\r\n     *        time_hi_and_version field to the 4-bit version number from\r\n     *        Section 4.1.3. Version4\r\n     *     Set all the other bits to randomly (or pseudo-randomly) chosen\r\n     *     values.\r\n     * UUID                   = time-low \"-\" time-mid \"-\"time-high-and-version \"-\"clock-seq-reserved and low(2hexOctet)\"-\" node\r\n     * time-low               = 4hexOctet\r\n     * time-mid               = 2hexOctet\r\n     * time-high-and-version  = 2hexOctet\r\n     * clock-seq-and-reserved = hexOctet:\r\n     * clock-seq-low          = hexOctet\r\n     * node                   = 6hexOctet\r\n     * Format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\r\n     * y could be 1000, 1001, 1010, 1011 since most significant two bits needs to be 10\r\n     * y values are 8, 9, A, B\r\n     */\r\n    generateGuid(): string {\r\n        try {\r\n            const buffer: Uint8Array = new Uint8Array(16);\r\n            this.cryptoObj.getRandomValues(buffer);\r\n\r\n            // buffer[6] and buffer[7] represents the time_hi_and_version field. We will set the four most significant bits (4 through 7) of buffer[6] to represent decimal number 4 (UUID version number).\r\n            buffer[6] |= 0x40; // buffer[6] | 01000000 will set the 6 bit to 1.\r\n            buffer[6] &= 0x4f; // buffer[6] & 01001111 will set the 4, 5, and 7 bit to 0 such that bits 4-7 == 0100 = \"4\".\r\n\r\n            // buffer[8] represents the clock_seq_hi_and_reserved field. We will set the two most significant bits (6 and 7) of the clock_seq_hi_and_reserved to zero and one, respectively.\r\n            buffer[8] |= 0x80; // buffer[8] | 10000000 will set the 7 bit to 1.\r\n            buffer[8] &= 0xbf; // buffer[8] & 10111111 will set the 6 bit to 0.\r\n\r\n            return MathUtils.decimalToHex(buffer[0]) + MathUtils.decimalToHex(buffer[1])\r\n                + MathUtils.decimalToHex(buffer[2]) + MathUtils.decimalToHex(buffer[3])\r\n                + \"-\" + MathUtils.decimalToHex(buffer[4]) + MathUtils.decimalToHex(buffer[5])\r\n                + \"-\" + MathUtils.decimalToHex(buffer[6]) + MathUtils.decimalToHex(buffer[7])\r\n                + \"-\" + MathUtils.decimalToHex(buffer[8]) + MathUtils.decimalToHex(buffer[9])\r\n                + \"-\" + MathUtils.decimalToHex(buffer[10]) + MathUtils.decimalToHex(buffer[11])\r\n                + MathUtils.decimalToHex(buffer[12]) + MathUtils.decimalToHex(buffer[13])\r\n                + MathUtils.decimalToHex(buffer[14]) + MathUtils.decimalToHex(buffer[15]);\r\n        }\r\n        catch (err) {\r\n            const guidHolder: string = \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\";\r\n            const hex: string = \"0123456789abcdef\";\r\n            let r: number = 0;\r\n            let guidResponse: string = Constants.EMPTY_STRING;\r\n            for (let i: number = 0; i < 36; i++) {\r\n                if (guidHolder[i] !== \"-\" && guidHolder[i] !== \"4\") {\r\n                    // each x and y needs to be random\r\n                    r = Math.random() * 16 | 0;\r\n                }\r\n                if (guidHolder[i] === \"x\") {\r\n                    guidResponse += hex[r];\r\n                } else if (guidHolder[i] === \"y\") {\r\n                    // clock-seq-and-reserved first hex is filtered and remaining hex values are random\r\n                    r &= 0x3; // bit and with 0011 to set pos 2 to zero ?0??\r\n                    r |= 0x8; // set pos 3 to 1 as 1???\r\n                    guidResponse += hex[r];\r\n                } else {\r\n                    guidResponse += guidHolder[i];\r\n                }\r\n            }\r\n            return guidResponse;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * verifies if a string is  GUID\r\n     * @param guid\r\n     */\r\n    isGuid(guid: string): boolean {\r\n        const regexGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n        return regexGuid.test(guid);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAKH,IAAA,aAAA,kBAAA,YAAA;AAKI,IAAA,SAAA,aAAA,CAAY,SAAwB,EAAA;AAChC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC9B;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACI,IAAI;AACA,YAAA,IAAM,MAAM,GAAe,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;;AAGvC,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAClB,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;;AAGlB,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAClB,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAElB,YAAA,OAAO,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtE,kBAAA,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACrE,kBAAA,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3E,kBAAA,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3E,kBAAA,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3E,kBAAA,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC7E,kBAAA,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACvE,kBAAA,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,SAAA;AACD,QAAA,OAAO,GAAG,EAAE;YACR,IAAM,UAAU,GAAW,sCAAsC,CAAC;YAClE,IAAM,GAAG,GAAW,kBAAkB,CAAC;YACvC,IAAI,CAAC,GAAW,CAAC,CAAC;AAClB,YAAA,IAAI,YAAY,GAAW,SAAS,CAAC,YAAY,CAAC;YAClD,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACjC,gBAAA,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;oBAEhD,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9B,iBAAA;AACD,gBAAA,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACvB,oBAAA,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1B,iBAAA;AAAM,qBAAA,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;AAE9B,oBAAA,CAAC,IAAI,GAAG,CAAC;AACT,oBAAA,CAAC,IAAI,GAAG,CAAC;AACT,oBAAA,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1B,iBAAA;AAAM,qBAAA;AACH,oBAAA,YAAY,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AACjC,iBAAA;AACJ,aAAA;AACD,YAAA,OAAO,YAAY,CAAC;AACvB,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,aAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,IAAY,EAAA;QACf,IAAM,SAAS,GAAG,4EAA4E,CAAC;AAC/F,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC/B,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA;;;;"}