{"version": 3, "file": "MsrBrowserCrypto.js", "sources": ["../../src/crypto/MsrBrowserCrypto.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { KEY_FORMAT_JWK } from \"../utils/BrowserConstants\";\r\nimport { ISubtleCrypto } from \"./ISubtleCrypto\";\r\n\r\ndeclare global {\r\n    interface Window {\r\n        msrCrypto: Crypto & {\r\n            initPrng: (entropy: Uint8Array | number[]) => void\r\n        }\r\n    }\r\n}\r\n\r\nexport class MsrBrowserCrypto implements ISubtleCrypto {\r\n    initPrng(entropy : Uint8Array): void {\r\n        // Turn into array, as initPrng seems to not always like Uint8Array (even though it should support both)\r\n        return window.msrCrypto.initPrng([...entropy]);\r\n    }\r\n\r\n    getRandomValues(dataBuffer: Uint8Array): Uint8Array {\r\n        return window.msrCrypto.getRandomValues(dataBuffer);\r\n    }\r\n\r\n    async generateKey(algorithm: RsaHashedKeyGenParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKeyPair> {\r\n        return window.msrCrypto.subtle.generateKey(algorithm, extractable, keyUsages) as Promise<CryptoKeyPair>;\r\n    }\r\n\r\n    async exportKey(key: CryptoKey): Promise<JsonWebKey> {\r\n        return window.msrCrypto.subtle.exportKey(KEY_FORMAT_JWK, key) as Promise<JsonWebKey> as Promise<JsonWebKey>;\r\n    }\r\n\r\n    async importKey(keyData: JsonWebKey, algorithm: RsaHashedImportParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey> {\r\n        return window.msrCrypto.subtle.importKey(KEY_FORMAT_JWK, keyData, algorithm, extractable, keyUsages) as Promise<CryptoKey>;\r\n    }\r\n\r\n    async sign(algorithm: AlgorithmIdentifier, key: CryptoKey, data: ArrayBuffer): Promise<ArrayBuffer> {\r\n        return window.msrCrypto.subtle.sign(algorithm, key, data) as Promise<ArrayBuffer>;\r\n    }\r\n\r\n    async digest(algorithm: AlgorithmIdentifier, data: Uint8Array): Promise<ArrayBuffer> {\r\n        return window.msrCrypto.subtle.digest(algorithm, data) as Promise<ArrayBuffer>; \r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAaH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KA6BC;IA5BG,gBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,OAAoB,EAAA;;QAEzB,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAK,QAAA,CAAA,OAAO,EAAE,CAAC;KAClD,CAAA;IAED,gBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,UAAsB,EAAA;QAClC,OAAO,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KACvD,CAAA;AAEK,IAAA,gBAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,UAAkB,SAAgC,EAAE,WAAoB,EAAE,SAAqB,EAAA;;;AAC3F,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAA2B,CAAC,CAAA;;;AAC3G,KAAA,CAAA;IAEK,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,GAAc,EAAA;;;AAC1B,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,GAAG,CAA+C,CAAC,CAAA;;;AAC/G,KAAA,CAAA;IAEK,gBAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,OAAmB,EAAE,SAAgC,EAAE,WAAoB,EAAE,SAAqB,EAAA;;;AAC9G,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAuB,CAAC,CAAA;;;AAC9H,KAAA,CAAA;AAEK,IAAA,gBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,UAAW,SAA8B,EAAE,GAAc,EAAE,IAAiB,EAAA;;;AACxE,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAyB,CAAC,CAAA;;;AACrF,KAAA,CAAA;AAEK,IAAA,gBAAA,CAAA,SAAA,CAAA,MAAM,GAAZ,UAAa,SAA8B,EAAE,IAAgB,EAAA;;;AACzD,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAyB,CAAC,CAAA;;;AAClF,KAAA,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}