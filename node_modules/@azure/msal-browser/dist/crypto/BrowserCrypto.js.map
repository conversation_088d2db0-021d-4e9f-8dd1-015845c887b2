{"version": 3, "file": "BrowserCrypto.js", "sources": ["../../src/crypto/BrowserCrypto.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { BrowserStringUtils } from \"../utils/BrowserStringUtils\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { ISubtleCrypto } from \"./ISubtleCrypto\";\r\nimport { ModernBrowserCrypto } from \"./ModernBrowserCrypto\";\r\nimport { MsrBrowserCrypto } from \"./MsrBrowserCrypto\";\r\nimport { MsBrowserCrypto } from \"./MsBrowserCrypto\";\r\nimport { Logger } from \"@azure/msal-common\";\r\nimport { BrowserConfigurationAuthError } from \"../error/BrowserConfigurationAuthError\";\r\nimport { CryptoOptions } from \"../config/Configuration\";\r\n/**\r\n * See here for more info on RsaHashedKeyGenParams: https://developer.mozilla.org/en-US/docs/Web/API/RsaHashedKeyGenParams\r\n */\r\n// RSA KeyGen Algorithm\r\nconst PKCS1_V15_KEYGEN_ALG = \"RSASSA-PKCS1-v1_5\";\r\n// SHA-256 hashing algorithm\r\nconst S256_HASH_ALG = \"SHA-256\";\r\n// MOD length for PoP tokens\r\nconst MODULUS_LENGTH = 2048;\r\n// Public Exponent\r\nconst PUBLIC_EXPONENT: Uint8Array = new Uint8Array([0x01, 0x00, 0x01]);\r\n\r\n/**\r\n * This class implements functions used by the browser library to perform cryptography operations such as\r\n * hashing and encoding. It also has helper functions to validate the availability of specific APIs.\r\n */\r\nexport class BrowserCrypto {\r\n\r\n    private keygenAlgorithmOptions: RsaHashedKeyGenParams;\r\n    private subtleCrypto: ISubtleCrypto;\r\n    private logger: Logger;\r\n    private cryptoOptions?: CryptoOptions;\r\n\r\n    constructor(logger: Logger, cryptoOptions?: CryptoOptions) {\r\n        this.logger = logger;\r\n        this.cryptoOptions = cryptoOptions;\r\n\r\n        if (this.hasBrowserCrypto()) {\r\n            // Use standard modern web crypto if available\r\n            this.logger.verbose(\"BrowserCrypto: modern crypto interface available\");\r\n            this.subtleCrypto = new ModernBrowserCrypto();\r\n        } else if (this.hasIECrypto()) {\r\n            // For IE11, use msCrypto interface\r\n            this.logger.verbose(\"BrowserCrypto: MS crypto interface available\");\r\n            this.subtleCrypto = new MsBrowserCrypto();\r\n        } else if (this.hasMsrCrypto() && this.cryptoOptions?.useMsrCrypto) {\r\n            // For other browsers, use MSR Crypto if found\r\n            this.logger.verbose(\"BrowserCrypto: MSR crypto interface available\");\r\n            this.subtleCrypto = new MsrBrowserCrypto();\r\n        } else {\r\n            if (this.hasMsrCrypto()) {\r\n                this.logger.info(\"BrowserCrypto: MSR Crypto interface available but system.cryptoOptions.useMsrCrypto not enabled\");\r\n            }\r\n            this.logger.error(\"BrowserCrypto: No crypto interfaces available.\");\r\n            throw BrowserAuthError.createCryptoNotAvailableError(\"Browser crypto, msCrypto, or msrCrypto interfaces not available.\");\r\n        }\r\n\r\n        // Mainly needed for MSR Crypto: https://github.com/microsoft/MSR-JavaScript-Crypto#random-number-generator-prng\r\n        if (this.subtleCrypto.initPrng) {\r\n            this.logger.verbose(\"BrowserCrypto: Interface requires entropy\");\r\n\r\n            if (!this.cryptoOptions?.entropy) {\r\n                this.logger.error(\"BrowserCrypto: Interface requires entropy but none provided.\");\r\n                throw BrowserConfigurationAuthError.createEntropyNotProvided();\r\n            }\r\n\r\n            this.logger.verbose(\"BrowserCrypto: Entropy provided\");\r\n            this.subtleCrypto.initPrng(this.cryptoOptions.entropy);\r\n        }\r\n\r\n        this.keygenAlgorithmOptions = {\r\n            name: PKCS1_V15_KEYGEN_ALG,\r\n            hash: S256_HASH_ALG,\r\n            modulusLength: MODULUS_LENGTH,\r\n            publicExponent: PUBLIC_EXPONENT\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Check whether IE crypto or other browser cryptography is available.\r\n     */\r\n    private hasIECrypto(): boolean {\r\n        return \"msCrypto\" in window;\r\n    }\r\n\r\n    /**\r\n     * Check whether browser crypto is available.\r\n     */\r\n    private hasBrowserCrypto(): boolean {\r\n        return \"crypto\" in window;\r\n    }\r\n\r\n    /**\r\n     * Check whether MSR crypto polyfill is available\r\n     */\r\n    private hasMsrCrypto(): boolean {\r\n        return \"msrCrypto\" in window;\r\n    }\r\n\r\n    /**\r\n     * Returns a sha-256 hash of the given dataString as an ArrayBuffer.\r\n     * @param dataString \r\n     */\r\n    async sha256Digest(dataString: string): Promise<ArrayBuffer> {\r\n        const data = BrowserStringUtils.stringToUtf8Arr(dataString);\r\n        // MSR Crypto wants object with name property, instead of string\r\n        return this.subtleCrypto.digest({ name: S256_HASH_ALG }, data);\r\n    }\r\n\r\n    /**\r\n     * Populates buffer with cryptographically random values.\r\n     * @param dataBuffer \r\n     */\r\n    getRandomValues(dataBuffer: Uint8Array): Uint8Array {\r\n        return this.subtleCrypto.getRandomValues(dataBuffer);\r\n    }\r\n\r\n    /**\r\n     * Generates a keypair based on current keygen algorithm config.\r\n     * @param extractable \r\n     * @param usages \r\n     */\r\n    async generateKeyPair(extractable: boolean, usages: Array<KeyUsage>): Promise<CryptoKeyPair> {\r\n        return this.subtleCrypto.generateKey(this.keygenAlgorithmOptions, extractable, usages);\r\n    }\r\n\r\n    /**\r\n     * Export key as Json Web Key (JWK)\r\n     * @param key \r\n     * @param format \r\n     */\r\n    async exportJwk(key: CryptoKey): Promise<JsonWebKey> {\r\n        return this.subtleCrypto.exportKey(key);\r\n    }\r\n\r\n    /**\r\n     * Imports key as Json Web Key (JWK), can set extractable and usages.\r\n     * @param key \r\n     * @param format \r\n     * @param extractable \r\n     * @param usages \r\n     */\r\n    async importJwk(key: JsonWebKey, extractable: boolean, usages: Array<KeyUsage>): Promise<CryptoKey> {\r\n        return this.subtleCrypto.importKey(key, this.keygenAlgorithmOptions, extractable, usages);\r\n    }\r\n\r\n    /**\r\n     * Signs given data with given key\r\n     * @param key \r\n     * @param data \r\n     */\r\n    async sign(key: CryptoKey, data: ArrayBuffer): Promise<ArrayBuffer> {\r\n        return this.subtleCrypto.sign(this.keygenAlgorithmOptions, key, data);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAWH;;AAEG;AACH;AACA,IAAM,oBAAoB,GAAG,mBAAmB,CAAC;AACjD;AACA,IAAM,aAAa,GAAG,SAAS,CAAC;AAChC;AACA,IAAM,cAAc,GAAG,IAAI,CAAC;AAC5B;AACA,IAAM,eAAe,GAAe,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAEvE;;;AAGG;AACH,IAAA,aAAA,kBAAA,YAAA;IAOI,SAAY,aAAA,CAAA,MAAc,EAAE,aAA6B,EAAA;;AACrD,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAEnC,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;;AAEzB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,mBAAmB,EAAE,CAAC;AACjD,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;;AAE3B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;AACpE,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;AAC7C,SAAA;aAAM,IAAI,IAAI,CAAC,YAAY,EAAE,KAAA,CAAA,EAAA,GAAI,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAA,EAAE;;AAEhE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;AACrE,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAC9C,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AACrB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAC;AACvH,aAAA;AACD,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACpE,YAAA,MAAM,gBAAgB,CAAC,6BAA6B,CAAC,kEAAkE,CAAC,CAAC;AAC5H,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC5B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;AAEjE,YAAA,IAAI,QAAC,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAA,EAAE;AAC9B,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAClF,gBAAA,MAAM,6BAA6B,CAAC,wBAAwB,EAAE,CAAC;AAClE,aAAA;AAED,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACvD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1D,SAAA;QAED,IAAI,CAAC,sBAAsB,GAAG;AAC1B,YAAA,IAAI,EAAE,oBAAoB;AAC1B,YAAA,IAAI,EAAE,aAAa;AACnB,YAAA,aAAa,EAAE,cAAc;AAC7B,YAAA,cAAc,EAAE,eAAe;SAClC,CAAC;KACL;AAED;;AAEG;AACK,IAAA,aAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;QACI,OAAO,UAAU,IAAI,MAAM,CAAC;KAC/B,CAAA;AAED;;AAEG;AACK,IAAA,aAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,YAAA;QACI,OAAO,QAAQ,IAAI,MAAM,CAAC;KAC7B,CAAA;AAED;;AAEG;AACK,IAAA,aAAA,CAAA,SAAA,CAAA,YAAY,GAApB,YAAA;QACI,OAAO,WAAW,IAAI,MAAM,CAAC;KAChC,CAAA;AAED;;;AAGG;IACG,aAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,UAAkB,EAAA;;;;AAC3B,gBAAA,IAAI,GAAG,kBAAkB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;;AAE5D,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;;;AAClE,KAAA,CAAA;AAED;;;AAGG;IACH,aAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,UAAsB,EAAA;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KACxD,CAAA;AAED;;;;AAIG;AACG,IAAA,aAAA,CAAA,SAAA,CAAA,eAAe,GAArB,UAAsB,WAAoB,EAAE,MAAuB,EAAA;;;AAC/D,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAA;;;AAC1F,KAAA,CAAA;AAED;;;;AAIG;IACG,aAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,GAAc,EAAA;;;gBAC1B,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA;;;AAC3C,KAAA,CAAA;AAED;;;;;;AAMG;AACG,IAAA,aAAA,CAAA,SAAA,CAAA,SAAS,GAAf,UAAgB,GAAe,EAAE,WAAoB,EAAE,MAAuB,EAAA;;;AAC1E,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAA;;;AAC7F,KAAA,CAAA;AAED;;;;AAIG;AACG,IAAA,aAAA,CAAA,SAAA,CAAA,IAAI,GAAV,UAAW,GAAc,EAAE,IAAiB,EAAA;;;AACxC,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAA;;;AACzE,KAAA,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA;;;;"}