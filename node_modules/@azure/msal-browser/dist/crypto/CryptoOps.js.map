{"version": 3, "file": "CryptoOps.js", "sources": ["../../src/crypto/CryptoOps.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ICrypto, IPerformanceClient, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, PerformanceEvents, PkceCodes, SignedHttpRequest, SignedHttpRequestParameters } from \"@azure/msal-common\";\r\nimport { GuidGenerator } from \"./GuidGenerator\";\r\nimport { Base64Encode } from \"../encode/Base64Encode\";\r\nimport { Base64Decode } from \"../encode/Base64Decode\";\r\nimport { PkceGenerator } from \"./PkceGenerator\";\r\nimport { BrowserCrypto } from \"./BrowserCrypto\";\r\nimport { BrowserStringUtils } from \"../utils/BrowserStringUtils\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { CryptoKeyStore } from \"../cache/CryptoKeyStore\";\r\nimport { CryptoOptions } from \"../config/Configuration\";\r\n\r\nexport type CachedKeyPair = {\r\n    publicKey: CryptoKey,\r\n    privateKey: CryptoKey,\r\n    requestMethod?: string,\r\n    requestUri?: string\r\n};\r\n\r\n/**\r\n * This class implements MSAL's crypto interface, which allows it to perform base64 encoding and decoding, generating cryptographically random GUIDs and \r\n * implementing Proof Key for Code Exchange specs for the OAuth Authorization Code Flow using PKCE (rfc here: https://tools.ietf.org/html/rfc7636).\r\n */\r\nexport class CryptoOps implements ICrypto {\r\n\r\n    private browserCrypto: BrowserCrypto;\r\n    private guidGenerator: GuidGenerator;\r\n    private b64Encode: Base64Encode;\r\n    private b64Decode: Base64Decode;\r\n    private pkceGenerator: PkceGenerator;\r\n    private logger: Logger;\r\n\r\n    /**\r\n     * CryptoOps can be used in contexts outside a PCA instance,\r\n     * meaning there won't be a performance manager available.\r\n     */\r\n    private performanceClient: IPerformanceClient | undefined;\r\n\r\n    private static POP_KEY_USAGES: Array<KeyUsage> = [\"sign\", \"verify\"];\r\n    private static EXTRACTABLE: boolean = true;\r\n    private cache: CryptoKeyStore;\r\n\r\n    constructor(logger: Logger, performanceClient?: IPerformanceClient, cryptoConfig?: CryptoOptions) {\r\n        this.logger = logger;\r\n        // Browser crypto needs to be validated first before any other classes can be set.\r\n        this.browserCrypto = new BrowserCrypto(this.logger, cryptoConfig);\r\n        this.b64Encode = new Base64Encode();\r\n        this.b64Decode = new Base64Decode();\r\n        this.guidGenerator = new GuidGenerator(this.browserCrypto);\r\n        this.pkceGenerator = new PkceGenerator(this.browserCrypto);\r\n        this.cache = new CryptoKeyStore(this.logger);\r\n        this.performanceClient = performanceClient;\r\n    }\r\n\r\n    /**\r\n     * Creates a new random GUID - used to populate state and nonce.\r\n     * @returns string (GUID)\r\n     */\r\n    createNewGuid(): string {\r\n        return this.guidGenerator.generateGuid();\r\n    }\r\n\r\n    /**\r\n     * Encodes input string to base64.\r\n     * @param input \r\n     */\r\n    base64Encode(input: string): string {\r\n        return this.b64Encode.encode(input);\r\n    }    \r\n    \r\n    /**\r\n     * Decodes input string from base64.\r\n     * @param input \r\n     */\r\n    base64Decode(input: string): string {\r\n        return this.b64Decode.decode(input);\r\n    }\r\n\r\n    /**\r\n     * Generates PKCE codes used in Authorization Code Flow.\r\n     */\r\n    async generatePkceCodes(): Promise<PkceCodes> {\r\n        return this.pkceGenerator.generateCodes();\r\n    }\r\n\r\n    /**\r\n     * Generates a keypair, stores it and returns a thumbprint\r\n     * @param request\r\n     */\r\n    async getPublicKeyThumbprint(request: SignedHttpRequestParameters): Promise<string> {\r\n        const publicKeyThumbMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.CryptoOptsGetPublicKeyThumbprint, request.correlationId);\r\n\r\n        // Generate Keypair\r\n        const keyPair: CryptoKeyPair = await this.browserCrypto.generateKeyPair(CryptoOps.EXTRACTABLE, CryptoOps.POP_KEY_USAGES);\r\n\r\n        // Generate Thumbprint for Public Key\r\n        const publicKeyJwk: JsonWebKey = await this.browserCrypto.exportJwk(keyPair.publicKey);\r\n        \r\n        const pubKeyThumprintObj: JsonWebKey = {\r\n            e: publicKeyJwk.e,\r\n            kty: publicKeyJwk.kty,\r\n            n: publicKeyJwk.n\r\n        };\r\n        \r\n        const publicJwkString: string = BrowserStringUtils.getSortedObjectString(pubKeyThumprintObj);\r\n        const publicJwkHash = await this.hashString(publicJwkString);\r\n\r\n        // Generate Thumbprint for Private Key\r\n        const privateKeyJwk: JsonWebKey = await this.browserCrypto.exportJwk(keyPair.privateKey);\r\n        // Re-import private key to make it unextractable\r\n        const unextractablePrivateKey: CryptoKey = await this.browserCrypto.importJwk(privateKeyJwk, false, [\"sign\"]);\r\n\r\n        // Store Keypair data in keystore\r\n        await this.cache.asymmetricKeys.setItem(\r\n            publicJwkHash, \r\n            {\r\n                privateKey: unextractablePrivateKey,\r\n                publicKey: keyPair.publicKey,\r\n                requestMethod: request.resourceRequestMethod,\r\n                requestUri: request.resourceRequestUri\r\n            }\r\n        );\r\n\r\n        if (publicKeyThumbMeasurement) {\r\n            publicKeyThumbMeasurement.endMeasurement({\r\n                success: true\r\n            });\r\n        }\r\n\r\n        return publicJwkHash;\r\n    }\r\n\r\n    /**\r\n     * Removes cryptographic keypair from key store matching the keyId passed in\r\n     * @param kid \r\n     */\r\n    async removeTokenBindingKey(kid: string): Promise<boolean> {\r\n        await this.cache.asymmetricKeys.removeItem(kid);\r\n        const keyFound = await this.cache.asymmetricKeys.containsKey(kid);\r\n        return !keyFound;\r\n    }\r\n\r\n    /**\r\n     * Removes all cryptographic keys from IndexedDB storage\r\n     */\r\n    async clearKeystore(): Promise<boolean> {\r\n        return await this.cache.clear();\r\n    }\r\n\r\n    /**\r\n     * Signs the given object as a jwt payload with private key retrieved by given kid.\r\n     * @param payload \r\n     * @param kid \r\n     */\r\n    async signJwt(payload: SignedHttpRequest, kid: string, correlationId?: string): Promise<string> {\r\n        const signJwtMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.CryptoOptsSignJwt, correlationId);\r\n        const cachedKeyPair = await this.cache.asymmetricKeys.getItem(kid);\r\n        \r\n        if (!cachedKeyPair) {\r\n            throw BrowserAuthError.createSigningKeyNotFoundInStorageError(kid);\r\n        }\r\n\r\n        // Get public key as JWK\r\n        const publicKeyJwk = await this.browserCrypto.exportJwk(cachedKeyPair.publicKey);\r\n        const publicKeyJwkString = BrowserStringUtils.getSortedObjectString(publicKeyJwk);\r\n\r\n        // Base64URL encode public key thumbprint with keyId only: BASE64URL({ kid: \"FULL_PUBLIC_KEY_HASH\" })\r\n        const encodedKeyIdThumbprint = this.b64Encode.urlEncode(JSON.stringify({ kid: kid }));\r\n        \r\n        // Generate header\r\n        const shrHeader = JoseHeader.getShrHeaderString({ kid: encodedKeyIdThumbprint, alg: publicKeyJwk.alg });\r\n        const encodedShrHeader = this.b64Encode.urlEncode(shrHeader);\r\n\r\n        // Generate payload\r\n        payload.cnf = {\r\n            jwk: JSON.parse(publicKeyJwkString)\r\n        };\r\n        const encodedPayload = this.b64Encode.urlEncode(JSON.stringify(payload));\r\n\r\n        // Form token string\r\n        const tokenString = `${encodedShrHeader}.${encodedPayload}`;\r\n\r\n        // Sign token\r\n        const tokenBuffer = BrowserStringUtils.stringToArrayBuffer(tokenString);\r\n        const signatureBuffer = await this.browserCrypto.sign(cachedKeyPair.privateKey, tokenBuffer);\r\n        const encodedSignature = this.b64Encode.urlEncodeArr(new Uint8Array(signatureBuffer));\r\n\r\n        const signedJwt = `${tokenString}.${encodedSignature}`;\r\n\r\n        if (signJwtMeasurement) {\r\n            signJwtMeasurement.endMeasurement({\r\n                success: true\r\n            });\r\n        }\r\n\r\n        return signedJwt;\r\n    }\r\n\r\n    /**\r\n     * Returns the SHA-256 hash of an input string\r\n     * @param plainText\r\n     */\r\n    async hashString(plainText: string): Promise<string> {\r\n        const hashBuffer: ArrayBuffer = await this.browserCrypto.sha256Digest(plainText);\r\n        const hashBytes = new Uint8Array(hashBuffer);\r\n        return this.b64Encode.urlEncodeArr(hashBytes);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAoBH;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AAmBI,IAAA,SAAA,SAAA,CAAY,MAAc,EAAE,iBAAsC,EAAE,YAA4B,EAAA;AAC5F,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;AAErB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,EAAE,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,KAAK,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;AAGG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;AACI,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;KAC5C,CAAA;AAED;;;AAGG;IACH,SAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACvC,CAAA;AAED;;;AAGG;IACH,SAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACvC,CAAA;AAED;;AAEG;AACG,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAvB,YAAA;;;AACI,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,CAAA;;;AAC7C,KAAA,CAAA;AAED;;;AAGG;IACG,SAAsB,CAAA,SAAA,CAAA,sBAAA,GAA5B,UAA6B,OAAoC,EAAA;;;;;;;AACvD,wBAAA,yBAAyB,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAGvH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,cAAc,CAAC,CAAA,CAAA;;AAAlH,wBAAA,OAAO,GAAkB,EAAyF,CAAA,IAAA,EAAA,CAAA;wBAGvF,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAhF,wBAAA,YAAY,GAAe,EAAqD,CAAA,IAAA,EAAA,CAAA;AAEhF,wBAAA,kBAAkB,GAAe;4BACnC,CAAC,EAAE,YAAY,CAAC,CAAC;4BACjB,GAAG,EAAE,YAAY,CAAC,GAAG;4BACrB,CAAC,EAAE,YAAY,CAAC,CAAC;yBACpB,CAAC;AAEI,wBAAA,eAAe,GAAW,kBAAkB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;AACvE,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAA,CAAA;;AAAtD,wBAAA,aAAa,GAAG,EAAsC,CAAA,IAAA,EAAA,CAAA;wBAG1B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,CAAA;;AAAlF,wBAAA,aAAa,GAAe,EAAsD,CAAA,IAAA,EAAA,CAAA;AAE7C,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA,CAAA;;AAAvG,wBAAA,uBAAuB,GAAc,EAAkE,CAAA,IAAA,EAAA,CAAA;;wBAG7G,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CACnC,aAAa,EACb;AACI,gCAAA,UAAU,EAAE,uBAAuB;gCACnC,SAAS,EAAE,OAAO,CAAC,SAAS;gCAC5B,aAAa,EAAE,OAAO,CAAC,qBAAqB;gCAC5C,UAAU,EAAE,OAAO,CAAC,kBAAkB;AACzC,6BAAA,CACJ,CAAA,CAAA;;;AARD,wBAAA,EAAA,CAAA,IAAA,EAQC,CAAC;AAEF,wBAAA,IAAI,yBAAyB,EAAE;4BAC3B,yBAAyB,CAAC,cAAc,CAAC;AACrC,gCAAA,OAAO,EAAE,IAAI;AAChB,6BAAA,CAAC,CAAC;AACN,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,CAAA;;;;AACxB,KAAA,CAAA;AAED;;;AAGG;IACG,SAAqB,CAAA,SAAA,CAAA,qBAAA,GAA3B,UAA4B,GAAW,EAAA;;;;;4BACnC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAA;;AAA/C,wBAAA,EAAA,CAAA,IAAA,EAA+C,CAAC;wBAC/B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,CAAA;;AAA3D,wBAAA,QAAQ,GAAG,EAAgD,CAAA,IAAA,EAAA,CAAA;wBACjE,OAAO,CAAA,CAAA,aAAA,CAAC,QAAQ,CAAC,CAAA;;;;AACpB,KAAA,CAAA;AAED;;AAEG;AACG,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAnB,YAAA;;;;AACW,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA,CAAA;AAA/B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAwB,CAAC,CAAA;;;;AACnC,KAAA,CAAA;AAED;;;;AAIG;AACG,IAAA,SAAA,CAAA,SAAA,CAAA,OAAO,GAAb,UAAc,OAA0B,EAAE,GAAW,EAAE,aAAsB,EAAA;;;;;;;AACnE,wBAAA,kBAAkB,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;wBAClG,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,CAAA;;AAA5D,wBAAA,aAAa,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;wBAElE,IAAI,CAAC,aAAa,EAAE;AAChB,4BAAA,MAAM,gBAAgB,CAAC,sCAAsC,CAAC,GAAG,CAAC,CAAC;AACtE,yBAAA;wBAGoB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA,CAAA;;AAA1E,wBAAA,YAAY,GAAG,EAA2D,CAAA,IAAA,EAAA,CAAA;AAC1E,wBAAA,kBAAkB,GAAG,kBAAkB,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;AAG5E,wBAAA,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAGhF,wBAAA,SAAS,GAAG,UAAU,CAAC,kBAAkB,CAAC,EAAE,GAAG,EAAE,sBAAsB,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;wBAClG,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;;wBAG7D,OAAO,CAAC,GAAG,GAAG;AACV,4BAAA,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;yBACtC,CAAC;AACI,wBAAA,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AAGnE,wBAAA,WAAW,GAAM,gBAAgB,GAAI,GAAA,GAAA,cAAgB,CAAC;AAGtD,wBAAA,WAAW,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAChD,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA,CAAA;;AAAtF,wBAAA,eAAe,GAAG,EAAoE,CAAA,IAAA,EAAA,CAAA;AACtF,wBAAA,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;AAEhF,wBAAA,SAAS,GAAM,WAAW,GAAI,GAAA,GAAA,gBAAkB,CAAC;AAEvD,wBAAA,IAAI,kBAAkB,EAAE;4BACpB,kBAAkB,CAAC,cAAc,CAAC;AAC9B,gCAAA,OAAO,EAAE,IAAI;AAChB,6BAAA,CAAC,CAAC;AACN,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;;;;AACpB,KAAA,CAAA;AAED;;;AAGG;IACG,SAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UAAiB,SAAiB,EAAA;;;;;4BACE,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA,CAAA;;AAA1E,wBAAA,UAAU,GAAgB,EAAgD,CAAA,IAAA,EAAA,CAAA;AAC1E,wBAAA,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;wBAC7C,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAA;;;;AACjD,KAAA,CAAA;AAxKc,IAAA,SAAA,CAAA,cAAc,GAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACrD,SAAW,CAAA,WAAA,GAAY,IAAI,CAAC;IAwK/C,OAAC,SAAA,CAAA;AAAA,CAxLD,EAwLC;;;;"}