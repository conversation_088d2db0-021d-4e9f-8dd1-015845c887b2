/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __awaiter, __generator } from '../_virtual/_tslib.js';
import { Constants } from '@azure/msal-common';
import { KEY_FORMAT_JWK } from '../utils/BrowserConstants.js';
import { BrowserStringUtils } from '../utils/BrowserStringUtils.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var MsBrowserCrypto = /** @class */ (function () {
    function MsBrowserCrypto() {
    }
    MsBrowserCrypto.prototype.getRandomValues = function (dataBuffer) {
        return window["msCrypto"].getRandomValues(dataBuffer);
    };
    MsBrowserCrypto.prototype.generateKey = function (algorithm, extractable, keyUsages) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var msGenerateKey = window["msCrypto"].subtle.generateKey(algorithm, extractable, keyUsages);
                        msGenerateKey.addEventListener("complete", function (e) {
                            resolve(e.target.result);
                        });
                        msGenerateKey.addEventListener("error", function (error) {
                            reject(error);
                        });
                    })];
            });
        });
    };
    MsBrowserCrypto.prototype.exportKey = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var msExportKey = window["msCrypto"].subtle.exportKey(KEY_FORMAT_JWK, key);
                        msExportKey.addEventListener("complete", function (e) {
                            var resultBuffer = e.target.result;
                            var resultString = BrowserStringUtils.utf8ArrToString(new Uint8Array(resultBuffer))
                                .replace(/\r/g, Constants.EMPTY_STRING)
                                .replace(/\n/g, Constants.EMPTY_STRING)
                                .replace(/\t/g, Constants.EMPTY_STRING)
                                .split(" ").join(Constants.EMPTY_STRING)
                                .replace("\u0000", Constants.EMPTY_STRING);
                            try {
                                resolve(JSON.parse(resultString));
                            }
                            catch (e) {
                                reject(e);
                            }
                        });
                        msExportKey.addEventListener("error", function (error) {
                            reject(error);
                        });
                    })];
            });
        });
    };
    MsBrowserCrypto.prototype.importKey = function (keyData, algorithm, extractable, keyUsages) {
        return __awaiter(this, void 0, void 0, function () {
            var keyString, keyBuffer;
            return __generator(this, function (_a) {
                keyString = BrowserStringUtils.getSortedObjectString(keyData);
                keyBuffer = BrowserStringUtils.stringToArrayBuffer(keyString);
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var msImportKey = window["msCrypto"].subtle.importKey(KEY_FORMAT_JWK, keyBuffer, algorithm, extractable, keyUsages);
                        msImportKey.addEventListener("complete", function (e) {
                            resolve(e.target.result);
                        });
                        msImportKey.addEventListener("error", function (error) {
                            reject(error);
                        });
                    })];
            });
        });
    };
    MsBrowserCrypto.prototype.sign = function (algorithm, key, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var msSign = window["msCrypto"].subtle.sign(algorithm, key, data);
                        msSign.addEventListener("complete", function (e) {
                            resolve(e.target.result);
                        });
                        msSign.addEventListener("error", function (error) {
                            reject(error);
                        });
                    })];
            });
        });
    };
    MsBrowserCrypto.prototype.digest = function (algorithm, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        var digestOperation = window["msCrypto"].subtle.digest(algorithm, data.buffer);
                        digestOperation.addEventListener("complete", function (e) {
                            resolve(e.target.result);
                        });
                        digestOperation.addEventListener("error", function (error) {
                            reject(error);
                        });
                    })];
            });
        });
    };
    return MsBrowserCrypto;
}());

export { MsBrowserCrypto };
//# sourceMappingURL=MsBrowserCrypto.js.map
