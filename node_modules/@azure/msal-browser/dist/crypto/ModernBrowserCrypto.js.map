{"version": 3, "file": "ModernBrowserCrypto.js", "sources": ["../../src/crypto/ModernBrowserCrypto.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { KEY_FORMAT_JWK } from \"../utils/BrowserConstants\";\r\nimport { ISubtleCrypto } from \"./ISubtleCrypto\";\r\n\r\nexport class ModernBrowserCrypto implements ISubtleCrypto {\r\n    getRandomValues(dataBuffer: Uint8Array): Uint8Array {\r\n        return window.crypto.getRandomValues(dataBuffer);\r\n    }\r\n\r\n    async generateKey(algorithm: RsaHashedKeyGenParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKeyPair> {\r\n        return window.crypto.subtle.generateKey(algorithm, extractable, keyUsages) as Promise<CryptoKeyPair>;\r\n    }\r\n\r\n    async exportKey(key: CryptoKey): Promise<JsonWebKey> {\r\n        return window.crypto.subtle.exportKey(KEY_FORMAT_JWK, key) as Promise<JsonWebKey>;\r\n    }\r\n\r\n    async importKey(keyData: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, algorithm: RsaHashedImportParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey> {\r\n        return window.crypto.subtle.importKey(KEY_FORMAT_JWK, keyData, algorithm, extractable, keyUsages) as Promise<CryptoKey>;\r\n    }\r\n\r\n    async sign(algorithm: AlgorithmIdentifier, key: CryptoKey, data: ArrayBuffer): Promise<ArrayBuffer> {\r\n        return window.crypto.subtle.sign(algorithm, key, data) as Promise<ArrayBuffer>;\r\n    }\r\n\r\n    async digest(algorithm: AlgorithmIdentifier, data: Uint8Array): Promise<ArrayBuffer> {\r\n        return window.crypto.subtle.digest(algorithm, data) as Promise<ArrayBuffer>;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAKH,IAAA,mBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,mBAAA,GAAA;KAwBC;IAvBG,mBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,UAAsB,EAAA;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KACpD,CAAA;AAEK,IAAA,mBAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,UAAkB,SAAgC,EAAE,WAAoB,EAAE,SAAqB,EAAA;;;AAC3F,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAA2B,CAAC,CAAA;;;AACxG,KAAA,CAAA;IAEK,mBAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,GAAc,EAAA;;;AAC1B,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,GAAG,CAAwB,CAAC,CAAA;;;AACrF,KAAA,CAAA;IAEK,mBAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,OAAmB,EAAE,SAAgC,EAAE,WAAoB,EAAE,SAAqB,EAAA;;;AAC9G,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAuB,CAAC,CAAA;;;AAC3H,KAAA,CAAA;AAEK,IAAA,mBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,UAAW,SAA8B,EAAE,GAAc,EAAE,IAAiB,EAAA;;;AACxE,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAyB,CAAC,CAAA;;;AAClF,KAAA,CAAA;AAEK,IAAA,mBAAA,CAAA,SAAA,CAAA,MAAM,GAAZ,UAAa,SAA8B,EAAE,IAAgB,EAAA;;;AACzD,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAyB,CAAC,CAAA;;;AAC/E,KAAA,CAAA;IACL,OAAC,mBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}