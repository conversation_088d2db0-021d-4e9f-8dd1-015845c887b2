export interface ISubtleCrypto {
    initPrng?(entropy: Uint8Array): void;
    getRandomValues(dataBuffer: Uint8Array): Uint8Array;
    generateKey(algorithm: RsaHashedKeyGenParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKeyPair>;
    exportKey(key: CryptoKey): Promise<JsonWebKey>;
    importKey(keyData: <PERSON>sonWebKey, algorithm: RsaHashedImportParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>;
    sign(algorithm: AlgorithmIdentifier, key: Crypto<PERSON><PERSON>, data: ArrayBuffer): Promise<ArrayBuffer>;
    digest(algorithm: AlgorithmIdentifier, data: Uint8Array): Promise<ArrayBuffer>;
}
//# sourceMappingURL=ISubtleCrypto.d.ts.map