{"version": 3, "file": "MsBrowserCrypto.js", "sources": ["../../src/crypto/MsBrowserCrypto.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"@azure/msal-common\";\r\nimport { KEY_FORMAT_JWK } from \"../utils/BrowserConstants\";\r\nimport { BrowserStringUtils } from \"../utils/BrowserStringUtils\";\r\nimport { ISubtleCrypto } from \"./ISubtleCrypto\";\r\n\r\nexport class MsBrowserCrypto implements ISubtleCrypto {\r\n    getRandomValues(dataBuffer: Uint8Array): Uint8Array {\r\n        return window[\"msCrypto\"].getRandomValues(dataBuffer);\r\n    }\r\n\r\n    async generateKey(algorithm: RsaHashedKeyGenParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKeyPair> {\r\n        return new Promise((resolve: Function, reject: Function) => {\r\n            const msGenerateKey = window[\"msCrypto\"].subtle.generateKey(algorithm, extractable, keyUsages);\r\n            msGenerateKey.addEventListener(\"complete\", (e: { target: { result: CryptoKeyPair | PromiseLike<CryptoKeyPair>; }; }) => {\r\n                resolve(e.target.result);\r\n            });\r\n\r\n            msGenerateKey.addEventListener(\"error\", (error: string) => {\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n\r\n    async exportKey(key: CryptoKey): Promise<JsonWebKey> {\r\n        return new Promise((resolve: Function, reject: Function) => {\r\n            const msExportKey = window[\"msCrypto\"].subtle.exportKey(KEY_FORMAT_JWK, key);\r\n            msExportKey.addEventListener(\"complete\", (e: { target: { result: ArrayBuffer; }; }) => {\r\n                const resultBuffer: ArrayBuffer = e.target.result;\r\n\r\n                const resultString = BrowserStringUtils.utf8ArrToString(new Uint8Array(resultBuffer))\r\n                    .replace(/\\r/g, Constants.EMPTY_STRING)\r\n                    .replace(/\\n/g, Constants.EMPTY_STRING)\r\n                    .replace(/\\t/g, Constants.EMPTY_STRING)\r\n                    .split(\" \").join(Constants.EMPTY_STRING)\r\n                    .replace(\"\\u0000\", Constants.EMPTY_STRING);\r\n\r\n                try {\r\n                    resolve(JSON.parse(resultString));\r\n                } catch (e) {\r\n                    reject(e);\r\n                }\r\n            });\r\n\r\n            msExportKey.addEventListener(\"error\", (error: string) => {\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n\r\n    async importKey(keyData: JsonWebKey, algorithm: RsaHashedImportParams, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey> {\r\n        const keyString = BrowserStringUtils.getSortedObjectString(keyData);\r\n        const keyBuffer = BrowserStringUtils.stringToArrayBuffer(keyString);\r\n\r\n        return new Promise((resolve: Function, reject: Function) => {\r\n            const msImportKey = window[\"msCrypto\"].subtle.importKey(KEY_FORMAT_JWK, keyBuffer, algorithm, extractable, keyUsages);\r\n            msImportKey.addEventListener(\"complete\", (e: { target: { result: CryptoKey | PromiseLike<CryptoKey>; }; }) => {\r\n                resolve(e.target.result);\r\n            });\r\n\r\n            msImportKey.addEventListener(\"error\", (error: string) => {\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n\r\n    async sign(algorithm: AlgorithmIdentifier, key: CryptoKey, data: ArrayBuffer): Promise<ArrayBuffer> {\r\n        return new Promise((resolve: Function, reject: Function) => {\r\n            const msSign = window[\"msCrypto\"].subtle.sign(algorithm, key, data);\r\n            msSign.addEventListener(\"complete\", (e: { target: { result: ArrayBuffer | PromiseLike<ArrayBuffer>; }; }) => {\r\n                resolve(e.target.result);\r\n            });\r\n\r\n            msSign.addEventListener(\"error\", (error: string) => {\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    \r\n    async digest(algorithm: AlgorithmIdentifier, data: Uint8Array): Promise<ArrayBuffer> {\r\n        return new Promise((resolve, reject) => {\r\n            const digestOperation = window[\"msCrypto\"].subtle.digest(algorithm, data.buffer);\r\n            digestOperation.addEventListener(\"complete\", (e: { target: { result: ArrayBuffer | PromiseLike<ArrayBuffer>; }; }) => {\r\n                resolve(e.target.result);\r\n            });\r\n            digestOperation.addEventListener(\"error\", (error: string) => {\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAOH,IAAA,eAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,eAAA,GAAA;KAoFC;IAnFG,eAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,UAAsB,EAAA;QAClC,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KACzD,CAAA;AAEK,IAAA,eAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,UAAkB,SAAgC,EAAE,WAAoB,EAAE,SAAqB,EAAA;;;AAC3F,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAiB,EAAE,MAAgB,EAAA;AACnD,wBAAA,IAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AAC/F,wBAAA,aAAa,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAC,CAAuE,EAAA;AAC/G,4BAAA,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,yBAAC,CAAC,CAAC;AAEH,wBAAA,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,KAAa,EAAA;4BAClD,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,yBAAC,CAAC,CAAC;AACP,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;IAEK,eAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,GAAc,EAAA;;;AAC1B,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAiB,EAAE,MAAgB,EAAA;AACnD,wBAAA,IAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AAC7E,wBAAA,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAC,CAAwC,EAAA;AAC9E,4BAAA,IAAM,YAAY,GAAgB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;4BAElD,IAAM,YAAY,GAAG,kBAAkB,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;AAChF,iCAAA,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,YAAY,CAAC;AACtC,iCAAA,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,YAAY,CAAC;AACtC,iCAAA,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,YAAY,CAAC;iCACtC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;AACvC,iCAAA,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;4BAE/C,IAAI;gCACA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AACrC,6BAAA;AAAC,4BAAA,OAAO,CAAC,EAAE;gCACR,MAAM,CAAC,CAAC,CAAC,CAAC;AACb,6BAAA;AACL,yBAAC,CAAC,CAAC;AAEH,wBAAA,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,KAAa,EAAA;4BAChD,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,yBAAC,CAAC,CAAC;AACP,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;IAEK,eAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,OAAmB,EAAE,SAAgC,EAAE,WAAoB,EAAE,SAAqB,EAAA;;;;AACxG,gBAAA,SAAS,GAAG,kBAAkB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;AAC9D,gBAAA,SAAS,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAEpE,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAiB,EAAE,MAAgB,EAAA;wBACnD,IAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACtH,wBAAA,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAC,CAA+D,EAAA;AACrG,4BAAA,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,yBAAC,CAAC,CAAC;AAEH,wBAAA,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,KAAa,EAAA;4BAChD,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,yBAAC,CAAC,CAAC;AACP,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;AAEK,IAAA,eAAA,CAAA,SAAA,CAAA,IAAI,GAAV,UAAW,SAA8B,EAAE,GAAc,EAAE,IAAiB,EAAA;;;AACxE,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAiB,EAAE,MAAgB,EAAA;AACnD,wBAAA,IAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACpE,wBAAA,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAC,CAAmE,EAAA;AACpG,4BAAA,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,yBAAC,CAAC,CAAC;AAEH,wBAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,KAAa,EAAA;4BAC3C,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,yBAAC,CAAC,CAAC;AACP,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;AAEK,IAAA,eAAA,CAAA,SAAA,CAAA,MAAM,GAAZ,UAAa,SAA8B,EAAE,IAAgB,EAAA;;;AACzD,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AAC/B,wBAAA,IAAM,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACjF,wBAAA,eAAe,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAC,CAAmE,EAAA;AAC7G,4BAAA,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,yBAAC,CAAC,CAAC;AACH,wBAAA,eAAe,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,KAAa,EAAA;4BACpD,MAAM,CAAC,KAAK,CAAC,CAAC;AAClB,yBAAC,CAAC,CAAC;AACP,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;;;"}