/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __awaiter, __generator } from '../_virtual/_tslib.js';
import { KEY_FORMAT_JWK } from '../utils/BrowserConstants.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var ModernBrowserCrypto = /** @class */ (function () {
    function ModernBrowserCrypto() {
    }
    ModernBrowserCrypto.prototype.getRandomValues = function (dataBuffer) {
        return window.crypto.getRandomValues(dataBuffer);
    };
    ModernBrowserCrypto.prototype.generateKey = function (algorithm, extractable, keyUsages) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.crypto.subtle.generateKey(algorithm, extractable, keyUsages)];
            });
        });
    };
    ModernBrowserCrypto.prototype.exportKey = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.crypto.subtle.exportKey(KEY_FORMAT_JWK, key)];
            });
        });
    };
    ModernBrowserCrypto.prototype.importKey = function (keyData, algorithm, extractable, keyUsages) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.crypto.subtle.importKey(KEY_FORMAT_JWK, keyData, algorithm, extractable, keyUsages)];
            });
        });
    };
    ModernBrowserCrypto.prototype.sign = function (algorithm, key, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.crypto.subtle.sign(algorithm, key, data)];
            });
        });
    };
    ModernBrowserCrypto.prototype.digest = function (algorithm, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, window.crypto.subtle.digest(algorithm, data)];
            });
        });
    };
    return ModernBrowserCrypto;
}());

export { ModernBrowserCrypto };
//# sourceMappingURL=ModernBrowserCrypto.js.map
