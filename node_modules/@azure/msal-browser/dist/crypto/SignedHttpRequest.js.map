{"version": 3, "file": "SignedHttpRequest.js", "sources": ["../../src/crypto/SignedHttpRequest.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CryptoOps } from \"./CryptoOps\";\r\nimport { Logger, LoggerOptions, PopTokenGenerator, SignedHttpRequestParameters } from \"@azure/msal-common\";\r\nimport { version, name } from \"../packageMetadata\";\r\n\r\nexport type SignedHttpRequestOptions = {\r\n    loggerOptions: LoggerOptions\r\n};\r\n\r\nexport class SignedHttpRequest {\r\n    private popTokenGenerator: PopTokenGenerator;\r\n    private cryptoOps: CryptoOps;\r\n    private shrParameters: SignedHttpRequestParameters;\r\n    private logger: Logger;\r\n\r\n    constructor(shrParameters: SignedHttpRequestParameters, shrOptions?: SignedHttpRequestOptions) {\r\n        const loggerOptions = (shrOptions && shrOptions.loggerOptions) || {};\r\n        this.logger = new Logger(loggerOptions, name, version);\r\n        this.cryptoOps = new CryptoOps(this.logger);\r\n        this.popTokenGenerator = new PopTokenGenerator(this.cryptoOps);\r\n        this.shrParameters = shrParameters;\r\n    }\r\n\r\n    /**\r\n     * Generates and caches a keypair for the given request options.\r\n     * @returns Public key digest, which should be sent to the token issuer.\r\n     */\r\n    async generatePublicKeyThumbprint(): Promise<string> {\r\n        const { kid } = await this.popTokenGenerator.generateKid(this.shrParameters);\r\n\r\n        return kid;\r\n    }\r\n\r\n    /**\r\n     * Generates a signed http request for the given payload with the given key.\r\n     * @param payload Payload to sign (e.g. access token)\r\n     * @param publicKeyThumbprint Public key digest (from generatePublicKeyThumbprint API)\r\n     * @param claims Additional claims to include/override in the signed JWT \r\n     * @returns Pop token signed with the corresponding private key\r\n     */\r\n    async signRequest(payload: string, publicKeyThumbprint: string, claims?: object): Promise<string> {\r\n        return this.popTokenGenerator.signPayload(\r\n            payload, \r\n            publicKeyThumbprint,\r\n            this.shrParameters, \r\n            claims\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Removes cached keys from browser for given public key thumbprint\r\n     * @param publicKeyThumbprint Public key digest (from generatePublicKeyThumbprint API)\r\n     * @returns If keys are properly deleted\r\n     */\r\n    async removeKeys(publicKeyThumbprint: string): Promise<boolean> {\r\n        return await this.cryptoOps.removeTokenBindingKey(publicKeyThumbprint);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAUH,IAAA,iBAAA,kBAAA,YAAA;IAMI,SAAY,iBAAA,CAAA,aAA0C,EAAE,UAAqC,EAAA;QACzF,IAAM,aAAa,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,aAAa,KAAK,EAAE,CAAC;AACrE,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;AAED;;;AAGG;AACG,IAAA,iBAAA,CAAA,SAAA,CAAA,2BAA2B,GAAjC,YAAA;;;;;4BACoB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA,CAAA;;AAApE,wBAAA,GAAG,GAAK,CAAA,EAA4D,CAAA,IAAA,EAAA,EAAjE,GAAA,CAAA;AAEX,wBAAA,OAAA,CAAA,CAAA,aAAO,GAAG,CAAC,CAAA;;;;AACd,KAAA,CAAA;AAED;;;;;;AAMG;AACG,IAAA,iBAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,UAAkB,OAAe,EAAE,mBAA2B,EAAE,MAAe,EAAA;;;AAC3E,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACrC,OAAO,EACP,mBAAmB,EACnB,IAAI,CAAC,aAAa,EAClB,MAAM,CACT,CAAC,CAAA;;;AACL,KAAA,CAAA;AAED;;;;AAIG;IACG,iBAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UAAiB,mBAA2B,EAAA;;;;4BACjC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAA,CAAA;AAAtE,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA+D,CAAC,CAAA;;;;AAC1E,KAAA,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}