/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __awaiter, __generator } from '../_virtual/_tslib.js';
import { CryptoOps } from './CryptoOps.js';
import { Logger, PopTokenGenerator } from '@azure/msal-common';
import { name, version } from '../packageMetadata.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var SignedHttpRequest = /** @class */ (function () {
    function SignedHttpRequest(shrParameters, shrOptions) {
        var loggerOptions = (shrOptions && shrOptions.loggerOptions) || {};
        this.logger = new Logger(loggerOptions, name, version);
        this.cryptoOps = new CryptoOps(this.logger);
        this.popTokenGenerator = new PopTokenGenerator(this.cryptoOps);
        this.shrParameters = shrParameters;
    }
    /**
     * Generates and caches a keypair for the given request options.
     * @returns Public key digest, which should be sent to the token issuer.
     */
    SignedHttpRequest.prototype.generatePublicKeyThumbprint = function () {
        return __awaiter(this, void 0, void 0, function () {
            var kid;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.popTokenGenerator.generateKid(this.shrParameters)];
                    case 1:
                        kid = (_a.sent()).kid;
                        return [2 /*return*/, kid];
                }
            });
        });
    };
    /**
     * Generates a signed http request for the given payload with the given key.
     * @param payload Payload to sign (e.g. access token)
     * @param publicKeyThumbprint Public key digest (from generatePublicKeyThumbprint API)
     * @param claims Additional claims to include/override in the signed JWT
     * @returns Pop token signed with the corresponding private key
     */
    SignedHttpRequest.prototype.signRequest = function (payload, publicKeyThumbprint, claims) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.popTokenGenerator.signPayload(payload, publicKeyThumbprint, this.shrParameters, claims)];
            });
        });
    };
    /**
     * Removes cached keys from browser for given public key thumbprint
     * @param publicKeyThumbprint Public key digest (from generatePublicKeyThumbprint API)
     * @returns If keys are properly deleted
     */
    SignedHttpRequest.prototype.removeKeys = function (publicKeyThumbprint) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.cryptoOps.removeTokenBindingKey(publicKeyThumbprint)];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    return SignedHttpRequest;
}());

export { SignedHttpRequest };
//# sourceMappingURL=SignedHttpRequest.js.map
