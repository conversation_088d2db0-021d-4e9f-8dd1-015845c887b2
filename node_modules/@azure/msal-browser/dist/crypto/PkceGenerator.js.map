{"version": 3, "file": "PkceGenerator.js", "sources": ["../../src/crypto/PkceGenerator.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { PkceCodes } from \"@azure/msal-common\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { Base64Encode } from \"../encode/Base64Encode\";\r\nimport { BrowserCrypto } from \"./BrowserCrypto\";\r\n\r\n// Constant byte array length\r\nconst RANDOM_BYTE_ARR_LENGTH = 32;\r\n\r\n/**\r\n * Class which exposes APIs to generate PKCE codes and code verifiers.\r\n */\r\nexport class PkceGenerator {\r\n\r\n    private base64Encode: Base64Encode;\r\n    private cryptoObj: BrowserCrypto;\r\n\r\n    constructor(cryptoObj: BrowserCrypto) {\r\n        this.base64Encode = new Base64Encode();\r\n        this.cryptoObj = cryptoObj;\r\n    }\r\n\r\n    /**\r\n     * Generates PKCE Codes. See the RFC for more information: https://tools.ietf.org/html/rfc7636\r\n     */\r\n    async generateCodes(): Promise<PkceCodes> {\r\n        const codeVerifier = this.generateCodeVerifier();\r\n        const codeChallenge = await this.generateCodeChallengeFromVerifier(codeVerifier);\r\n        return {\r\n            verifier: codeVerifier,\r\n            challenge: codeChallenge\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Generates a random 32 byte buffer and returns the base64\r\n     * encoded string to be used as a PKCE Code Verifier\r\n     */\r\n    private generateCodeVerifier(): string {\r\n        try {\r\n            // Generate random values as utf-8\r\n            const buffer: Uint8Array = new Uint8Array(RANDOM_BYTE_ARR_LENGTH);\r\n            this.cryptoObj.getRandomValues(buffer);\r\n            // encode verifier as base64\r\n            const pkceCodeVerifierB64: string = this.base64Encode.urlEncodeArr(buffer);\r\n            return pkceCodeVerifierB64;\r\n        } catch (e) {\r\n            throw BrowserAuthError.createPkceNotGeneratedError(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a base64 encoded PKCE Code Challenge string from the\r\n     * hash created from the PKCE Code Verifier supplied\r\n     */\r\n    private async generateCodeChallengeFromVerifier(pkceCodeVerifier: string): Promise<string> {\r\n        try {\r\n            // hashed verifier\r\n            const pkceHashedCodeVerifier = await this.cryptoObj.sha256Digest(pkceCodeVerifier);\r\n            // encode hash as base64\r\n            return this.base64Encode.urlEncodeArr(new Uint8Array(pkceHashedCodeVerifier));\r\n        } catch (e) {\r\n            throw BrowserAuthError.createPkceNotGeneratedError(e);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAOH;AACA,IAAM,sBAAsB,GAAG,EAAE,CAAC;AAElC;;AAEG;AACH,IAAA,aAAA,kBAAA,YAAA;AAKI,IAAA,SAAA,aAAA,CAAY,SAAwB,EAAA;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC9B;AAED;;AAEG;AACG,IAAA,aAAA,CAAA,SAAA,CAAA,aAAa,GAAnB,YAAA;;;;;;AACU,wBAAA,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC3B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iCAAiC,CAAC,YAAY,CAAC,CAAA,CAAA;;AAA1E,wBAAA,aAAa,GAAG,EAA0D,CAAA,IAAA,EAAA,CAAA;wBAChF,OAAO,CAAA,CAAA,aAAA;AACH,gCAAA,QAAQ,EAAE,YAAY;AACtB,gCAAA,SAAS,EAAE,aAAa;6BAC3B,CAAC,CAAA;;;;AACL,KAAA,CAAA;AAED;;;AAGG;AACK,IAAA,aAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QACI,IAAI;;AAEA,YAAA,IAAM,MAAM,GAAe,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;;YAEvC,IAAM,mBAAmB,GAAW,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC3E,YAAA,OAAO,mBAAmB,CAAC;AAC9B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC;AACzD,SAAA;KACJ,CAAA;AAED;;;AAGG;IACW,aAAiC,CAAA,SAAA,CAAA,iCAAA,GAA/C,UAAgD,gBAAwB,EAAA;;;;;;;wBAGjC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAA,CAAA;;AAA5E,wBAAA,sBAAsB,GAAG,EAAmD,CAAA,IAAA,EAAA,CAAA;;AAElF,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;;;AAE9E,wBAAA,MAAM,gBAAgB,CAAC,2BAA2B,CAAC,GAAC,CAAC,CAAC;;;;;AAE7D,KAAA,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA;;;;"}