{"version": 3, "file": "Configuration.js", "sources": ["../../src/config/Configuration.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { SystemOptions, LoggerOptions, INetworkModule, DEFAULT_SYSTEM_OPTIONS, Constants, ProtocolMode, LogLevel, StubbedNetworkModule, AzureCloudInstance, AzureCloudOptions, ApplicationTelemetry } from \"@azure/msal-common\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { BrowserCacheLocation, BrowserConstants } from \"../utils/BrowserConstants\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { NavigationClient } from \"../navigation/NavigationClient\";\r\n\r\n// Default timeout for popup windows and iframes in milliseconds\r\nexport const DEFAULT_POPUP_TIMEOUT_MS = 60000;\r\nexport const DEFAULT_IFRAME_TIMEOUT_MS = 6000;\r\nexport const DEFAULT_REDIRECT_TIMEOUT_MS = 30000;\r\nexport const DEFAULT_NATIVE_BROKER_HANDSHAKE_TIMEOUT_MS = 2000;\r\n\r\n/**\r\n * Use this to configure the auth options in the Configuration object\r\n */\r\nexport type BrowserAuthOptions = {\r\n    /**\r\n     * Client ID of your app registered with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview in Microsoft Identity Platform\r\n     */\r\n    clientId: string;\r\n    /**\r\n     * You can configure a specific authority, defaults to \" \" or \"https://login.microsoftonline.com/common\"\r\n     */\r\n    authority?: string;\r\n    /**\r\n     * An array of URIs that are known to be valid. Used in B2C scenarios.\r\n     */\r\n    knownAuthorities?: Array<string>;\r\n    /**\r\n     * A string containing the cloud discovery response. Used in AAD scenarios.\r\n     */\r\n    cloudDiscoveryMetadata?: string;\r\n    /**\r\n     * A string containing the .well-known/openid-configuration endpoint response\r\n     */\r\n    authorityMetadata?: string;\r\n    /**\r\n     * The redirect URI where authentication responses can be received by your application. It must exactly match one of the redirect URIs registered in the Azure portal.\r\n     */\r\n    redirectUri?: string;\r\n    /**\r\n     * The redirect URI where the window navigates after a successful logout.\r\n     */\r\n    postLogoutRedirectUri?: string | null;\r\n    /**\r\n     * Boolean indicating whether to navigate to the original request URL after the auth server navigates to the redirect URL.\r\n     */\r\n    navigateToLoginRequestUrl?: boolean;\r\n    /**\r\n     * Array of capabilities which will be added to the claims.access_token.xms_cc request property on every network request.\r\n     */\r\n    clientCapabilities?: Array<string>;\r\n    /**\r\n     * Enum that represents the protocol that msal follows. Used for configuring proper endpoints.\r\n     */\r\n    protocolMode?: ProtocolMode;\r\n    /**\r\n     * Enum that represents the Azure Cloud to use.\r\n     */\r\n    azureCloudOptions?: AzureCloudOptions;\r\n    /**\r\n     * Flag of whether to use the local metadata cache\r\n     */\r\n    skipAuthorityMetadataCache?: boolean;\r\n};\r\n\r\n/**\r\n * Use this to configure the below cache configuration options:\r\n */\r\nexport type CacheOptions = {\r\n    /**\r\n     * Used to specify the cacheLocation user wants to set. Valid values are \"localStorage\", \"sessionStorage\" and \"memoryStorage\".\r\n     */\r\n    cacheLocation?: BrowserCacheLocation | string;\r\n    /**\r\n     * Used to specify the temporaryCacheLocation user wants to set. Valid values are \"localStorage\", \"sessionStorage\" and \"memoryStorage\".\r\n     */\r\n    temporaryCacheLocation?: BrowserCacheLocation | string;\r\n    /**\r\n     * If set, MSAL stores the auth request state required for validation of the auth flows in the browser cookies. By default this flag is set to false.\r\n     */\r\n    storeAuthStateInCookie?: boolean;\r\n    /**\r\n     * If set, MSAL sets the \"Secure\" flag on cookies so they can only be sent over HTTPS. By default this flag is set to false.\r\n     */\r\n    secureCookies?: boolean;\r\n    /**\r\n     * If set, MSAL will attempt to migrate cache entries from older versions on initialization. By default this flag is set to true if cacheLocation is localStorage, otherwise false.\r\n     */\r\n    cacheMigrationEnabled?: boolean;\r\n    /**\r\n     * Flag that determines whether access tokens are stored based on requested claims\r\n     */\r\n    claimsBasedCachingEnabled?: boolean;\r\n};\r\n\r\nexport type BrowserSystemOptions = SystemOptions & {\r\n    /**\r\n     * Used to initialize the Logger object (See ClientConfiguration.ts)\r\n     */\r\n    loggerOptions?: LoggerOptions;\r\n    /**\r\n     * Network interface implementation\r\n     */\r\n    networkClient?: INetworkModule;\r\n    /**\r\n     * Override the methods used to navigate to other webpages. Particularly useful if you are using a client-side router\r\n     */\r\n    navigationClient?: INavigationClient;\r\n    /**\r\n     * Sets the timeout for waiting for a response hash in a popup. Will take precedence over loadFrameTimeout if both are set.\r\n     */\r\n    windowHashTimeout?: number;\r\n    /**\r\n     * Sets the timeout for waiting for a response hash in an iframe. Will take precedence over loadFrameTimeout if both are set.\r\n     */\r\n    iframeHashTimeout?: number;\r\n    /**\r\n     * Sets the timeout for waiting for a response hash in an iframe or popup\r\n     */\r\n    loadFrameTimeout?: number;\r\n    /**\r\n     * Maximum time the library should wait for a frame to load\r\n     */\r\n    navigateFrameWait?: number;\r\n    /**\r\n     * Time to wait for redirection to occur before resolving promise\r\n     */\r\n    redirectNavigationTimeout?: number;\r\n    /**\r\n     * Sets whether popups are opened asynchronously. By default, this flag is set to false. When set to false, blank popups are opened before anything else happens. When set to true, popups are opened when making the network request.\r\n     */\r\n    asyncPopups?: boolean;\r\n    /**\r\n     * Flag to enable redirect opertaions when the app is rendered in an iframe (to support scenarios such as embedded B2C login).\r\n     */\r\n    allowRedirectInIframe?: boolean;\r\n    /**\r\n     * Flag to enable native broker support (e.g. acquiring tokens from WAM on Windows)\r\n     */\r\n    allowNativeBroker?: boolean;\r\n    /**\r\n     * Sets the timeout for waiting for the native broker handshake to resolve\r\n     */\r\n    nativeBrokerHandshakeTimeout?: number;\r\n    /**\r\n     * Options related to browser crypto APIs\r\n     */\r\n    cryptoOptions?: CryptoOptions;\r\n    /**\r\n     * Sets the interval length in milliseconds for polling the location attribute in popup windows (default is 30ms)\r\n     */\r\n    pollIntervalMilliseconds?: number;\r\n};\r\n\r\nexport type CryptoOptions = {\r\n\r\n    /**\r\n     * Enables the application to use the MSR Crypto interface, if available (and other interfaces are not)\r\n     * @type {?boolean}\r\n     */\r\n    useMsrCrypto?: boolean;\r\n\r\n    /**\r\n     * Entropy to seed browser crypto API (needed for MSR Crypto). Must be cryptographically strong random numbers (e.g. crypto.randomBytes(48) from Node)\r\n     * @type {?Uint8Array}\r\n     */\r\n    entropy?: Uint8Array;\r\n};\r\n\r\n/**\r\n * Telemetry Options\r\n */\r\nexport type BrowserTelemetryOptions = {\r\n    /**\r\n     * Telemetry information sent on request\r\n     * - appName: Unique string name of an application\r\n     * - appVersion: Version of the application using MSAL\r\n     */\r\n    application?: ApplicationTelemetry;\r\n};\r\n\r\n/**\r\n * This object allows you to configure important elements of MSAL functionality and is passed into the constructor of PublicClientApplication\r\n */\r\nexport type Configuration = {\r\n    /**\r\n     * This is where you configure auth elements like clientID, authority used for authenticating against the Microsoft Identity Platform\r\n     */\r\n    auth: BrowserAuthOptions,\r\n    /**\r\n     * This is where you configure cache location and whether to store cache in cookies\r\n     */\r\n    cache?: CacheOptions,\r\n    /**\r\n     * This is where you can configure the network client, logger, token renewal offset\r\n     */\r\n    system?: BrowserSystemOptions,\r\n    /**\r\n     * This is where you can configure telemetry data and options\r\n     */\r\n    telemetry?: BrowserTelemetryOptions\r\n};\r\n\r\nexport type BrowserConfiguration = {\r\n    auth: Required<BrowserAuthOptions>,\r\n    cache: Required<CacheOptions>,\r\n    system: Required<BrowserSystemOptions>,\r\n    telemetry: Required<BrowserTelemetryOptions>\r\n};\r\n\r\n/**\r\n * MSAL function that sets the default options when not explicitly configured from app developer\r\n *\r\n * @param auth\r\n * @param cache\r\n * @param system\r\n *\r\n * @returns Configuration object\r\n */\r\nexport function buildConfiguration({ auth: userInputAuth, cache: userInputCache, system: userInputSystem, telemetry: userInputTelemetry }: Configuration, isBrowserEnvironment: boolean): BrowserConfiguration {\r\n\r\n    // Default auth options for browser\r\n    const DEFAULT_AUTH_OPTIONS: Required<BrowserAuthOptions> = {\r\n        clientId: Constants.EMPTY_STRING,\r\n        authority: `${Constants.DEFAULT_AUTHORITY}`,\r\n        knownAuthorities: [],\r\n        cloudDiscoveryMetadata: Constants.EMPTY_STRING,\r\n        authorityMetadata: Constants.EMPTY_STRING,\r\n        redirectUri: Constants.EMPTY_STRING,\r\n        postLogoutRedirectUri: Constants.EMPTY_STRING,\r\n        navigateToLoginRequestUrl: true,\r\n        clientCapabilities: [],\r\n        protocolMode: ProtocolMode.AAD,\r\n        azureCloudOptions: {\r\n            azureCloudInstance: AzureCloudInstance.None,\r\n            tenant: Constants.EMPTY_STRING\r\n        },\r\n        skipAuthorityMetadataCache: false,  \r\n    };\r\n\r\n    // Default cache options for browser\r\n    const DEFAULT_CACHE_OPTIONS: Required<CacheOptions> = {\r\n        cacheLocation: BrowserCacheLocation.SessionStorage,\r\n        temporaryCacheLocation: BrowserCacheLocation.SessionStorage,\r\n        storeAuthStateInCookie: false,\r\n        secureCookies: false,\r\n        // Default cache migration to true if cache location is localStorage since entries are preserved across tabs/windows. Migration has little to no benefit in sessionStorage and memoryStorage\r\n        cacheMigrationEnabled: userInputCache && userInputCache.cacheLocation === BrowserCacheLocation.LocalStorage ? true : false,\r\n        claimsBasedCachingEnabled: true\r\n    };\r\n\r\n    // Default logger options for browser\r\n    const DEFAULT_LOGGER_OPTIONS: LoggerOptions = {\r\n        // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n        loggerCallback: (): void => {\r\n            // allow users to not set logger call back \r\n        },\r\n        logLevel: LogLevel.Info,\r\n        piiLoggingEnabled: false\r\n    };\r\n\r\n    // Default system options for browser\r\n    const DEFAULT_BROWSER_SYSTEM_OPTIONS: Required<BrowserSystemOptions> = {\r\n        ...DEFAULT_SYSTEM_OPTIONS,\r\n        loggerOptions: DEFAULT_LOGGER_OPTIONS,\r\n        networkClient: isBrowserEnvironment ? BrowserUtils.getBrowserNetworkClient() : StubbedNetworkModule,\r\n        navigationClient: new NavigationClient(),\r\n        loadFrameTimeout: 0,\r\n        // If loadFrameTimeout is provided, use that as default.\r\n        windowHashTimeout: userInputSystem?.loadFrameTimeout || DEFAULT_POPUP_TIMEOUT_MS,\r\n        iframeHashTimeout: userInputSystem?.loadFrameTimeout || DEFAULT_IFRAME_TIMEOUT_MS,\r\n        navigateFrameWait: isBrowserEnvironment && BrowserUtils.detectIEOrEdge() ? 500 : 0,\r\n        redirectNavigationTimeout: DEFAULT_REDIRECT_TIMEOUT_MS,\r\n        asyncPopups: false,\r\n        allowRedirectInIframe: false,\r\n        allowNativeBroker: false,\r\n        nativeBrokerHandshakeTimeout: userInputSystem?.nativeBrokerHandshakeTimeout || DEFAULT_NATIVE_BROKER_HANDSHAKE_TIMEOUT_MS,\r\n        pollIntervalMilliseconds: BrowserConstants.DEFAULT_POLL_INTERVAL_MS,\r\n        cryptoOptions: {\r\n            useMsrCrypto: false,\r\n            entropy: undefined\r\n        }\r\n    };\r\n\r\n    const providedSystemOptions: BrowserSystemOptions = {\r\n        ...userInputSystem,\r\n        loggerOptions: userInputSystem?.loggerOptions || DEFAULT_LOGGER_OPTIONS\r\n    };\r\n\r\n    const DEFAULT_TELEMETRY_OPTIONS: Required<BrowserTelemetryOptions> = {\r\n        application: {\r\n            appName: Constants.EMPTY_STRING,\r\n            appVersion: Constants.EMPTY_STRING\r\n        }\r\n    };\r\n\r\n    const overlayedConfig: BrowserConfiguration = {\r\n        auth: { ...DEFAULT_AUTH_OPTIONS, ...userInputAuth },\r\n        cache: { ...DEFAULT_CACHE_OPTIONS, ...userInputCache },\r\n        system: { ...DEFAULT_BROWSER_SYSTEM_OPTIONS, ...providedSystemOptions },\r\n        telemetry: { ...DEFAULT_TELEMETRY_OPTIONS, ...userInputTelemetry }\r\n    };\r\n    return overlayedConfig;\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAQH;AACO,IAAM,wBAAwB,GAAG,MAAM;AACvC,IAAM,yBAAyB,GAAG,KAAK;AACvC,IAAM,2BAA2B,GAAG,MAAM;AAC1C,IAAM,0CAA0C,GAAG,KAAK;AAyM/D;;;;;;;;AAQG;AACa,SAAA,kBAAkB,CAAC,EAAqH,EAAE,oBAA6B,EAAA;QAA5I,aAAa,GAAA,EAAA,CAAA,IAAA,EAAS,cAAc,GAAA,EAAA,CAAA,KAAA,EAAU,eAAe,GAAA,EAAA,CAAA,MAAA,EAAa,kBAAkB,GAAA,EAAA,CAAA,SAAA,CAAA;;AAGnI,IAAA,IAAM,oBAAoB,GAAiC;QACvD,QAAQ,EAAE,SAAS,CAAC,YAAY;AAChC,QAAA,SAAS,EAAE,EAAA,GAAG,SAAS,CAAC,iBAAmB;AAC3C,QAAA,gBAAgB,EAAE,EAAE;QACpB,sBAAsB,EAAE,SAAS,CAAC,YAAY;QAC9C,iBAAiB,EAAE,SAAS,CAAC,YAAY;QACzC,WAAW,EAAE,SAAS,CAAC,YAAY;QACnC,qBAAqB,EAAE,SAAS,CAAC,YAAY;AAC7C,QAAA,yBAAyB,EAAE,IAAI;AAC/B,QAAA,kBAAkB,EAAE,EAAE;QACtB,YAAY,EAAE,YAAY,CAAC,GAAG;AAC9B,QAAA,iBAAiB,EAAE;YACf,kBAAkB,EAAE,kBAAkB,CAAC,IAAI;YAC3C,MAAM,EAAE,SAAS,CAAC,YAAY;AACjC,SAAA;AACD,QAAA,0BAA0B,EAAE,KAAK;KACpC,CAAC;;AAGF,IAAA,IAAM,qBAAqB,GAA2B;QAClD,aAAa,EAAE,oBAAoB,CAAC,cAAc;QAClD,sBAAsB,EAAE,oBAAoB,CAAC,cAAc;AAC3D,QAAA,sBAAsB,EAAE,KAAK;AAC7B,QAAA,aAAa,EAAE,KAAK;;AAEpB,QAAA,qBAAqB,EAAE,cAAc,IAAI,cAAc,CAAC,aAAa,KAAK,oBAAoB,CAAC,YAAY,GAAG,IAAI,GAAG,KAAK;AAC1H,QAAA,yBAAyB,EAAE,IAAI;KAClC,CAAC;;AAGF,IAAA,IAAM,sBAAsB,GAAkB;;AAE1C,QAAA,cAAc,EAAE,YAAA;;SAEf;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI;AACvB,QAAA,iBAAiB,EAAE,KAAK;KAC3B,CAAC;;AAGF,IAAA,IAAM,8BAA8B,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EAC7B,sBAAsB,CAAA,EAAA,EACzB,aAAa,EAAE,sBAAsB,EACrC,aAAa,EAAE,oBAAoB,GAAG,YAAY,CAAC,uBAAuB,EAAE,GAAG,oBAAoB,EACnG,gBAAgB,EAAE,IAAI,gBAAgB,EAAE,EACxC,gBAAgB,EAAE,CAAC;;AAEnB,QAAA,iBAAiB,EAAE,CAAA,eAAe,KAAf,IAAA,IAAA,eAAe,uBAAf,eAAe,CAAE,gBAAgB,KAAI,wBAAwB,EAChF,iBAAiB,EAAE,CAAA,eAAe,KAAf,IAAA,IAAA,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAE,gBAAgB,KAAI,yBAAyB,EACjF,iBAAiB,EAAE,oBAAoB,IAAI,YAAY,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,CAAC,EAClF,yBAAyB,EAAE,2BAA2B,EACtD,WAAW,EAAE,KAAK,EAClB,qBAAqB,EAAE,KAAK,EAC5B,iBAAiB,EAAE,KAAK,EACxB,4BAA4B,EAAE,CAAA,eAAe,KAAf,IAAA,IAAA,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAE,4BAA4B,KAAI,0CAA0C,EACzH,wBAAwB,EAAE,gBAAgB,CAAC,wBAAwB,EACnE,aAAa,EAAE;AACX,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,OAAO,EAAE,SAAS;AACrB,SAAA,EAAA,CACJ,CAAC;AAEF,IAAA,IAAM,qBAAqB,GACpB,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,eAAe,CAClB,EAAA,EAAA,aAAa,EAAE,CAAA,eAAe,KAAf,IAAA,IAAA,eAAe,uBAAf,eAAe,CAAE,aAAa,KAAI,sBAAsB,GAC1E,CAAC;AAEF,IAAA,IAAM,yBAAyB,GAAsC;AACjE,QAAA,WAAW,EAAE;YACT,OAAO,EAAE,SAAS,CAAC,YAAY;YAC/B,UAAU,EAAE,SAAS,CAAC,YAAY;AACrC,SAAA;KACJ,CAAC;AAEF,IAAA,IAAM,eAAe,GAAyB;AAC1C,QAAA,IAAI,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,oBAAoB,CAAK,EAAA,aAAa,CAAE;AACnD,QAAA,KAAK,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,qBAAqB,CAAK,EAAA,cAAc,CAAE;AACtD,QAAA,MAAM,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,8BAA8B,CAAK,EAAA,qBAAqB,CAAE;AACvE,QAAA,SAAS,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,yBAAyB,CAAK,EAAA,kBAAkB,CAAE;KACrE,CAAC;AACF,IAAA,OAAO,eAAe,CAAC;AAC3B;;;;"}