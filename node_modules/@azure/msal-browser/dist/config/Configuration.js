/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __assign } from '../_virtual/_tslib.js';
import { DEFAULT_SYSTEM_OPTIONS, StubbedNetworkModule, Constants, ProtocolMode, AzureCloudInstance, LogLevel } from '@azure/msal-common';
import { BrowserUtils } from '../utils/BrowserUtils.js';
import { BrowserConstants, BrowserCacheLocation } from '../utils/BrowserConstants.js';
import { NavigationClient } from '../navigation/NavigationClient.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
// Default timeout for popup windows and iframes in milliseconds
var DEFAULT_POPUP_TIMEOUT_MS = 60000;
var DEFAULT_IFRAME_TIMEOUT_MS = 6000;
var DEFAULT_REDIRECT_TIMEOUT_MS = 30000;
var DEFAULT_NATIVE_BROKER_HANDSHAKE_TIMEOUT_MS = 2000;
/**
 * MSAL function that sets the default options when not explicitly configured from app developer
 *
 * @param auth
 * @param cache
 * @param system
 *
 * @returns Configuration object
 */
function buildConfiguration(_a, isBrowserEnvironment) {
    var userInputAuth = _a.auth, userInputCache = _a.cache, userInputSystem = _a.system, userInputTelemetry = _a.telemetry;
    // Default auth options for browser
    var DEFAULT_AUTH_OPTIONS = {
        clientId: Constants.EMPTY_STRING,
        authority: "" + Constants.DEFAULT_AUTHORITY,
        knownAuthorities: [],
        cloudDiscoveryMetadata: Constants.EMPTY_STRING,
        authorityMetadata: Constants.EMPTY_STRING,
        redirectUri: Constants.EMPTY_STRING,
        postLogoutRedirectUri: Constants.EMPTY_STRING,
        navigateToLoginRequestUrl: true,
        clientCapabilities: [],
        protocolMode: ProtocolMode.AAD,
        azureCloudOptions: {
            azureCloudInstance: AzureCloudInstance.None,
            tenant: Constants.EMPTY_STRING
        },
        skipAuthorityMetadataCache: false,
    };
    // Default cache options for browser
    var DEFAULT_CACHE_OPTIONS = {
        cacheLocation: BrowserCacheLocation.SessionStorage,
        temporaryCacheLocation: BrowserCacheLocation.SessionStorage,
        storeAuthStateInCookie: false,
        secureCookies: false,
        // Default cache migration to true if cache location is localStorage since entries are preserved across tabs/windows. Migration has little to no benefit in sessionStorage and memoryStorage
        cacheMigrationEnabled: userInputCache && userInputCache.cacheLocation === BrowserCacheLocation.LocalStorage ? true : false,
        claimsBasedCachingEnabled: true
    };
    // Default logger options for browser
    var DEFAULT_LOGGER_OPTIONS = {
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        loggerCallback: function () {
            // allow users to not set logger call back 
        },
        logLevel: LogLevel.Info,
        piiLoggingEnabled: false
    };
    // Default system options for browser
    var DEFAULT_BROWSER_SYSTEM_OPTIONS = __assign(__assign({}, DEFAULT_SYSTEM_OPTIONS), { loggerOptions: DEFAULT_LOGGER_OPTIONS, networkClient: isBrowserEnvironment ? BrowserUtils.getBrowserNetworkClient() : StubbedNetworkModule, navigationClient: new NavigationClient(), loadFrameTimeout: 0, 
        // If loadFrameTimeout is provided, use that as default.
        windowHashTimeout: (userInputSystem === null || userInputSystem === void 0 ? void 0 : userInputSystem.loadFrameTimeout) || DEFAULT_POPUP_TIMEOUT_MS, iframeHashTimeout: (userInputSystem === null || userInputSystem === void 0 ? void 0 : userInputSystem.loadFrameTimeout) || DEFAULT_IFRAME_TIMEOUT_MS, navigateFrameWait: isBrowserEnvironment && BrowserUtils.detectIEOrEdge() ? 500 : 0, redirectNavigationTimeout: DEFAULT_REDIRECT_TIMEOUT_MS, asyncPopups: false, allowRedirectInIframe: false, allowNativeBroker: false, nativeBrokerHandshakeTimeout: (userInputSystem === null || userInputSystem === void 0 ? void 0 : userInputSystem.nativeBrokerHandshakeTimeout) || DEFAULT_NATIVE_BROKER_HANDSHAKE_TIMEOUT_MS, pollIntervalMilliseconds: BrowserConstants.DEFAULT_POLL_INTERVAL_MS, cryptoOptions: {
            useMsrCrypto: false,
            entropy: undefined
        } });
    var providedSystemOptions = __assign(__assign({}, userInputSystem), { loggerOptions: (userInputSystem === null || userInputSystem === void 0 ? void 0 : userInputSystem.loggerOptions) || DEFAULT_LOGGER_OPTIONS });
    var DEFAULT_TELEMETRY_OPTIONS = {
        application: {
            appName: Constants.EMPTY_STRING,
            appVersion: Constants.EMPTY_STRING
        }
    };
    var overlayedConfig = {
        auth: __assign(__assign({}, DEFAULT_AUTH_OPTIONS), userInputAuth),
        cache: __assign(__assign({}, DEFAULT_CACHE_OPTIONS), userInputCache),
        system: __assign(__assign({}, DEFAULT_BROWSER_SYSTEM_OPTIONS), providedSystemOptions),
        telemetry: __assign(__assign({}, DEFAULT_TELEMETRY_OPTIONS), userInputTelemetry)
    };
    return overlayedConfig;
}

export { DEFAULT_IFRAME_TIMEOUT_MS, DEFAULT_NATIVE_BROKER_HANDSHAKE_TIMEOUT_MS, DEFAULT_POPUP_TIMEOUT_MS, DEFAULT_REDIRECT_TIMEOUT_MS, buildConfiguration };
//# sourceMappingURL=Configuration.js.map
