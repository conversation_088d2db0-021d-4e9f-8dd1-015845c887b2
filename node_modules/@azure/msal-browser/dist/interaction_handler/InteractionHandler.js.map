{"version": 3, "file": "InteractionHandler.js", "sources": ["../../src/interaction_handler/InteractionHandler.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthorizationCodePayload, StringUtils, CommonAuthorizationCodeRequest, AuthenticationResult, AuthorizationCodeClient, AuthorityFactory, Authority, INetworkModule, ClientAuthError, CcsCredential, Logger, ServerError, IPerformanceClient, PerformanceEvents } from \"@azure/msal-common\";\r\n\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { BrowserAuthError, BrowserAuthErrorMessage } from \"../error/BrowserAuthError\";\r\nimport { TemporaryCacheKeys } from \"../utils/BrowserConstants\";\r\n\r\nexport type InteractionParams = {};\r\n\r\n/**\r\n * Abstract class which defines operations for a browser interaction handling class.\r\n */\r\nexport class InteractionHandler {\r\n\r\n    protected authModule: AuthorizationCodeClient;\r\n    protected browserStorage: BrowserCacheManager;\r\n    protected authCodeRequest: CommonAuthorizationCodeRequest;\r\n    protected logger: Logger;\r\n    protected performanceClient: IPerformanceClient;\r\n\r\n    constructor(authCodeModule: AuthorizationCodeClient, storageImpl: BrowserCacheManager, authCodeRequest: CommonAuthorizationCodeRequest, logger: Logger, performanceClient: IPerformanceClient) {\r\n        this.authModule = authCodeModule;\r\n        this.browserStorage = storageImpl;\r\n        this.authCodeRequest = authCodeRequest;\r\n        this.logger = logger;\r\n        this.performanceClient = performanceClient;\r\n    }\r\n\r\n    /**\r\n     * Function to handle response parameters from hash.\r\n     * @param locationHash\r\n     */\r\n    async handleCodeResponseFromHash(locationHash: string, state: string, authority: Authority, networkModule: INetworkModule): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.HandleCodeResponseFromHash, this.authCodeRequest.correlationId);\r\n        this.logger.verbose(\"InteractionHandler.handleCodeResponse called\");\r\n        // Check that location hash isn't empty.\r\n        if (StringUtils.isEmpty(locationHash)) {\r\n            throw BrowserAuthError.createEmptyHashError(locationHash);\r\n        }\r\n\r\n        // Handle code response.\r\n        const stateKey = this.browserStorage.generateStateKey(state);\r\n        const requestState = this.browserStorage.getTemporaryCache(stateKey);\r\n        if (!requestState) {\r\n            throw ClientAuthError.createStateNotFoundError(\"Cached State\");\r\n        }\r\n\r\n        let authCodeResponse;\r\n        try {\r\n            authCodeResponse = this.authModule.handleFragmentResponse(locationHash, requestState);\r\n        } catch (e) {\r\n            if (e instanceof ServerError && e.subError === BrowserAuthErrorMessage.userCancelledError.code) {\r\n                // Translate server error caused by user closing native prompt to corresponding first class MSAL error\r\n                throw BrowserAuthError.createUserCancelledError();\r\n            } else {\r\n                throw e;\r\n            }\r\n        }\r\n\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.HandleCodeResponseFromServer, this.authCodeRequest.correlationId);\r\n        return this.handleCodeResponseFromServer(authCodeResponse, state, authority, networkModule);\r\n    }\r\n\r\n    /**\r\n     * Process auth code response from AAD\r\n     * @param authCodeResponse \r\n     * @param state \r\n     * @param authority \r\n     * @param networkModule \r\n     * @returns \r\n     */\r\n    async handleCodeResponseFromServer(authCodeResponse: AuthorizationCodePayload, state: string, authority: Authority, networkModule: INetworkModule, validateNonce: boolean = true): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.HandleCodeResponseFromServer, this.authCodeRequest.correlationId);\r\n        this.logger.trace(\"InteractionHandler.handleCodeResponseFromServer called\");\r\n\r\n        // Handle code response.\r\n        const stateKey = this.browserStorage.generateStateKey(state);\r\n        const requestState = this.browserStorage.getTemporaryCache(stateKey);\r\n        if (!requestState) {\r\n            throw ClientAuthError.createStateNotFoundError(\"Cached State\");\r\n        }\r\n\r\n        // Get cached items\r\n        const nonceKey = this.browserStorage.generateNonceKey(requestState);\r\n        const cachedNonce = this.browserStorage.getTemporaryCache(nonceKey);\r\n\r\n        // Assign code to request\r\n        this.authCodeRequest.code = authCodeResponse.code;\r\n\r\n        // Check for new cloud instance\r\n        if (authCodeResponse.cloud_instance_host_name) {\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.UpdateTokenEndpointAuthority, this.authCodeRequest.correlationId);\r\n            await this.updateTokenEndpointAuthority(authCodeResponse.cloud_instance_host_name, authority, networkModule);\r\n        }\r\n\r\n        // Nonce validation not needed when redirect not involved (e.g. hybrid spa, renewing token via rt)\r\n        if (validateNonce) {\r\n            authCodeResponse.nonce = cachedNonce || undefined;\r\n        }\r\n\r\n        authCodeResponse.state = requestState;\r\n\r\n        // Add CCS parameters if available\r\n        if (authCodeResponse.client_info) {\r\n            this.authCodeRequest.clientInfo = authCodeResponse.client_info;\r\n        } else {\r\n            const cachedCcsCred = this.checkCcsCredentials();\r\n            if (cachedCcsCred) {\r\n                this.authCodeRequest.ccsCredential = cachedCcsCred;\r\n            }\r\n        }\r\n\r\n        // Acquire token with retrieved code.\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.AuthClientAcquireToken, this.authCodeRequest.correlationId);\r\n        const tokenResponse = await this.authModule.acquireToken(this.authCodeRequest, authCodeResponse);\r\n        this.browserStorage.cleanRequestByState(state);\r\n        return tokenResponse;\r\n    }\r\n\r\n    /**\r\n     * Updates authority based on cloudInstanceHostname\r\n     * @param cloudInstanceHostname \r\n     * @param authority \r\n     * @param networkModule \r\n     */\r\n    protected async updateTokenEndpointAuthority(cloudInstanceHostname: string, authority: Authority, networkModule: INetworkModule): Promise<void> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.UpdateTokenEndpointAuthority, this.authCodeRequest.correlationId);\r\n        const cloudInstanceAuthorityUri = `https://${cloudInstanceHostname}/${authority.tenant}/`;\r\n        const cloudInstanceAuthority = await AuthorityFactory.createDiscoveredInstance(cloudInstanceAuthorityUri, networkModule, this.browserStorage, authority.options, this.logger, this.performanceClient, this.authCodeRequest.correlationId);\r\n        this.authModule.updateAuthority(cloudInstanceAuthority);\r\n    }\r\n\r\n    /**\r\n     * Looks up ccs creds in the cache\r\n     */\r\n    protected checkCcsCredentials(): CcsCredential | null {\r\n        // Look up ccs credential in temp cache\r\n        const cachedCcsCred = this.browserStorage.getTemporaryCache(TemporaryCacheKeys.CCS_CREDENTIAL, true);\r\n        if (cachedCcsCred) {\r\n            try {\r\n                return JSON.parse(cachedCcsCred) as CcsCredential;\r\n            } catch (e) {\r\n                this.authModule.logger.error(\"Cache credential could not be parsed\");\r\n                this.authModule.logger.errorPii(`Cache credential could not be parsed: ${cachedCcsCred}`);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAUH;;AAEG;AACH,IAAA,kBAAA,kBAAA,YAAA;IAQI,SAAY,kBAAA,CAAA,cAAuC,EAAE,WAAgC,EAAE,eAA+C,EAAE,MAAc,EAAE,iBAAqC,EAAA;AACzL,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;AACjC,QAAA,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;AAClC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AACvC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;AAGG;IACG,kBAA0B,CAAA,SAAA,CAAA,0BAAA,GAAhC,UAAiC,YAAoB,EAAE,KAAa,EAAE,SAAoB,EAAE,aAA6B,EAAA;;;;AACrH,gBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC7H,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;;AAEpE,gBAAA,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACnC,oBAAA,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAC7D,iBAAA;gBAGK,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACvD,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACrE,IAAI,CAAC,YAAY,EAAE;AACf,oBAAA,MAAM,eAAe,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;AAClE,iBAAA;gBAGD,IAAI;oBACA,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACzF,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,IAAI,CAAC,YAAY,WAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,EAAE;;AAE5F,wBAAA,MAAM,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;AACrD,qBAAA;AAAM,yBAAA;AACH,wBAAA,MAAM,CAAC,CAAC;AACX,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC3H,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAA;;;AAC/F,KAAA,CAAA;AAED;;;;;;;AAOG;IACG,kBAA4B,CAAA,SAAA,CAAA,4BAAA,GAAlC,UAAmC,gBAA0C,EAAE,KAAa,EAAE,SAAoB,EAAE,aAA6B,EAAE,aAA6B,EAAA;AAA7B,QAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAA6B,GAAA,IAAA,CAAA,EAAA;;;;;;AAC5K,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC/H,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;wBAGtE,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACvD,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACrE,IAAI,CAAC,YAAY,EAAE;AACf,4BAAA,MAAM,eAAe,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;AAClE,yBAAA;wBAGK,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;wBAC9D,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;;wBAGpE,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;6BAG9C,gBAAgB,CAAC,wBAAwB,EAAzC,OAAyC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACzC,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC3H,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,SAAS,EAAE,aAAa,CAAC,CAAA,CAAA;;AAA5G,wBAAA,EAAA,CAAA,IAAA,EAA4G,CAAC;;;;AAIjH,wBAAA,IAAI,aAAa,EAAE;AACf,4BAAA,gBAAgB,CAAC,KAAK,GAAG,WAAW,IAAI,SAAS,CAAC;AACrD,yBAAA;AAED,wBAAA,gBAAgB,CAAC,KAAK,GAAG,YAAY,CAAC;;wBAGtC,IAAI,gBAAgB,CAAC,WAAW,EAAE;4BAC9B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAClE,yBAAA;AAAM,6BAAA;AACG,4BAAA,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACjD,4BAAA,IAAI,aAAa,EAAE;AACf,gCAAA,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,aAAa,CAAC;AACtD,6BAAA;AACJ,yBAAA;;AAGD,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC/F,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAA,CAAA;;AAA1F,wBAAA,aAAa,GAAG,EAA0E,CAAA,IAAA,EAAA,CAAA;AAChG,wBAAA,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC/C,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,CAAA;;;;AACxB,KAAA,CAAA;AAED;;;;;AAKG;AACa,IAAA,kBAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5C,UAA6C,qBAA6B,EAAE,SAAoB,EAAE,aAA6B,EAAA;;;;;;AAC3H,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;wBACzH,yBAAyB,GAAG,aAAW,qBAAqB,GAAA,GAAA,GAAI,SAAS,CAAC,MAAM,MAAG,CAAC;AAC3D,wBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,wBAAwB,CAAC,yBAAyB,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAA,CAAA;;AAAnO,wBAAA,sBAAsB,GAAG,EAA0M,CAAA,IAAA,EAAA,CAAA;AACzO,wBAAA,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;;;;;AAC3D,KAAA,CAAA;AAED;;AAEG;AACO,IAAA,kBAAA,CAAA,SAAA,CAAA,mBAAmB,GAA7B,YAAA;;AAEI,QAAA,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrG,QAAA,IAAI,aAAa,EAAE;YACf,IAAI;AACA,gBAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAkB,CAAC;AACrD,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;gBACrE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,wCAAA,GAAyC,aAAe,CAAC,CAAC;AAC7F,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,kBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}