{"version": 3, "file": "SilentHandler.js", "sources": ["../../src/interaction_handler/SilentHandler.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { UrlString, StringUtils, CommonAuthorizationCodeRequest, AuthorizationCodeClient, Constants, Logger, IPerformanceClient, PerformanceEvents } from \"@azure/msal-common\";\r\nimport { InteractionHandler } from \"./InteractionHandler\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { BrowserSystemOptions, DEFAULT_IFRAME_TIMEOUT_MS } from \"../config/Configuration\";\r\n\r\nexport class SilentHandler extends InteractionHandler {\r\n\r\n    private navigateFrameWait: number;\r\n    private pollIntervalMilliseconds: number;\r\n\r\n    constructor(authCodeModule: AuthorizationCodeClient, storageImpl: BrowserCacheManager, authCodeRequest: CommonAuthorizationCodeRequest, logger: Logger, systemOptions: Required<Pick<BrowserSystemOptions, \"navigateFrameWait\" | \"pollIntervalMilliseconds\">>, performanceClient: IPerformanceClient) {\r\n        super(authCodeModule, storageImpl, authCodeRequest, logger, performanceClient);\r\n        this.navigateFrameWait = systemOptions.navigateFrameWait;\r\n        this.pollIntervalMilliseconds = systemOptions.pollIntervalMilliseconds;\r\n    }\r\n\r\n    /**\r\n     * Creates a hidden iframe to given URL using user-requested scopes as an id.\r\n     * @param urlNavigate\r\n     * @param userRequestScopes\r\n     */\r\n    async initiateAuthRequest(requestUrl: string): Promise<HTMLIFrameElement> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentHandlerInitiateAuthRequest, this.authCodeRequest.correlationId);\r\n\r\n        if (StringUtils.isEmpty(requestUrl)) {\r\n            // Throw error if request URL is empty.\r\n            this.logger.info(\"Navigate url is empty\");\r\n            throw BrowserAuthError.createEmptyNavigationUriError();\r\n        }\r\n\r\n        if (this.navigateFrameWait) {\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.SilentHandlerLoadFrame, this.authCodeRequest.correlationId);\r\n            return await this.loadFrame(requestUrl);\r\n        }\r\n        return this.loadFrameSync(requestUrl);\r\n    }\r\n\r\n    /**\r\n     * Monitors an iframe content window until it loads a url with a known hash, or hits a specified timeout.\r\n     * @param iframe\r\n     * @param timeout\r\n     */\r\n    monitorIframeForHash(iframe: HTMLIFrameElement, timeout: number): Promise<string> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentHandlerMonitorIframeForHash, this.authCodeRequest.correlationId);\r\n\r\n        return new Promise((resolve, reject) => {\r\n            if (timeout < DEFAULT_IFRAME_TIMEOUT_MS) {\r\n                this.logger.warning(`system.loadFrameTimeout or system.iframeHashTimeout set to lower (${timeout}ms) than the default (${DEFAULT_IFRAME_TIMEOUT_MS}ms). This may result in timeouts.`);\r\n            }\r\n\r\n            /*\r\n             * Polling for iframes can be purely timing based,\r\n             * since we don't need to account for interaction.\r\n             */\r\n            const nowMark = window.performance.now();\r\n            const timeoutMark = nowMark + timeout;\r\n\r\n            const intervalId = setInterval(() => {\r\n                if (window.performance.now() > timeoutMark) {\r\n                    this.removeHiddenIframe(iframe);\r\n                    clearInterval(intervalId);\r\n                    reject(BrowserAuthError.createMonitorIframeTimeoutError());\r\n                    return;\r\n                }\r\n\r\n                let href: string = Constants.EMPTY_STRING;\r\n                const contentWindow = iframe.contentWindow;\r\n                try {\r\n                    /*\r\n                     * Will throw if cross origin,\r\n                     * which should be caught and ignored\r\n                     * since we need the interval to keep running while on STS UI.\r\n                     */\r\n                    href = contentWindow ? contentWindow.location.href : Constants.EMPTY_STRING;\r\n                } catch (e) {}\r\n\r\n                if (StringUtils.isEmpty(href)) {\r\n                    return;\r\n                }\r\n\r\n                const contentHash = contentWindow ? contentWindow.location.hash: Constants.EMPTY_STRING;\r\n                if (UrlString.hashContainsKnownProperties(contentHash)) {\r\n                    // Success case\r\n                    this.removeHiddenIframe(iframe);\r\n                    clearInterval(intervalId);\r\n                    resolve(contentHash);\r\n                    return;\r\n                }\r\n            }, this.pollIntervalMilliseconds);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     * Loads iframe with authorization endpoint URL\r\n     * @ignore\r\n     */\r\n    private loadFrame(urlNavigate: string): Promise<HTMLIFrameElement> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.SilentHandlerLoadFrame, this.authCodeRequest.correlationId);\r\n\r\n        /*\r\n         * This trick overcomes iframe navigation in IE\r\n         * IE does not load the page consistently in iframe\r\n         */\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const frameHandle = this.createHiddenIframe();\r\n\r\n            setTimeout(() => {\r\n                if (!frameHandle) {\r\n                    reject(\"Unable to load iframe\");\r\n                    return;\r\n                }\r\n\r\n                frameHandle.src = urlNavigate;\r\n\r\n                resolve(frameHandle);\r\n            }, this.navigateFrameWait);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     * Loads the iframe synchronously when the navigateTimeFrame is set to `0`\r\n     * @param urlNavigate\r\n     * @param frameName\r\n     * @param logger\r\n     */\r\n    private loadFrameSync(urlNavigate: string): HTMLIFrameElement{\r\n        const frameHandle = this.createHiddenIframe();\r\n\r\n        frameHandle.src = urlNavigate;\r\n\r\n        return frameHandle;\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     * Creates a new hidden iframe or gets an existing one for silent token renewal.\r\n     * @ignore\r\n     */\r\n    private createHiddenIframe(): HTMLIFrameElement {\r\n        const authFrame = document.createElement(\"iframe\");\r\n\r\n        authFrame.className = \"msalSilentIframe\";\r\n        authFrame.style.visibility = \"hidden\";\r\n        authFrame.style.position = \"absolute\";\r\n        authFrame.style.width = authFrame.style.height = \"0\";\r\n        authFrame.style.border = \"0\";\r\n        authFrame.setAttribute(\"sandbox\", \"allow-scripts allow-same-origin allow-forms\");\r\n        document.getElementsByTagName(\"body\")[0].appendChild(authFrame);\r\n\r\n        return authFrame;\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     * Removes a hidden iframe from the page.\r\n     * @ignore\r\n     */\r\n    private removeHiddenIframe(iframe: HTMLIFrameElement): void {\r\n        if (document.body === iframe.parentNode) {\r\n            document.body.removeChild(iframe);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAQH,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;IAAmC,SAAkB,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;IAKjD,SAAY,aAAA,CAAA,cAAuC,EAAE,WAAgC,EAAE,eAA+C,EAAE,MAAc,EAAE,aAAqG,EAAE,iBAAqC,EAAA;QAApS,IACI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,iBAAiB,CAAC,IAGjF,IAAA,CAAA;AAFG,QAAA,KAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,CAAC;AACzD,QAAA,KAAI,CAAC,wBAAwB,GAAG,aAAa,CAAC,wBAAwB,CAAC;;KAC1E;AAED;;;;AAIG;IACG,aAAmB,CAAA,SAAA,CAAA,mBAAA,GAAzB,UAA0B,UAAkB,EAAA;;;;;AACxC,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAEnI,wBAAA,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;;AAEjC,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC1C,4BAAA,MAAM,gBAAgB,CAAC,6BAA6B,EAAE,CAAC;AAC1D,yBAAA;6BAEG,IAAI,CAAC,iBAAiB,EAAtB,OAAsB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACtB,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC9G,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA,CAAA;AAAvC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAgC,CAAC,CAAA;AAE5C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAA;;;;AACzC,KAAA,CAAA;AAED;;;;AAIG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,UAAqB,MAAyB,EAAE,OAAe,EAAA;QAA/D,IAgDC,KAAA,GAAA,IAAA,CAAA;AA/CG,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,iCAAiC,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAEpI,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;YAC/B,IAAI,OAAO,GAAG,yBAAyB,EAAE;gBACrC,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oEAAqE,GAAA,OAAO,GAAyB,wBAAA,GAAA,yBAAyB,GAAmC,mCAAA,CAAC,CAAC;AAC1L,aAAA;AAED;;;AAGG;YACH,IAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AACzC,YAAA,IAAM,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC;YAEtC,IAAM,UAAU,GAAG,WAAW,CAAC,YAAA;gBAC3B,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,WAAW,EAAE;AACxC,oBAAA,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;oBAChC,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,oBAAA,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,EAAE,CAAC,CAAC;oBAC3D,OAAO;AACV,iBAAA;AAED,gBAAA,IAAI,IAAI,GAAW,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAA,IAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;gBAC3C,IAAI;AACA;;;;AAIG;AACH,oBAAA,IAAI,GAAG,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC,YAAY,CAAC;AAC/E,iBAAA;gBAAC,OAAO,CAAC,EAAE,GAAE;AAEd,gBAAA,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC3B,OAAO;AACV,iBAAA;AAED,gBAAA,IAAM,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAE,SAAS,CAAC,YAAY,CAAC;AACxF,gBAAA,IAAI,SAAS,CAAC,2BAA2B,CAAC,WAAW,CAAC,EAAE;;AAEpD,oBAAA,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;oBAChC,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC1B,OAAO,CAAC,WAAW,CAAC,CAAC;oBACrB,OAAO;AACV,iBAAA;AACL,aAAC,EAAE,KAAI,CAAC,wBAAwB,CAAC,CAAC;AACtC,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;IACK,aAAS,CAAA,SAAA,CAAA,SAAA,GAAjB,UAAkB,WAAmB,EAAA;QAArC,IAsBC,KAAA,GAAA,IAAA,CAAA;AArBG,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAEzH;;;AAGG;AAEH,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AAC/B,YAAA,IAAM,WAAW,GAAG,KAAI,CAAC,kBAAkB,EAAE,CAAC;AAE9C,YAAA,UAAU,CAAC,YAAA;gBACP,IAAI,CAAC,WAAW,EAAE;oBACd,MAAM,CAAC,uBAAuB,CAAC,CAAC;oBAChC,OAAO;AACV,iBAAA;AAED,gBAAA,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC;gBAE9B,OAAO,CAAC,WAAW,CAAC,CAAC;AACzB,aAAC,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAC;AAC/B,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;;;AAMG;IACK,aAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,WAAmB,EAAA;AACrC,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE9C,QAAA,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC;AAE9B,QAAA,OAAO,WAAW,CAAC;KACtB,CAAA;AAED;;;;AAIG;AACK,IAAA,aAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,YAAA;QACI,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEnD,QAAA,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACzC,QAAA,SAAS,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACtC,QAAA,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACtC,QAAA,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AACrD,QAAA,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AAC7B,QAAA,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,6CAA6C,CAAC,CAAC;AACjF,QAAA,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAEhE,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;;AAIG;IACK,aAAkB,CAAA,SAAA,CAAA,kBAAA,GAA1B,UAA2B,MAAyB,EAAA;AAChD,QAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE;AACrC,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,SAAA;KACJ,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAhKA,CAAmC,kBAAkB,CAgKpD;;;;"}