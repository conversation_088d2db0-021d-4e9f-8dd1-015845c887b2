{"version": 3, "file": "RedirectHandler.js", "sources": ["../../src/interaction_handler/RedirectHandler.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthorizationCodeClient, StringUtils, CommonAuthorizationCodeRequest, ICrypto, AuthenticationResult, Authority, INetworkModule, ClientAuthError, Logger, ServerError, IPerformanceClient } from \"@azure/msal-common\";\r\nimport { BrowserAuthError, BrowserAuthErrorMessage } from \"../error/BrowserAuthError\";\r\nimport { ApiId, TemporaryCacheKeys } from \"../utils/BrowserConstants\";\r\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\r\nimport { InteractionHandler, InteractionParams } from \"./InteractionHandler\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { NavigationOptions } from \"../navigation/NavigationOptions\";\r\n\r\nexport type RedirectParams = InteractionParams & {\r\n    navigationClient: INavigationClient;\r\n    redirectTimeout: number;\r\n    redirectStartPage: string;\r\n    onRedirectNavigate?: (url: string) => void | boolean;\r\n};\r\n\r\nexport class RedirectHandler extends InteractionHandler {\r\n\r\n    private browserCrypto: ICrypto;\r\n\r\n    constructor(authCodeModule: AuthorizationCodeClient, storageImpl: BrowserCacheManager, authCodeRequest: CommonAuthorizationCodeRequest, logger: Logger, browserCrypto: ICrypto, performanceClient: IPerformanceClient) {\r\n        super(authCodeModule, storageImpl, authCodeRequest, logger, performanceClient);\r\n        this.browserCrypto = browserCrypto;\r\n    }\r\n\r\n    /**\r\n     * Redirects window to given URL.\r\n     * @param urlNavigate\r\n     */\r\n    async initiateAuthRequest(requestUrl: string, params: RedirectParams): Promise<void> {\r\n        this.logger.verbose(\"RedirectHandler.initiateAuthRequest called\");\r\n        // Navigate if valid URL\r\n        if (!StringUtils.isEmpty(requestUrl)) {\r\n            // Cache start page, returns to this page after redirectUri if navigateToLoginRequestUrl is true\r\n            if (params.redirectStartPage) {\r\n                this.logger.verbose(\"RedirectHandler.initiateAuthRequest: redirectStartPage set, caching start page\");\r\n                this.browserStorage.setTemporaryCache(TemporaryCacheKeys.ORIGIN_URI, params.redirectStartPage, true);\r\n            }\r\n\r\n            // Set interaction status in the library.\r\n            this.browserStorage.setTemporaryCache(TemporaryCacheKeys.CORRELATION_ID, this.authCodeRequest.correlationId, true);\r\n            this.browserStorage.cacheCodeRequest(this.authCodeRequest, this.browserCrypto);\r\n            this.logger.infoPii(`RedirectHandler.initiateAuthRequest: Navigate to: ${requestUrl}`);\r\n            const navigationOptions: NavigationOptions = {\r\n                apiId: ApiId.acquireTokenRedirect,\r\n                timeout: params.redirectTimeout,\r\n                noHistory: false\r\n            };\r\n            \r\n            // If onRedirectNavigate is implemented, invoke it and provide requestUrl\r\n            if (typeof params.onRedirectNavigate === \"function\") {\r\n                this.logger.verbose(\"RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback\");\r\n                const navigate = params.onRedirectNavigate(requestUrl);\r\n\r\n                // Returning false from onRedirectNavigate will stop navigation\r\n                if (navigate !== false) {\r\n                    this.logger.verbose(\"RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating\");\r\n                    await params.navigationClient.navigateExternal(requestUrl, navigationOptions);\r\n                    return;\r\n                } else {\r\n                    this.logger.verbose(\"RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation\");\r\n                    return;\r\n                }\r\n            } else {\r\n                // Navigate window to request URL\r\n                this.logger.verbose(\"RedirectHandler.initiateAuthRequest: Navigating window to navigate url\");\r\n                await params.navigationClient.navigateExternal(requestUrl, navigationOptions);\r\n                return;\r\n            }\r\n        } else {\r\n            // Throw error if request URL is empty.\r\n            this.logger.info(\"RedirectHandler.initiateAuthRequest: Navigate url is empty\");\r\n            throw BrowserAuthError.createEmptyNavigationUriError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Handle authorization code response in the window.\r\n     * @param hash\r\n     */\r\n    async handleCodeResponseFromHash(locationHash: string, state: string, authority: Authority, networkModule: INetworkModule): Promise<AuthenticationResult> {\r\n        this.logger.verbose(\"RedirectHandler.handleCodeResponse called\");\r\n\r\n        // Check that location hash isn't empty.\r\n        if (StringUtils.isEmpty(locationHash)) {\r\n            throw BrowserAuthError.createEmptyHashError(locationHash);\r\n        }\r\n\r\n        // Interaction is completed - remove interaction status.\r\n        this.browserStorage.setInteractionInProgress(false);\r\n\r\n        // Handle code response.\r\n        const stateKey = this.browserStorage.generateStateKey(state);\r\n        const requestState = this.browserStorage.getTemporaryCache(stateKey);\r\n        if (!requestState) {\r\n            throw ClientAuthError.createStateNotFoundError(\"Cached State\");\r\n        }\r\n\r\n        let authCodeResponse;\r\n        try {\r\n            authCodeResponse = this.authModule.handleFragmentResponse(locationHash, requestState);\r\n        } catch (e) {\r\n            if (e instanceof ServerError && e.subError === BrowserAuthErrorMessage.userCancelledError.code) {\r\n                // Translate server error caused by user closing native prompt to corresponding first class MSAL error\r\n                throw BrowserAuthError.createUserCancelledError();\r\n            } else {\r\n                throw e;\r\n            }\r\n        }\r\n\r\n        // Get cached items\r\n        const nonceKey = this.browserStorage.generateNonceKey(requestState);\r\n        const cachedNonce = this.browserStorage.getTemporaryCache(nonceKey);\r\n\r\n        // Assign code to request\r\n        this.authCodeRequest.code = authCodeResponse.code;\r\n\r\n        // Check for new cloud instance\r\n        if (authCodeResponse.cloud_instance_host_name) {\r\n            await this.updateTokenEndpointAuthority(authCodeResponse.cloud_instance_host_name, authority, networkModule);\r\n        }\r\n\r\n        authCodeResponse.nonce = cachedNonce || undefined;\r\n        authCodeResponse.state = requestState;\r\n\r\n        // Add CCS parameters if available\r\n        if (authCodeResponse.client_info) {\r\n            this.authCodeRequest.clientInfo = authCodeResponse.client_info;\r\n        } else {\r\n            const cachedCcsCred = this.checkCcsCredentials();\r\n            if (cachedCcsCred) {\r\n                this.authCodeRequest.ccsCredential = cachedCcsCred;\r\n            }\r\n        }\r\n\r\n        // Acquire token with retrieved code.\r\n        const tokenResponse = await this.authModule.acquireToken(this.authCodeRequest, authCodeResponse);\r\n\r\n        this.browserStorage.cleanRequestByState(state);\r\n        return tokenResponse;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAiBH,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAqC,SAAkB,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;IAInD,SAAY,eAAA,CAAA,cAAuC,EAAE,WAAgC,EAAE,eAA+C,EAAE,MAAc,EAAE,aAAsB,EAAE,iBAAqC,EAAA;QAArN,IACI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,iBAAiB,CAAC,IAEjF,IAAA,CAAA;AADG,QAAA,KAAI,CAAC,aAAa,GAAG,aAAa,CAAC;;KACtC;AAED;;;AAGG;AACG,IAAA,eAAA,CAAA,SAAA,CAAA,mBAAmB,GAAzB,UAA0B,UAAkB,EAAE,MAAsB,EAAA;;;;;;AAChE,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;AAE9D,wBAAA,IAAA,CAAA,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAhC,OAAgC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;wBAEhC,IAAI,MAAM,CAAC,iBAAiB,EAAE;AAC1B,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gFAAgF,CAAC,CAAC;AACtG,4BAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACxG,yBAAA;;AAGD,wBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACnH,wBAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC/E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oDAAqD,GAAA,UAAY,CAAC,CAAC;AACjF,wBAAA,iBAAiB,GAAsB;4BACzC,KAAK,EAAE,KAAK,CAAC,oBAAoB;4BACjC,OAAO,EAAE,MAAM,CAAC,eAAe;AAC/B,4BAAA,SAAS,EAAE,KAAK;yBACnB,CAAC;8BAGE,OAAO,MAAM,CAAC,kBAAkB,KAAK,UAAU,CAAA,EAA/C,OAA+C,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC/C,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2EAA2E,CAAC,CAAC;AAC3F,wBAAA,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAGnD,wBAAA,IAAA,EAAA,QAAQ,KAAK,KAAK,CAAA,EAAlB,OAAkB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAClB,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0FAA0F,CAAC,CAAC;wBAChH,OAAM,CAAA,CAAA,YAAA,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAA,CAAA;;AAA7E,wBAAA,EAAA,CAAA,IAAA,EAA6E,CAAC;wBAC9E,OAAO,CAAA,CAAA,YAAA,CAAA;;AAEP,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6FAA6F,CAAC,CAAC;wBACnH,OAAO,CAAA,CAAA,YAAA,CAAA;;;;AAIX,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wEAAwE,CAAC,CAAC;wBAC9F,OAAM,CAAA,CAAA,YAAA,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAA,CAAA;;AAA7E,wBAAA,EAAA,CAAA,IAAA,EAA6E,CAAC;wBAC9E,OAAO,CAAA,CAAA,YAAA,CAAA;;;;AAIX,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;AAC/E,wBAAA,MAAM,gBAAgB,CAAC,6BAA6B,EAAE,CAAC;;;;;AAE9D,KAAA,CAAA;AAED;;;AAGG;IACG,eAA0B,CAAA,SAAA,CAAA,0BAAA,GAAhC,UAAiC,YAAoB,EAAE,KAAa,EAAE,SAAoB,EAAE,aAA6B,EAAA;;;;;;AACrH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;;AAGjE,wBAAA,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACnC,4BAAA,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAC7D,yBAAA;;AAGD,wBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;wBAG9C,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACvD,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACrE,IAAI,CAAC,YAAY,EAAE;AACf,4BAAA,MAAM,eAAe,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;AAClE,yBAAA;wBAGD,IAAI;4BACA,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACzF,yBAAA;AAAC,wBAAA,OAAO,CAAC,EAAE;AACR,4BAAA,IAAI,CAAC,YAAY,WAAW,IAAI,CAAC,CAAC,QAAQ,KAAK,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,EAAE;;AAE5F,gCAAA,MAAM,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;AACrD,6BAAA;AAAM,iCAAA;AACH,gCAAA,MAAM,CAAC,CAAC;AACX,6BAAA;AACJ,yBAAA;wBAGK,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;wBAC9D,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;;wBAGpE,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;6BAG9C,gBAAgB,CAAC,wBAAwB,EAAzC,OAAyC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACzC,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,SAAS,EAAE,aAAa,CAAC,CAAA,CAAA;;AAA5G,wBAAA,EAAA,CAAA,IAAA,EAA4G,CAAC;;;AAGjH,wBAAA,gBAAgB,CAAC,KAAK,GAAG,WAAW,IAAI,SAAS,CAAC;AAClD,wBAAA,gBAAgB,CAAC,KAAK,GAAG,YAAY,CAAC;;wBAGtC,IAAI,gBAAgB,CAAC,WAAW,EAAE;4BAC9B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAClE,yBAAA;AAAM,6BAAA;AACG,4BAAA,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACjD,4BAAA,IAAI,aAAa,EAAE;AACf,gCAAA,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,aAAa,CAAC;AACtD,6BAAA;AACJ,yBAAA;AAGqB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAA,CAAA;;AAA1F,wBAAA,aAAa,GAAG,EAA0E,CAAA,IAAA,EAAA,CAAA;AAEhG,wBAAA,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC/C,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,CAAA;;;;AACxB,KAAA,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CA7HA,CAAqC,kBAAkB,CA6HtD;;;;"}