/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __extends } from '../_virtual/_tslib.js';
import { InteractionRequiredAuthError, AuthError } from '@azure/msal-common';
import { BrowserAuthError } from './BrowserAuthError.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var NativeStatusCode;
(function (NativeStatusCode) {
    NativeStatusCode["USER_INTERACTION_REQUIRED"] = "USER_INTERACTION_REQUIRED";
    NativeStatusCode["USER_CANCEL"] = "USER_CANCEL";
    NativeStatusCode["NO_NETWORK"] = "NO_NETWORK";
    NativeStatusCode["TRANSIENT_ERROR"] = "TRANSIENT_ERROR";
    NativeStatusCode["PERSISTENT_ERROR"] = "PERSISTENT_ERROR";
    NativeStatusCode["DISABLED"] = "DISABLED";
    NativeStatusCode["ACCOUNT_UNAVAILABLE"] = "ACCOUNT_UNAVAILABLE";
})(NativeStatusCode || (NativeStatusCode = {}));
var NativeAuthErrorMessage = {
    extensionError: {
        code: "ContentError"
    },
    userSwitch: {
        code: "user_switch",
        desc: "User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."
    },
    tokensNotFoundInCache: {
        code: "tokens_not_found_in_internal_memory_cache",
        desc: "Tokens not cached in MSAL JS internal memory, please make the WAM request"
    }
};
var NativeAuthError = /** @class */ (function (_super) {
    __extends(NativeAuthError, _super);
    function NativeAuthError(errorCode, description, ext) {
        var _this = _super.call(this, errorCode, description) || this;
        Object.setPrototypeOf(_this, NativeAuthError.prototype);
        _this.name = "NativeAuthError";
        _this.ext = ext;
        return _this;
    }
    /**
     * These errors should result in a fallback to the 'standard' browser based auth flow.
     */
    NativeAuthError.prototype.isFatal = function () {
        if (this.ext && this.ext.status && (this.ext.status === NativeStatusCode.PERSISTENT_ERROR || this.ext.status === NativeStatusCode.DISABLED)) {
            return true;
        }
        switch (this.errorCode) {
            case NativeAuthErrorMessage.extensionError.code:
                return true;
            default:
                return false;
        }
    };
    /**
     * Create the appropriate error object based on the WAM status code.
     * @param code
     * @param description
     * @param ext
     * @returns
     */
    NativeAuthError.createError = function (code, description, ext) {
        if (ext && ext.status) {
            switch (ext.status) {
                case NativeStatusCode.ACCOUNT_UNAVAILABLE:
                    return InteractionRequiredAuthError.createNativeAccountUnavailableError();
                case NativeStatusCode.USER_INTERACTION_REQUIRED:
                    return new InteractionRequiredAuthError(code, description);
                case NativeStatusCode.USER_CANCEL:
                    return BrowserAuthError.createUserCancelledError();
                case NativeStatusCode.NO_NETWORK:
                    return BrowserAuthError.createNoNetworkConnectivityError();
            }
        }
        return new NativeAuthError(code, description, ext);
    };
    /**
     * Creates user switch error when the user chooses a different account in the native broker prompt
     * @returns
     */
    NativeAuthError.createUserSwitchError = function () {
        return new NativeAuthError(NativeAuthErrorMessage.userSwitch.code, NativeAuthErrorMessage.userSwitch.desc);
    };
    /**
     * Creates a tokens not found error when the internal cache look up fails
     * @returns NativeAuthError: tokensNotFoundInCache
     */
    NativeAuthError.createTokensNotFoundInCacheError = function () {
        return new NativeAuthError(NativeAuthErrorMessage.tokensNotFoundInCache.code, NativeAuthErrorMessage.tokensNotFoundInCache.desc);
    };
    return NativeAuthError;
}(AuthError));

export { NativeAuthError, NativeAuthErrorMessage, NativeStatusCode };
//# sourceMappingURL=NativeAuthError.js.map
