{"version": 3, "file": "BrowserAuthError.d.ts", "sourceRoot": "", "sources": ["../../src/error/BrowserAuthError.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,SAAS,EAAe,MAAM,oBAAoB,CAAC;AAE5D;;GAEG;AACH,eAAO,MAAM,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqLnC,CAAC;AAEF;;GAEG;AACH,qBAAa,gBAAiB,SAAQ,SAAS;gBAE/B,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM;IAOpD;;;OAGG;IACH,MAAM,CAAC,2BAA2B,CAAC,SAAS,EAAE,MAAM,GAAG,gBAAgB;IAKvE;;;OAGG;IACH,MAAM,CAAC,6BAA6B,CAAC,SAAS,EAAE,MAAM,GAAG,gBAAgB;IAKzE;;;OAGG;IACH,MAAM,CAAC,mCAAmC,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB;IAK5E;;OAEG;IACH,MAAM,CAAC,6BAA6B,IAAI,gBAAgB;IAIxD;;;OAGG;IACH,MAAM,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,GAAG,gBAAgB;IAIhE;;OAEG;IACH,MAAM,CAAC,kCAAkC,IAAI,gBAAgB;IAI7D;;OAEG;IACH,MAAM,CAAC,4CAA4C,IAAI,gBAAgB;IAIvE;;OAEG;IACH,MAAM,CAAC,6BAA6B,IAAI,gBAAgB;IAIxD;;OAEG;IACH,MAAM,CAAC,uCAAuC,IAAI,gBAAgB;IAIlE;;OAEG;IACH,MAAM,CAAC,gCAAgC,IAAI,gBAAgB;IAI3D;;;OAGG;IACH,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,gBAAgB;IAMnE;;;OAGG;IACH,MAAM,CAAC,6BAA6B,IAAI,gBAAgB;IAIxD;;OAEG;IACH,MAAM,CAAC,wBAAwB,IAAI,gBAAgB;IAKnD;;OAEG;IACH,MAAM,CAAC,8BAA8B,IAAI,gBAAgB;IAKzD;;OAEG;IACH,MAAM,CAAC,+BAA+B,IAAI,gBAAgB;IAK1D;;;OAGG;IACH,MAAM,CAAC,2BAA2B,CAAC,iBAAiB,EAAE,OAAO,GAAG,gBAAgB;IAKhF;;OAEG;IACH,MAAM,CAAC,oCAAoC,IAAI,gBAAgB;IAK/D;;;OAGG;IACH,MAAM,CAAC,oCAAoC,IAAI,gBAAgB;IAK/D;;OAEG;IACH,MAAM,CAAC,kCAAkC,IAAI,gBAAgB;IAI7D;;OAEG;IACH,MAAM,CAAC,kCAAkC,IAAI,gBAAgB;IAI7D;;OAEG;IACH,MAAM,CAAC,oBAAoB,IAAI,gBAAgB;IAI/C;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,WAAW,EAAE,MAAM,GAAG,gBAAgB;IAI1E;;OAEG;IACH,MAAM,CAAC,yCAAyC,IAAI,gBAAgB;IAKpE;;OAEG;IACH,MAAM,CAAC,8BAA8B,IAAI,gBAAgB;IAKzD;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,gBAAgB;IAKvD;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,gBAAgB;IAKvD;;OAEG;IACH,MAAM,CAAC,2BAA2B,IAAI,gBAAgB;IAItD;;OAEG;IACH,MAAM,CAAC,gCAAgC,IAAI,gBAAgB;IAI3D;;OAEG;IACH,MAAM,CAAC,0BAA0B,IAAI,gBAAgB;IAIrD;;OAEG;IACH,MAAM,CAAC,gCAAgC,IAAI,gBAAgB;IAI3D;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,gBAAgB;IAI1F;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,gBAAgB;IAIzF;;OAEG;IACH,MAAM,CAAC,uCAAuC,CAAC,QAAQ,EAAE,MAAM,GAAG,gBAAgB;IAIlF;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,WAAW,EAAE,MAAM,GAAG,gBAAgB;IAI1E;;OAEG;IACH,MAAM,CAAC,sCAAsC,CAAC,KAAK,EAAE,MAAM,GAAG,gBAAgB;IAI9E;;OAEG;IACH,MAAM,CAAC,2BAA2B,IAAI,gBAAgB;IAItD;;OAEG;IACH,MAAM,CAAC,4CAA4C,IAAI,gBAAgB;IAIvE;;OAEG;IACH,MAAM,CAAC,2CAA2C,IAAI,gBAAgB;IAItE;;OAEG;IACH,MAAM,CAAC,8BAA8B,IAAI,gBAAgB;IAIzD;;OAEG;IACH,MAAM,CAAC,iDAAiD,IAAI,gBAAgB;IAI5E;;OAEG;IACH,MAAM,CAAC,iCAAiC,IAAI,gBAAgB;IAI5D;;OAEG;IACH,MAAM,CAAC,sCAAsC,IAAI,gBAAgB;IAIjE;;;OAGG;IACH,MAAM,CAAC,yCAAyC,IAAI,gBAAgB;IAIpE;;OAEG;IACH,MAAM,CAAC,wCAAwC,IAAI,gBAAgB;IAInE;;;OAGG;IACH,MAAM,CAAC,4CAA4C,IAAI,gBAAgB;CAG1E"}