{"version": 3, "file": "BrowserConfigurationAuthError.js", "sources": ["../../src/error/BrowserConfigurationAuthError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"@azure/msal-common\";\r\n\r\n/**\r\n * BrowserAuthErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const BrowserConfigurationAuthErrorMessage = {\r\n    redirectUriNotSet: {\r\n        code: \"redirect_uri_empty\",\r\n        desc: \"A redirect URI is required for all calls, and none has been set.\"\r\n    },\r\n    postLogoutUriNotSet: {\r\n        code: \"post_logout_uri_empty\",\r\n        desc: \"A post logout redirect has not been set.\"\r\n    },\r\n    storageNotSupportedError: {\r\n        code: \"storage_not_supported\",\r\n        desc: \"Given storage configuration option was not supported.\"\r\n    },\r\n    noRedirectCallbacksSet: {\r\n        code: \"no_redirect_callbacks\",\r\n        desc: \"No redirect callbacks have been set. Please call setRedirectCallbacks() with the appropriate function arguments before continuing. \" +\r\n            \"More information is available here: https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics.\"\r\n    },\r\n    invalidCallbackObject: {\r\n        code: \"invalid_callback_object\",\r\n        desc: \"The object passed for the callback was invalid. \" +\r\n          \"More information is available here: https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics.\"\r\n    },\r\n    stubPcaInstanceCalled: {\r\n        code: \"stubbed_public_client_application_called\",\r\n        desc: \"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors\"\r\n    },\r\n    inMemRedirectUnavailable: {\r\n        code: \"in_mem_redirect_unavailable\",\r\n        desc: \"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true.\"\r\n    },\r\n    entropyNotProvided: {\r\n        code: \"entropy_not_provided\",\r\n        desc: \"The available browser crypto interface requires entropy set via system.cryptoOptions.entropy configuration option.\"\r\n    }\r\n};\r\n\r\n/**\r\n * Browser library error class thrown by the MSAL.js library for SPAs\r\n */\r\nexport class BrowserConfigurationAuthError extends AuthError {\r\n\r\n    constructor(errorCode: string, errorMessage?: string) {\r\n        super(errorCode, errorMessage);\r\n        this.name = \"BrowserConfigurationAuthError\";\r\n\r\n        Object.setPrototypeOf(this, BrowserConfigurationAuthError.prototype);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the redirect uri is empty (not set by caller)\r\n     */\r\n    static createRedirectUriEmptyError(): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.redirectUriNotSet.code,\r\n            BrowserConfigurationAuthErrorMessage.redirectUriNotSet.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the post-logout redirect uri is empty (not set by caller)\r\n     */\r\n    static createPostLogoutRedirectUriEmptyError(): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.postLogoutUriNotSet.code,\r\n            BrowserConfigurationAuthErrorMessage.postLogoutUriNotSet.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates error thrown when given storage location is not supported.\r\n     * @param givenStorageLocation \r\n     */\r\n    static createStorageNotSupportedError(givenStorageLocation: string): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.storageNotSupportedError.code, `${BrowserConfigurationAuthErrorMessage.storageNotSupportedError.desc} Given Location: ${givenStorageLocation}`);\r\n    }\r\n\r\n    /**\r\n     * Creates error thrown when redirect callbacks are not set before calling loginRedirect() or acquireTokenRedirect().\r\n     */\r\n    static createRedirectCallbacksNotSetError(): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.noRedirectCallbacksSet.code, \r\n            BrowserConfigurationAuthErrorMessage.noRedirectCallbacksSet.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates error thrown when the stub instance of PublicClientApplication is called.\r\n     */\r\n    static createStubPcaInstanceCalledError(): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.stubPcaInstanceCalled.code,\r\n            BrowserConfigurationAuthErrorMessage.stubPcaInstanceCalled.desc);\r\n    }\r\n\r\n    /*\r\n     * Create an error thrown when in-memory storage is used and storeAuthStateInCookie=false.\r\n     */\r\n    static createInMemoryRedirectUnavailableError(): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.inMemRedirectUnavailable.code, BrowserConfigurationAuthErrorMessage.inMemRedirectUnavailable.desc);\r\n    }\r\n    \r\n    /**\r\n     * Creates an error thrown when a crypto interface that requires entropy is initialized without entropy\r\n     */\r\n    static createEntropyNotProvided(): BrowserConfigurationAuthError {\r\n        return new BrowserConfigurationAuthError(BrowserConfigurationAuthErrorMessage.entropyNotProvided.code, BrowserConfigurationAuthErrorMessage.entropyNotProvided.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,IAAA,oCAAoC,GAAG;AAChD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,kEAAkE;AAC3E,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,IAAI,EAAE,0CAA0C;AACnD,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,IAAI,EAAE,uDAAuD;AAChE,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,IAAI,EAAE,qIAAqI;YACvI,0HAA0H;AACjI,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,kDAAkD;YACtD,0HAA0H;AAC/H,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,0CAA0C;AAChD,QAAA,IAAI,EAAE,gLAAgL;AACzL,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,6BAA6B;AACnC,QAAA,IAAI,EAAE,uRAAuR;AAChS,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,oHAAoH;AAC7H,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,6BAAA,kBAAA,UAAA,MAAA,EAAA;IAAmD,SAAS,CAAA,6BAAA,EAAA,MAAA,CAAA,CAAA;IAExD,SAAY,6BAAA,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAApD,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,SAAS,EAAE,YAAY,CAAC,IAIjC,IAAA,CAAA;AAHG,QAAA,KAAI,CAAC,IAAI,GAAG,+BAA+B,CAAC;QAE5C,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,6BAA6B,CAAC,SAAS,CAAC,CAAC;;KACxE;AAED;;AAEG;AACI,IAAA,6BAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,iBAAiB,CAAC,IAAI,EAChG,oCAAoC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACpE,CAAA;AAED;;AAEG;AACI,IAAA,6BAAA,CAAA,qCAAqC,GAA5C,YAAA;AACI,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,mBAAmB,CAAC,IAAI,EAClG,oCAAoC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACtE,CAAA;AAED;;;AAGG;IACI,6BAA8B,CAAA,8BAAA,GAArC,UAAsC,oBAA4B,EAAA;AAC9D,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,wBAAwB,CAAC,IAAI,EAAK,oCAAoC,CAAC,wBAAwB,CAAC,IAAI,GAAoB,mBAAA,GAAA,oBAAsB,CAAC,CAAC;KACjO,CAAA;AAED;;AAEG;AACI,IAAA,6BAAA,CAAA,kCAAkC,GAAzC,YAAA;AACI,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,sBAAsB,CAAC,IAAI,EACrG,oCAAoC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;KACzE,CAAA;AAED;;AAEG;AACI,IAAA,6BAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,qBAAqB,CAAC,IAAI,EACpG,oCAAoC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACxE,CAAA;AAED;;AAEG;AACI,IAAA,6BAAA,CAAA,sCAAsC,GAA7C,YAAA;AACI,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,wBAAwB,CAAC,IAAI,EAAE,oCAAoC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KACpL,CAAA;AAED;;AAEG;AACI,IAAA,6BAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,6BAA6B,CAAC,oCAAoC,CAAC,kBAAkB,CAAC,IAAI,EAAE,oCAAoC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACxK,CAAA;IACL,OAAC,6BAAA,CAAA;AAAD,CA9DA,CAAmD,SAAS,CA8D3D;;;;"}