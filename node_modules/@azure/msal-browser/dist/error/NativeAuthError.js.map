{"version": 3, "file": "NativeAuthError.js", "sources": ["../../src/error/NativeAuthError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError, InteractionRequiredAuthError } from \"@azure/msal-common\";\r\nimport { BrowserAuthError } from \"./BrowserAuthError\";\r\n\r\nexport type OSError = {\r\n    error: number;\r\n    protocol_error: string;\r\n    properties: object;\r\n    status: string;\r\n    retryable?: boolean;\r\n};\r\n\r\nexport enum NativeStatusCode {\r\n    USER_INTERACTION_REQUIRED = \"USER_INTERACTION_REQUIRED\",\r\n    USER_CANCEL = \"USER_CANCEL\",\r\n    NO_NETWORK = \"NO_NETWORK\",\r\n    TRANSIENT_ERROR = \"TRANSIENT_ERROR\",\r\n    PERSISTENT_ERROR = \"PERSISTENT_ERROR\",\r\n    DISABLED = \"DISABLED\",\r\n    ACCOUNT_UNAVAILABLE = \"ACCOUNT_UNAVAILABLE\"\r\n}\r\n\r\nexport const NativeAuthErrorMessage = {\r\n    extensionError: {\r\n        code: \"ContentError\"\r\n    },\r\n    userSwitch: {\r\n        code: \"user_switch\",\r\n        desc: \"User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again.\"\r\n    },\r\n    tokensNotFoundInCache: {\r\n        code: \"tokens_not_found_in_internal_memory_cache\",\r\n        desc: \"Tokens not cached in MSAL JS internal memory, please make the WAM request\"\r\n    }\r\n};\r\n\r\nexport class NativeAuthError extends AuthError {\r\n    ext: OSError | undefined;\r\n\r\n    constructor(errorCode: string, description: string, ext?: OSError) {\r\n        super(errorCode, description);\r\n\r\n        Object.setPrototypeOf(this, NativeAuthError.prototype);\r\n        this.name = \"NativeAuthError\";\r\n        this.ext = ext;\r\n    }\r\n\r\n    /**\r\n     * These errors should result in a fallback to the 'standard' browser based auth flow.\r\n     */\r\n    isFatal(): boolean {\r\n        if (this.ext && this.ext.status && (this.ext.status === NativeStatusCode.PERSISTENT_ERROR || this.ext.status === NativeStatusCode.DISABLED)) {\r\n            return true;\r\n        }\r\n\r\n        switch (this.errorCode) {\r\n            case NativeAuthErrorMessage.extensionError.code:\r\n                return true;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create the appropriate error object based on the WAM status code.\r\n     * @param code\r\n     * @param description\r\n     * @param ext\r\n     * @returns\r\n     */\r\n    static createError(code: string, description: string, ext?: OSError): AuthError {\r\n        if (ext && ext.status) {\r\n            switch (ext.status) {\r\n                case NativeStatusCode.ACCOUNT_UNAVAILABLE:\r\n                    return InteractionRequiredAuthError.createNativeAccountUnavailableError();\r\n                case NativeStatusCode.USER_INTERACTION_REQUIRED:\r\n                    return new InteractionRequiredAuthError(code, description);\r\n                case NativeStatusCode.USER_CANCEL:\r\n                    return BrowserAuthError.createUserCancelledError();\r\n                case NativeStatusCode.NO_NETWORK:\r\n                    return BrowserAuthError.createNoNetworkConnectivityError();\r\n            }\r\n        }\r\n\r\n        return new NativeAuthError(code, description, ext);\r\n    }\r\n\r\n    /**\r\n     * Creates user switch error when the user chooses a different account in the native broker prompt\r\n     * @returns\r\n     */\r\n    static createUserSwitchError(): NativeAuthError {\r\n        return new NativeAuthError(NativeAuthErrorMessage.userSwitch.code, NativeAuthErrorMessage.userSwitch.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates a tokens not found error when the internal cache look up fails\r\n     * @returns NativeAuthError: tokensNotFoundInCache\r\n     */\r\n    static createTokensNotFoundInCacheError(): NativeAuthError {\r\n        return new NativeAuthError(NativeAuthErrorMessage.tokensNotFoundInCache.code, NativeAuthErrorMessage.tokensNotFoundInCache.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;IAaS,iBAQX;AARD,CAAA,UAAY,gBAAgB,EAAA;AACxB,IAAA,gBAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;AACvD,IAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,gBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,gBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,gBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC/C,CAAC,EARW,gBAAgB,KAAhB,gBAAgB,GAQ3B,EAAA,CAAA,CAAA,CAAA;AAEY,IAAA,sBAAsB,GAAG;AAClC,IAAA,cAAc,EAAE;AACZ,QAAA,IAAI,EAAE,cAAc;AACvB,KAAA;AACD,IAAA,UAAU,EAAE;AACR,QAAA,IAAI,EAAE,aAAa;AACnB,QAAA,IAAI,EAAE,oKAAoK;AAC7K,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,2CAA2C;AACjD,QAAA,IAAI,EAAE,2EAA2E;AACpF,KAAA;EACH;AAEF,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAqC,SAAS,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;AAG1C,IAAA,SAAA,eAAA,CAAY,SAAiB,EAAE,WAAmB,EAAE,GAAa,EAAA;AAAjE,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,SAAS,EAAE,WAAW,CAAC,IAKhC,IAAA,CAAA;QAHG,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;AACvD,QAAA,KAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC9B,QAAA,KAAI,CAAC,GAAG,GAAG,GAAG,CAAC;;KAClB;AAED;;AAEG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,gBAAgB,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AACzI,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,QAAQ,IAAI,CAAC,SAAS;AAClB,YAAA,KAAK,sBAAsB,CAAC,cAAc,CAAC,IAAI;AAC3C,gBAAA,OAAO,IAAI,CAAC;AAChB,YAAA;AACI,gBAAA,OAAO,KAAK,CAAC;AACpB,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;AACI,IAAA,eAAA,CAAA,WAAW,GAAlB,UAAmB,IAAY,EAAE,WAAmB,EAAE,GAAa,EAAA;AAC/D,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;YACnB,QAAQ,GAAG,CAAC,MAAM;gBACd,KAAK,gBAAgB,CAAC,mBAAmB;AACrC,oBAAA,OAAO,4BAA4B,CAAC,mCAAmC,EAAE,CAAC;gBAC9E,KAAK,gBAAgB,CAAC,yBAAyB;AAC3C,oBAAA,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC/D,KAAK,gBAAgB,CAAC,WAAW;AAC7B,oBAAA,OAAO,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;gBACvD,KAAK,gBAAgB,CAAC,UAAU;AAC5B,oBAAA,OAAO,gBAAgB,CAAC,gCAAgC,EAAE,CAAC;AAClE,aAAA;AACJ,SAAA;QAED,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;KACtD,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,qBAAqB,GAA5B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,IAAI,EAAE,sBAAsB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAC9G,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACpI,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAlEA,CAAqC,SAAS,CAkE7C;;;;"}