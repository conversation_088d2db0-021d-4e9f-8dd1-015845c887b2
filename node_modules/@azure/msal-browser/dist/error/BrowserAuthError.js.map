{"version": 3, "file": "BrowserAuthError.js", "sources": ["../../src/error/BrowserAuthError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError, StringUtils } from \"@azure/msal-common\";\r\n\r\n/**\r\n * BrowserAuthErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const BrowserAuthErrorMessage = {\r\n    pkceNotGenerated: {\r\n        code: \"pkce_not_created\",\r\n        desc: \"The PKCE code challenge and verifier could not be generated.\"\r\n    },\r\n    cryptoDoesNotExist: {\r\n        code: \"crypto_nonexistent\",\r\n        desc: \"The crypto object or function is not available.\"\r\n    },\r\n    httpMethodNotImplementedError: {\r\n        code: \"http_method_not_implemented\",\r\n        desc: \"The HTTP method given has not been implemented in this library.\"\r\n    },\r\n    emptyNavigateUriError: {\r\n        code: \"empty_navigate_uri\",\r\n        desc: \"Navigation URI is empty. Please check stack trace for more info.\"\r\n    },\r\n    hashEmptyError: {\r\n        code: \"hash_empty_error\",\r\n        desc: \"Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. For more visit: aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    hashDoesNotContainStateError: {\r\n        code: \"no_state_in_hash\",\r\n        desc: \"Hash does not contain state. Please verify that the request originated from msal.\"\r\n    },\r\n    hashDoesNotContainKnownPropertiesError: {\r\n        code: \"hash_does_not_contain_known_properties\",\r\n        desc: \"Hash does not contain known properites. Please verify that your redirectUri is not changing the hash. For more visit: aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    unableToParseStateError: {\r\n        code: \"unable_to_parse_state\",\r\n        desc: \"Unable to parse state. Please verify that the request originated from msal.\"\r\n    },\r\n    stateInteractionTypeMismatchError: {\r\n        code: \"state_interaction_type_mismatch\",\r\n        desc: \"Hash contains state but the interaction type does not match the caller.\"\r\n    },\r\n    interactionInProgress: {\r\n        code: \"interaction_in_progress\",\r\n        desc: \"Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.  For more visit: aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    popupWindowError: {\r\n        code: \"popup_window_error\",\r\n        desc: \"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.\"\r\n    },\r\n    emptyWindowError: {\r\n        code: \"empty_window_error\",\r\n        desc: \"window.open returned null or undefined window object.\"\r\n    },\r\n    userCancelledError: {\r\n        code: \"user_cancelled\",\r\n        desc: \"User cancelled the flow.\"\r\n    },\r\n    monitorPopupTimeoutError: {\r\n        code: \"monitor_window_timeout\",\r\n        desc: \"Token acquisition in popup failed due to timeout. For more visit: aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    monitorIframeTimeoutError: {\r\n        code: \"monitor_window_timeout\",\r\n        desc: \"Token acquisition in iframe failed due to timeout. For more visit: aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    redirectInIframeError: {\r\n        code: \"redirect_in_iframe\",\r\n        desc: \"Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs.\"\r\n    },\r\n    blockTokenRequestsInHiddenIframeError: {\r\n        code: \"block_iframe_reload\",\r\n        desc: \"Request was blocked inside an iframe because MSAL detected an authentication response. For more visit: aka.ms/msaljs/browser-errors\"\r\n    },\r\n    blockAcquireTokenInPopupsError: {\r\n        code: \"block_nested_popups\",\r\n        desc: \"Request was blocked inside a popup because MSAL detected it was running in a popup.\"\r\n    },\r\n    iframeClosedPrematurelyError: {\r\n        code: \"iframe_closed_prematurely\",\r\n        desc: \"The iframe being monitored was closed prematurely.\"\r\n    },\r\n    silentLogoutUnsupportedError: {\r\n        code: \"silent_logout_unsupported\",\r\n        desc: \"Silent logout not supported. Please call logoutRedirect or logoutPopup instead.\"\r\n    },\r\n    noAccountError: {\r\n        code: \"no_account_error\",\r\n        desc: \"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request.\"\r\n    },\r\n    silentPromptValueError: {\r\n        code: \"silent_prompt_value_error\",\r\n        desc: \"The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'.\"\r\n    },\r\n    noTokenRequestCacheError: {\r\n        code: \"no_token_request_cache_error\",\r\n        desc: \"No token request found in cache.\"\r\n    },\r\n    unableToParseTokenRequestCacheError: {\r\n        code: \"unable_to_parse_token_request_cache_error\",\r\n        desc: \"The cached token request could not be parsed.\"\r\n    },\r\n    noCachedAuthorityError: {\r\n        code: \"no_cached_authority_error\",\r\n        desc: \"No cached authority found.\"\r\n    },\r\n    authRequestNotSet: {\r\n        code: \"auth_request_not_set_error\",\r\n        desc: \"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler\"\r\n    },\r\n    invalidCacheType: {\r\n        code: \"invalid_cache_type\",\r\n        desc: \"Invalid cache type\"\r\n    },\r\n    notInBrowserEnvironment: {\r\n        code: \"non_browser_environment\",\r\n        desc: \"Login and token requests are not supported in non-browser environments.\"\r\n    },\r\n    databaseNotOpen: {\r\n        code: \"database_not_open\",\r\n        desc: \"Database is not open!\"\r\n    },\r\n    noNetworkConnectivity: {\r\n        code: \"no_network_connectivity\",\r\n        desc: \"No network connectivity. Check your internet connection.\"\r\n    },\r\n    postRequestFailed: {\r\n        code: \"post_request_failed\",\r\n        desc: \"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'\"\r\n    },\r\n    getRequestFailed: {\r\n        code: \"get_request_failed\",\r\n        desc: \"Network request failed. Please check the network trace to determine root cause.\"\r\n    },\r\n    failedToParseNetworkResponse: {\r\n        code: \"failed_to_parse_response\",\r\n        desc: \"Failed to parse network response. Check network trace.\"\r\n    },\r\n    unableToLoadTokenError: {\r\n        code: \"unable_to_load_token\",\r\n        desc: \"Error loading token to cache.\"\r\n    },\r\n    signingKeyNotFoundInStorage: {\r\n        code: \"crypto_key_not_found\",\r\n        desc: \"Cryptographic Key or Keypair not found in browser storage.\"\r\n    },\r\n    authCodeRequired: {\r\n        code: \"auth_code_required\",\r\n        desc: \"An authorization code must be provided (as the `code` property on the request) to this flow.\"\r\n    },\r\n    authCodeOrNativeAccountRequired: {\r\n        code: \"auth_code_or_nativeAccountId_required\",\r\n        desc: \"An authorization code or nativeAccountId must be provided to this flow.\"\r\n    },\r\n    spaCodeAndNativeAccountPresent: {\r\n        code: \"spa_code_and_nativeAccountId_present\",\r\n        desc: \"Request cannot contain both spa code and native account id.\"\r\n    },\r\n    databaseUnavailable: {\r\n        code: \"database_unavailable\",\r\n        desc: \"IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts.\"\r\n    },\r\n    unableToAcquireTokenFromNativePlatform: {\r\n        code: \"unable_to_acquire_token_from_native_platform\",\r\n        desc: \"Unable to acquire token from native platform. For a list of possible reasons visit aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    nativeHandshakeTimeout: {\r\n        code: \"native_handshake_timeout\",\r\n        desc: \"Timed out while attempting to establish connection to browser extension\"\r\n    },\r\n    nativeExtensionNotInstalled: {\r\n        code: \"native_extension_not_installed\",\r\n        desc: \"Native extension is not installed. If you think this is a mistake call the initialize function.\"\r\n    },\r\n    nativeConnectionNotEstablished: {\r\n        code: \"native_connection_not_established\",\r\n        desc: \"Connection to native platform has not been established. Please install a compatible browser extension and run initialize(). For more please visit aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    nativeBrokerCalledBeforeInitialize: {\r\n        code: \"native_broker_called_before_initialize\",\r\n        desc: \"You must call and await the initialize function before attempting to call any other MSAL API when native brokering is enabled. For more please visit aka.ms/msaljs/browser-errors.\"\r\n    },\r\n    nativePromptNotSupported: {\r\n        code: \"native_prompt_not_supported\",\r\n        desc: \"The provided prompt is not supported by the native platform. This request should be routed to the web based flow.\"\r\n    }\r\n};\r\n\r\n/**\r\n * Browser library error class thrown by the MSAL.js library for SPAs\r\n */\r\nexport class BrowserAuthError extends AuthError {\r\n\r\n    constructor(errorCode: string, errorMessage?: string) {\r\n        super(errorCode, errorMessage);\r\n\r\n        Object.setPrototypeOf(this, BrowserAuthError.prototype);\r\n        this.name = \"BrowserAuthError\";\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when PKCE is not implemented.\r\n     * @param errDetail\r\n     */\r\n    static createPkceNotGeneratedError(errDetail: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.pkceNotGenerated.code,\r\n            `${BrowserAuthErrorMessage.pkceNotGenerated.desc} Detail:${errDetail}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the crypto object is unavailable.\r\n     * @param errDetail\r\n     */\r\n    static createCryptoNotAvailableError(errDetail: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.cryptoDoesNotExist.code,\r\n            `${BrowserAuthErrorMessage.cryptoDoesNotExist.desc} Detail:${errDetail}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when an HTTP method hasn't been implemented by the browser class.\r\n     * @param method\r\n     */\r\n    static createHttpMethodNotImplementedError(method: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.httpMethodNotImplementedError.code,\r\n            `${BrowserAuthErrorMessage.httpMethodNotImplementedError.desc} Given Method: ${method}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the navigation URI is empty.\r\n     */\r\n    static createEmptyNavigationUriError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.emptyNavigateUriError.code, BrowserAuthErrorMessage.emptyNavigateUriError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the hash string value is unexpectedly empty.\r\n     * @param hashValue\r\n     */\r\n    static createEmptyHashError(hashValue: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.hashEmptyError.code, `${BrowserAuthErrorMessage.hashEmptyError.desc} Given Url: ${hashValue}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the hash string value is unexpectedly empty.\r\n     */\r\n    static createHashDoesNotContainStateError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.hashDoesNotContainStateError.code, BrowserAuthErrorMessage.hashDoesNotContainStateError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the hash string value does not contain known properties\r\n     */\r\n    static createHashDoesNotContainKnownPropertiesError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.hashDoesNotContainKnownPropertiesError.code, BrowserAuthErrorMessage.hashDoesNotContainKnownPropertiesError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the hash string value is unexpectedly empty.\r\n     */\r\n    static createUnableToParseStateError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.unableToParseStateError.code, BrowserAuthErrorMessage.unableToParseStateError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the state value in the hash does not match the interaction type of the API attempting to consume it.\r\n     */\r\n    static createStateInteractionTypeMismatchError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.stateInteractionTypeMismatchError.code, BrowserAuthErrorMessage.stateInteractionTypeMismatchError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when a browser interaction (redirect or popup) is in progress.\r\n     */\r\n    static createInteractionInProgressError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.interactionInProgress.code, BrowserAuthErrorMessage.interactionInProgress.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the popup window could not be opened.\r\n     * @param errDetail\r\n     */\r\n    static createPopupWindowError(errDetail?: string): BrowserAuthError {\r\n        let errorMessage = BrowserAuthErrorMessage.popupWindowError.desc;\r\n        errorMessage = !StringUtils.isEmpty(errDetail) ? `${errorMessage} Details: ${errDetail}` : errorMessage;\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.popupWindowError.code, errorMessage);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when window.open returns an empty window object.\r\n     * @param errDetail\r\n     */\r\n    static createEmptyWindowCreatedError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.emptyWindowError.code, BrowserAuthErrorMessage.emptyWindowError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the user closes a popup.\r\n     */\r\n    static createUserCancelledError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.userCancelledError.code,\r\n            BrowserAuthErrorMessage.userCancelledError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when monitorPopupFromHash times out for a given popup.\r\n     */\r\n    static createMonitorPopupTimeoutError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.monitorPopupTimeoutError.code,\r\n            BrowserAuthErrorMessage.monitorPopupTimeoutError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when monitorIframeFromHash times out for a given iframe.\r\n     */\r\n    static createMonitorIframeTimeoutError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.monitorIframeTimeoutError.code,\r\n            BrowserAuthErrorMessage.monitorIframeTimeoutError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when navigateWindow is called inside an iframe or brokered applications.\r\n     * @param windowParentCheck\r\n     */\r\n    static createRedirectInIframeError(windowParentCheck: boolean): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.redirectInIframeError.code,\r\n            `${BrowserAuthErrorMessage.redirectInIframeError.desc} (window.parent !== window) => ${windowParentCheck}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when an auth reload is done inside an iframe.\r\n     */\r\n    static createBlockReloadInHiddenIframeError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.blockTokenRequestsInHiddenIframeError.code,\r\n            BrowserAuthErrorMessage.blockTokenRequestsInHiddenIframeError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when a popup attempts to call an acquireToken API\r\n     * @returns\r\n     */\r\n    static createBlockAcquireTokenInPopupsError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.blockAcquireTokenInPopupsError.code,\r\n            BrowserAuthErrorMessage.blockAcquireTokenInPopupsError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when an iframe is found to be closed before the timeout is reached.\r\n     */\r\n    static createIframeClosedPrematurelyError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.iframeClosedPrematurelyError.code, BrowserAuthErrorMessage.iframeClosedPrematurelyError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the logout API is called on any of the silent interaction clients\r\n     */\r\n    static createSilentLogoutUnsupportedError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.silentLogoutUnsupportedError.code, BrowserAuthErrorMessage.silentLogoutUnsupportedError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the account object is not provided in the acquireTokenSilent API.\r\n     */\r\n    static createNoAccountError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.noAccountError.code, BrowserAuthErrorMessage.noAccountError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when a given prompt value is invalid for silent requests.\r\n     */\r\n    static createSilentPromptValueError(givenPrompt: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.silentPromptValueError.code, `${BrowserAuthErrorMessage.silentPromptValueError.desc} Given value: ${givenPrompt}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the cached token request could not be retrieved from the cache\r\n     */\r\n    static createUnableToParseTokenRequestCacheError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.unableToParseTokenRequestCacheError.code,\r\n            BrowserAuthErrorMessage.unableToParseTokenRequestCacheError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the token request could not be retrieved from the cache\r\n     */\r\n    static createNoTokenRequestCacheError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.noTokenRequestCacheError.code,\r\n            BrowserAuthErrorMessage.noTokenRequestCacheError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when handleCodeResponse is called before initiateAuthRequest (InteractionHandler)\r\n     */\r\n    static createAuthRequestNotSetError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.authRequestNotSet.code,\r\n            BrowserAuthErrorMessage.authRequestNotSet.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the authority could not be retrieved from the cache\r\n     */\r\n    static createNoCachedAuthorityError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.noCachedAuthorityError.code,\r\n            BrowserAuthErrorMessage.noCachedAuthorityError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if cache type is invalid.\r\n     */\r\n    static createInvalidCacheTypeError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.invalidCacheType.code, `${BrowserAuthErrorMessage.invalidCacheType.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when login and token requests are made from a non-browser environment\r\n     */\r\n    static createNonBrowserEnvironmentError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.notInBrowserEnvironment.code, BrowserAuthErrorMessage.notInBrowserEnvironment.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when indexDB database is not open\r\n     */\r\n    static createDatabaseNotOpenError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.databaseNotOpen.code, BrowserAuthErrorMessage.databaseNotOpen.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when token fetch fails due to no internet\r\n     */\r\n    static createNoNetworkConnectivityError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.noNetworkConnectivity.code, BrowserAuthErrorMessage.noNetworkConnectivity.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when token fetch fails due to reasons other than internet connectivity\r\n     */\r\n    static createPostRequestFailedError(errorDesc: string, endpoint: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.postRequestFailed.code, `${BrowserAuthErrorMessage.postRequestFailed.desc} | Network client threw: ${errorDesc} | Attempted to reach: ${endpoint.split(\"?\")[0]}`);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when get request fails due to reasons other than internet connectivity\r\n     */\r\n    static createGetRequestFailedError(errorDesc: string, endpoint: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.getRequestFailed.code, `${BrowserAuthErrorMessage.getRequestFailed.desc} | Network client threw: ${errorDesc} | Attempted to reach: ${endpoint.split(\"?\")[0]}`);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when network client fails to parse network response\r\n     */\r\n    static createFailedToParseNetworkResponseError(endpoint: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.failedToParseNetworkResponse.code, `${BrowserAuthErrorMessage.failedToParseNetworkResponse.desc} | Attempted to reach: ${endpoint.split(\"?\")[0]}`);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when the necessary information is not available to sideload tokens\r\n     */\r\n    static createUnableToLoadTokenError(errorDetail: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.unableToLoadTokenError.code, `${BrowserAuthErrorMessage.unableToLoadTokenError.desc} | ${errorDetail}`);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when the queried cryptographic key is not found in IndexedDB\r\n     */\r\n    static createSigningKeyNotFoundInStorageError(keyId: string): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.signingKeyNotFoundInStorage.code, `${BrowserAuthErrorMessage.signingKeyNotFoundInStorage.desc} | No match found for KeyId: ${keyId}`);\r\n    }\r\n\r\n    /**\r\n     * Create an error when an authorization code is required but not provided\r\n     */\r\n    static createAuthCodeRequiredError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.authCodeRequired.code, BrowserAuthErrorMessage.authCodeRequired.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when an authorization code or native account ID is required but not provided\r\n     */\r\n    static createAuthCodeOrNativeAccountIdRequiredError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.authCodeOrNativeAccountRequired.code, BrowserAuthErrorMessage.authCodeOrNativeAccountRequired.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when both authorization code and native account ID are provided\r\n     */\r\n    static createSpaCodeAndNativeAccountIdPresentError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.spaCodeAndNativeAccountPresent.code, BrowserAuthErrorMessage.spaCodeAndNativeAccountPresent.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when IndexedDB is unavailable\r\n     */\r\n    static createDatabaseUnavailableError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.databaseUnavailable.code, BrowserAuthErrorMessage.databaseUnavailable.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when native token acquisition is not possible\r\n     */\r\n    static createUnableToAcquireTokenFromNativePlatformError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.unableToAcquireTokenFromNativePlatform.code, BrowserAuthErrorMessage.unableToAcquireTokenFromNativePlatform.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when Handshake with browser extension times out\r\n     */\r\n    static createNativeHandshakeTimeoutError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.nativeHandshakeTimeout.code, BrowserAuthErrorMessage.nativeHandshakeTimeout.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when browser extension is not installed\r\n     */\r\n    static createNativeExtensionNotInstalledError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.nativeExtensionNotInstalled.code, BrowserAuthErrorMessage.nativeExtensionNotInstalled.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when native connection has not been established\r\n     * @returns\r\n     */\r\n    static createNativeConnectionNotEstablishedError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.nativeConnectionNotEstablished.code, BrowserAuthErrorMessage.nativeConnectionNotEstablished.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when the initialize function hasn't been called\r\n     */\r\n    static createNativeBrokerCalledBeforeInitialize(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.nativeBrokerCalledBeforeInitialize.code, BrowserAuthErrorMessage.nativeBrokerCalledBeforeInitialize.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error thrown when requesting a token directly from the native platform with an unsupported prompt parameter e.g. select_account, login or create\r\n     * These requests must go through eSTS to ensure eSTS is aware of the new account\r\n     */\r\n    static createNativePromptParameterNotSupportedError(): BrowserAuthError {\r\n        return new BrowserAuthError(BrowserAuthErrorMessage.nativePromptNotSupported.code, BrowserAuthErrorMessage.nativePromptNotSupported.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,IAAA,uBAAuB,GAAG;AACnC,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,8DAA8D;AACvE,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,iDAAiD;AAC1D,KAAA;AACD,IAAA,6BAA6B,EAAE;AAC3B,QAAA,IAAI,EAAE,6BAA6B;AACnC,QAAA,IAAI,EAAE,iEAAiE;AAC1E,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,kEAAkE;AAC3E,KAAA;AACD,IAAA,cAAc,EAAE;AACZ,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,iKAAiK;AAC1K,KAAA;AACD,IAAA,4BAA4B,EAAE;AAC1B,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,mFAAmF;AAC5F,KAAA;AACD,IAAA,sCAAsC,EAAE;AACpC,QAAA,IAAI,EAAE,wCAAwC;AAC9C,QAAA,IAAI,EAAE,qJAAqJ;AAC9J,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,IAAI,EAAE,6EAA6E;AACtF,KAAA;AACD,IAAA,iCAAiC,EAAE;AAC/B,QAAA,IAAI,EAAE,iCAAiC;AACvC,QAAA,IAAI,EAAE,yEAAyE;AAClF,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,gLAAgL;AACzL,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,0GAA0G;AACnH,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,uDAAuD;AAChE,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,IAAI,EAAE,0BAA0B;AACnC,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,IAAI,EAAE,iGAAiG;AAC1G,KAAA;AACD,IAAA,yBAAyB,EAAE;AACvB,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,IAAI,EAAE,kGAAkG;AAC3G,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,uLAAuL;AAChM,KAAA;AACD,IAAA,qCAAqC,EAAE;AACnC,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,qIAAqI;AAC9I,KAAA;AACD,IAAA,8BAA8B,EAAE;AAC5B,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,qFAAqF;AAC9F,KAAA;AACD,IAAA,4BAA4B,EAAE;AAC1B,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,oDAAoD;AAC7D,KAAA;AACD,IAAA,4BAA4B,EAAE;AAC1B,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,iFAAiF;AAC1F,KAAA;AACD,IAAA,cAAc,EAAE;AACZ,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,yJAAyJ;AAClK,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,gHAAgH;AACzH,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,8BAA8B;AACpC,QAAA,IAAI,EAAE,kCAAkC;AAC3C,KAAA;AACD,IAAA,mCAAmC,EAAE;AACjC,QAAA,IAAI,EAAE,2CAA2C;AACjD,QAAA,IAAI,EAAE,+CAA+C;AACxD,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,4BAA4B;AACrC,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,gGAAgG;AACzG,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,oBAAoB;AAC7B,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,yEAAyE;AAClF,KAAA;AACD,IAAA,eAAe,EAAE;AACb,QAAA,IAAI,EAAE,mBAAmB;AACzB,QAAA,IAAI,EAAE,uBAAuB;AAChC,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,0DAA0D;AACnE,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,2IAA2I;AACpJ,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,iFAAiF;AAC1F,KAAA;AACD,IAAA,4BAA4B,EAAE;AAC1B,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,wDAAwD;AACjE,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,+BAA+B;AACxC,KAAA;AACD,IAAA,2BAA2B,EAAE;AACzB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,4DAA4D;AACrE,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,8FAA8F;AACvG,KAAA;AACD,IAAA,+BAA+B,EAAE;AAC7B,QAAA,IAAI,EAAE,uCAAuC;AAC7C,QAAA,IAAI,EAAE,yEAAyE;AAClF,KAAA;AACD,IAAA,8BAA8B,EAAE;AAC5B,QAAA,IAAI,EAAE,sCAAsC;AAC5C,QAAA,IAAI,EAAE,6DAA6D;AACtE,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,+LAA+L;AACxM,KAAA;AACD,IAAA,sCAAsC,EAAE;AACpC,QAAA,IAAI,EAAE,8CAA8C;AACpD,QAAA,IAAI,EAAE,kHAAkH;AAC3H,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,yEAAyE;AAClF,KAAA;AACD,IAAA,2BAA2B,EAAE;AACzB,QAAA,IAAI,EAAE,gCAAgC;AACtC,QAAA,IAAI,EAAE,iGAAiG;AAC1G,KAAA;AACD,IAAA,8BAA8B,EAAE;AAC5B,QAAA,IAAI,EAAE,mCAAmC;AACzC,QAAA,IAAI,EAAE,iLAAiL;AAC1L,KAAA;AACD,IAAA,kCAAkC,EAAE;AAChC,QAAA,IAAI,EAAE,wCAAwC;AAC9C,QAAA,IAAI,EAAE,oLAAoL;AAC7L,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,6BAA6B;AACnC,QAAA,IAAI,EAAE,mHAAmH;AAC5H,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAS,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;IAE3C,SAAY,gBAAA,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAApD,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,SAAS,EAAE,YAAY,CAAC,IAIjC,IAAA,CAAA;QAFG,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACxD,QAAA,KAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;;KAClC;AAED;;;AAGG;IACI,gBAA2B,CAAA,2BAAA,GAAlC,UAAmC,SAAiB,EAAA;AAChD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,EAClE,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,GAAW,UAAA,GAAA,SAAW,CAAC,CAAC;KAC/E,CAAA;AAED;;;AAGG;IACI,gBAA6B,CAAA,6BAAA,GAApC,UAAqC,SAAiB,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,EACpE,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,GAAW,UAAA,GAAA,SAAW,CAAC,CAAC;KACjF,CAAA;AAED;;;AAGG;IACI,gBAAmC,CAAA,mCAAA,GAA1C,UAA2C,MAAc,EAAA;AACrD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,6BAA6B,CAAC,IAAI,EAC/E,uBAAuB,CAAC,6BAA6B,CAAC,IAAI,GAAkB,iBAAA,GAAA,MAAQ,CAAC,CAAC;KAChG,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,EAAE,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACvI,CAAA;AAED;;;AAGG;IACI,gBAAoB,CAAA,oBAAA,GAA3B,UAA4B,SAAiB,EAAA;AACzC,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAK,uBAAuB,CAAC,cAAc,CAAC,IAAI,GAAe,cAAA,GAAA,SAAW,CAAC,CAAC;KACtJ,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,kCAAkC,GAAzC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,EAAE,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;KACrJ,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,4CAA4C,GAAnD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,sCAAsC,CAAC,IAAI,EAAE,uBAAuB,CAAC,sCAAsC,CAAC,IAAI,CAAC,CAAC;KACzK,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,EAAE,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;KAC3I,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,uCAAuC,GAA9C,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,iCAAiC,CAAC,IAAI,EAAE,uBAAuB,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;KAC/J,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,EAAE,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACvI,CAAA;AAED;;;AAGG;IACI,gBAAsB,CAAA,sBAAA,GAA7B,UAA8B,SAAkB,EAAA;AAC5C,QAAA,IAAI,YAAY,GAAG,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACjE,QAAA,YAAY,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,GAAM,YAAY,GAAa,YAAA,GAAA,SAAW,GAAG,YAAY,CAAC;QACxG,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KAC5F,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,EAAE,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAC7H,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,EACvE,uBAAuB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACxD,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,8BAA8B,GAArC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,EAC7E,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KAC9D,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,+BAA+B,GAAtC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,yBAAyB,CAAC,IAAI,EAC9E,uBAAuB,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;KAC/D,CAAA;AAED;;;AAGG;IACI,gBAA2B,CAAA,2BAAA,GAAlC,UAAmC,iBAA0B,EAAA;AACzD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,EACvE,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,GAAkC,iCAAA,GAAA,iBAAmB,CAAC,CAAC;KACnH,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,oCAAoC,GAA3C,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,qCAAqC,CAAC,IAAI,EAC1F,uBAAuB,CAAC,qCAAqC,CAAC,IAAI,CAAC,CAAC;KAC3E,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,oCAAoC,GAA3C,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,EACnF,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;KACpE,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,kCAAkC,GAAzC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,EAAE,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;KACrJ,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,kCAAkC,GAAzC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,EAAE,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;KACrJ,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,oBAAoB,GAA3B,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAE,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KACzH,CAAA;AAED;;AAEG;IACI,gBAA4B,CAAA,4BAAA,GAAnC,UAAoC,WAAmB,EAAA;AACnD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,EAAK,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,GAAiB,gBAAA,GAAA,WAAa,CAAC,CAAC;KAC1K,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,yCAAyC,GAAhD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,mCAAmC,CAAC,IAAI,EACxF,uBAAuB,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;KACzE,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,8BAA8B,GAArC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,EAC7E,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KAC9D,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,EACtE,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACvD,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,EAC3E,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;KAC5D,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAA,GAAG,uBAAuB,CAAC,gBAAgB,CAAC,IAAM,CAAC,CAAC;KAClI,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,EAAE,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;KAC3I,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,0BAA0B,GAAjC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,EAAE,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KAC3H,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,EAAE,uBAAuB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACvI,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,4BAA4B,GAAnC,UAAoC,SAAiB,EAAE,QAAgB,EAAA;QACnE,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,EAAK,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,GAAA,2BAAA,GAA4B,SAAS,GAAA,yBAAA,GAA0B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG,CAAC,CAAC;KACzN,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,2BAA2B,GAAlC,UAAmC,SAAiB,EAAE,QAAgB,EAAA;QAClE,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,EAAK,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,GAAA,2BAAA,GAA4B,SAAS,GAAA,yBAAA,GAA0B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG,CAAC,CAAC;KACvN,CAAA;AAED;;AAEG;IACI,gBAAuC,CAAA,uCAAA,GAA9C,UAA+C,QAAgB,EAAA;QAC3D,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,EAAK,uBAAuB,CAAC,4BAA4B,CAAC,IAAI,GAAA,yBAAA,GAA0B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG,CAAC,CAAC;KAC1M,CAAA;AAED;;AAEG;IACI,gBAA4B,CAAA,4BAAA,GAAnC,UAAoC,WAAmB,EAAA;AACnD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,EAAK,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,GAAM,KAAA,GAAA,WAAa,CAAC,CAAC;KAC/J,CAAA;AAED;;AAEG;IACI,gBAAsC,CAAA,sCAAA,GAA7C,UAA8C,KAAa,EAAA;AACvD,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,2BAA2B,CAAC,IAAI,EAAK,uBAAuB,CAAC,2BAA2B,CAAC,IAAI,GAAgC,+BAAA,GAAA,KAAO,CAAC,CAAC;KAC7L,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,EAAE,uBAAuB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAC7H,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,4CAA4C,GAAnD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,+BAA+B,CAAC,IAAI,EAAE,uBAAuB,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;KAC3J,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,2CAA2C,GAAlD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,EAAE,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;KACzJ,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,8BAA8B,GAArC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,EAAE,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACnI,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,iDAAiD,GAAxD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,sCAAsC,CAAC,IAAI,EAAE,uBAAuB,CAAC,sCAAsC,CAAC,IAAI,CAAC,CAAC;KACzK,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,iCAAiC,GAAxC,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,EAAE,uBAAuB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;KACzI,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,sCAAsC,GAA7C,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,2BAA2B,CAAC,IAAI,EAAE,uBAAuB,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;KACnJ,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,yCAAyC,GAAhD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,EAAE,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;KACzJ,CAAA;AAED;;AAEG;AACI,IAAA,gBAAA,CAAA,wCAAwC,GAA/C,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,kCAAkC,CAAC,IAAI,EAAE,uBAAuB,CAAC,kCAAkC,CAAC,IAAI,CAAC,CAAC;KACjK,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,4CAA4C,GAAnD,YAAA;AACI,QAAA,OAAO,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,EAAE,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KAC7I,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CA5VA,CAAsC,SAAS,CA4V9C;;;;"}