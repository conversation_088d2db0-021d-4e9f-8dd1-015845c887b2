{"version": 3, "file": "internals.js", "sources": ["../src/internals.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Warning: This set of exports is purely intended to be used by other MSAL libraries, and should be considered potentially unstable. We strongly discourage using them directly, you do so at your own risk.\r\n * Breaking changes to these APIs will be shipped under a minor version, instead of a major version.\r\n */\r\n\r\n// Cache Manager\r\nexport { BrowserCacheManager } from \"./cache/BrowserCacheManager\";\r\n\r\n// Clients\r\nexport { StandardInteractionClient } from \"./interaction_client/StandardInteractionClient\";\r\nexport { RedirectClient } from \"./interaction_client/RedirectClient\";\r\nexport { PopupClient } from \"./interaction_client/PopupClient\";\r\nexport { SilentIframeClient } from \"./interaction_client/SilentIframeClient\";\r\nexport { SilentCacheClient } from \"./interaction_client/SilentCacheClient\";\r\nexport { SilentRefreshClient } from \"./interaction_client/SilentRefreshClient\";\r\nexport { NativeInteractionClient } from \"./interaction_client/NativeInteractionClient\";\r\n\r\n// Handlers\r\nexport { RedirectHandler } from \"./interaction_handler/RedirectHandler\";\r\nexport { EventHandler } from \"./event/EventHandler\";\r\nexport { NativeMessageHandler } from \"./broker/nativeBroker/NativeMessageHandler\";\r\n\r\n// Utilities\r\nexport { BrowserStateObject } from \"./utils/BrowserProtocolUtils\";\r\nexport { BrowserConstants, TemporaryCacheKeys } from \"./utils/BrowserConstants\";\r\n\r\n// Crypto\r\nexport { CryptoOps } from \"./crypto/CryptoOps\";\r\n\r\n// Browser Errors\r\nexport { NativeAuthError } from \"./error/NativeAuthError\";\r\n\r\n// Telemetry\r\nexport { BrowserPerformanceClient } from \"./telemetry/BrowserPerformanceClient\";\r\nexport { BrowserPerformanceMeasurement } from \"./telemetry/BrowserPerformanceMeasurement\";\r\n\r\n// Native request and response\r\nexport { NativeTokenRequest } from \"./broker/nativeBroker/NativeRequest\";\r\nexport { NativeResponse, MATS } from \"./broker/nativeBroker/NativeResponse\";\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;AAGG"}