{"version": 3, "file": "EventType.js", "sources": ["../../src/event/EventType.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nexport enum EventType {\r\n    INITIALIZE_START = \"msal:initializeStart\",\r\n    INITIALIZE_END = \"msal:initializeEnd\",\r\n    ACCOUNT_ADDED = \"msal:accountAdded\",\r\n    ACCOUNT_REMOVED = \"msal:accountRemoved\",\r\n    LOGIN_START = \"msal:loginStart\",\r\n    LOGIN_SUCCESS = \"msal:loginSuccess\",\r\n    LOGIN_FAILURE = \"msal:loginFailure\",\r\n    ACQUIRE_TOKEN_START = \"msal:acquireTokenStart\",\r\n    ACQUIRE_TOKEN_SUCCESS = \"msal:acquireTokenSuccess\",\r\n    ACQUIRE_TOKEN_FAILURE = \"msal:acquireTokenFailure\",\r\n    ACQUIRE_TOKEN_NETWORK_START = \"msal:acquireTokenFromNetworkStart\",\r\n    SSO_SILENT_START = \"msal:ssoSilentStart\",\r\n    SSO_SILENT_SUCCESS = \"msal:ssoSilentSuccess\",\r\n    SSO_SILENT_FAILURE = \"msal:ssoSilentFailure\",\r\n    ACQUIRE_TOKEN_BY_CODE_START = \"msal:acquireTokenByCodeStart\",\r\n    ACQUIRE_TOKEN_BY_CODE_SUCCESS = \"msal:acquireTokenByCodeSuccess\",\r\n    ACQUIRE_TOKEN_BY_CODE_FAILURE = \"msal:acquireTokenByCodeFailure\",\r\n    HANDLE_REDIRECT_START = \"msal:handleRedirectStart\",\r\n    HANDLE_REDIRECT_END = \"msal:handleRedirectEnd\",\r\n    POPUP_OPENED = \"msal:popupOpened\",\r\n    LOGOUT_START = \"msal:logoutStart\",\r\n    LOGOUT_SUCCESS = \"msal:logoutSuccess\",\r\n    LOGOUT_FAILURE = \"msal:logoutFailure\",\r\n    LOGOUT_END = \"msal:logoutEnd\",\r\n    RESTORE_FROM_BFCACHE = \"msal:restoreFromBFCache\"\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;IAES,UA0BX;AA1BD,CAAA,UAAY,SAAS,EAAA;AACjB,IAAA,SAAA,CAAA,kBAAA,CAAA,GAAA,sBAAyC,CAAA;AACzC,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,oBAAqC,CAAA;AACrC,IAAA,SAAA,CAAA,eAAA,CAAA,GAAA,mBAAmC,CAAA;AACnC,IAAA,SAAA,CAAA,iBAAA,CAAA,GAAA,qBAAuC,CAAA;AACvC,IAAA,SAAA,CAAA,aAAA,CAAA,GAAA,iBAA+B,CAAA;AAC/B,IAAA,SAAA,CAAA,eAAA,CAAA,GAAA,mBAAmC,CAAA;AACnC,IAAA,SAAA,CAAA,eAAA,CAAA,GAAA,mBAAmC,CAAA;AACnC,IAAA,SAAA,CAAA,qBAAA,CAAA,GAAA,wBAA8C,CAAA;AAC9C,IAAA,SAAA,CAAA,uBAAA,CAAA,GAAA,0BAAkD,CAAA;AAClD,IAAA,SAAA,CAAA,uBAAA,CAAA,GAAA,0BAAkD,CAAA;AAClD,IAAA,SAAA,CAAA,6BAAA,CAAA,GAAA,mCAAiE,CAAA;AACjE,IAAA,SAAA,CAAA,kBAAA,CAAA,GAAA,qBAAwC,CAAA;AACxC,IAAA,SAAA,CAAA,oBAAA,CAAA,GAAA,uBAA4C,CAAA;AAC5C,IAAA,SAAA,CAAA,oBAAA,CAAA,GAAA,uBAA4C,CAAA;AAC5C,IAAA,SAAA,CAAA,6BAAA,CAAA,GAAA,8BAA4D,CAAA;AAC5D,IAAA,SAAA,CAAA,+BAAA,CAAA,GAAA,gCAAgE,CAAA;AAChE,IAAA,SAAA,CAAA,+BAAA,CAAA,GAAA,gCAAgE,CAAA;AAChE,IAAA,SAAA,CAAA,uBAAA,CAAA,GAAA,0BAAkD,CAAA;AAClD,IAAA,SAAA,CAAA,qBAAA,CAAA,GAAA,wBAA8C,CAAA;AAC9C,IAAA,SAAA,CAAA,cAAA,CAAA,GAAA,kBAAiC,CAAA;AACjC,IAAA,SAAA,CAAA,cAAA,CAAA,GAAA,kBAAiC,CAAA;AACjC,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,oBAAqC,CAAA;AACrC,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,oBAAqC,CAAA;AACrC,IAAA,SAAA,CAAA,YAAA,CAAA,GAAA,gBAA6B,CAAA;AAC7B,IAAA,SAAA,CAAA,sBAAA,CAAA,GAAA,yBAAgD,CAAA;AACpD,CAAC,EA1BW,SAAS,KAAT,SAAS,GA0BpB,EAAA,CAAA,CAAA;;;;"}