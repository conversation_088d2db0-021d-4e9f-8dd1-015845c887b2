{"version": 3, "file": "EventMessage.js", "sources": ["../../src/event/EventMessage.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, AuthError, AccountInfo } from \"@azure/msal-common\";\r\nimport { EventType } from \"./EventType\";\r\nimport { InteractionStatus, InteractionType } from \"../utils/BrowserConstants\";\r\nimport { PopupRequest, RedirectRequest, SilentRequest, SsoSilentRequest, EndSessionRequest } from \"..\";\r\n\r\nexport type EventMessage = {\r\n    eventType: EventType;\r\n    interactionType: InteractionType | null;\r\n    payload: EventPayload;\r\n    error: EventError;\r\n    timestamp: number;\r\n};\r\n\r\nexport type PopupEvent = {\r\n    popupWindow: Window;\r\n};\r\n\r\nexport type EventPayload = AccountInfo | PopupRequest | RedirectRequest | SilentRequest | SsoSilentRequest | EndSessionRequest | AuthenticationResult | PopupEvent | null;\r\n\r\nexport type EventError = AuthError | Error | null;\r\n\r\nexport type EventCallbackFunction = (message: EventMessage) => void;\r\n\r\nexport class EventMessageUtils {\r\n\r\n    /**\r\n     * Gets interaction status from event message\r\n     * @param message\r\n     * @param currentStatus\r\n     */\r\n    static getInteractionStatusFromEvent(message: EventMessage, currentStatus?: InteractionStatus): InteractionStatus|null {\r\n        switch (message.eventType) {\r\n            case EventType.LOGIN_START:\r\n                return InteractionStatus.Login;\r\n            case EventType.SSO_SILENT_START:\r\n                return InteractionStatus.SsoSilent;\r\n            case EventType.ACQUIRE_TOKEN_START:\r\n                if (message.interactionType === InteractionType.Redirect || message.interactionType === InteractionType.Popup) {\r\n                    return InteractionStatus.AcquireToken;\r\n                }\r\n                break;\r\n            case EventType.HANDLE_REDIRECT_START:\r\n                return InteractionStatus.HandleRedirect;\r\n            case EventType.LOGOUT_START:\r\n                return InteractionStatus.Logout;\r\n            case EventType.SSO_SILENT_SUCCESS:\r\n            case EventType.SSO_SILENT_FAILURE:\r\n                if (currentStatus && currentStatus !== InteractionStatus.SsoSilent) {\r\n                    // Prevent this event from clearing any status other than ssoSilent\r\n                    break;\r\n                }\r\n                return InteractionStatus.None;\r\n            case EventType.LOGOUT_END:\r\n                if (currentStatus && currentStatus !== InteractionStatus.Logout) {\r\n                    // Prevent this event from clearing any status other than logout\r\n                    break;\r\n                }\r\n                return InteractionStatus.None;\r\n            case EventType.HANDLE_REDIRECT_END:\r\n                if (currentStatus && currentStatus !== InteractionStatus.HandleRedirect) {\r\n                    // Prevent this event from clearing any status other than handleRedirect\r\n                    break;\r\n                }\r\n                return InteractionStatus.None;\r\n            case EventType.LOGIN_SUCCESS:\r\n            case EventType.LOGIN_FAILURE:\r\n            case EventType.ACQUIRE_TOKEN_SUCCESS:\r\n            case EventType.ACQUIRE_TOKEN_FAILURE:\r\n            case EventType.RESTORE_FROM_BFCACHE:\r\n                if (message.interactionType === InteractionType.Redirect || message.interactionType === InteractionType.Popup) {\r\n                    if (currentStatus && currentStatus !== InteractionStatus.Login && currentStatus !== InteractionStatus.AcquireToken) {\r\n                        // Prevent this event from clearing any status other than login or acquireToken\r\n                        break;\r\n                    }\r\n                    return InteractionStatus.None;\r\n                }\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n        return null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAyBH,IAAA,iBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,iBAAA,GAAA;KA2DC;AAzDG;;;;AAIG;AACI,IAAA,iBAAA,CAAA,6BAA6B,GAApC,UAAqC,OAAqB,EAAE,aAAiC,EAAA;QACzF,QAAQ,OAAO,CAAC,SAAS;YACrB,KAAK,SAAS,CAAC,WAAW;gBACtB,OAAO,iBAAiB,CAAC,KAAK,CAAC;YACnC,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,OAAO,iBAAiB,CAAC,SAAS,CAAC;YACvC,KAAK,SAAS,CAAC,mBAAmB;AAC9B,gBAAA,IAAI,OAAO,CAAC,eAAe,KAAK,eAAe,CAAC,QAAQ,IAAI,OAAO,CAAC,eAAe,KAAK,eAAe,CAAC,KAAK,EAAE;oBAC3G,OAAO,iBAAiB,CAAC,YAAY,CAAC;AACzC,iBAAA;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,iBAAiB,CAAC,cAAc,CAAC;YAC5C,KAAK,SAAS,CAAC,YAAY;gBACvB,OAAO,iBAAiB,CAAC,MAAM,CAAC;YACpC,KAAK,SAAS,CAAC,kBAAkB,CAAC;YAClC,KAAK,SAAS,CAAC,kBAAkB;AAC7B,gBAAA,IAAI,aAAa,IAAI,aAAa,KAAK,iBAAiB,CAAC,SAAS,EAAE;;oBAEhE,MAAM;AACT,iBAAA;gBACD,OAAO,iBAAiB,CAAC,IAAI,CAAC;YAClC,KAAK,SAAS,CAAC,UAAU;AACrB,gBAAA,IAAI,aAAa,IAAI,aAAa,KAAK,iBAAiB,CAAC,MAAM,EAAE;;oBAE7D,MAAM;AACT,iBAAA;gBACD,OAAO,iBAAiB,CAAC,IAAI,CAAC;YAClC,KAAK,SAAS,CAAC,mBAAmB;AAC9B,gBAAA,IAAI,aAAa,IAAI,aAAa,KAAK,iBAAiB,CAAC,cAAc,EAAE;;oBAErE,MAAM;AACT,iBAAA;gBACD,OAAO,iBAAiB,CAAC,IAAI,CAAC;YAClC,KAAK,SAAS,CAAC,aAAa,CAAC;YAC7B,KAAK,SAAS,CAAC,aAAa,CAAC;YAC7B,KAAK,SAAS,CAAC,qBAAqB,CAAC;YACrC,KAAK,SAAS,CAAC,qBAAqB,CAAC;YACrC,KAAK,SAAS,CAAC,oBAAoB;AAC/B,gBAAA,IAAI,OAAO,CAAC,eAAe,KAAK,eAAe,CAAC,QAAQ,IAAI,OAAO,CAAC,eAAe,KAAK,eAAe,CAAC,KAAK,EAAE;AAC3G,oBAAA,IAAI,aAAa,IAAI,aAAa,KAAK,iBAAiB,CAAC,KAAK,IAAI,aAAa,KAAK,iBAAiB,CAAC,YAAY,EAAE;;wBAEhH,MAAM;AACT,qBAAA;oBACD,OAAO,iBAAiB,CAAC,IAAI,CAAC;AACjC,iBAAA;gBACD,MAAM;AAGb,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}