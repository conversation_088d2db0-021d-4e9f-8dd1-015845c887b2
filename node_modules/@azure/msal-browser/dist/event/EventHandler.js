/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { Account<PERSON>ntity, CacheManager } from '@azure/msal-common';
import { EventType } from './EventType.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var EventHandler = /** @class */ (function () {
    function EventHandler(logger, browserCrypto) {
        this.eventCallbacks = new Map();
        this.logger = logger;
        this.browserCrypto = browserCrypto;
        this.listeningToStorageEvents = false;
        this.handleAccountCacheChange = this.handleAccountCacheChange.bind(this);
    }
    /**
     * Adds event callbacks to array
     * @param callback
     */
    EventHandler.prototype.addEventCallback = function (callback) {
        if (typeof window !== "undefined") {
            var callbackId = this.browserCrypto.createNewGuid();
            this.eventCallbacks.set(callbackId, callback);
            this.logger.verbose("Event callback registered with id: " + callbackId);
            return callbackId;
        }
        return null;
    };
    /**
     * Removes callback with provided id from callback array
     * @param callbackId
     */
    EventHandler.prototype.removeEventCallback = function (callbackId) {
        this.eventCallbacks.delete(callbackId);
        this.logger.verbose("Event callback " + callbackId + " removed.");
    };
    /**
     * Adds event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
     */
    EventHandler.prototype.enableAccountStorageEvents = function () {
        if (typeof window === "undefined") {
            return;
        }
        if (!this.listeningToStorageEvents) {
            this.logger.verbose("Adding account storage listener.");
            this.listeningToStorageEvents = true;
            window.addEventListener("storage", this.handleAccountCacheChange);
        }
        else {
            this.logger.verbose("Account storage listener already registered.");
        }
    };
    /**
     * Removes event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
     */
    EventHandler.prototype.disableAccountStorageEvents = function () {
        if (typeof window === "undefined") {
            return;
        }
        if (this.listeningToStorageEvents) {
            this.logger.verbose("Removing account storage listener.");
            window.removeEventListener("storage", this.handleAccountCacheChange);
            this.listeningToStorageEvents = false;
        }
        else {
            this.logger.verbose("No account storage listener registered.");
        }
    };
    /**
     * Emits events by calling callback with event message
     * @param eventType
     * @param interactionType
     * @param payload
     * @param error
     */
    EventHandler.prototype.emitEvent = function (eventType, interactionType, payload, error) {
        var _this = this;
        if (typeof window !== "undefined") {
            var message_1 = {
                eventType: eventType,
                interactionType: interactionType || null,
                payload: payload || null,
                error: error || null,
                timestamp: Date.now()
            };
            this.logger.info("Emitting event: " + eventType);
            this.eventCallbacks.forEach(function (callback, callbackId) {
                _this.logger.verbose("Emitting event to callback " + callbackId + ": " + eventType);
                callback.apply(null, [message_1]);
            });
        }
    };
    /**
     * Emit account added/removed events when cached accounts are changed in a different tab or frame
     */
    EventHandler.prototype.handleAccountCacheChange = function (e) {
        try {
            var cacheValue = e.newValue || e.oldValue;
            if (!cacheValue) {
                return;
            }
            var parsedValue = JSON.parse(cacheValue);
            if (typeof parsedValue !== "object" || !AccountEntity.isAccountEntity(parsedValue)) {
                return;
            }
            var accountEntity = CacheManager.toObject(new AccountEntity(), parsedValue);
            var accountInfo = accountEntity.getAccountInfo();
            if (!e.oldValue && e.newValue) {
                this.logger.info("Account was added to cache in a different window");
                this.emitEvent(EventType.ACCOUNT_ADDED, undefined, accountInfo);
            }
            else if (!e.newValue && e.oldValue) {
                this.logger.info("Account was removed from cache in a different window");
                this.emitEvent(EventType.ACCOUNT_REMOVED, undefined, accountInfo);
            }
        }
        catch (e) {
            return;
        }
    };
    return EventHandler;
}());

export { EventHandler };
//# sourceMappingURL=EventHandler.js.map
