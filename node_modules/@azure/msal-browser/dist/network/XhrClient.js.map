{"version": 3, "file": "XhrClient.js", "sources": ["../../src/network/XhrClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { INetworkModule, NetworkRequestOptions, NetworkResponse } from \"@azure/msal-common\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { HTTP_REQUEST_TYPE } from \"../utils/BrowserConstants\";\r\n\r\n/**\r\n * This client implements the XMLHttpRequest class to send GET and POST requests.\r\n */\r\nexport class XhrClient implements INetworkModule {\r\n\r\n    /**\r\n     * XhrClient for REST endpoints - Get request\r\n     * @param url \r\n     * @param headers \r\n     * @param body \r\n     */\r\n    async sendGetRequestAsync<T>(url: string, options?: NetworkRequestOptions): Promise<NetworkResponse<T>> {\r\n        return this.sendRequestAsync(url, HTTP_REQUEST_TYPE.GET, options);\r\n    }\r\n\r\n    /**\r\n     * XhrClient for REST endpoints - Post request\r\n     * @param url \r\n     * @param headers \r\n     * @param body \r\n     */\r\n    async sendPostRequestAsync<T>(url: string, options?: NetworkRequestOptions): Promise<NetworkResponse<T>> {\r\n        return this.sendRequestAsync(url, HTTP_REQUEST_TYPE.POST, options);\r\n    }\r\n\r\n    /**\r\n     * Helper for XhrClient requests.\r\n     * @param url \r\n     * @param method \r\n     * @param options \r\n     */\r\n    private sendRequestAsync<T>(url: string, method: HTTP_REQUEST_TYPE, options?: NetworkRequestOptions): Promise<NetworkResponse<T>> {\r\n        return new Promise<NetworkResponse<T>>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n            xhr.open(method, url, /* async: */ true);\r\n            this.setXhrHeaders(xhr, options);\r\n            xhr.onload = (): void => {\r\n                if (xhr.status < 200 || xhr.status >= 300) {\r\n                    if (method === HTTP_REQUEST_TYPE.POST) {\r\n                        reject(BrowserAuthError.createPostRequestFailedError(`Failed with status ${xhr.status}`, url));\r\n                    } else {\r\n                        reject(BrowserAuthError.createGetRequestFailedError(`Failed with status ${xhr.status}`, url));\r\n                    }\r\n                }\r\n                try {\r\n                    const jsonResponse = JSON.parse(xhr.responseText) as T;\r\n                    const networkResponse: NetworkResponse<T> = {\r\n                        headers: this.getHeaderDict(xhr),\r\n                        body: jsonResponse,\r\n                        status: xhr.status\r\n                    };\r\n                    resolve(networkResponse);\r\n                } catch (e) {\r\n                    reject(BrowserAuthError.createFailedToParseNetworkResponseError(url));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = (): void => {\r\n                if (window.navigator.onLine) {\r\n                    if (method === HTTP_REQUEST_TYPE.POST) {\r\n                        reject(BrowserAuthError.createPostRequestFailedError(`Failed with status ${xhr.status}`, url));\r\n                    } else {\r\n                        reject(BrowserAuthError.createGetRequestFailedError(`Failed with status ${xhr.status}`, url));\r\n                    }\r\n                } else {\r\n                    reject(BrowserAuthError.createNoNetworkConnectivityError());\r\n                }\r\n            };\r\n\r\n            if (method === HTTP_REQUEST_TYPE.POST && options && options.body) {\r\n                xhr.send(options.body);\r\n            } else if (method === HTTP_REQUEST_TYPE.GET) {\r\n                xhr.send();\r\n            } else {\r\n                throw BrowserAuthError.createHttpMethodNotImplementedError(method);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Helper to set XHR headers for request.\r\n     * @param xhr \r\n     * @param options \r\n     */\r\n    private setXhrHeaders(xhr: XMLHttpRequest, options?: NetworkRequestOptions): void {\r\n        if (options && options.headers) {\r\n            const headers = options.headers;\r\n            Object.keys(headers).forEach((key: string) => {\r\n                xhr.setRequestHeader(key, headers[key]);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a string map of the headers received in the response.\r\n     * \r\n     * Algorithm comes from https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders\r\n     * @param xhr \r\n     */\r\n    private getHeaderDict(xhr: XMLHttpRequest): Record<string, string> {\r\n        const headerString = xhr.getAllResponseHeaders();\r\n        const headerArr = headerString.trim().split(/[\\r\\n]+/);\r\n        const headerDict: Record<string, string> = {};\r\n        headerArr.forEach((value: string) => {\r\n            const parts = value.split(\": \");\r\n            const headerName = parts.shift();\r\n            const headerVal = parts.join(\": \");\r\n            if (headerName && headerVal) {\r\n                headerDict[headerName] = headerVal;\r\n            }\r\n        });\r\n\r\n        return headerDict;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAMH;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,SAAA,GAAA;KA+GC;AA7GG;;;;;AAKG;AACG,IAAA,SAAA,CAAA,SAAA,CAAA,mBAAmB,GAAzB,UAA6B,GAAW,EAAE,OAA+B,EAAA;;;AACrE,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAA;;;AACrE,KAAA,CAAA;AAED;;;;;AAKG;AACG,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAA1B,UAA8B,GAAW,EAAE,OAA+B,EAAA;;;AACtE,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;;;AACtE,KAAA,CAAA;AAED;;;;;AAKG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,UAA4B,GAAW,EAAE,MAAyB,EAAE,OAA+B,EAAA;QAAnG,IA8CC,KAAA,GAAA,IAAA,CAAA;AA7CG,QAAA,OAAO,IAAI,OAAO,CAAqB,UAAC,OAAO,EAAE,MAAM,EAAA;AACnD,YAAA,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,IAAI,CAAC,CAAC;AACzC,YAAA,KAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACjC,GAAG,CAAC,MAAM,GAAG,YAAA;gBACT,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE;AACvC,oBAAA,IAAI,MAAM,KAAK,iBAAiB,CAAC,IAAI,EAAE;AACnC,wBAAA,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,qBAAA,GAAsB,GAAG,CAAC,MAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;AAClG,qBAAA;AAAM,yBAAA;AACH,wBAAA,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,qBAAA,GAAsB,GAAG,CAAC,MAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;AACjG,qBAAA;AACJ,iBAAA;gBACD,IAAI;oBACA,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAM,CAAC;AACvD,oBAAA,IAAM,eAAe,GAAuB;AACxC,wBAAA,OAAO,EAAE,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC;AAChC,wBAAA,IAAI,EAAE,YAAY;wBAClB,MAAM,EAAE,GAAG,CAAC,MAAM;qBACrB,CAAC;oBACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAC5B,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;oBACR,MAAM,CAAC,gBAAgB,CAAC,uCAAuC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzE,iBAAA;AACL,aAAC,CAAC;YAEF,GAAG,CAAC,OAAO,GAAG,YAAA;AACV,gBAAA,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,oBAAA,IAAI,MAAM,KAAK,iBAAiB,CAAC,IAAI,EAAE;AACnC,wBAAA,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,qBAAA,GAAsB,GAAG,CAAC,MAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;AAClG,qBAAA;AAAM,yBAAA;AACH,wBAAA,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,qBAAA,GAAsB,GAAG,CAAC,MAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;AACjG,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,EAAE,CAAC,CAAC;AAC/D,iBAAA;AACL,aAAC,CAAC;YAEF,IAAI,MAAM,KAAK,iBAAiB,CAAC,IAAI,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE;AAC9D,gBAAA,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1B,aAAA;AAAM,iBAAA,IAAI,MAAM,KAAK,iBAAiB,CAAC,GAAG,EAAE;gBACzC,GAAG,CAAC,IAAI,EAAE,CAAC;AACd,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,gBAAgB,CAAC,mCAAmC,CAAC,MAAM,CAAC,CAAC;AACtE,aAAA;AACL,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,GAAmB,EAAE,OAA+B,EAAA;AACtE,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;AAC5B,YAAA,IAAM,SAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,SAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAW,EAAA;gBACrC,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,aAAC,CAAC,CAAC;AACN,SAAA;KACJ,CAAA;AAED;;;;;AAKG;IACK,SAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,GAAmB,EAAA;AACrC,QAAA,IAAM,YAAY,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC;QACjD,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvD,IAAM,UAAU,GAA2B,EAAE,CAAC;AAC9C,QAAA,SAAS,CAAC,OAAO,CAAC,UAAC,KAAa,EAAA;YAC5B,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC,YAAA,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YACjC,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,UAAU,IAAI,SAAS,EAAE;AACzB,gBAAA,UAAU,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;AACtC,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;"}