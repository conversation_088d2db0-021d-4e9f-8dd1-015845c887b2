{"version": 3, "file": "FetchClient.js", "sources": ["../../src/network/FetchClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants, INetworkModule, NetworkRequestOptions, NetworkResponse } from \"@azure/msal-common\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { HTTP_REQUEST_TYPE } from \"../utils/BrowserConstants\";\r\n\r\n/**\r\n * This class implements the Fetch API for GET and POST requests. See more here: https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\r\n */\r\nexport class FetchClient implements INetworkModule {\r\n\r\n    /**\r\n     * Fetch Client for REST endpoints - Get request\r\n     * @param url \r\n     * @param headers \r\n     * @param body \r\n     */\r\n    async sendGetRequestAsync<T>(url: string, options?: NetworkRequestOptions): Promise<NetworkResponse<T>> {\r\n        let response;\r\n        try {\r\n            response = await fetch(url, {\r\n                method: HTTP_REQUEST_TYPE.GET,\r\n                headers: this.getFetchHeaders(options)\r\n            });\r\n        } catch (e) {\r\n            if (window.navigator.onLine) {\r\n                throw BrowserAuthError.createGetRequestFailedError(e, url);\r\n            } else {\r\n                throw BrowserAuthError.createNoNetworkConnectivityError();\r\n            }\r\n        }\r\n\r\n        try {\r\n            return {\r\n                headers: this.getHeaderDict(response.headers),\r\n                body: await response.json() as T,\r\n                status: response.status\r\n            };\r\n        } catch (e) {\r\n            throw BrowserAuthError.createFailedToParseNetworkResponseError(url);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Fetch Client for REST endpoints - Post request\r\n     * @param url \r\n     * @param headers \r\n     * @param body \r\n     */\r\n    async sendPostRequestAsync<T>(url: string, options?: NetworkRequestOptions): Promise<NetworkResponse<T>> {\r\n        const reqBody = (options && options.body) || Constants.EMPTY_STRING;\r\n\r\n        let response;\r\n        try {\r\n            response = await fetch(url, {\r\n                method: HTTP_REQUEST_TYPE.POST,\r\n                headers: this.getFetchHeaders(options),\r\n                body: reqBody\r\n            });\r\n        } catch (e) {\r\n            if (window.navigator.onLine) {\r\n                throw BrowserAuthError.createPostRequestFailedError(e, url);\r\n            } else {\r\n                throw BrowserAuthError.createNoNetworkConnectivityError();\r\n            }\r\n        }\r\n\r\n        try {\r\n            return {\r\n                headers: this.getHeaderDict(response.headers),\r\n                body: await response.json() as T,\r\n                status: response.status\r\n            };\r\n        } catch (e) {\r\n            throw BrowserAuthError.createFailedToParseNetworkResponseError(url);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get Fetch API Headers object from string map\r\n     * @param inputHeaders \r\n     */\r\n    private getFetchHeaders(options?: NetworkRequestOptions): Headers {\r\n        const headers = new Headers();\r\n        if (!(options && options.headers)) {\r\n            return headers;\r\n        }\r\n        const optionsHeaders = options.headers;\r\n        Object.keys(optionsHeaders).forEach((key) => {\r\n            headers.append(key, optionsHeaders[key]);\r\n        });\r\n        return headers;\r\n    }\r\n\r\n    private getHeaderDict(headers: Headers): Record<string, string> {\r\n        const headerDict: Record<string, string> = {};\r\n        headers.forEach((value: string, key: string) => {\r\n            headerDict[key] = value;\r\n        });\r\n        return headerDict;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAMH;;AAEG;AACH,IAAA,WAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,WAAA,GAAA;KA4FC;AA1FG;;;;;AAKG;AACG,IAAA,WAAA,CAAA,SAAA,CAAA,mBAAmB,GAAzB,UAA6B,GAAW,EAAE,OAA+B,EAAA;;;;;;;wBAGtD,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,GAAG,EAAE;gCACxB,MAAM,EAAE,iBAAiB,CAAC,GAAG;AAC7B,gCAAA,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;AACzC,6BAAA,CAAC,CAAA,CAAA;;wBAHF,QAAQ,GAAG,SAGT,CAAC;;;;AAEH,wBAAA,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;4BACzB,MAAM,gBAAgB,CAAC,2BAA2B,CAAC,GAAC,EAAE,GAAG,CAAC,CAAC;AAC9D,yBAAA;AAAM,6BAAA;AACH,4BAAA,MAAM,gBAAgB,CAAC,gCAAgC,EAAE,CAAC;AAC7D,yBAAA;;;;4BAKG,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC;;AACvC,wBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;4BAF/B,OAEI,CAAA,CAAA,cAAA,EAAA,CAAA,IAAI,IAAE,EAAA,CAAA,IAAA,EAA0B,CAAA;4BAChC,EAAM,CAAA,MAAA,GAAE,QAAQ,CAAC,MAAM;AACzB,4BAAA,EAAA,EAAA,CAAA;;;AAEF,wBAAA,MAAM,gBAAgB,CAAC,uCAAuC,CAAC,GAAG,CAAC,CAAC;;;;;AAE3E,KAAA,CAAA;AAED;;;;;AAKG;AACG,IAAA,WAAA,CAAA,SAAA,CAAA,oBAAoB,GAA1B,UAA8B,GAAW,EAAE,OAA+B,EAAA;;;;;;AAChE,wBAAA,OAAO,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,YAAY,CAAC;;;;wBAIrD,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,GAAG,EAAE;gCACxB,MAAM,EAAE,iBAAiB,CAAC,IAAI;AAC9B,gCAAA,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;AACtC,gCAAA,IAAI,EAAE,OAAO;AAChB,6BAAA,CAAC,CAAA,CAAA;;wBAJF,QAAQ,GAAG,SAIT,CAAC;;;;AAEH,wBAAA,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;4BACzB,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,GAAC,EAAE,GAAG,CAAC,CAAC;AAC/D,yBAAA;AAAM,6BAAA;AACH,4BAAA,MAAM,gBAAgB,CAAC,gCAAgC,EAAE,CAAC;AAC7D,yBAAA;;;;4BAKG,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC;;AACvC,wBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;4BAF/B,OAEI,CAAA,CAAA,cAAA,EAAA,CAAA,IAAI,IAAE,EAAA,CAAA,IAAA,EAA0B,CAAA;4BAChC,EAAM,CAAA,MAAA,GAAE,QAAQ,CAAC,MAAM;AACzB,4BAAA,EAAA,EAAA,CAAA;;;AAEF,wBAAA,MAAM,gBAAgB,CAAC,uCAAuC,CAAC,GAAG,CAAC,CAAC;;;;;AAE3E,KAAA,CAAA;AAED;;;AAGG;IACK,WAAe,CAAA,SAAA,CAAA,eAAA,GAAvB,UAAwB,OAA+B,EAAA;AACnD,QAAA,IAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;AAC/B,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AACD,QAAA,IAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC;QACvC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;YACpC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;IAEO,WAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,OAAgB,EAAA;QAClC,IAAM,UAAU,GAA2B,EAAE,CAAC;AAC9C,QAAA,OAAO,CAAC,OAAO,CAAC,UAAC,KAAa,EAAE,GAAW,EAAA;AACvC,YAAA,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA;;;;"}