{"version": 3, "file": "BrowserUtils.js", "sources": ["../../src/utils/BrowserUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants, INetworkModule, UrlString } from \"@azure/msal-common\";\r\nimport { FetchClient } from \"../network/FetchClient\";\r\nimport { XhrClient } from \"../network/XhrClient\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { InteractionType, BrowserConstants } from \"./BrowserConstants\";\r\n\r\n/**\r\n * Utility class for browser specific functions\r\n */\r\nexport class BrowserUtils {\r\n\r\n    // #region Window Navigation and URL management\r\n\r\n    /**\r\n     * Clears hash from window url.\r\n     */\r\n    static clearHash(contentWindow: Window): void {\r\n        // Office.js sets history.replaceState to null\r\n        contentWindow.location.hash = Constants.EMPTY_STRING;\r\n        if (typeof contentWindow.history.replaceState === \"function\") {\r\n            // Full removes \"#\" from url\r\n            contentWindow.history.replaceState(null, Constants.EMPTY_STRING, `${contentWindow.location.origin}${contentWindow.location.pathname}${contentWindow.location.search}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Replaces current hash with hash from provided url\r\n     */\r\n    static replaceHash(url: string): void {\r\n        const urlParts = url.split(\"#\");\r\n        urlParts.shift(); // Remove part before the hash\r\n        window.location.hash = urlParts.length > 0 ? urlParts.join(\"#\") : Constants.EMPTY_STRING;\r\n    }\r\n\r\n    /**\r\n     * Returns boolean of whether the current window is in an iframe or not.\r\n     */\r\n    static isInIframe(): boolean {\r\n        return window.parent !== window;\r\n    }\r\n\r\n    /**\r\n     * Returns boolean of whether or not the current window is a popup opened by msal\r\n     */\r\n    static isInPopup(): boolean {\r\n        return typeof window !== \"undefined\" && !!window.opener && \r\n            window.opener !== window && \r\n            typeof window.name === \"string\" && \r\n            window.name.indexOf(`${BrowserConstants.POPUP_NAME_PREFIX}.`) === 0;\r\n    }\r\n\r\n    // #endregion\r\n\r\n    /**\r\n     * Returns current window URL as redirect uri\r\n     */\r\n    static getCurrentUri(): string {\r\n        return window.location.href.split(\"?\")[0].split(\"#\")[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the homepage url for the current window location.\r\n     */\r\n    static getHomepage(): string {\r\n        const currentUrl = new UrlString(window.location.href);\r\n        const urlComponents = currentUrl.getUrlComponents();\r\n        return `${urlComponents.Protocol}//${urlComponents.HostNameAndPort}/`;\r\n    }\r\n\r\n    /**\r\n     * Returns best compatible network client object. \r\n     */\r\n    static getBrowserNetworkClient(): INetworkModule {\r\n        if (window.fetch && window.Headers) {\r\n            return new FetchClient();\r\n        } else {\r\n            return new XhrClient();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Throws error if we have completed an auth and are \r\n     * attempting another auth request inside an iframe.\r\n     */\r\n    static blockReloadInHiddenIframes(): void {\r\n        const isResponseHash = UrlString.hashContainsKnownProperties(window.location.hash);\r\n        // return an error if called from the hidden iframe created by the msal js silent calls\r\n        if (isResponseHash && BrowserUtils.isInIframe()) {\r\n            throw BrowserAuthError.createBlockReloadInHiddenIframeError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Block redirect operations in iframes unless explicitly allowed\r\n     * @param interactionType Interaction type for the request\r\n     * @param allowRedirectInIframe Config value to allow redirects when app is inside an iframe\r\n     */\r\n    static blockRedirectInIframe(interactionType: InteractionType, allowRedirectInIframe: boolean): void {\r\n        const isIframedApp = BrowserUtils.isInIframe();\r\n        if (interactionType === InteractionType.Redirect && isIframedApp && !allowRedirectInIframe) {\r\n            // If we are not in top frame, we shouldn't redirect. This is also handled by the service.\r\n            throw BrowserAuthError.createRedirectInIframeError(isIframedApp);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Block redirectUri loaded in popup from calling AcquireToken APIs\r\n     */\r\n    static blockAcquireTokenInPopups(): void {\r\n        // Popups opened by msal popup APIs are given a name that starts with \"msal.\"\r\n        if (BrowserUtils.isInPopup()) {\r\n            throw BrowserAuthError.createBlockAcquireTokenInPopupsError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Throws error if token requests are made in non-browser environment\r\n     * @param isBrowserEnvironment Flag indicating if environment is a browser.\r\n     */\r\n    static blockNonBrowserEnvironment(isBrowserEnvironment: boolean): void {\r\n        if (!isBrowserEnvironment) {\r\n            throw BrowserAuthError.createNonBrowserEnvironmentError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Throws error if native brokering is enabled but initialize hasn't been called\r\n     * @param allowNativeBroker \r\n     * @param initialized \r\n     */\r\n    static blockNativeBrokerCalledBeforeInitialized(allowNativeBroker: boolean, initialized: boolean): void {\r\n        if (allowNativeBroker && !initialized) {\r\n            throw BrowserAuthError.createNativeBrokerCalledBeforeInitialize();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns boolean of whether current browser is an Internet Explorer or Edge browser.\r\n     */\r\n    static detectIEOrEdge(): boolean {\r\n        const ua = window.navigator.userAgent;\r\n        const msie = ua.indexOf(\"MSIE \");\r\n        const msie11 = ua.indexOf(\"Trident/\");\r\n        const msedge = ua.indexOf(\"Edge/\");\r\n        const isIE = msie > 0 || msie11 > 0;\r\n        const isEdge = msedge > 0;\r\n        return isIE || isEdge;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAQH;;AAEG;AACH,IAAA,YAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,YAAA,GAAA;KA2IC;;AAvIG;;AAEG;IACI,YAAS,CAAA,SAAA,GAAhB,UAAiB,aAAqB,EAAA;;QAElC,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC,YAAY,CAAC;QACrD,IAAI,OAAO,aAAa,CAAC,OAAO,CAAC,YAAY,KAAK,UAAU,EAAE;;AAE1D,YAAA,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,EAAE,EAAA,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAQ,CAAC,CAAC;AAC1K,SAAA;KACJ,CAAA;AAED;;AAEG;IACI,YAAW,CAAA,WAAA,GAAlB,UAAmB,GAAW,EAAA;QAC1B,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,QAAQ,CAAC,KAAK,EAAE,CAAC;QACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC;KAC5F,CAAA;AAED;;AAEG;AACI,IAAA,YAAA,CAAA,UAAU,GAAjB,YAAA;AACI,QAAA,OAAO,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;KACnC,CAAA;AAED;;AAEG;AACI,IAAA,YAAA,CAAA,SAAS,GAAhB,YAAA;QACI,OAAO,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM;YACnD,MAAM,CAAC,MAAM,KAAK,MAAM;AACxB,YAAA,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;AAC/B,YAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAI,gBAAgB,CAAC,iBAAiB,GAAG,GAAA,CAAC,KAAK,CAAC,CAAC;KAC3E,CAAA;;AAID;;AAEG;AACI,IAAA,YAAA,CAAA,aAAa,GAApB,YAAA;QACI,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3D,CAAA;AAED;;AAEG;AACI,IAAA,YAAA,CAAA,WAAW,GAAlB,YAAA;QACI,IAAM,UAAU,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvD,QAAA,IAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;QACpD,OAAU,aAAa,CAAC,QAAQ,GAAA,IAAA,GAAK,aAAa,CAAC,eAAe,MAAG,CAAC;KACzE,CAAA;AAED;;AAEG;AACI,IAAA,YAAA,CAAA,uBAAuB,GAA9B,YAAA;AACI,QAAA,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE;YAChC,OAAO,IAAI,WAAW,EAAE,CAAC;AAC5B,SAAA;AAAM,aAAA;YACH,OAAO,IAAI,SAAS,EAAE,CAAC;AAC1B,SAAA;KACJ,CAAA;AAED;;;AAGG;AACI,IAAA,YAAA,CAAA,0BAA0B,GAAjC,YAAA;AACI,QAAA,IAAM,cAAc,GAAG,SAAS,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;AAEnF,QAAA,IAAI,cAAc,IAAI,YAAY,CAAC,UAAU,EAAE,EAAE;AAC7C,YAAA,MAAM,gBAAgB,CAAC,oCAAoC,EAAE,CAAC;AACjE,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACI,IAAA,YAAA,CAAA,qBAAqB,GAA5B,UAA6B,eAAgC,EAAE,qBAA8B,EAAA;AACzF,QAAA,IAAM,YAAY,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC;QAC/C,IAAI,eAAe,KAAK,eAAe,CAAC,QAAQ,IAAI,YAAY,IAAI,CAAC,qBAAqB,EAAE;;AAExF,YAAA,MAAM,gBAAgB,CAAC,2BAA2B,CAAC,YAAY,CAAC,CAAC;AACpE,SAAA;KACJ,CAAA;AAED;;AAEG;AACI,IAAA,YAAA,CAAA,yBAAyB,GAAhC,YAAA;;AAEI,QAAA,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE;AAC1B,YAAA,MAAM,gBAAgB,CAAC,oCAAoC,EAAE,CAAC;AACjE,SAAA;KACJ,CAAA;AAED;;;AAGG;IACI,YAA0B,CAAA,0BAAA,GAAjC,UAAkC,oBAA6B,EAAA;QAC3D,IAAI,CAAC,oBAAoB,EAAE;AACvB,YAAA,MAAM,gBAAgB,CAAC,gCAAgC,EAAE,CAAC;AAC7D,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACI,IAAA,YAAA,CAAA,wCAAwC,GAA/C,UAAgD,iBAA0B,EAAE,WAAoB,EAAA;AAC5F,QAAA,IAAI,iBAAiB,IAAI,CAAC,WAAW,EAAE;AACnC,YAAA,MAAM,gBAAgB,CAAC,wCAAwC,EAAE,CAAC;AACrE,SAAA;KACJ,CAAA;AAED;;AAEG;AACI,IAAA,YAAA,CAAA,cAAc,GAArB,YAAA;AACI,QAAA,IAAM,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;QACtC,IAAM,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACjC,IAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACtC,IAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnC,IAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AACpC,QAAA,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;QAC1B,OAAO,IAAI,IAAI,MAAM,CAAC;KACzB,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA;;;;"}