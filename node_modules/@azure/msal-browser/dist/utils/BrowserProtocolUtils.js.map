{"version": 3, "file": "BrowserProtocolUtils.js", "sources": ["../../src/utils/BrowserProtocolUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { InteractionType } from \"./BrowserConstants\";\r\nimport { StringUtils, ClientAuthError, ICrypto, RequestStateObject, ProtocolUtils, ServerAuthorizationCodeResponse, UrlString } from \"@azure/msal-common\";\r\n\r\nexport type BrowserStateObject = {\r\n    interactionType: InteractionType\r\n};\r\n\r\nexport class BrowserProtocolUtils {\r\n\r\n    /**\r\n     * Extracts the BrowserStateObject from the state string.\r\n     * @param browserCrypto \r\n     * @param state \r\n     */\r\n    static extractBrowserRequestState(browserCrypto: ICrypto, state: string): BrowserStateObject | null {\r\n        if (StringUtils.isEmpty(state)) {\r\n            return null;\r\n        }\r\n\r\n        try {\r\n            const requestStateObj: RequestStateObject = ProtocolUtils.parseRequestState(browserCrypto, state);\r\n            return requestStateObj.libraryState.meta as BrowserStateObject;\r\n        } catch (e) {\r\n            throw ClientAuthError.createInvalidStateError(state, e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Parses properties of server response from url hash\r\n     * @param locationHash Hash from url\r\n     */\r\n    static parseServerResponseFromHash(locationHash: string): ServerAuthorizationCodeResponse {\r\n        if (!locationHash) {\r\n            return {};\r\n        }\r\n        \r\n        const hashUrlString = new UrlString(locationHash);\r\n        return UrlString.getDeserializedHash(hashUrlString.getHash());\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AASH,IAAA,oBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,oBAAA,GAAA;KAgCC;AA9BG;;;;AAIG;AACI,IAAA,oBAAA,CAAA,0BAA0B,GAAjC,UAAkC,aAAsB,EAAE,KAAa,EAAA;AACnE,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI;YACA,IAAM,eAAe,GAAuB,aAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAClG,YAAA,OAAO,eAAe,CAAC,YAAY,CAAC,IAA0B,CAAC;AAClE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,MAAM,eAAe,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3D,SAAA;KACJ,CAAA;AAED;;;AAGG;IACI,oBAA2B,CAAA,2BAAA,GAAlC,UAAmC,YAAoB,EAAA;QACnD,IAAI,CAAC,YAAY,EAAE;AACf,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;AAED,QAAA,IAAM,aAAa,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;QAClD,OAAO,SAAS,CAAC,mBAAmB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;KACjE,CAAA;IACL,OAAC,oBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}