/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { OIDC_DEFAULT_SCOPES } from '@azure/msal-common';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Constants
 */
var BrowserConstants = {
    /**
     * Interaction in progress cache value
     */
    INTERACTION_IN_PROGRESS_VALUE: "interaction_in_progress",
    /**
     * Invalid grant error code
     */
    INVALID_GRANT_ERROR: "invalid_grant",
    /**
     * Default popup window width
     */
    POPUP_WIDTH: 483,
    /**
     * Default popup window height
     */
    POPUP_HEIGHT: 600,
    /**
     * Name of the popup window starts with
     */
    POPUP_NAME_PREFIX: "msal",
    /**
     * Default popup monitor poll interval in milliseconds
     */
    DEFAULT_POLL_INTERVAL_MS: 30,
    /**
     * Msal-browser SKU
     */
    MSAL_SKU: "msal.js.browser",
};
var NativeConstants = {
    CHANNEL_ID: "53ee284d-920a-4b59-9d30-a60315b26836",
    PREFERRED_EXTENSION_ID: "ppnbnpeolgkicgegkbkbjmhlideopiji",
    MATS_TELEMETRY: "MATS"
};
var NativeExtensionMethod;
(function (NativeExtensionMethod) {
    NativeExtensionMethod["HandshakeRequest"] = "Handshake";
    NativeExtensionMethod["HandshakeResponse"] = "HandshakeResponse";
    NativeExtensionMethod["GetToken"] = "GetToken";
    NativeExtensionMethod["Response"] = "Response";
})(NativeExtensionMethod || (NativeExtensionMethod = {}));
var BrowserCacheLocation;
(function (BrowserCacheLocation) {
    BrowserCacheLocation["LocalStorage"] = "localStorage";
    BrowserCacheLocation["SessionStorage"] = "sessionStorage";
    BrowserCacheLocation["MemoryStorage"] = "memoryStorage";
})(BrowserCacheLocation || (BrowserCacheLocation = {}));
/**
 * HTTP Request types supported by MSAL.
 */
var HTTP_REQUEST_TYPE;
(function (HTTP_REQUEST_TYPE) {
    HTTP_REQUEST_TYPE["GET"] = "GET";
    HTTP_REQUEST_TYPE["POST"] = "POST";
})(HTTP_REQUEST_TYPE || (HTTP_REQUEST_TYPE = {}));
/**
 * Temporary cache keys for MSAL, deleted after any request.
 */
var TemporaryCacheKeys;
(function (TemporaryCacheKeys) {
    TemporaryCacheKeys["AUTHORITY"] = "authority";
    TemporaryCacheKeys["ACQUIRE_TOKEN_ACCOUNT"] = "acquireToken.account";
    TemporaryCacheKeys["SESSION_STATE"] = "session.state";
    TemporaryCacheKeys["REQUEST_STATE"] = "request.state";
    TemporaryCacheKeys["NONCE_IDTOKEN"] = "nonce.id_token";
    TemporaryCacheKeys["ORIGIN_URI"] = "request.origin";
    TemporaryCacheKeys["RENEW_STATUS"] = "token.renew.status";
    TemporaryCacheKeys["URL_HASH"] = "urlHash";
    TemporaryCacheKeys["REQUEST_PARAMS"] = "request.params";
    TemporaryCacheKeys["SCOPES"] = "scopes";
    TemporaryCacheKeys["INTERACTION_STATUS_KEY"] = "interaction.status";
    TemporaryCacheKeys["CCS_CREDENTIAL"] = "ccs.credential";
    TemporaryCacheKeys["CORRELATION_ID"] = "request.correlationId";
    TemporaryCacheKeys["NATIVE_REQUEST"] = "request.native";
    TemporaryCacheKeys["REDIRECT_CONTEXT"] = "request.redirect.context";
})(TemporaryCacheKeys || (TemporaryCacheKeys = {}));
var StaticCacheKeys;
(function (StaticCacheKeys) {
    StaticCacheKeys["ACCOUNT_KEYS"] = "msal.account.keys";
    StaticCacheKeys["TOKEN_KEYS"] = "msal.token.keys";
})(StaticCacheKeys || (StaticCacheKeys = {}));
/**
 * Cache keys stored in-memory
 */
var InMemoryCacheKeys;
(function (InMemoryCacheKeys) {
    InMemoryCacheKeys["WRAPPER_SKU"] = "wrapper.sku";
    InMemoryCacheKeys["WRAPPER_VER"] = "wrapper.version";
})(InMemoryCacheKeys || (InMemoryCacheKeys = {}));
/**
 * API Codes for Telemetry purposes.
 * Before adding a new code you must claim it in the MSAL Telemetry tracker as these number spaces are shared across all MSALs
 * 0-99 Silent Flow
 * 800-899 Auth Code Flow
 */
var ApiId;
(function (ApiId) {
    ApiId[ApiId["acquireTokenRedirect"] = 861] = "acquireTokenRedirect";
    ApiId[ApiId["acquireTokenPopup"] = 862] = "acquireTokenPopup";
    ApiId[ApiId["ssoSilent"] = 863] = "ssoSilent";
    ApiId[ApiId["acquireTokenSilent_authCode"] = 864] = "acquireTokenSilent_authCode";
    ApiId[ApiId["handleRedirectPromise"] = 865] = "handleRedirectPromise";
    ApiId[ApiId["acquireTokenByCode"] = 866] = "acquireTokenByCode";
    ApiId[ApiId["acquireTokenSilent_silentFlow"] = 61] = "acquireTokenSilent_silentFlow";
    ApiId[ApiId["logout"] = 961] = "logout";
    ApiId[ApiId["logoutPopup"] = 962] = "logoutPopup";
})(ApiId || (ApiId = {}));
/*
 * Interaction type of the API - used for state and telemetry
 */
var InteractionType;
(function (InteractionType) {
    InteractionType["Redirect"] = "redirect";
    InteractionType["Popup"] = "popup";
    InteractionType["Silent"] = "silent";
    InteractionType["None"] = "none";
})(InteractionType || (InteractionType = {}));
/**
 * Types of interaction currently in progress.
 * Used in events in wrapper libraries to invoke functions when certain interaction is in progress or all interactions are complete.
 */
var InteractionStatus;
(function (InteractionStatus) {
    /**
     * Initial status before interaction occurs
     */
    InteractionStatus["Startup"] = "startup";
    /**
     * Status set when all login calls occuring
     */
    InteractionStatus["Login"] = "login";
    /**
     * Status set when logout call occuring
     */
    InteractionStatus["Logout"] = "logout";
    /**
     * Status set for acquireToken calls
     */
    InteractionStatus["AcquireToken"] = "acquireToken";
    /**
     * Status set for ssoSilent calls
     */
    InteractionStatus["SsoSilent"] = "ssoSilent";
    /**
     * Status set when handleRedirect in progress
     */
    InteractionStatus["HandleRedirect"] = "handleRedirect";
    /**
     * Status set when interaction is complete
     */
    InteractionStatus["None"] = "none";
})(InteractionStatus || (InteractionStatus = {}));
var DEFAULT_REQUEST = {
    scopes: OIDC_DEFAULT_SCOPES
};
/**
 * JWK Key Format string (Type MUST be defined for window crypto APIs)
 */
var KEY_FORMAT_JWK = "jwk";
// Supported wrapper SKUs
var WrapperSKU;
(function (WrapperSKU) {
    WrapperSKU["React"] = "@azure/msal-react";
    WrapperSKU["Angular"] = "@azure/msal-angular";
})(WrapperSKU || (WrapperSKU = {}));
// DatabaseStorage Constants
var DB_NAME = "msal.db";
var DB_VERSION = 1;
var DB_TABLE_NAME = DB_NAME + ".keys";
var CacheLookupPolicy;
(function (CacheLookupPolicy) {
    /*
     * acquireTokenSilent will attempt to retrieve an access token from the cache. If the access token is expired
     * or cannot be found the refresh token will be used to acquire a new one. Finally, if the refresh token
     * is expired acquireTokenSilent will attempt to acquire new access and refresh tokens.
     */
    CacheLookupPolicy[CacheLookupPolicy["Default"] = 0] = "Default";
    /*
     * acquireTokenSilent will only look for access tokens in the cache. It will not attempt to renew access or
     * refresh tokens.
     */
    CacheLookupPolicy[CacheLookupPolicy["AccessToken"] = 1] = "AccessToken";
    /*
     * acquireTokenSilent will attempt to retrieve an access token from the cache. If the access token is expired or
     * cannot be found, the refresh token will be used to acquire a new one. If the refresh token is expired, it
     * will not be renewed and acquireTokenSilent will fail.
     */
    CacheLookupPolicy[CacheLookupPolicy["AccessTokenAndRefreshToken"] = 2] = "AccessTokenAndRefreshToken";
    /*
     * acquireTokenSilent will not attempt to retrieve access tokens from the cache and will instead attempt to
     * exchange the cached refresh token for a new access token. If the refresh token is expired, it will not be
     * renewed and acquireTokenSilent will fail.
     */
    CacheLookupPolicy[CacheLookupPolicy["RefreshToken"] = 3] = "RefreshToken";
    /*
     * acquireTokenSilent will not look in the cache for the access token. It will go directly to network with the
     * cached refresh token. If the refresh token is expired an attempt will be made to renew it. This is equivalent to
     * setting "forceRefresh: true".
     */
    CacheLookupPolicy[CacheLookupPolicy["RefreshTokenAndNetwork"] = 4] = "RefreshTokenAndNetwork";
    /*
     * acquireTokenSilent will attempt to renew both access and refresh tokens. It will not look in the cache. This will
     * always fail if 3rd party cookies are blocked by the browser.
     */
    CacheLookupPolicy[CacheLookupPolicy["Skip"] = 5] = "Skip";
})(CacheLookupPolicy || (CacheLookupPolicy = {}));

export { ApiId, BrowserCacheLocation, BrowserConstants, CacheLookupPolicy, DB_NAME, DB_TABLE_NAME, DB_VERSION, DEFAULT_REQUEST, HTTP_REQUEST_TYPE, InMemoryCacheKeys, InteractionStatus, InteractionType, KEY_FORMAT_JWK, NativeConstants, NativeExtensionMethod, StaticCacheKeys, TemporaryCacheKeys, WrapperSKU };
//# sourceMappingURL=BrowserConstants.js.map
