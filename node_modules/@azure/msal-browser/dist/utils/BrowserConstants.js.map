{"version": 3, "file": "BrowserConstants.js", "sources": ["../../src/utils/BrowserConstants.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { OIDC_DEFAULT_SCOPES } from \"@azure/msal-common\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\n\r\n/**\r\n * Constants\r\n */\r\nexport const BrowserConstants = {\r\n    /**\r\n     * Interaction in progress cache value\r\n     */\r\n    INTERACTION_IN_PROGRESS_VALUE: \"interaction_in_progress\",\r\n    /**\r\n     * Invalid grant error code\r\n     */\r\n    INVALID_GRANT_ERROR: \"invalid_grant\",\r\n    /**\r\n     * Default popup window width\r\n     */\r\n    POPUP_WIDTH: 483,\r\n    /**\r\n     * Default popup window height\r\n     */\r\n    POPUP_HEIGHT: 600,\r\n    /**\r\n     * Name of the popup window starts with\r\n     */\r\n    POPUP_NAME_PREFIX: \"msal\",\r\n    /**\r\n     * Default popup monitor poll interval in milliseconds\r\n     */\r\n    DEFAULT_POLL_INTERVAL_MS: 30,\r\n    /**\r\n     * Msal-browser SKU\r\n     */\r\n    MSAL_SKU: \"msal.js.browser\",\r\n};\r\n\r\nexport const NativeConstants = {\r\n    CHANNEL_ID: \"53ee284d-920a-4b59-9d30-a60315b26836\",\r\n    PREFERRED_EXTENSION_ID: \"ppnbnpeolgkicgegkbkbjmhlideopiji\",\r\n    MATS_TELEMETRY: \"MATS\"\r\n};\r\n\r\nexport enum NativeExtensionMethod {\r\n    HandshakeRequest = \"Handshake\",\r\n    HandshakeResponse = \"HandshakeResponse\",\r\n    GetToken = \"GetToken\",\r\n    Response = \"Response\"\r\n}\r\n\r\nexport enum BrowserCacheLocation {\r\n    LocalStorage = \"localStorage\",\r\n    SessionStorage = \"sessionStorage\",\r\n    MemoryStorage = \"memoryStorage\"\r\n}\r\n\r\n/**\r\n * HTTP Request types supported by MSAL.\r\n */\r\nexport enum HTTP_REQUEST_TYPE {\r\n    GET = \"GET\",\r\n    POST = \"POST\"\r\n}\r\n\r\n/**\r\n * Temporary cache keys for MSAL, deleted after any request.\r\n */\r\nexport enum TemporaryCacheKeys {\r\n    AUTHORITY = \"authority\",\r\n    ACQUIRE_TOKEN_ACCOUNT = \"acquireToken.account\",\r\n    SESSION_STATE = \"session.state\",\r\n    REQUEST_STATE = \"request.state\",\r\n    NONCE_IDTOKEN = \"nonce.id_token\",\r\n    ORIGIN_URI = \"request.origin\",\r\n    RENEW_STATUS = \"token.renew.status\",\r\n    URL_HASH = \"urlHash\",\r\n    REQUEST_PARAMS = \"request.params\",\r\n    SCOPES = \"scopes\",\r\n    INTERACTION_STATUS_KEY = \"interaction.status\",\r\n    CCS_CREDENTIAL = \"ccs.credential\",\r\n    CORRELATION_ID = \"request.correlationId\",\r\n    NATIVE_REQUEST = \"request.native\",\r\n    REDIRECT_CONTEXT = \"request.redirect.context\"\r\n}\r\n\r\nexport enum StaticCacheKeys {\r\n    ACCOUNT_KEYS = \"msal.account.keys\",\r\n    TOKEN_KEYS = \"msal.token.keys\"\r\n}\r\n\r\n/**\r\n * Cache keys stored in-memory\r\n */\r\nexport enum InMemoryCacheKeys {\r\n    WRAPPER_SKU = \"wrapper.sku\",\r\n    WRAPPER_VER = \"wrapper.version\"\r\n}\r\n\r\n/**\r\n * API Codes for Telemetry purposes. \r\n * Before adding a new code you must claim it in the MSAL Telemetry tracker as these number spaces are shared across all MSALs\r\n * 0-99 Silent Flow\r\n * 800-899 Auth Code Flow\r\n */\r\nexport enum ApiId {\r\n    acquireTokenRedirect = 861,\r\n    acquireTokenPopup = 862,\r\n    ssoSilent = 863,\r\n    acquireTokenSilent_authCode = 864,\r\n    handleRedirectPromise = 865,\r\n    acquireTokenByCode = 866,\r\n    acquireTokenSilent_silentFlow = 61,\r\n    logout = 961,\r\n    logoutPopup = 962\r\n}\r\n\r\n/*\r\n * Interaction type of the API - used for state and telemetry\r\n */\r\nexport enum InteractionType {\r\n    Redirect = \"redirect\",\r\n    Popup = \"popup\",\r\n    Silent = \"silent\",\r\n    None = \"none\"\r\n}\r\n\r\n/**\r\n * Types of interaction currently in progress.\r\n * Used in events in wrapper libraries to invoke functions when certain interaction is in progress or all interactions are complete.\r\n */\r\nexport enum InteractionStatus {\r\n    /**\r\n     * Initial status before interaction occurs\r\n     */\r\n    Startup = \"startup\",\r\n    /**\r\n     * Status set when all login calls occuring\r\n     */\r\n    Login = \"login\",\r\n    /**\r\n     * Status set when logout call occuring\r\n     */ \r\n    Logout = \"logout\",\r\n    /**\r\n     * Status set for acquireToken calls\r\n     */\r\n    AcquireToken = \"acquireToken\",\r\n    /**\r\n     * Status set for ssoSilent calls\r\n     */\r\n    SsoSilent = \"ssoSilent\",\r\n    /**\r\n     * Status set when handleRedirect in progress\r\n     */\r\n    HandleRedirect = \"handleRedirect\",\r\n    /**\r\n     * Status set when interaction is complete\r\n     */\r\n    None = \"none\"\r\n}\r\n\r\nexport const DEFAULT_REQUEST: RedirectRequest|PopupRequest = {\r\n    scopes: OIDC_DEFAULT_SCOPES\r\n};\r\n\r\n/**\r\n * JWK Key Format string (Type MUST be defined for window crypto APIs)\r\n */\r\nexport const KEY_FORMAT_JWK = \"jwk\";\r\n\r\n// Supported wrapper SKUs\r\nexport enum WrapperSKU {\r\n    React = \"@azure/msal-react\",\r\n    Angular = \"@azure/msal-angular\"\r\n}\r\n\r\n// DatabaseStorage Constants\r\nexport const DB_NAME = \"msal.db\";\r\nexport const DB_VERSION = 1;\r\nexport const DB_TABLE_NAME = `${DB_NAME}.keys`;\r\n\r\nexport enum CacheLookupPolicy {\r\n    /*\r\n     * acquireTokenSilent will attempt to retrieve an access token from the cache. If the access token is expired\r\n     * or cannot be found the refresh token will be used to acquire a new one. Finally, if the refresh token\r\n     * is expired acquireTokenSilent will attempt to acquire new access and refresh tokens.\r\n     */\r\n    Default = 0, // 0 is falsy, is equivalent to not passing in a CacheLookupPolicy\r\n    /*\r\n     * acquireTokenSilent will only look for access tokens in the cache. It will not attempt to renew access or\r\n     * refresh tokens.\r\n     */\r\n    AccessToken = 1,\r\n    /*\r\n     * acquireTokenSilent will attempt to retrieve an access token from the cache. If the access token is expired or\r\n     * cannot be found, the refresh token will be used to acquire a new one. If the refresh token is expired, it\r\n     * will not be renewed and acquireTokenSilent will fail.\r\n     */\r\n    AccessTokenAndRefreshToken = 2,\r\n    /*\r\n     * acquireTokenSilent will not attempt to retrieve access tokens from the cache and will instead attempt to\r\n     * exchange the cached refresh token for a new access token. If the refresh token is expired, it will not be\r\n     * renewed and acquireTokenSilent will fail.\r\n     */\r\n    RefreshToken = 3,\r\n    /*\r\n     * acquireTokenSilent will not look in the cache for the access token. It will go directly to network with the\r\n     * cached refresh token. If the refresh token is expired an attempt will be made to renew it. This is equivalent to\r\n     * setting \"forceRefresh: true\".\r\n     */\r\n    RefreshTokenAndNetwork = 4,\r\n    /*\r\n     * acquireTokenSilent will attempt to renew both access and refresh tokens. It will not look in the cache. This will\r\n     * always fail if 3rd party cookies are blocked by the browser.\r\n     */\r\n    Skip = 5,\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAMH;;AAEG;AACU,IAAA,gBAAgB,GAAG;AAC5B;;AAEG;AACH,IAAA,6BAA6B,EAAE,yBAAyB;AACxD;;AAEG;AACH,IAAA,mBAAmB,EAAE,eAAe;AACpC;;AAEG;AACH,IAAA,WAAW,EAAE,GAAG;AAChB;;AAEG;AACH,IAAA,YAAY,EAAE,GAAG;AACjB;;AAEG;AACH,IAAA,iBAAiB,EAAE,MAAM;AACzB;;AAEG;AACH,IAAA,wBAAwB,EAAE,EAAE;AAC5B;;AAEG;AACH,IAAA,QAAQ,EAAE,iBAAiB;EAC7B;AAEW,IAAA,eAAe,GAAG;AAC3B,IAAA,UAAU,EAAE,sCAAsC;AAClD,IAAA,sBAAsB,EAAE,kCAAkC;AAC1D,IAAA,cAAc,EAAE,MAAM;EACxB;IAEU,sBAKX;AALD,CAAA,UAAY,qBAAqB,EAAA;AAC7B,IAAA,qBAAA,CAAA,kBAAA,CAAA,GAAA,WAA8B,CAAA;AAC9B,IAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,qBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,qBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACzB,CAAC,EALW,qBAAqB,KAArB,qBAAqB,GAKhC,EAAA,CAAA,CAAA,CAAA;IAEW,qBAIX;AAJD,CAAA,UAAY,oBAAoB,EAAA;AAC5B,IAAA,oBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,oBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,oBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AACnC,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,GAI/B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,kBAGX;AAHD,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,GAG5B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,mBAgBX;AAhBD,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,kBAAA,CAAA,uBAAA,CAAA,GAAA,sBAA8C,CAAA;AAC9C,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,gBAAgC,CAAA;AAChC,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,gBAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,oBAAmC,CAAA;AACnC,IAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,SAAoB,CAAA;AACpB,IAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,kBAAA,CAAA,wBAAA,CAAA,GAAA,oBAA6C,CAAA;AAC7C,IAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,uBAAwC,CAAA;AACxC,IAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,kBAAA,CAAA,kBAAA,CAAA,GAAA,0BAA6C,CAAA;AACjD,CAAC,EAhBW,kBAAkB,KAAlB,kBAAkB,GAgB7B,EAAA,CAAA,CAAA,CAAA;IAEW,gBAGX;AAHD,CAAA,UAAY,eAAe,EAAA;AACvB,IAAA,eAAA,CAAA,cAAA,CAAA,GAAA,mBAAkC,CAAA;AAClC,IAAA,eAAA,CAAA,YAAA,CAAA,GAAA,iBAA8B,CAAA;AAClC,CAAC,EAHW,eAAe,KAAf,eAAe,GAG1B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,kBAGX;AAHD,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,iBAA+B,CAAA;AACnC,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,GAG5B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;AAKG;IACS,MAUX;AAVD,CAAA,UAAY,KAAK,EAAA;AACb,IAAA,KAAA,CAAA,KAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC1B,IAAA,KAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,GAAA,CAAA,GAAA,WAAe,CAAA;AACf,IAAA,KAAA,CAAA,KAAA,CAAA,6BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,6BAAiC,CAAA;AACjC,IAAA,KAAA,CAAA,KAAA,CAAA,uBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,uBAA2B,CAAA;AAC3B,IAAA,KAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,KAAA,CAAA,KAAA,CAAA,+BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,+BAAkC,CAAA;AAClC,IAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,GAAA,CAAA,GAAA,QAAY,CAAA;AACZ,IAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,GAAA,GAAA,CAAA,GAAA,aAAiB,CAAA;AACrB,CAAC,EAVW,KAAK,KAAL,KAAK,GAUhB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,gBAKX;AALD,CAAA,UAAY,eAAe,EAAA;AACvB,IAAA,eAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,eAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EALW,eAAe,KAAf,eAAe,GAK1B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,kBA6BX;AA7BD,CAAA,UAAY,iBAAiB,EAAA;AACzB;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB;;AAEG;AACH,IAAA,iBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf;;AAEG;AACH,IAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB;;AAEG;AACH,IAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B;;AAEG;AACH,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB;;AAEG;AACH,IAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC;;AAEG;AACH,IAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EA7BW,iBAAiB,KAAjB,iBAAiB,GA6B5B,EAAA,CAAA,CAAA,CAAA;AAEY,IAAA,eAAe,GAAiC;AACzD,IAAA,MAAM,EAAE,mBAAmB;EAC7B;AAEF;;AAEG;AACI,IAAM,cAAc,GAAG,MAAM;AAEpC;IACY,WAGX;AAHD,CAAA,UAAY,UAAU,EAAA;AAClB,IAAA,UAAA,CAAA,OAAA,CAAA,GAAA,mBAA2B,CAAA;AAC3B,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,qBAA+B,CAAA;AACnC,CAAC,EAHW,UAAU,KAAV,UAAU,GAGrB,EAAA,CAAA,CAAA,CAAA;AAED;AACO,IAAM,OAAO,GAAG,UAAU;AAC1B,IAAM,UAAU,GAAG,EAAE;AACf,IAAA,aAAa,GAAM,OAAO,WAAQ;IAEnC,kBAmCX;AAnCD,CAAA,UAAY,iBAAiB,EAAA;AACzB;;;;AAIG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe,CAAA;AACf;;;;AAIG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,4BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,4BAA8B,CAAA;AAC9B;;;;AAIG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB,CAAA;AAChB;;;;AAIG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,wBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,wBAA0B,CAAA;AAC1B;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EAnCW,iBAAiB,KAAjB,iBAAiB,GAmC5B,EAAA,CAAA,CAAA;;;;"}