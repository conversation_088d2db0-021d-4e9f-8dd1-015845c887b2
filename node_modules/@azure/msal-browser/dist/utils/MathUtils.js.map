{"version": 3, "file": "MathUtils.js", "sources": ["../../src/utils/MathUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Utility class for math specific functions in browser.\r\n */\r\nexport class MathUtils {\r\n\r\n    /**\r\n     * Decimal to Hex\r\n     *\r\n     * @param num\r\n     */\r\n    static decimalToHex(num: number): string {\r\n        let hex: string = num.toString(16);\r\n        while (hex.length < 2) {\r\n            hex = \"0\" + hex;\r\n        }\r\n        return hex;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAEH;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,SAAA,GAAA;KAcC;AAZG;;;;AAIG;IACI,SAAY,CAAA,YAAA,GAAnB,UAAoB,GAAW,EAAA;QAC3B,IAAI,GAAG,GAAW,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AACnC,QAAA,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;AACnB,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACnB,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;"}