{"version": 3, "file": "BrowserStringUtils.js", "sources": ["../../src/utils/BrowserStringUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"@azure/msal-common\";\r\n\r\n/**\r\n * Utility functions for strings in a browser. See here for implementation details:\r\n * https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#Solution_2_%E2%80%93_JavaScript's_UTF-16_%3E_UTF-8_%3E_base64\r\n */\r\nexport class BrowserStringUtils {\r\n\r\n    /**\r\n     * Converts string to Uint8Array\r\n     * @param sDOMStr \r\n     */\r\n    static stringToUtf8Arr (sDOMStr: string): Uint8Array {\r\n        let nChr;\r\n        let nArrLen = 0;\r\n        const nStrLen = sDOMStr.length;\r\n        /* mapping... */\r\n        for (let nMapIdx = 0; nMapIdx < nStrLen; nMapIdx++) {\r\n            nChr = sDOMStr.charCodeAt(nMapIdx);\r\n            nArrLen += nChr < 0x80 ? 1 : nChr < 0x800 ? 2 : nChr < 0x10000 ? 3 : nChr < 0x200000 ? 4 : nChr < 0x4000000 ? 5 : 6;\r\n        }\r\n\r\n        const aBytes = new Uint8Array(nArrLen);\r\n\r\n        /* transcription... */\r\n\r\n        for (let nIdx = 0, nChrIdx = 0; nIdx < nArrLen; nChrIdx++) {\r\n            nChr = sDOMStr.charCodeAt(nChrIdx);\r\n            if (nChr < 128) {\r\n                /* one byte */\r\n                aBytes[nIdx++] = nChr;\r\n            } else if (nChr < 0x800) {\r\n                /* two bytes */\r\n                aBytes[nIdx++] = 192 + (nChr >>> 6);\r\n                aBytes[nIdx++] = 128 + (nChr & 63);\r\n            } else if (nChr < 0x10000) {\r\n                /* three bytes */\r\n                aBytes[nIdx++] = 224 + (nChr >>> 12);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 6 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr & 63);\r\n            } else if (nChr < 0x200000) {\r\n                /* four bytes */\r\n                aBytes[nIdx++] = 240 + (nChr >>> 18);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 12 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 6 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr & 63);\r\n            } else if (nChr < 0x4000000) {\r\n                /* five bytes */\r\n                aBytes[nIdx++] = 248 + (nChr >>> 24);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 18 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 12 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 6 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr & 63);\r\n            } else /* if (nChr <= 0x7fffffff) */ {\r\n                /* six bytes */\r\n                aBytes[nIdx++] = 252 + (nChr >>> 30);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 24 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 18 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 12 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr >>> 6 & 63);\r\n                aBytes[nIdx++] = 128 + (nChr & 63);\r\n            }\r\n        }\r\n\r\n        return aBytes;      \r\n    }\r\n\r\n    /**\r\n     * Converst string to ArrayBuffer\r\n     * @param dataString \r\n     */\r\n    static stringToArrayBuffer(dataString: string): ArrayBuffer {\r\n        const data = new ArrayBuffer(dataString.length);\r\n        const dataView = new Uint8Array(data);\r\n        for (let i: number = 0; i < dataString.length; i++) {\r\n            dataView[i] = dataString.charCodeAt(i);\r\n        }\r\n        return data;\r\n    }\r\n\r\n    /**\r\n     * Converts Uint8Array to a string\r\n     * @param aBytes \r\n     */\r\n    static utf8ArrToString (aBytes: Uint8Array): string {\r\n        let sView = Constants.EMPTY_STRING;\r\n        for (let nPart, nLen = aBytes.length, nIdx = 0; nIdx < nLen; nIdx++) {\r\n            nPart = aBytes[nIdx];\r\n            sView += String.fromCharCode(\r\n                nPart > 251 && nPart < 254 && nIdx + 5 < nLen ? /* six bytes */\r\n                    /* (nPart - 252 << 30) may be not so safe in ECMAScript! So...: */\r\n                    (nPart - 252) * 1073741824 + (aBytes[++nIdx] - 128 << 24) + (aBytes[++nIdx] - 128 << 18) + (aBytes[++nIdx] - 128 << 12) + (aBytes[++nIdx] - 128 << 6) + aBytes[++nIdx] - 128\r\n                    : nPart > 247 && nPart < 252 && nIdx + 4 < nLen ? /* five bytes */\r\n                        (nPart - 248 << 24) + (aBytes[++nIdx] - 128 << 18) + (aBytes[++nIdx] - 128 << 12) + (aBytes[++nIdx] - 128 << 6) + aBytes[++nIdx] - 128\r\n                        : nPart > 239 && nPart < 248 && nIdx + 3 < nLen ? /* four bytes */\r\n                            (nPart - 240 << 18) + (aBytes[++nIdx] - 128 << 12) + (aBytes[++nIdx] - 128 << 6) + aBytes[++nIdx] - 128\r\n                            : nPart > 223 && nPart < 240 && nIdx + 2 < nLen ? /* three bytes */\r\n                                (nPart - 224 << 12) + (aBytes[++nIdx] - 128 << 6) + aBytes[++nIdx] - 128\r\n                                : nPart > 191 && nPart < 224 && nIdx + 1 < nLen ? /* two bytes */\r\n                                    (nPart - 192 << 6) + aBytes[++nIdx] - 128\r\n                                    : /* nPart < 127 ? */ /* one byte */\r\n                                    nPart\r\n            );\r\n        }\r\n        return sView;\r\n    }\r\n\r\n    /**\r\n     * Returns stringified jwk.\r\n     * @param jwk \r\n     */\r\n    static getSortedObjectString(obj: object): string {\r\n        return JSON.stringify(obj, Object.keys(obj).sort());\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAIH;;;AAGG;AACH,IAAA,kBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,kBAAA,GAAA;KA4GC;AA1GG;;;AAGG;IACI,kBAAe,CAAA,eAAA,GAAtB,UAAwB,OAAe,EAAA;AACnC,QAAA,IAAI,IAAI,CAAC;QACT,IAAI,OAAO,GAAG,CAAC,CAAC;AAChB,QAAA,IAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;;QAE/B,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE;AAChD,YAAA,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;AACvH,SAAA;AAED,QAAA,IAAM,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;;AAIvC,QAAA,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE;AACvD,YAAA,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,IAAI,IAAI,GAAG,GAAG,EAAE;;AAEZ,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;AACzB,aAAA;iBAAM,IAAI,IAAI,GAAG,KAAK,EAAE;;AAErB,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC;AACpC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AACtC,aAAA;iBAAM,IAAI,IAAI,GAAG,OAAO,EAAE;;AAEvB,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;AACrC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AACtC,aAAA;iBAAM,IAAI,IAAI,GAAG,QAAQ,EAAE;;AAExB,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;AACrC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AACtC,aAAA;iBAAM,IAAI,IAAI,GAAG,SAAS,EAAE;;AAEzB,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;AACrC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AACtC,aAAA;AAAM,+CAA8B;;AAEjC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;AACrC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AACtC,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;AAGG;IACI,kBAAmB,CAAA,mBAAA,GAA1B,UAA2B,UAAkB,EAAA;QACzC,IAAM,IAAI,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAChD,QAAA,IAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,QAAA,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC1C,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACI,kBAAe,CAAA,eAAA,GAAtB,UAAwB,MAAkB,EAAA;AACtC,QAAA,IAAI,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC;QACnC,KAAK,IAAI,KAAK,GAAA,KAAA,CAAA,EAAE,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE;AACjE,YAAA,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YACrB,KAAK,IAAI,MAAM,CAAC,YAAY,CACxB,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI;;AAEzC,gBAAA,CAAC,KAAK,GAAG,GAAG,IAAI,UAAU,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG;AAC5K,kBAAE,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI;oBAC3C,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG;AACtI,sBAAE,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3C,wBAAA,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG;AACvG,0BAAE,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI;4BAC3C,CAAC,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG;AACxE,8BAAE,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3C,gCAAA,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG;;AAEzC,oCAAA,KAAK,CAC5B,CAAC;AACL,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;AAGG;IACI,kBAAqB,CAAA,qBAAA,GAA5B,UAA6B,GAAW,EAAA;AACpC,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;KACvD,CAAA;IACL,OAAC,kBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}