{"version": 3, "file": "NativeMessageHandler.d.ts", "sourceRoot": "", "sources": ["../../../src/broker/nativeBroker/NativeMessageHandler.ts"], "names": [], "mappings": "AAMA,OAAO,EACH,MAAM,EAEN,oBAAoB,EAGpB,kBAAkB,EACrB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAA0B,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AAGrF,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAOlE,qBAAa,oBAAoB;IAC7B,OAAO,CAAC,WAAW,CAAqB;IACxC,OAAO,CAAC,gBAAgB,CAAqB;IAC7C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAS;IAC5C,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,SAAS,CAAyC;IAC1D,OAAO,CAAC,kBAAkB,CAAuC;IACjE,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgC;IAC/D,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAqB;IACvD,OAAO,CAAC,QAAQ,CAAC,cAAc,CAA6B;gBAEhD,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,WAAW,CAAC,EAAE,MAAM;IAanH;;;OAGG;IACG,WAAW,CAAC,IAAI,EAAE,0BAA0B,GAAG,OAAO,CAAC,MAAM,CAAC;IAkBpE;;;;;OAKG;WACU,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,iBAAiB,EAAE,kBAAkB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAc7I;;OAEG;YACW,oBAAoB;IAyClC;;;OAGG;IACH,OAAO,CAAC,eAAe;IAgCvB;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAyDxB;;;OAGG;IACH,cAAc,IAAI,MAAM,GAAG,SAAS;IAIpC;;;OAGG;IACH,mBAAmB,IAAI,MAAM,GAAG,SAAS;IAIzC;;;;;;OAMG;IACH,MAAM,CAAC,iBAAiB,CAAC,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,EAAE,uBAAuB,CAAC,EAAE,oBAAoB,EAAE,oBAAoB,CAAC,EAAE,oBAAoB,GAAG,OAAO;CA4B/K"}