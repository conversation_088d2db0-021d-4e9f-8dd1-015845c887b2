{"version": 3, "file": "DatabaseStorage.js", "sources": ["../../src/cache/DatabaseStorage.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { DB_NAME, DB_TABLE_NAME, DB_VERSION } from \"../utils/BrowserConstants\";\r\nimport { IAsyncStorage } from \"./IAsyncMemoryStorage\";\r\n\r\ninterface IDBOpenDBRequestEvent extends Event {\r\n    target: IDBOpenDBRequest & EventTarget;\r\n}\r\n\r\ninterface IDBOpenOnUpgradeNeededEvent extends IDBVersionChangeEvent {\r\n    target: IDBOpenDBRequest & EventTarget;\r\n}\r\n\r\ninterface IDBRequestEvent extends Event {\r\n    target: IDBRequest & EventTarget;\r\n}\r\n\r\n/**\r\n * Storage wrapper for IndexedDB storage in browsers: https://developer.mozilla.org/en-US/docs/Web/API/IndexedDB_API\r\n */\r\nexport class DatabaseStorage<T> implements IAsyncStorage<T> {\r\n    private db: IDBDatabase|undefined;\r\n    private dbName: string;\r\n    private tableName: string;\r\n    private version: number;\r\n    private dbOpen: boolean;\r\n\r\n    constructor() {\r\n        this.dbName = DB_NAME;\r\n        this.version = DB_VERSION;\r\n        this.tableName = DB_TABLE_NAME;\r\n        this.dbOpen = false;\r\n    }\r\n\r\n    /**\r\n     * Opens IndexedDB instance.\r\n     */\r\n    async open(): Promise<void> {\r\n        return new Promise((resolve, reject) => {\r\n            const openDB = window.indexedDB.open(this.dbName, this.version);\r\n            openDB.addEventListener(\"upgradeneeded\", (e: IDBVersionChangeEvent) => {\r\n                const event = e as IDBOpenOnUpgradeNeededEvent;\r\n                event.target.result.createObjectStore(this.tableName);\r\n            });\r\n            openDB.addEventListener(\"success\", (e: Event) => {\r\n                const event = e as IDBOpenDBRequestEvent;\r\n                this.db = event.target.result;\r\n                this.dbOpen = true;\r\n                resolve();\r\n            });\r\n            openDB.addEventListener(\"error\",  () => reject(BrowserAuthError.createDatabaseUnavailableError()));\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Closes the connection to IndexedDB database when all pending transactions\r\n     * complete.\r\n     */\r\n    closeConnection(): void {\r\n        const db = this.db;\r\n        if (db && this.dbOpen) {\r\n            db.close();\r\n            this.dbOpen = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Opens database if it's not already open\r\n     */\r\n    private async validateDbIsOpen(): Promise<void> {\r\n        if (!this.dbOpen) {\r\n            return await this.open();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Retrieves item from IndexedDB instance.\r\n     * @param key \r\n     */\r\n    async getItem(key: string): Promise<T | null> {\r\n        await this.validateDbIsOpen();\r\n        return new Promise<T>((resolve, reject) => {\r\n            // TODO: Add timeouts?\r\n            if (!this.db) {\r\n                return reject(BrowserAuthError.createDatabaseNotOpenError());\r\n            }\r\n            const transaction = this.db.transaction([this.tableName], \"readonly\");\r\n            const objectStore = transaction.objectStore(this.tableName);\r\n            const dbGet = objectStore.get(key);\r\n            \r\n            dbGet.addEventListener(\"success\", (e: Event) => {\r\n                const event = e as IDBRequestEvent;\r\n                this.closeConnection();\r\n                resolve(event.target.result);\r\n            });\r\n\r\n            dbGet.addEventListener(\"error\", (e: Event) => {\r\n                this.closeConnection();\r\n                reject(e);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds item to IndexedDB under given key\r\n     * @param key \r\n     * @param payload \r\n     */\r\n    async setItem(key: string, payload: T): Promise<void> {\r\n        await this.validateDbIsOpen();\r\n        return new Promise<void>((resolve: Function, reject: Function) => {\r\n            // TODO: Add timeouts?\r\n            if (!this.db) {\r\n                return reject(BrowserAuthError.createDatabaseNotOpenError());\r\n            }\r\n            const transaction = this.db.transaction([this.tableName], \"readwrite\");\r\n\r\n            const objectStore = transaction.objectStore(this.tableName);\r\n\r\n            const dbPut = objectStore.put(payload, key);\r\n\r\n            dbPut.addEventListener(\"success\", () => {\r\n                this.closeConnection();\r\n                resolve();\r\n            });\r\n\r\n            dbPut.addEventListener(\"error\", (e) => {\r\n                this.closeConnection();\r\n                reject(e);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Removes item from IndexedDB under given key\r\n     * @param key\r\n     */\r\n    async removeItem(key: string): Promise<void> {\r\n        await this.validateDbIsOpen();\r\n        return new Promise<void>((resolve: Function, reject: Function) => {\r\n            if (!this.db) {\r\n                return reject(BrowserAuthError.createDatabaseNotOpenError());\r\n            }\r\n\r\n            const transaction = this.db.transaction([this.tableName], \"readwrite\");\r\n            const objectStore = transaction.objectStore(this.tableName);\r\n            const dbDelete = objectStore.delete(key);\r\n\r\n            dbDelete.addEventListener(\"success\", () => {\r\n                this.closeConnection();\r\n                resolve();\r\n            });\r\n\r\n            dbDelete.addEventListener(\"error\", (e) => {\r\n                this.closeConnection();\r\n                reject(e);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Get all the keys from the storage object as an iterable array of strings.\r\n     */\r\n    async getKeys(): Promise<string[]> {\r\n        await this.validateDbIsOpen();\r\n        return new Promise<string[]>((resolve: Function, reject: Function) => {\r\n            if (!this.db) {\r\n                return reject(BrowserAuthError.createDatabaseNotOpenError());\r\n            }\r\n\r\n            const transaction = this.db.transaction([this.tableName], \"readonly\");\r\n            const objectStore = transaction.objectStore(this.tableName);\r\n            const dbGetKeys = objectStore.getAllKeys();\r\n\r\n            dbGetKeys.addEventListener(\"success\", (e: Event) => {\r\n                const event = e as IDBRequestEvent;\r\n                this.closeConnection();\r\n                resolve(event.target.result);\r\n            });\r\n\r\n            dbGetKeys.addEventListener(\"error\",  (e: Event) => {\r\n                this.closeConnection();\r\n                reject(e);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * \r\n     * Checks whether there is an object under the search key in the object store\r\n     */\r\n    async containsKey(key: string): Promise<boolean> {\r\n        await this.validateDbIsOpen();\r\n\r\n        return new Promise<boolean>((resolve: Function, reject: Function) => {\r\n            if (!this.db) {\r\n                return reject(BrowserAuthError.createDatabaseNotOpenError());\r\n            }\r\n\r\n            const transaction = this.db.transaction([this.tableName], \"readonly\");\r\n            const objectStore = transaction.objectStore(this.tableName);\r\n            const dbContainsKey = objectStore.count(key);\r\n\r\n            dbContainsKey.addEventListener(\"success\", (e: Event) => {\r\n                const event = e as IDBRequestEvent;\r\n                this.closeConnection();\r\n                resolve(event.target.result === 1);\r\n            });\r\n\r\n            dbContainsKey.addEventListener(\"error\", (e: Event) => {\r\n                this.closeConnection();\r\n                reject(e);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Deletes the MSAL database. The database is deleted rather than cleared to make it possible\r\n     * for client applications to downgrade to a previous MSAL version without worrying about forward compatibility issues\r\n     * with IndexedDB database versions.\r\n     */\r\n    async deleteDatabase(): Promise<boolean> {\r\n        // Check if database being deleted exists\r\n\r\n        if (this.db && this.dbOpen) {\r\n            this.closeConnection();\r\n        }\r\n\r\n        return new Promise<boolean>((resolve: Function, reject: Function) => {\r\n            const deleteDbRequest = window.indexedDB.deleteDatabase(DB_NAME);\r\n            deleteDbRequest.addEventListener(\"success\", () => resolve(true));\r\n            deleteDbRequest.addEventListener(\"blocked\", () => resolve(true));\r\n            deleteDbRequest.addEventListener(\"error\", () => reject(false));\r\n        });\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAkBH;;AAEG;AACH,IAAA,eAAA,kBAAA,YAAA;AAOI,IAAA,SAAA,eAAA,GAAA;AACI,QAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;AAC/B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACvB;AAED;;AAEG;AACG,IAAA,eAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;AACI,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AAC/B,wBAAA,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAI,CAAC,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,CAAC;AAChE,wBAAA,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,UAAC,CAAwB,EAAA;4BAC9D,IAAM,KAAK,GAAG,CAAgC,CAAC;4BAC/C,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;AAC1D,yBAAC,CAAC,CAAC;AACH,wBAAA,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAC,CAAQ,EAAA;4BACxC,IAAM,KAAK,GAAG,CAA0B,CAAC;4BACzC,KAAI,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9B,4BAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,4BAAA,OAAO,EAAE,CAAC;AACd,yBAAC,CAAC,CAAC;AACH,wBAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAG,cAAM,OAAA,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,EAAE,CAAC,CAAzD,EAAyD,CAAC,CAAC;AACvG,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;AAED;;;AAGG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACI,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,QAAA,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;YACnB,EAAE,CAAC,KAAK,EAAE,CAAC;AACX,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACvB,SAAA;KACJ,CAAA;AAED;;AAEG;AACW,IAAA,eAAA,CAAA,SAAA,CAAA,gBAAgB,GAA9B,YAAA;;;;;AACQ,wBAAA,IAAA,CAAA,CAAC,IAAI,CAAC,MAAM,EAAZ,OAAY,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACL,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;AAAxB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAiB,CAAC,CAAA;;;;;AAEhC,KAAA,CAAA;AAED;;;AAGG;IACG,eAAO,CAAA,SAAA,CAAA,OAAA,GAAb,UAAc,GAAW,EAAA;;;;;AACrB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;AAA7B,wBAAA,EAAA,CAAA,IAAA,EAA6B,CAAC;AAC9B,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAI,UAAC,OAAO,EAAE,MAAM,EAAA;;AAElC,gCAAA,IAAI,CAAC,KAAI,CAAC,EAAE,EAAE;AACV,oCAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,CAAC;AAChE,iCAAA;AACD,gCAAA,IAAM,WAAW,GAAG,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;gCACtE,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;gCAC5D,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEnC,gCAAA,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAC,CAAQ,EAAA;oCACvC,IAAM,KAAK,GAAG,CAAoB,CAAC;oCACnC,KAAI,CAAC,eAAe,EAAE,CAAC;AACvB,oCAAA,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,iCAAC,CAAC,CAAC;AAEH,gCAAA,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,CAAQ,EAAA;oCACrC,KAAI,CAAC,eAAe,EAAE,CAAC;oCACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,iCAAC,CAAC,CAAC;AACP,6BAAC,CAAC,CAAC,CAAA;;;;AACN,KAAA,CAAA;AAED;;;;AAIG;AACG,IAAA,eAAA,CAAA,SAAA,CAAA,OAAO,GAAb,UAAc,GAAW,EAAE,OAAU,EAAA;;;;;AACjC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;AAA7B,wBAAA,EAAA,CAAA,IAAA,EAA6B,CAAC;AAC9B,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAO,UAAC,OAAiB,EAAE,MAAgB,EAAA;;AAEzD,gCAAA,IAAI,CAAC,KAAI,CAAC,EAAE,EAAE;AACV,oCAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,CAAC;AAChE,iCAAA;AACD,gCAAA,IAAM,WAAW,GAAG,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC,CAAC;gCAEvE,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;gCAE5D,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAE5C,gCAAA,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAA;oCAC9B,KAAI,CAAC,eAAe,EAAE,CAAC;AACvB,oCAAA,OAAO,EAAE,CAAC;AACd,iCAAC,CAAC,CAAC;AAEH,gCAAA,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,CAAC,EAAA;oCAC9B,KAAI,CAAC,eAAe,EAAE,CAAC;oCACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,iCAAC,CAAC,CAAC;AACP,6BAAC,CAAC,CAAC,CAAA;;;;AACN,KAAA,CAAA;AAED;;;AAGG;IACG,eAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UAAiB,GAAW,EAAA;;;;;AACxB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;AAA7B,wBAAA,EAAA,CAAA,IAAA,EAA6B,CAAC;AAC9B,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAO,UAAC,OAAiB,EAAE,MAAgB,EAAA;AACzD,gCAAA,IAAI,CAAC,KAAI,CAAC,EAAE,EAAE;AACV,oCAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,CAAC;AAChE,iCAAA;AAED,gCAAA,IAAM,WAAW,GAAG,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC,CAAC;gCACvE,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;gCAC5D,IAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAEzC,gCAAA,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAA;oCACjC,KAAI,CAAC,eAAe,EAAE,CAAC;AACvB,oCAAA,OAAO,EAAE,CAAC;AACd,iCAAC,CAAC,CAAC;AAEH,gCAAA,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,CAAC,EAAA;oCACjC,KAAI,CAAC,eAAe,EAAE,CAAC;oCACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,iCAAC,CAAC,CAAC;AACP,6BAAC,CAAC,CAAC,CAAA;;;;AACN,KAAA,CAAA;AAED;;AAEG;AACG,IAAA,eAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;;AACI,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;AAA7B,wBAAA,EAAA,CAAA,IAAA,EAA6B,CAAC;AAC9B,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAW,UAAC,OAAiB,EAAE,MAAgB,EAAA;AAC7D,gCAAA,IAAI,CAAC,KAAI,CAAC,EAAE,EAAE;AACV,oCAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,CAAC;AAChE,iCAAA;AAED,gCAAA,IAAM,WAAW,GAAG,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;gCACtE,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;AAC5D,gCAAA,IAAM,SAAS,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;AAE3C,gCAAA,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAC,CAAQ,EAAA;oCAC3C,IAAM,KAAK,GAAG,CAAoB,CAAC;oCACnC,KAAI,CAAC,eAAe,EAAE,CAAC;AACvB,oCAAA,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,iCAAC,CAAC,CAAC;AAEH,gCAAA,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAG,UAAC,CAAQ,EAAA;oCAC1C,KAAI,CAAC,eAAe,EAAE,CAAC;oCACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,iCAAC,CAAC,CAAC;AACP,6BAAC,CAAC,CAAC,CAAA;;;;AACN,KAAA,CAAA;AAED;;;AAGG;IACG,eAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,GAAW,EAAA;;;;;AACzB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;AAA7B,wBAAA,EAAA,CAAA,IAAA,EAA6B,CAAC;AAE9B,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAU,UAAC,OAAiB,EAAE,MAAgB,EAAA;AAC5D,gCAAA,IAAI,CAAC,KAAI,CAAC,EAAE,EAAE;AACV,oCAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,CAAC,CAAC;AAChE,iCAAA;AAED,gCAAA,IAAM,WAAW,GAAG,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;gCACtE,IAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;gCAC5D,IAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAE7C,gCAAA,aAAa,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAC,CAAQ,EAAA;oCAC/C,IAAM,KAAK,GAAG,CAAoB,CAAC;oCACnC,KAAI,CAAC,eAAe,EAAE,CAAC;oCACvB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AACvC,iCAAC,CAAC,CAAC;AAEH,gCAAA,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,CAAQ,EAAA;oCAC7C,KAAI,CAAC,eAAe,EAAE,CAAC;oCACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,iCAAC,CAAC,CAAC;AACP,6BAAC,CAAC,CAAC,CAAA;;;;AACN,KAAA,CAAA;AAED;;;;AAIG;AACG,IAAA,eAAA,CAAA,SAAA,CAAA,cAAc,GAApB,YAAA;;;;AAGI,gBAAA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;oBACxB,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,iBAAA;AAED,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAU,UAAC,OAAiB,EAAE,MAAgB,EAAA;wBAC5D,IAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACjE,wBAAA,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAM,EAAA,OAAA,OAAO,CAAC,IAAI,CAAC,CAAb,EAAa,CAAC,CAAC;AACjE,wBAAA,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAM,EAAA,OAAA,OAAO,CAAC,IAAI,CAAC,CAAb,EAAa,CAAC,CAAC;AACjE,wBAAA,eAAe,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAM,EAAA,OAAA,MAAM,CAAC,KAAK,CAAC,CAAb,EAAa,CAAC,CAAC;AACnE,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;;;"}