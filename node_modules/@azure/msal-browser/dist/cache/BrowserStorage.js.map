{"version": 3, "file": "BrowserStorage.js", "sources": ["../../src/cache/BrowserStorage.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { BrowserConfigurationAuthError } from \"../error/BrowserConfigurationAuthError\";\r\nimport { BrowserCacheLocation } from \"../utils/BrowserConstants\";\r\nimport { IWindowStorage } from \"./IWindowStorage\";\r\n\r\nexport class BrowserStorage implements IWindowStorage<string> {\r\n\r\n    private windowStorage: Storage;\r\n\r\n    constructor(cacheLocation: string) {\r\n        this.validateWindowStorage(cacheLocation);\r\n        this.windowStorage = window[cacheLocation];\r\n    }\r\n\r\n    private validateWindowStorage(cacheLocation: string): void {\r\n        if (cacheLocation !== BrowserCacheLocation.LocalStorage && cacheLocation !== BrowserCacheLocation.SessionStorage) {\r\n            throw BrowserConfigurationAuthError.createStorageNotSupportedError(cacheLocation);\r\n        }\r\n        const storageSupported = !!window[cacheLocation];\r\n        if (!storageSupported) {\r\n            throw BrowserConfigurationAuthError.createStorageNotSupportedError(cacheLocation);\r\n        }\r\n    }\r\n\r\n    getItem(key: string): string | null {\r\n        return this.windowStorage.getItem(key);\r\n    }\r\n\r\n    setItem(key: string, value: string): void {\r\n        this.windowStorage.setItem(key, value);\r\n    }\r\n\r\n    removeItem(key: string): void {\r\n        this.windowStorage.removeItem(key);\r\n    }\r\n\r\n    getKeys(): string[] {\r\n        return Object.keys(this.windowStorage);\r\n    }\r\n\r\n    containsKey(key: string): boolean {\r\n        return this.windowStorage.hasOwnProperty(key);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAMH,IAAA,cAAA,kBAAA,YAAA;AAII,IAAA,SAAA,cAAA,CAAY,aAAqB,EAAA;AAC7B,QAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;KAC9C;IAEO,cAAqB,CAAA,SAAA,CAAA,qBAAA,GAA7B,UAA8B,aAAqB,EAAA;QAC/C,IAAI,aAAa,KAAK,oBAAoB,CAAC,YAAY,IAAI,aAAa,KAAK,oBAAoB,CAAC,cAAc,EAAE;AAC9G,YAAA,MAAM,6BAA6B,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;AACrF,SAAA;QACD,IAAM,gBAAgB,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,EAAE;AACnB,YAAA,MAAM,6BAA6B,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;AACrF,SAAA;KACJ,CAAA;IAED,cAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,GAAW,EAAA;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC1C,CAAA;AAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,GAAW,EAAE,KAAa,EAAA;QAC9B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC1C,CAAA;IAED,cAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAW,EAAA;AAClB,QAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACtC,CAAA;AAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAC1C,CAAA;IAED,cAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,GAAW,EAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;KACjD,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA;;;;"}