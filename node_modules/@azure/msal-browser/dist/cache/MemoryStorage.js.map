{"version": 3, "file": "MemoryStorage.js", "sources": ["../../src/cache/MemoryStorage.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { IWindowStorage } from \"./IWindowStorage\";\r\n\r\nexport class MemoryStorage<T> implements IWindowStorage<T> {\r\n\r\n    private cache: Map<string, T>;\r\n\r\n    constructor() {\r\n        this.cache = new Map<string, T>();\r\n    }\r\n\r\n    getItem(key: string): T | null {\r\n        return this.cache.get(key) || null;\r\n    }\r\n\r\n    setItem(key: string, value: T): void {\r\n        this.cache.set(key, value);\r\n    }\r\n\r\n    removeItem(key: string): void {\r\n        this.cache.delete(key);\r\n    }\r\n\r\n    getKeys(): string[] {\r\n        const cacheKeys: string[] = [];\r\n        this.cache.forEach((value: T, key: string) => {\r\n            cacheKeys.push(key);\r\n        });\r\n        return cacheKeys;\r\n    }\r\n\r\n    containsKey(key: string): boolean {\r\n        return this.cache.has(key);\r\n    }\r\n\r\n    clear() :void {\r\n        this.cache.clear();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAIH,IAAA,aAAA,kBAAA,YAAA;AAII,IAAA,SAAA,aAAA,GAAA;AACI,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAa,CAAC;KACrC;IAED,aAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,GAAW,EAAA;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;KACtC,CAAA;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,GAAW,EAAE,KAAQ,EAAA;QACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC9B,CAAA;IAED,aAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAW,EAAA;AAClB,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC1B,CAAA;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACI,IAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,KAAQ,EAAE,GAAW,EAAA;AACrC,YAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;IAED,aAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,GAAW,EAAA;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9B,CAAA;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACI,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;KACtB,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA;;;;"}