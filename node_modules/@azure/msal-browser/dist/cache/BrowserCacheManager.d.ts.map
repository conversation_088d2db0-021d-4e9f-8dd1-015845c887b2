{"version": 3, "file": "BrowserCacheManager.d.ts", "sourceRoot": "", "sources": ["../../src/cache/BrowserCacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EAA+C,8BAA8B,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,YAAY,EAAE,qBAAqB,EAAE,gBAAgB,EAAiB,MAAM,EAAE,uBAAuB,EAAiC,WAAW,EAAmE,mBAAmB,EAAmB,SAAS,EAAE,cAAc,EAAE,oBAAoB,EAAqC,MAAM,oBAAoB,CAAC;AAChiB,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAA0D,MAAM,2BAA2B,CAAC;AAE1I,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sCAAsC,CAAC;AAC1E,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAC/D,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,YAAY;IAGjD,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAE9C,SAAS,CAAC,cAAc,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAEjD,SAAS,CAAC,eAAe,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAEjD,SAAS,CAAC,qBAAqB,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAExD,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IAGzB,SAAS,CAAC,QAAQ,CAAC,sBAAsB,SAAuB;gBAEpD,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;IAetG;;;OAGG;IACH,SAAS,CAAC,mBAAmB,CAAC,aAAa,EAAE,oBAAoB,GAAG,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;IAkBnG;;;;OAIG;IACH,SAAS,CAAC,0BAA0B,CAAC,sBAAsB,EAAE,oBAAoB,GAAG,MAAM,EAAE,aAAa,EAAE,oBAAoB,GAAG,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;IAoBjK;;;OAGG;IACH,SAAS,CAAC,mBAAmB,IAAI,IAAI;IAiBrC;;;;;OAKG;IACH,SAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAMvE;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAgFrB;;;OAGG;IACH,SAAS,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAehE;;;OAGG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAInC;;;;OAIG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAIzC;;;OAGG;IACH,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAiBpD;;;;OAIG;IACH,UAAU,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAOxC;;;OAGG;IACH,cAAc,IAAI,KAAK,CAAC,MAAM,CAAC;IAW/B;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAcrC;;;OAGG;IACH,uBAAuB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAc1C;;;OAGG;IACG,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAK/C;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAKhC;;;OAGG;IACG,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKnD;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAKrC;;;OAGG;IACH,YAAY,IAAI,SAAS;IAyBzB;;;;OAIG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI;IA+BpD;;;;OAIG;IACH,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI;IA2CvD;;;OAGG;IACH,oBAAoB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAmB9D;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IASlD;;;OAGG;IACH,wBAAwB,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAkB1E;;;OAGG;IACH,wBAAwB,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IAQ9D;;;OAGG;IACH,yBAAyB,CAAC,eAAe,EAAE,MAAM,GAAG,kBAAkB,GAAG,IAAI;IAkB7E;;;OAGG;IACH,yBAAyB,CAAC,YAAY,EAAE,kBAAkB,GAAG,IAAI;IAQjE;;;OAGG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAiBhE;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IAMpD;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,qBAAqB,GAAG,IAAI;IAgB5E;;;;OAIG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,qBAAqB,GAAG,IAAI;IAK5F;;OAEG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IAcjE;;OAEG;IACH,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAOzC;;;;OAIG;IACH,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,IAAI;IAKpE;;OAEG;IACH,kBAAkB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;IAMtC;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,uBAAuB,GAAG,IAAI;IAKxE;;OAEG;IACH,gBAAgB,IAAI,WAAW,GAAG,IAAI;IAiCtC;;;OAGG;IACH,gBAAgB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,GAAG,IAAI;IAkBnD;;;OAGG;IACH,sBAAsB,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,eAAe,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,EAAE;IA6B1G;;;;OAIG;IACH,qBAAqB,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IAuB3E;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IAiBvE;;;;OAIG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,GAAG,IAAI;IAKvF;;;;OAIG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,IAAI;IA2BzE;;;;;;OAMG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI;IAU/E;;;;OAIG;IACH,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAS7B;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAIjC;;OAEG;IACH,OAAO,IAAI,MAAM,EAAE;IAOnB;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAgB5B;;;;OAIG;IACG,4BAA4B,IAAI,OAAO,CAAC,IAAI,CAAC;IAqBnD;;;;;OAKG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAc9E;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM;IAezC;;OAEG;IACH,gBAAgB,IAAI,IAAI;IAexB;;;OAGG;IACH,eAAe,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAIzC;;;OAGG;IACH,uBAAuB,CAAC,cAAc,EAAE,MAAM,GAAG,MAAM;IAMvD;;OAEG;IACH,QAAQ,IAAI,MAAM;IAIlB;;OAEG;IACH,QAAQ,IAAI,IAAI;IAIhB;;;;OAIG;IACH,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAYrC;;;OAGG;IACH,oBAAoB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM;IAUjD;;;OAGG;IACH,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM;IAU7C;;;OAGG;IACH,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM;IAU7C;;OAEG;IACH,kBAAkB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAWtD;;;;OAIG;IACH,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,GAAG,IAAI,GAAG,IAAI;IA6BjI;;;OAGG;IACH,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IA0BtC;;;OAGG;IACH,mBAAmB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAY9C;;;;OAIG;IACH,6BAA6B,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI;IAyBrE,gBAAgB,CAAC,eAAe,EAAE,8BAA8B,EAAE,aAAa,EAAE,OAAO,GAAG,IAAI;IAO/F;;OAEG;IACH,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,GAAG,8BAA8B;IA2BvF;;OAEG;IACH,sBAAsB,IAAI,kBAAkB,GAAG,IAAI;IAiBnD,uBAAuB,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,OAAO;IAUzD,wBAAwB,IAAI,MAAM,GAAG,IAAI;IAKzC,wBAAwB,CAAC,UAAU,EAAE,OAAO,GAAG,IAAI;IAenD;;OAEG;IACH,kBAAkB,IAAI,MAAM,GAAG,IAAI;IAkCnC;;OAEG;IACH,wBAAwB,CAAC,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,mBAAmB,GAAG,MAAM;IAkB1F;;OAEG;IACH,yBAAyB,IAAI,MAAM,GAAG,IAAI;IAI1C;;;OAGG;IACH,yBAAyB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAI9C;;;;OAIG;IACG,YAAY,CACd,MAAM,EAAE,oBAAoB,EAC5B,OAAO,EAAE,aAAa,GACpB,gBAAgB,GAChB,eAAe,GACf,YAAY,GACf,OAAO,CAAC,IAAI,CAAC;CAsCnB;AAED,eAAO,MAAM,6BAA6B,aAAc,MAAM,UAAU,MAAM,KAAG,mBAUhF,CAAC"}