{"version": 3, "file": "TokenCache.js", "sources": ["../../src/cache/TokenCache.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AccessTokenEntity, ICrypto, IdTokenEntity, Logger, ScopeSet, Authority, AuthorityOptions, ExternalTokenResponse, AccountEntity, AuthToken, RefreshTokenEntity, CacheRecord, AuthenticationResult, Constants } from \"@azure/msal-common\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { BrowserCacheManager } from \"./BrowserCacheManager\";\r\nimport { ITokenCache } from \"./ITokenCache\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\n\r\nexport type LoadTokenOptions = {\r\n    clientInfo?: string,\r\n    expiresOn?: number,\r\n    extendedExpiresOn?: number\r\n};\r\n\r\n/**\r\n * Token cache manager\r\n */\r\nexport class TokenCache implements ITokenCache {\r\n    // Flag to indicate if in browser environment\r\n    public isBrowserEnvironment: boolean;\r\n    // Input configuration by developer/user\r\n    protected config: BrowserConfiguration;\r\n    // Browser cache storage\r\n    private storage: BrowserCacheManager;\r\n    // Logger\r\n    private logger: Logger;\r\n    // Crypto class\r\n    private cryptoObj: ICrypto;\r\n\r\n    constructor(configuration: BrowserConfiguration, storage: BrowserCacheManager, logger: Logger, cryptoObj: ICrypto) {\r\n        this.isBrowserEnvironment = typeof window !== \"undefined\";\r\n        this.config = configuration;\r\n        this.storage = storage;\r\n        this.logger = logger;\r\n        this.cryptoObj = cryptoObj;\r\n    }\r\n\r\n    // Move getAllAccounts here and cache utility APIs\r\n\r\n    /**\r\n     * API to load tokens to msal-browser cache.\r\n     * @param request\r\n     * @param response\r\n     * @param options\r\n     * @returns `AuthenticationResult` for the response that was loaded.\r\n     */\r\n    loadExternalTokens(request: SilentRequest, response: ExternalTokenResponse, options: LoadTokenOptions): AuthenticationResult {\r\n        this.logger.info(\"TokenCache - loadExternalTokens called\");\r\n\r\n        if (!response.id_token) {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"Please ensure server response includes id token.\");\r\n        }\r\n\r\n        const idToken = new AuthToken(response.id_token, this.cryptoObj);\r\n\r\n        let cacheRecord: CacheRecord;\r\n        let authority: Authority | undefined;\r\n        let cacheRecordAccount: AccountEntity;\r\n\r\n        if (request.account) {\r\n            cacheRecordAccount = AccountEntity.createFromAccountInfo(request.account);\r\n            cacheRecord = new CacheRecord(\r\n                cacheRecordAccount,\r\n                this.loadIdToken(idToken, cacheRecordAccount.homeAccountId, request.account.environment, request.account.tenantId),\r\n                this.loadAccessToken(request, response, cacheRecordAccount.homeAccountId, request.account.environment, request.account.tenantId, options),\r\n                this.loadRefreshToken(request, response, cacheRecordAccount.homeAccountId, request.account.environment)\r\n            );\r\n        } else if (request.authority) {\r\n\r\n            const authorityUrl = Authority.generateAuthority(request.authority, request.azureCloudOptions);\r\n            const authorityOptions: AuthorityOptions = {\r\n                protocolMode: this.config.auth.protocolMode,\r\n                knownAuthorities: this.config.auth.knownAuthorities,\r\n                cloudDiscoveryMetadata: this.config.auth.cloudDiscoveryMetadata,\r\n                authorityMetadata: this.config.auth.authorityMetadata,\r\n                skipAuthorityMetadataCache: this.config.auth.skipAuthorityMetadataCache,\r\n            };\r\n            authority = new Authority(authorityUrl, this.config.system.networkClient, this.storage, authorityOptions, this.logger);\r\n\r\n            // \"clientInfo\" from options takes precedence over \"clientInfo\" in response\r\n            if (options.clientInfo) {\r\n                this.logger.trace(\"TokenCache - homeAccountId from options\");\r\n                cacheRecordAccount = this.loadAccount(\r\n                    idToken,\r\n                    authority,\r\n                    options.clientInfo\r\n                );\r\n                cacheRecord = new CacheRecord(\r\n                    cacheRecordAccount,\r\n                    this.loadIdToken(idToken, cacheRecordAccount.homeAccountId, authority.hostnameAndPort, authority.tenant),\r\n                    this.loadAccessToken(request, response, cacheRecordAccount.homeAccountId, authority.hostnameAndPort, authority.tenant, options),\r\n                    this.loadRefreshToken(request, response, cacheRecordAccount.homeAccountId, authority.hostnameAndPort)\r\n                );\r\n            } else if (response.client_info) {\r\n                this.logger.trace(\"TokenCache - homeAccountId from response\");\r\n                cacheRecordAccount = this.loadAccount(idToken, authority, response.client_info);\r\n                cacheRecord = new CacheRecord(\r\n                    cacheRecordAccount,\r\n                    this.loadIdToken(idToken, cacheRecordAccount.homeAccountId, authority.hostnameAndPort, authority.tenant),\r\n                    this.loadAccessToken(request, response, cacheRecordAccount.homeAccountId, authority.hostnameAndPort, authority.tenant, options),\r\n                    this.loadRefreshToken(request, response, cacheRecordAccount.homeAccountId, authority.hostnameAndPort)\r\n                );\r\n            } else {\r\n                throw BrowserAuthError.createUnableToLoadTokenError(\"Please provide clientInfo in the response or options.\");\r\n            }\r\n        } else {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"Please provide a request with an account or a request with authority.\");\r\n        }\r\n\r\n        return this.generateAuthenticationResult(request, idToken, cacheRecord, cacheRecordAccount, authority);\r\n    }\r\n\r\n    /**\r\n     * Helper function to load account to msal-browser cache\r\n     * @param idToken\r\n     * @param environment\r\n     * @param clientInfo\r\n     * @param authorityType\r\n     * @param requestHomeAccountId\r\n     * @returns `AccountEntity`\r\n     */\r\n    private loadAccount(idToken: AuthToken, authority: Authority, clientInfo?: string, requestHomeAccountId?: string): AccountEntity {\r\n\r\n        let homeAccountId;\r\n        if (requestHomeAccountId) {\r\n            homeAccountId = requestHomeAccountId;\r\n        } else if (authority.authorityType !== undefined && clientInfo) {\r\n            homeAccountId = AccountEntity.generateHomeAccountId(clientInfo, authority.authorityType, this.logger, this.cryptoObj, idToken.claims);\r\n        }\r\n\r\n        if (!homeAccountId) {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"Unexpected missing homeAccountId\");\r\n        }\r\n\r\n        const accountEntity = AccountEntity.createAccount({homeAccountId, idTokenClaims: idToken.claims, clientInfo, environment: authority.hostnameAndPort}, authority);\r\n\r\n        if (this.isBrowserEnvironment) {\r\n            this.logger.verbose(\"TokenCache - loading account\");\r\n\r\n            this.storage.setAccount(accountEntity);\r\n            return accountEntity;\r\n        } else {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"loadExternalTokens is designed to work in browser environments only.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper function to load id tokens to msal-browser cache\r\n     * @param idToken\r\n     * @param homeAccountId\r\n     * @param environment\r\n     * @param tenantId\r\n     * @returns `IdTokenEntity`\r\n     */\r\n    private loadIdToken(idToken: AuthToken, homeAccountId: string, environment: string, tenantId: string): IdTokenEntity {\r\n\r\n        const idTokenEntity = IdTokenEntity.createIdTokenEntity(homeAccountId, environment, idToken.rawToken, this.config.auth.clientId, tenantId);\r\n\r\n        if (this.isBrowserEnvironment) {\r\n            this.logger.verbose(\"TokenCache - loading id token\");\r\n            this.storage.setIdTokenCredential(idTokenEntity);\r\n            return idTokenEntity;\r\n        } else {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"loadExternalTokens is designed to work in browser environments only.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper function to load access tokens to msal-browser cache\r\n     * @param request\r\n     * @param response\r\n     * @param homeAccountId\r\n     * @param environment\r\n     * @param tenantId\r\n     * @returns `AccessTokenEntity`\r\n     */\r\n    private loadAccessToken(request: SilentRequest, response: ExternalTokenResponse, homeAccountId: string, environment: string, tenantId: string, options: LoadTokenOptions): AccessTokenEntity | null {\r\n\r\n        if (!response.access_token) {\r\n            this.logger.verbose(\"TokenCache - No access token provided for caching\");\r\n            return null;\r\n        }\r\n\r\n        if (!response.expires_in) {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"Please ensure server response includes expires_in value.\");\r\n        }\r\n\r\n        if (!options.extendedExpiresOn) {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"Please provide an extendedExpiresOn value in the options.\");\r\n        }\r\n\r\n        const scopes = new ScopeSet(request.scopes).printScopes();\r\n        const expiresOn = options.expiresOn || (response.expires_in + new Date().getTime() / 1000);\r\n        const extendedExpiresOn = options.extendedExpiresOn;\r\n\r\n        const accessTokenEntity = AccessTokenEntity.createAccessTokenEntity(homeAccountId, environment, response.access_token, this.config.auth.clientId, tenantId, scopes, expiresOn, extendedExpiresOn, this.cryptoObj);\r\n\r\n        if (this.isBrowserEnvironment) {\r\n            this.logger.verbose(\"TokenCache - loading access token\");\r\n            this.storage.setAccessTokenCredential(accessTokenEntity);\r\n            return accessTokenEntity;\r\n        } else {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"loadExternalTokens is designed to work in browser environments only.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper function to load refresh tokens to msal-browser cache\r\n     * @param request\r\n     * @param response\r\n     * @param homeAccountId\r\n     * @param environment\r\n     * @returns `RefreshTokenEntity`\r\n     */\r\n    private loadRefreshToken(request: SilentRequest, response: ExternalTokenResponse, homeAccountId: string, environment: string): RefreshTokenEntity | null {\r\n\r\n        if (!response.refresh_token) {\r\n            this.logger.verbose(\"TokenCache - No refresh token provided for caching\");\r\n            return null;\r\n        }\r\n\r\n        const refreshTokenEntity = RefreshTokenEntity.createRefreshTokenEntity(homeAccountId, environment, response.refresh_token, this.config.auth.clientId);\r\n\r\n        if (this.isBrowserEnvironment) {\r\n            this.logger.verbose(\"TokenCache - loading refresh token\");\r\n            this.storage.setRefreshTokenCredential(refreshTokenEntity);\r\n            return refreshTokenEntity;\r\n        } else {\r\n            throw BrowserAuthError.createUnableToLoadTokenError(\"loadExternalTokens is designed to work in browser environments only.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper function to generate an `AuthenticationResult` for the result.\r\n     * @param request\r\n     * @param idTokenObj\r\n     * @param cacheRecord\r\n     * @param accountEntity\r\n     * @param authority\r\n     * @returns `AuthenticationResult`\r\n     */\r\n    private generateAuthenticationResult(\r\n        request: SilentRequest,\r\n        idTokenObj: AuthToken,\r\n        cacheRecord: CacheRecord,\r\n        accountEntity: AccountEntity,\r\n        authority?: Authority,\r\n    ): AuthenticationResult {\r\n        let accessToken: string = Constants.EMPTY_STRING;\r\n        let responseScopes: Array<string> = [];\r\n        let expiresOn: Date | null = null;\r\n        let extExpiresOn: Date | undefined;\r\n\r\n        if (cacheRecord.accessToken) {\r\n            accessToken = cacheRecord.accessToken.secret;\r\n            responseScopes = ScopeSet.fromString(cacheRecord.accessToken.target).asArray();\r\n            expiresOn = new Date(Number(cacheRecord.accessToken.expiresOn) * 1000);\r\n            extExpiresOn = new Date(Number(cacheRecord.accessToken.extendedExpiresOn) * 1000);\r\n        }\r\n\r\n        const uid = idTokenObj?.claims.oid || idTokenObj?.claims.sub || Constants.EMPTY_STRING;\r\n        const tid = idTokenObj?.claims.tid || Constants.EMPTY_STRING;\r\n\r\n        return {\r\n            authority: authority ? authority.canonicalAuthority : Constants.EMPTY_STRING,\r\n            uniqueId: uid,\r\n            tenantId: tid,\r\n            scopes: responseScopes,\r\n            account: accountEntity ? accountEntity.getAccountInfo() : null,\r\n            idToken: idTokenObj ? idTokenObj.rawToken : Constants.EMPTY_STRING,\r\n            idTokenClaims: idTokenObj ? idTokenObj.claims : {},\r\n            accessToken: accessToken,\r\n            fromCache: true,\r\n            expiresOn: expiresOn,\r\n            correlationId: request.correlationId || Constants.EMPTY_STRING,\r\n            requestId: Constants.EMPTY_STRING,\r\n            extExpiresOn: extExpiresOn,\r\n            familyId: Constants.EMPTY_STRING,\r\n            tokenType: cacheRecord?.accessToken?.tokenType || Constants.EMPTY_STRING,\r\n            state: Constants.EMPTY_STRING,\r\n            cloudGraphHostName: accountEntity.cloudGraphHostName || Constants.EMPTY_STRING,\r\n            msGraphHost: accountEntity.msGraphHost || Constants.EMPTY_STRING,\r\n            code: undefined,\r\n            fromNativeBroker: false\r\n        };\r\n    }\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAeH;;AAEG;AACH,IAAA,UAAA,kBAAA,YAAA;AAYI,IAAA,SAAA,UAAA,CAAY,aAAmC,EAAE,OAA4B,EAAE,MAAc,EAAE,SAAkB,EAAA;AAC7G,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AAC1D,QAAA,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC9B;;AAID;;;;;;AAMG;AACH,IAAA,UAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,UAAmB,OAAsB,EAAE,QAA+B,EAAE,OAAyB,EAAA;AACjG,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;AAE3D,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACpB,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,kDAAkD,CAAC,CAAC;AAC3G,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAEjE,QAAA,IAAI,WAAwB,CAAC;AAC7B,QAAA,IAAI,SAAgC,CAAC;AACrC,QAAA,IAAI,kBAAiC,CAAC;QAEtC,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,kBAAkB,GAAG,aAAa,CAAC,qBAAqB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1E,YAAA,WAAW,GAAG,IAAI,WAAW,CACzB,kBAAkB,EAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAClH,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,EACzI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAC1G,CAAC;AACL,SAAA;aAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAE1B,YAAA,IAAM,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC/F,YAAA,IAAM,gBAAgB,GAAqB;AACvC,gBAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;AAC3C,gBAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;AACnD,gBAAA,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAC/D,gBAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;AACrD,gBAAA,0BAA0B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B;aAC1E,CAAC;YACF,SAAS,GAAG,IAAI,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;YAGvH,IAAI,OAAO,CAAC,UAAU,EAAE;AACpB,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC7D,gBAAA,kBAAkB,GAAG,IAAI,CAAC,WAAW,CACjC,OAAO,EACP,SAAS,EACT,OAAO,CAAC,UAAU,CACrB,CAAC;AACF,gBAAA,WAAW,GAAG,IAAI,WAAW,CACzB,kBAAkB,EAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,MAAM,CAAC,EACxG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,EAC/H,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,CAAC,CACxG,CAAC;AACL,aAAA;iBAAM,IAAI,QAAQ,CAAC,WAAW,EAAE;AAC7B,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC9D,gBAAA,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AAChF,gBAAA,WAAW,GAAG,IAAI,WAAW,CACzB,kBAAkB,EAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,MAAM,CAAC,EACxG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,EAC/H,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,kBAAkB,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,CAAC,CACxG,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,uDAAuD,CAAC,CAAC;AAChH,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,uEAAuE,CAAC,CAAC;AAChI,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,kBAAkB,EAAE,SAAS,CAAC,CAAC;KAC1G,CAAA;AAED;;;;;;;;AAQG;IACK,UAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,OAAkB,EAAE,SAAoB,EAAE,UAAmB,EAAE,oBAA6B,EAAA;AAE5G,QAAA,IAAI,aAAa,CAAC;AAClB,QAAA,IAAI,oBAAoB,EAAE;YACtB,aAAa,GAAG,oBAAoB,CAAC;AACxC,SAAA;AAAM,aAAA,IAAI,SAAS,CAAC,aAAa,KAAK,SAAS,IAAI,UAAU,EAAE;YAC5D,aAAa,GAAG,aAAa,CAAC,qBAAqB,CAAC,UAAU,EAAE,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACzI,SAAA;QAED,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,kCAAkC,CAAC,CAAC;AAC3F,SAAA;AAED,QAAA,IAAM,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC,EAAC,aAAa,EAAA,aAAA,EAAE,aAAa,EAAE,OAAO,CAAC,MAAM,EAAE,UAAU,EAAA,UAAA,EAAE,WAAW,EAAE,SAAS,CAAC,eAAe,EAAC,EAAE,SAAS,CAAC,CAAC;QAEjK,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAEpD,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AACvC,YAAA,OAAO,aAAa,CAAC;AACxB,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,sEAAsE,CAAC,CAAC;AAC/H,SAAA;KACJ,CAAA;AAED;;;;;;;AAOG;IACK,UAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,OAAkB,EAAE,aAAqB,EAAE,WAAmB,EAAE,QAAgB,EAAA;QAEhG,IAAM,aAAa,GAAG,aAAa,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE3I,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AACrD,YAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;AACjD,YAAA,OAAO,aAAa,CAAC;AACxB,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,sEAAsE,CAAC,CAAC;AAC/H,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;AACK,IAAA,UAAA,CAAA,SAAA,CAAA,eAAe,GAAvB,UAAwB,OAAsB,EAAE,QAA+B,EAAE,aAAqB,EAAE,WAAmB,EAAE,QAAgB,EAAE,OAAyB,EAAA;AAEpK,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;AACxB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;AACzE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AACtB,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,0DAA0D,CAAC,CAAC;AACnH,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;AAC5B,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,2DAA2D,CAAC,CAAC;AACpH,SAAA;AAED,QAAA,IAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1D,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAC3F,QAAA,IAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;AAEpD,QAAA,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,aAAa,EAAE,WAAW,EAAE,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAElN,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AACzD,YAAA,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;AACzD,YAAA,OAAO,iBAAiB,CAAC;AAC5B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,sEAAsE,CAAC,CAAC;AAC/H,SAAA;KACJ,CAAA;AAED;;;;;;;AAOG;IACK,UAAgB,CAAA,SAAA,CAAA,gBAAA,GAAxB,UAAyB,OAAsB,EAAE,QAA+B,EAAE,aAAqB,EAAE,WAAmB,EAAA;AAExH,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;AAC1E,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,kBAAkB,GAAG,kBAAkB,CAAC,wBAAwB,CAAC,aAAa,EAAE,WAAW,EAAE,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtJ,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,CAAC;AAC3D,YAAA,OAAO,kBAAkB,CAAC;AAC7B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,sEAAsE,CAAC,CAAC;AAC/H,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;IACK,UAA4B,CAAA,SAAA,CAAA,4BAAA,GAApC,UACI,OAAsB,EACtB,UAAqB,EACrB,WAAwB,EACxB,aAA4B,EAC5B,SAAqB,EAAA;;AAErB,QAAA,IAAI,WAAW,GAAW,SAAS,CAAC,YAAY,CAAC;QACjD,IAAI,cAAc,GAAkB,EAAE,CAAC;QACvC,IAAI,SAAS,GAAgB,IAAI,CAAC;AAClC,QAAA,IAAI,YAA8B,CAAC;QAEnC,IAAI,WAAW,CAAC,WAAW,EAAE;AACzB,YAAA,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;AAC7C,YAAA,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;AAC/E,YAAA,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;AACvE,YAAA,YAAY,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,CAAC;AACrF,SAAA;QAED,IAAM,GAAG,GAAG,CAAA,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,MAAM,CAAC,GAAG,MAAI,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,MAAM,CAAC,GAAG,CAAA,IAAI,SAAS,CAAC,YAAY,CAAC;AACvF,QAAA,IAAM,GAAG,GAAG,CAAA,UAAU,KAAA,IAAA,IAAV,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,MAAM,CAAC,GAAG,KAAI,SAAS,CAAC,YAAY,CAAC;QAE7D,OAAO;AACH,YAAA,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC,kBAAkB,GAAG,SAAS,CAAC,YAAY;AAC5E,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,MAAM,EAAE,cAAc;AACtB,YAAA,OAAO,EAAE,aAAa,GAAG,aAAa,CAAC,cAAc,EAAE,GAAG,IAAI;AAC9D,YAAA,OAAO,EAAE,UAAU,GAAG,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,YAAY;YAClE,aAAa,EAAE,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,EAAE;AAClD,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,SAAS,EAAE,IAAI;AACf,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY;YAC9D,SAAS,EAAE,SAAS,CAAC,YAAY;AACjC,YAAA,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,SAAS,CAAC,YAAY;AAChC,YAAA,SAAS,EAAE,CAAA,CAAA,EAAA,GAAA,WAAW,KAAA,IAAA,IAAX,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAS,KAAI,SAAS,CAAC,YAAY;YACxE,KAAK,EAAE,SAAS,CAAC,YAAY;AAC7B,YAAA,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,IAAI,SAAS,CAAC,YAAY;AAC9E,YAAA,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY;AAChE,YAAA,IAAI,EAAE,SAAS;AACf,YAAA,gBAAgB,EAAE,KAAK;SAC1B,CAAC;KACL,CAAA;IACL,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA;;;;"}