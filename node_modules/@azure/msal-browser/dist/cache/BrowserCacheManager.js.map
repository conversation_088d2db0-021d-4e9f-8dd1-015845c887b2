{"version": 3, "file": "BrowserCacheManager.js", "sources": ["../../src/cache/BrowserCacheManager.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants, PersistentCacheKeys, StringUtils, CommonAuthorizationCodeRequest, ICrypto, AccountEntity, IdTokenEntity, AccessTokenEntity, RefreshTokenEntity, AppMetadataEntity, CacheManager, ServerTelemetryEntity, ThrottlingEntity, ProtocolUtils, Logger, AuthorityMetadataEntity, DEFAULT_CRYPTO_IMPLEMENTATION, AccountInfo, ActiveAccountFilters, CcsCredential, CcsCredentialType, IdToken, ValidCredentialType, ClientAuthError, TokenKeys, CredentialType, AuthenticationResult, AuthenticationScheme, CacheRecord } from \"@azure/msal-common\";\r\nimport { CacheOptions } from \"../config/Configuration\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { BrowserCacheLocation, InteractionType, TemporaryCacheKeys, InMemoryCacheK<PERSON>s, StaticCacheKeys } from \"../utils/BrowserConstants\";\r\nimport { BrowserStorage } from \"./BrowserStorage\";\r\nimport { MemoryStorage } from \"./MemoryStorage\";\r\nimport { IWindowStorage } from \"./IWindowStorage\";\r\nimport { BrowserProtocolUtils } from \"../utils/BrowserProtocolUtils\";\r\nimport { NativeTokenRequest } from \"../broker/nativeBroker/NativeRequest\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\n\r\n/**\r\n * This class implements the cache storage interface for MSAL through browser local or session storage.\r\n * Cookies are only used if storeAuthStateInCookie is true, and are only used for\r\n * parameters such as state and nonce, generally.\r\n */\r\nexport class BrowserCacheManager extends CacheManager {\r\n\r\n    // Cache configuration, either set by user or default values.\r\n    protected cacheConfig: Required<CacheOptions>;\r\n    // Window storage object (either local or sessionStorage)\r\n    protected browserStorage: IWindowStorage<string>;\r\n    // Internal in-memory storage object used for data used by msal that does not need to persist across page loads\r\n    protected internalStorage: MemoryStorage<string>;\r\n    // Temporary cache\r\n    protected temporaryCacheStorage: IWindowStorage<string>;\r\n    // Logger instance\r\n    protected logger: Logger;\r\n\r\n    // Cookie life calculation (hours * minutes * seconds * ms)\r\n    protected readonly COOKIE_LIFE_MULTIPLIER = 24 * 60 * 60 * 1000;\r\n\r\n    constructor(clientId: string, cacheConfig: Required<CacheOptions>, cryptoImpl: ICrypto, logger: Logger) {\r\n        super(clientId, cryptoImpl, logger);\r\n        this.cacheConfig = cacheConfig;\r\n        this.logger = logger;\r\n        this.internalStorage = new MemoryStorage();\r\n        this.browserStorage = this.setupBrowserStorage(this.cacheConfig.cacheLocation);\r\n        this.temporaryCacheStorage = this.setupTemporaryCacheStorage(this.cacheConfig.temporaryCacheLocation, this.cacheConfig.cacheLocation);\r\n\r\n        // Migrate cache entries from older versions of MSAL.\r\n        if (cacheConfig.cacheMigrationEnabled) {\r\n            this.migrateCacheEntries();\r\n            this.createKeyMaps();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns a window storage class implementing the IWindowStorage interface that corresponds to the configured cacheLocation.\r\n     * @param cacheLocation\r\n     */\r\n    protected setupBrowserStorage(cacheLocation: BrowserCacheLocation | string): IWindowStorage<string> {\r\n        switch (cacheLocation) {\r\n            case BrowserCacheLocation.LocalStorage:\r\n            case BrowserCacheLocation.SessionStorage:\r\n                try {\r\n                    return new BrowserStorage(cacheLocation);\r\n                } catch (e) {\r\n                    this.logger.verbose(e);\r\n                    break;\r\n                }\r\n            case BrowserCacheLocation.MemoryStorage:\r\n            default:\r\n                break;\r\n        }\r\n        this.cacheConfig.cacheLocation = BrowserCacheLocation.MemoryStorage;\r\n        return new MemoryStorage();\r\n    }\r\n\r\n    /**\r\n     * Returns a window storage class implementing the IWindowStorage interface that corresponds to the configured temporaryCacheLocation.\r\n     * @param temporaryCacheLocation\r\n     * @param cacheLocation\r\n     */\r\n    protected setupTemporaryCacheStorage(temporaryCacheLocation: BrowserCacheLocation | string, cacheLocation: BrowserCacheLocation | string): IWindowStorage<string> {\r\n        switch (cacheLocation) {\r\n            case BrowserCacheLocation.LocalStorage:\r\n            case BrowserCacheLocation.SessionStorage:\r\n                try {\r\n                    /*\r\n                     * When users do not explicitly choose their own temporaryCacheLocation, \r\n                     * temporary cache items will always be stored in session storage to mitigate problems caused by multiple tabs\r\n                     */\r\n                    return new BrowserStorage(temporaryCacheLocation || BrowserCacheLocation.SessionStorage);\r\n                } catch (e) {\r\n                    this.logger.verbose(e);\r\n                    return this.internalStorage;\r\n                }\r\n            case BrowserCacheLocation.MemoryStorage:\r\n            default:\r\n                return this.internalStorage;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Migrate all old cache entries to new schema. No rollback supported.\r\n     * @param storeAuthStateInCookie\r\n     */\r\n    protected migrateCacheEntries(): void {\r\n        const idTokenKey = `${Constants.CACHE_PREFIX}.${PersistentCacheKeys.ID_TOKEN}`;\r\n        const clientInfoKey = `${Constants.CACHE_PREFIX}.${PersistentCacheKeys.CLIENT_INFO}`;\r\n        const errorKey = `${Constants.CACHE_PREFIX}.${PersistentCacheKeys.ERROR}`;\r\n        const errorDescKey = `${Constants.CACHE_PREFIX}.${PersistentCacheKeys.ERROR_DESC}`;\r\n\r\n        const idTokenValue = this.browserStorage.getItem(idTokenKey);\r\n        const clientInfoValue = this.browserStorage.getItem(clientInfoKey);\r\n        const errorValue = this.browserStorage.getItem(errorKey);\r\n        const errorDescValue = this.browserStorage.getItem(errorDescKey);\r\n\r\n        const values = [idTokenValue, clientInfoValue, errorValue, errorDescValue];\r\n        const keysToMigrate = [PersistentCacheKeys.ID_TOKEN, PersistentCacheKeys.CLIENT_INFO, PersistentCacheKeys.ERROR, PersistentCacheKeys.ERROR_DESC];\r\n\r\n        keysToMigrate.forEach((cacheKey: string, index: number) => this.migrateCacheEntry(cacheKey, values[index]));\r\n    }\r\n\r\n    /**\r\n     * Utility function to help with migration.\r\n     * @param newKey\r\n     * @param value\r\n     * @param storeAuthStateInCookie\r\n     */\r\n    protected migrateCacheEntry(newKey: string, value: string | null): void {\r\n        if (value) {\r\n            this.setTemporaryCache(newKey, value, true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Searches all cache entries for MSAL accounts and creates the account key map\r\n     * This is used to migrate users from older versions of MSAL which did not create the map.\r\n     * @returns \r\n     */\r\n    private createKeyMaps(): void {\r\n        this.logger.trace(\"BrowserCacheManager - createKeyMaps called.\");\r\n        const accountKeys = this.getItem(StaticCacheKeys.ACCOUNT_KEYS);\r\n        const tokenKeys = this.getItem(`${StaticCacheKeys.TOKEN_KEYS}.${this.clientId}`);\r\n        if (accountKeys && tokenKeys) {\r\n            this.logger.verbose(\"BrowserCacheManager:createKeyMaps - account and token key maps already exist, skipping migration.\");\r\n            // Key maps already exist, no need to iterate through cache\r\n            return;\r\n        }\r\n\r\n        const allKeys = this.browserStorage.getKeys();\r\n        allKeys.forEach((key) => {\r\n            if (this.isCredentialKey(key)) {\r\n                // Get item, parse, validate and write key to map\r\n                const value = this.getItem(key);\r\n                if (value) {\r\n                    const credObj = this.validateAndParseJson(value);\r\n                    if (credObj && credObj.hasOwnProperty(\"credentialType\")) {\r\n                        switch (credObj[\"credentialType\"]) {\r\n                            case CredentialType.ID_TOKEN:\r\n                                if (IdTokenEntity.isIdTokenEntity(credObj)) {\r\n                                    this.logger.trace(\"BrowserCacheManager:createKeyMaps - idToken found, saving key to token key map\");\r\n                                    this.logger.tracePii(`BrowserCacheManager:createKeyMaps - idToken with key: ${key} found, saving key to token key map`);\r\n                                    const idTokenEntity = CacheManager.toObject(new IdTokenEntity(), credObj);\r\n                                    const newKey = this.updateCredentialCacheKey(key, idTokenEntity);\r\n                                    this.addTokenKey(newKey, CredentialType.ID_TOKEN);\r\n                                    return;\r\n                                } else {\r\n                                    this.logger.trace(\"BrowserCacheManager:createKeyMaps - key found matching idToken schema with value containing idToken credentialType field but value failed IdTokenEntity validation, skipping.\");\r\n                                    this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed idToken validation on key: ${key}`);\r\n                                }\r\n                                break;\r\n                            case CredentialType.ACCESS_TOKEN:\r\n                            case CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME:\r\n                                if (AccessTokenEntity.isAccessTokenEntity(credObj)) {\r\n                                    this.logger.trace(\"BrowserCacheManager:createKeyMaps - accessToken found, saving key to token key map\");\r\n                                    this.logger.tracePii(`BrowserCacheManager:createKeyMaps - accessToken with key: ${key} found, saving key to token key map`);\r\n                                    const accessTokenEntity = CacheManager.toObject(new AccessTokenEntity(), credObj);\r\n                                    const newKey = this.updateCredentialCacheKey(key, accessTokenEntity);\r\n                                    this.addTokenKey(newKey, CredentialType.ACCESS_TOKEN);\r\n                                    return;\r\n                                } else {\r\n                                    this.logger.trace(\"BrowserCacheManager:createKeyMaps - key found matching accessToken schema with value containing accessToken credentialType field but value failed AccessTokenEntity validation, skipping.\");\r\n                                    this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed accessToken validation on key: ${key}`);\r\n                                }\r\n                                break;\r\n                            case CredentialType.REFRESH_TOKEN:\r\n                                if (RefreshTokenEntity.isRefreshTokenEntity(credObj)) {\r\n                                    this.logger.trace(\"BrowserCacheManager:createKeyMaps - refreshToken found, saving key to token key map\");\r\n                                    this.logger.tracePii(`BrowserCacheManager:createKeyMaps - refreshToken with key: ${key} found, saving key to token key map`);\r\n                                    const refreshTokenEntity = CacheManager.toObject(new RefreshTokenEntity(), credObj);\r\n                                    const newKey = this.updateCredentialCacheKey(key, refreshTokenEntity);\r\n                                    this.addTokenKey(newKey, CredentialType.REFRESH_TOKEN);\r\n                                    return;\r\n                                } else {\r\n                                    this.logger.trace(\"BrowserCacheManager:createKeyMaps - key found matching refreshToken schema with value containing refreshToken credentialType field but value failed RefreshTokenEntity validation, skipping.\");\r\n                                    this.logger.tracePii(`BrowserCacheManager:createKeyMaps - failed refreshToken validation on key: ${key}`);\r\n                                }\r\n                                break;\r\n                            default:\r\n                                // If credentialType isn't one of our predefined ones, it may not be an MSAL cache value. Ignore.\r\n                        }\r\n                    }\r\n                }\r\n            } \r\n            \r\n            if (this.isAccountKey(key)) {\r\n                const value = this.getItem(key);\r\n                if (value) {\r\n                    const accountObj = this.validateAndParseJson(value);\r\n                    if (accountObj && AccountEntity.isAccountEntity(accountObj)) {\r\n                        this.logger.trace(\"BrowserCacheManager:createKeyMaps - account found, saving key to account key map\");\r\n                        this.logger.tracePii(`BrowserCacheManager:createKeyMaps - account with key: ${key} found, saving key to account key map`);\r\n                        this.addAccountKeyToMap(key);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Parses passed value as JSON object, JSON.parse() will throw an error.\r\n     * @param input\r\n     */\r\n    protected validateAndParseJson(jsonValue: string): object | null {\r\n        try {\r\n            const parsedJson = JSON.parse(jsonValue);\r\n            /**\r\n             * There are edge cases in which JSON.parse will successfully parse a non-valid JSON object\r\n             * (e.g. JSON.parse will parse an escaped string into an unescaped string), so adding a type check\r\n             * of the parsed value is necessary in order to be certain that the string represents a valid JSON object.\r\n             *\r\n             */\r\n            return (parsedJson && typeof parsedJson === \"object\") ? parsedJson : null;\r\n        } catch (error) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * fetches the entry from the browser storage based off the key\r\n     * @param key\r\n     */\r\n    getItem(key: string): string | null {\r\n        return this.browserStorage.getItem(key);\r\n    }\r\n\r\n    /**\r\n     * sets the entry in the browser storage\r\n     * @param key\r\n     * @param value\r\n     */\r\n    setItem(key: string, value: string): void {\r\n        this.browserStorage.setItem(key, value);\r\n    }\r\n\r\n    /**\r\n     * fetch the account entity from the platform cache\r\n     * @param accountKey\r\n     */\r\n    getAccount(accountKey: string): AccountEntity | null {\r\n        this.logger.trace(\"BrowserCacheManager.getAccount called\");\r\n        const account = this.getItem(accountKey);\r\n        if (!account) {\r\n            this.removeAccountKeyFromMap(accountKey);\r\n            return null;\r\n        }\r\n\r\n        const parsedAccount = this.validateAndParseJson(account);\r\n        if (!parsedAccount || !AccountEntity.isAccountEntity(parsedAccount)) {\r\n            this.removeAccountKeyFromMap(accountKey);\r\n            return null;\r\n        }\r\n\r\n        return CacheManager.toObject<AccountEntity>(new AccountEntity(), parsedAccount);\r\n    }\r\n\r\n    /**\r\n     * set account entity in the platform cache\r\n     * @param key\r\n     * @param value\r\n     */\r\n    setAccount(account: AccountEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setAccount called\");\r\n        const key = account.generateAccountKey();\r\n        this.setItem(key, JSON.stringify(account));\r\n        this.addAccountKeyToMap(key);\r\n    }\r\n\r\n    /**\r\n     * Returns the array of account keys currently cached\r\n     * @returns \r\n     */\r\n    getAccountKeys(): Array<string> {\r\n        this.logger.trace(\"BrowserCacheManager.getAccountKeys called\");\r\n        const accountKeys = this.getItem(StaticCacheKeys.ACCOUNT_KEYS);\r\n        if (accountKeys) {\r\n            return JSON.parse(accountKeys);\r\n        }\r\n\r\n        this.logger.verbose(\"BrowserCacheManager.getAccountKeys - No account keys found\");\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Add a new account to the key map\r\n     * @param key \r\n     */\r\n    addAccountKeyToMap(key: string): void {\r\n        this.logger.trace(\"BrowserCacheManager.addAccountKeyToMap called\");\r\n        this.logger.tracePii(`BrowserCacheManager.addAccountKeyToMap called with key: ${key}`);\r\n        const accountKeys = this.getAccountKeys();\r\n        if (accountKeys.indexOf(key) === -1) {\r\n            // Only add key if it does not already exist in the map\r\n            accountKeys.push(key);\r\n            this.setItem(StaticCacheKeys.ACCOUNT_KEYS, JSON.stringify(accountKeys));\r\n            this.logger.verbose(\"BrowserCacheManager.addAccountKeyToMap account key added\");\r\n        } else {\r\n            this.logger.verbose(\"BrowserCacheManager.addAccountKeyToMap account key already exists in map\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove an account from the key map\r\n     * @param key \r\n     */\r\n    removeAccountKeyFromMap(key: string): void {\r\n        this.logger.trace(\"BrowserCacheManager.removeAccountKeyFromMap called\");\r\n        this.logger.tracePii(`BrowserCacheManager.removeAccountKeyFromMap called with key: ${key}`);\r\n        const accountKeys = this.getAccountKeys();\r\n        const removalIndex = accountKeys.indexOf(key);\r\n        if (removalIndex > -1) {\r\n            accountKeys.splice(removalIndex, 1);\r\n            this.setItem(StaticCacheKeys.ACCOUNT_KEYS, JSON.stringify(accountKeys));\r\n            this.logger.trace(\"BrowserCacheManager.removeAccountKeyFromMap account key removed\");\r\n        } else {\r\n            this.logger.trace(\"BrowserCacheManager.removeAccountKeyFromMap key not found in existing map\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Extends inherited removeAccount function to include removal of the account key from the map\r\n     * @param key \r\n     */\r\n    async removeAccount(key: string): Promise<void> {\r\n        super.removeAccount(key);\r\n        this.removeAccountKeyFromMap(key);\r\n    }\r\n\r\n    /**\r\n     * Removes given idToken from the cache and from the key map\r\n     * @param key \r\n     */\r\n    removeIdToken(key: string): void {\r\n        super.removeIdToken(key);\r\n        this.removeTokenKey(key, CredentialType.ID_TOKEN);\r\n    }\r\n\r\n    /**\r\n     * Removes given accessToken from the cache and from the key map\r\n     * @param key \r\n     */\r\n    async removeAccessToken(key: string): Promise<void> {\r\n        super.removeAccessToken(key);\r\n        this.removeTokenKey(key, CredentialType.ACCESS_TOKEN);\r\n    }\r\n\r\n    /**\r\n     * Removes given refreshToken from the cache and from the key map\r\n     * @param key \r\n     */\r\n    removeRefreshToken(key: string): void {\r\n        super.removeRefreshToken(key);\r\n        this.removeTokenKey(key, CredentialType.REFRESH_TOKEN);\r\n    }\r\n\r\n    /**\r\n     * Gets the keys for the cached tokens associated with this clientId\r\n     * @returns \r\n     */\r\n    getTokenKeys(): TokenKeys {\r\n        this.logger.trace(\"BrowserCacheManager.getTokenKeys called\");\r\n        const item = this.getItem(`${StaticCacheKeys.TOKEN_KEYS}.${this.clientId}`);\r\n        if (item) {\r\n            const tokenKeys = this.validateAndParseJson(item);\r\n            if (tokenKeys && \r\n                tokenKeys.hasOwnProperty(\"idToken\") &&\r\n                tokenKeys.hasOwnProperty(\"accessToken\") &&\r\n                tokenKeys.hasOwnProperty(\"refreshToken\")\r\n            ) {\r\n                return tokenKeys as TokenKeys;\r\n            } else {\r\n                this.logger.error(\"BrowserCacheManager.getTokenKeys - Token keys found but in an unknown format. Returning empty key map.\");\r\n            }\r\n        } else {\r\n            this.logger.verbose(\"BrowserCacheManager.getTokenKeys - No token keys found\");\r\n        }\r\n\r\n        return {\r\n            idToken: [],\r\n            accessToken: [],\r\n            refreshToken: []\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Adds the given key to the token key map\r\n     * @param key \r\n     * @param type \r\n     */\r\n    addTokenKey(key: string, type: CredentialType): void {\r\n        this.logger.trace(\"BrowserCacheManager addTokenKey called\");\r\n        const tokenKeys = this.getTokenKeys();\r\n\r\n        switch (type) {\r\n            case CredentialType.ID_TOKEN:\r\n                if (tokenKeys.idToken.indexOf(key) === -1) {\r\n                    this.logger.info(\"BrowserCacheManager: addTokenKey - idToken added to map\");\r\n                    tokenKeys.idToken.push(key);\r\n                }\r\n                break;\r\n            case CredentialType.ACCESS_TOKEN:\r\n                if (tokenKeys.accessToken.indexOf(key) === -1) {\r\n                    this.logger.info(\"BrowserCacheManager: addTokenKey - accessToken added to map\");\r\n                    tokenKeys.accessToken.push(key);\r\n                }\r\n                break;\r\n            case CredentialType.REFRESH_TOKEN:\r\n                if (tokenKeys.refreshToken.indexOf(key) === -1) {\r\n                    this.logger.info(\"BrowserCacheManager: addTokenKey - refreshToken added to map\");\r\n                    tokenKeys.refreshToken.push(key);\r\n                }\r\n                break;\r\n            default:\r\n                this.logger.error(`BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: ${type}`);\r\n                ClientAuthError.createUnexpectedCredentialTypeError();\r\n        }\r\n\r\n        this.setItem(`${StaticCacheKeys.TOKEN_KEYS}.${this.clientId}`, JSON.stringify(tokenKeys));\r\n    }\r\n\r\n    /**\r\n     * Removes the given key from the token key map\r\n     * @param key \r\n     * @param type \r\n     */\r\n    removeTokenKey(key: string, type: CredentialType): void {\r\n        this.logger.trace(\"BrowserCacheManager removeTokenKey called\");\r\n        const tokenKeys = this.getTokenKeys();\r\n\r\n        switch (type) {\r\n            case CredentialType.ID_TOKEN:\r\n                this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: ${key} from map`);\r\n                const idRemoval = tokenKeys.idToken.indexOf(key);\r\n                if (idRemoval > -1) {\r\n                    this.logger.info(\"BrowserCacheManager: removeTokenKey - idToken removed from map\");\r\n                    tokenKeys.idToken.splice(idRemoval, 1);\r\n                } else {\r\n                    this.logger.info(\"BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.\");\r\n                }\r\n                break;\r\n            case CredentialType.ACCESS_TOKEN:\r\n                this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: ${key} from map`);\r\n                const accessRemoval = tokenKeys.accessToken.indexOf(key);\r\n                if (accessRemoval > -1) {\r\n                    this.logger.info(\"BrowserCacheManager: removeTokenKey - accessToken removed from map\");\r\n                    tokenKeys.accessToken.splice(accessRemoval, 1);\r\n                } else {\r\n                    this.logger.info(\"BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.\");\r\n                }\r\n                break;\r\n            case CredentialType.REFRESH_TOKEN:\r\n                this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: ${key} from map`);\r\n                const refreshRemoval = tokenKeys.refreshToken.indexOf(key);\r\n                if (refreshRemoval > -1) {\r\n                    this.logger.info(\"BrowserCacheManager: removeTokenKey - refreshToken removed from map\");\r\n                    tokenKeys.refreshToken.splice(refreshRemoval, 1);\r\n                } else {\r\n                    this.logger.info(\"BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.\");\r\n                }\r\n                break;\r\n            default:\r\n                this.logger.error(`BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: ${type}`);\r\n                ClientAuthError.createUnexpectedCredentialTypeError();\r\n        }\r\n\r\n        this.setItem(`${StaticCacheKeys.TOKEN_KEYS}.${this.clientId}`, JSON.stringify(tokenKeys));\r\n    }\r\n\r\n    /**\r\n     * generates idToken entity from a string\r\n     * @param idTokenKey\r\n     */\r\n    getIdTokenCredential(idTokenKey: string): IdTokenEntity | null {\r\n        const value = this.getItem(idTokenKey);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getIdTokenCredential: called, no cache hit\");\r\n            this.removeTokenKey(idTokenKey, CredentialType.ID_TOKEN);\r\n            return null;\r\n        }\r\n\r\n        const parsedIdToken = this.validateAndParseJson(value);\r\n        if (!parsedIdToken || !IdTokenEntity.isIdTokenEntity(parsedIdToken)) {\r\n            this.logger.trace(\"BrowserCacheManager.getIdTokenCredential: called, no cache hit\");\r\n            this.removeTokenKey(idTokenKey, CredentialType.ID_TOKEN);\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(\"BrowserCacheManager.getIdTokenCredential: cache hit\");\r\n        return CacheManager.toObject(new IdTokenEntity(), parsedIdToken);\r\n    }\r\n\r\n    /**\r\n     * set IdToken credential to the platform cache\r\n     * @param idToken\r\n     */\r\n    setIdTokenCredential(idToken: IdTokenEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setIdTokenCredential called\");\r\n        const idTokenKey = idToken.generateCredentialKey();\r\n\r\n        this.setItem(idTokenKey, JSON.stringify(idToken));\r\n\r\n        this.addTokenKey(idTokenKey, CredentialType.ID_TOKEN);\r\n    }\r\n\r\n    /**\r\n     * generates accessToken entity from a string\r\n     * @param key\r\n     */\r\n    getAccessTokenCredential(accessTokenKey: string): AccessTokenEntity | null {\r\n        const value = this.getItem(accessTokenKey);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getAccessTokenCredential: called, no cache hit\");\r\n            this.removeTokenKey(accessTokenKey, CredentialType.ACCESS_TOKEN);\r\n            return null;\r\n        }\r\n        const parsedAccessToken = this.validateAndParseJson(value);\r\n        if (!parsedAccessToken || !AccessTokenEntity.isAccessTokenEntity(parsedAccessToken)) {\r\n            this.logger.trace(\"BrowserCacheManager.getAccessTokenCredential: called, no cache hit\");\r\n            this.removeTokenKey(accessTokenKey, CredentialType.ACCESS_TOKEN);\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(\"BrowserCacheManager.getAccessTokenCredential: cache hit\");\r\n        return CacheManager.toObject(new AccessTokenEntity(), parsedAccessToken);\r\n    }\r\n\r\n    /**\r\n     * set accessToken credential to the platform cache\r\n     * @param accessToken\r\n     */\r\n    setAccessTokenCredential(accessToken: AccessTokenEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setAccessTokenCredential called\");\r\n        const accessTokenKey = accessToken.generateCredentialKey();\r\n        this.setItem(accessTokenKey, JSON.stringify(accessToken));\r\n\r\n        this.addTokenKey(accessTokenKey, CredentialType.ACCESS_TOKEN);\r\n    }\r\n\r\n    /**\r\n     * generates refreshToken entity from a string\r\n     * @param refreshTokenKey\r\n     */\r\n    getRefreshTokenCredential(refreshTokenKey: string): RefreshTokenEntity | null {\r\n        const value = this.getItem(refreshTokenKey);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getRefreshTokenCredential: called, no cache hit\");\r\n            this.removeTokenKey(refreshTokenKey, CredentialType.REFRESH_TOKEN);\r\n            return null;\r\n        }\r\n        const parsedRefreshToken = this.validateAndParseJson(value);\r\n        if (!parsedRefreshToken || !RefreshTokenEntity.isRefreshTokenEntity(parsedRefreshToken)) {\r\n            this.logger.trace(\"BrowserCacheManager.getRefreshTokenCredential: called, no cache hit\");\r\n            this.removeTokenKey(refreshTokenKey, CredentialType.REFRESH_TOKEN);\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(\"BrowserCacheManager.getRefreshTokenCredential: cache hit\");\r\n        return CacheManager.toObject(new RefreshTokenEntity(), parsedRefreshToken);\r\n    }\r\n\r\n    /**\r\n     * set refreshToken credential to the platform cache\r\n     * @param refreshToken\r\n     */\r\n    setRefreshTokenCredential(refreshToken: RefreshTokenEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setRefreshTokenCredential called\");\r\n        const refreshTokenKey = refreshToken.generateCredentialKey();\r\n        this.setItem(refreshTokenKey, JSON.stringify(refreshToken));\r\n\r\n        this.addTokenKey(refreshTokenKey, CredentialType.REFRESH_TOKEN);\r\n    }\r\n\r\n    /**\r\n     * fetch appMetadata entity from the platform cache\r\n     * @param appMetadataKey\r\n     */\r\n    getAppMetadata(appMetadataKey: string): AppMetadataEntity | null {\r\n        const value = this.getItem(appMetadataKey);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getAppMetadata: called, no cache hit\");\r\n            return null;\r\n        }\r\n\r\n        const parsedMetadata = this.validateAndParseJson(value);\r\n        if (!parsedMetadata || !AppMetadataEntity.isAppMetadataEntity(appMetadataKey, parsedMetadata)) {\r\n            this.logger.trace(\"BrowserCacheManager.getAppMetadata: called, no cache hit\");\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(\"BrowserCacheManager.getAppMetadata: cache hit\");\r\n        return CacheManager.toObject(new AppMetadataEntity(), parsedMetadata);\r\n    }\r\n\r\n    /**\r\n     * set appMetadata entity to the platform cache\r\n     * @param appMetadata\r\n     */\r\n    setAppMetadata(appMetadata: AppMetadataEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setAppMetadata called\");\r\n        const appMetadataKey = appMetadata.generateAppMetadataKey();\r\n        this.setItem(appMetadataKey, JSON.stringify(appMetadata));\r\n    }\r\n\r\n    /**\r\n     * fetch server telemetry entity from the platform cache\r\n     * @param serverTelemetryKey\r\n     */\r\n    getServerTelemetry(serverTelemetryKey: string): ServerTelemetryEntity | null {\r\n        const value = this.getItem(serverTelemetryKey);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getServerTelemetry: called, no cache hit\");\r\n            return null;\r\n        }\r\n        const parsedMetadata = this.validateAndParseJson(value);\r\n        if (!parsedMetadata || !ServerTelemetryEntity.isServerTelemetryEntity(serverTelemetryKey, parsedMetadata)) {\r\n            this.logger.trace(\"BrowserCacheManager.getServerTelemetry: called, no cache hit\");\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(\"BrowserCacheManager.getServerTelemetry: cache hit\");\r\n        return CacheManager.toObject(new ServerTelemetryEntity(), parsedMetadata);\r\n    }\r\n\r\n    /**\r\n     * set server telemetry entity to the platform cache\r\n     * @param serverTelemetryKey\r\n     * @param serverTelemetry\r\n     */\r\n    setServerTelemetry(serverTelemetryKey: string, serverTelemetry: ServerTelemetryEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setServerTelemetry called\");\r\n        this.setItem(serverTelemetryKey, JSON.stringify(serverTelemetry));\r\n    }\r\n\r\n    /**\r\n     *\r\n     */\r\n    getAuthorityMetadata(key: string): AuthorityMetadataEntity | null {\r\n        const value = this.internalStorage.getItem(key);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getAuthorityMetadata: called, no cache hit\");\r\n            return null;\r\n        }\r\n        const parsedMetadata = this.validateAndParseJson(value);\r\n        if (parsedMetadata && AuthorityMetadataEntity.isAuthorityMetadataEntity(key, parsedMetadata)) {\r\n            this.logger.trace(\"BrowserCacheManager.getAuthorityMetadata: cache hit\");\r\n            return CacheManager.toObject(new AuthorityMetadataEntity(), parsedMetadata);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     *\r\n     */\r\n    getAuthorityMetadataKeys(): Array<string> {\r\n        const allKeys = this.internalStorage.getKeys();\r\n        return allKeys.filter((key) => {\r\n            return this.isAuthorityMetadata(key);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Sets wrapper metadata in memory\r\n     * @param wrapperSKU\r\n     * @param wrapperVersion\r\n     */\r\n    setWrapperMetadata(wrapperSKU: string, wrapperVersion: string): void {\r\n        this.internalStorage.setItem(InMemoryCacheKeys.WRAPPER_SKU, wrapperSKU);\r\n        this.internalStorage.setItem(InMemoryCacheKeys.WRAPPER_VER, wrapperVersion);\r\n    }\r\n\r\n    /**\r\n     * Returns wrapper metadata from in-memory storage\r\n     */\r\n    getWrapperMetadata(): [string, string] {\r\n        const sku = this.internalStorage.getItem(InMemoryCacheKeys.WRAPPER_SKU) || Constants.EMPTY_STRING;\r\n        const version = this.internalStorage.getItem(InMemoryCacheKeys.WRAPPER_VER) || Constants.EMPTY_STRING;\r\n        return [sku, version];\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @param entity\r\n     */\r\n    setAuthorityMetadata(key: string, entity: AuthorityMetadataEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setAuthorityMetadata called\");\r\n        this.internalStorage.setItem(key, JSON.stringify(entity));\r\n    }\r\n\r\n    /**\r\n     * Gets the active account\r\n     */\r\n    getActiveAccount(): AccountInfo | null {\r\n        const activeAccountKeyFilters = this.generateCacheKey(PersistentCacheKeys.ACTIVE_ACCOUNT_FILTERS);\r\n        const activeAccountValueFilters = this.getItem(activeAccountKeyFilters);\r\n        if (!activeAccountValueFilters) {\r\n            // if new active account cache type isn't found, it's an old version, so look for that instead\r\n            this.logger.trace(\"BrowserCacheManager.getActiveAccount: No active account filters cache schema found, looking for legacy schema\");\r\n            const activeAccountKeyLocal = this.generateCacheKey(PersistentCacheKeys.ACTIVE_ACCOUNT);\r\n            const activeAccountValueLocal = this.getItem(activeAccountKeyLocal);\r\n            if (!activeAccountValueLocal) {\r\n                this.logger.trace(\"BrowserCacheManager.getActiveAccount: No active account found\");\r\n                return null;\r\n            }\r\n            const activeAccount = this.getAccountInfoByFilter({ localAccountId: activeAccountValueLocal })[0] || null;\r\n            if (activeAccount) {\r\n                this.logger.trace(\"BrowserCacheManager.getActiveAccount: Legacy active account cache schema found\");\r\n                this.logger.trace(\"BrowserCacheManager.getActiveAccount: Adding active account filters cache schema\");\r\n                this.setActiveAccount(activeAccount);\r\n                return activeAccount;\r\n            }\r\n            return null;\r\n        }\r\n        const activeAccountValueObj = this.validateAndParseJson(activeAccountValueFilters) as AccountInfo;\r\n        if (activeAccountValueObj) {\r\n            this.logger.trace(\"BrowserCacheManager.getActiveAccount: Active account filters schema found\");\r\n            return this.getAccountInfoByFilter({\r\n                homeAccountId: activeAccountValueObj.homeAccountId,\r\n                localAccountId: activeAccountValueObj.localAccountId\r\n            })[0] || null;\r\n        }\r\n        this.logger.trace(\"BrowserCacheManager.getActiveAccount: No active account found\");\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Sets the active account's localAccountId in cache\r\n     * @param account\r\n     */\r\n    setActiveAccount(account: AccountInfo | null): void {\r\n        const activeAccountKey = this.generateCacheKey(PersistentCacheKeys.ACTIVE_ACCOUNT_FILTERS);\r\n        const activeAccountKeyLocal = this.generateCacheKey(PersistentCacheKeys.ACTIVE_ACCOUNT);\r\n        if (account) {\r\n            this.logger.verbose(\"setActiveAccount: Active account set\");\r\n            const activeAccountValue: ActiveAccountFilters = {\r\n                homeAccountId: account.homeAccountId,\r\n                localAccountId: account.localAccountId\r\n            };\r\n            this.browserStorage.setItem(activeAccountKey, JSON.stringify(activeAccountValue));\r\n            this.browserStorage.setItem(activeAccountKeyLocal, account.localAccountId);\r\n        } else {\r\n            this.logger.verbose(\"setActiveAccount: No account passed, active account not set\");\r\n            this.browserStorage.removeItem(activeAccountKey);\r\n            this.browserStorage.removeItem(activeAccountKeyLocal);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a list of accounts that match all of the filters provided\r\n     * @param account\r\n     */\r\n    getAccountInfoByFilter(accountFilter: Partial<Omit<AccountInfo, \"idTokenClaims\" | \"name\">>): AccountInfo[] {\r\n        const allAccounts = this.getAllAccounts();\r\n        this.logger.trace(`BrowserCacheManager.getAccountInfoByFilter: total ${allAccounts.length} accounts found`);\r\n\r\n        return allAccounts.filter((accountObj) => {\r\n            if (accountFilter.username && accountFilter.username.toLowerCase() !== accountObj.username.toLowerCase()) {\r\n                return false;\r\n            }\r\n\r\n            if (accountFilter.homeAccountId && accountFilter.homeAccountId !== accountObj.homeAccountId) {\r\n                return false;\r\n            }\r\n\r\n            if (accountFilter.localAccountId && accountFilter.localAccountId !== accountObj.localAccountId) {\r\n                return false;\r\n            }\r\n\r\n            if (accountFilter.tenantId && accountFilter.tenantId !== accountObj.tenantId) {\r\n                return false;\r\n            }\r\n\r\n            if (accountFilter.environment && accountFilter.environment !== accountObj.environment) {\r\n                return false;\r\n            }\r\n\r\n            return true;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Checks the cache for accounts matching loginHint or SID\r\n     * @param loginHint\r\n     * @param sid\r\n     */\r\n    getAccountInfoByHints(loginHint?: string, sid?: string): AccountInfo | null {\r\n        const matchingAccounts = this.getAllAccounts().filter((accountInfo) => {\r\n            if (sid) {\r\n                const accountSid = accountInfo.idTokenClaims && accountInfo.idTokenClaims[\"sid\"];\r\n                return sid === accountSid;\r\n            }\r\n\r\n            if (loginHint) {\r\n                return loginHint === accountInfo.username;\r\n            }\r\n\r\n            return false;\r\n        });\r\n\r\n        if (matchingAccounts.length === 1) {\r\n            return matchingAccounts[0];\r\n        } else if (matchingAccounts.length > 1) {\r\n            throw ClientAuthError.createMultipleMatchingAccountsInCacheError();\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * fetch throttling entity from the platform cache\r\n     * @param throttlingCacheKey\r\n     */\r\n    getThrottlingCache(throttlingCacheKey: string): ThrottlingEntity | null {\r\n        const value = this.getItem(throttlingCacheKey);\r\n        if (!value) {\r\n            this.logger.trace(\"BrowserCacheManager.getThrottlingCache: called, no cache hit\");\r\n            return null;\r\n        }\r\n\r\n        const parsedThrottlingCache = this.validateAndParseJson(value);\r\n        if (!parsedThrottlingCache || !ThrottlingEntity.isThrottlingEntity(throttlingCacheKey, parsedThrottlingCache)) {\r\n            this.logger.trace(\"BrowserCacheManager.getThrottlingCache: called, no cache hit\");\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(\"BrowserCacheManager.getThrottlingCache: cache hit\");\r\n        return CacheManager.toObject(new ThrottlingEntity(), parsedThrottlingCache);\r\n    }\r\n\r\n    /**\r\n     * set throttling entity to the platform cache\r\n     * @param throttlingCacheKey\r\n     * @param throttlingCache\r\n     */\r\n    setThrottlingCache(throttlingCacheKey: string, throttlingCache: ThrottlingEntity): void {\r\n        this.logger.trace(\"BrowserCacheManager.setThrottlingCache called\");\r\n        this.setItem(throttlingCacheKey, JSON.stringify(throttlingCache));\r\n    }\r\n\r\n    /**\r\n     * Gets cache item with given key.\r\n     * Will retrieve from cookies if storeAuthStateInCookie is set to true.\r\n     * @param key\r\n     */\r\n    getTemporaryCache(cacheKey: string, generateKey?: boolean): string | null {\r\n        const key = generateKey ? this.generateCacheKey(cacheKey) : cacheKey;\r\n        if (this.cacheConfig.storeAuthStateInCookie) {\r\n            const itemCookie = this.getItemCookie(key);\r\n            if (itemCookie) {\r\n                this.logger.trace(\"BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies\");\r\n                return itemCookie;\r\n            }\r\n        }\r\n\r\n        const value = this.temporaryCacheStorage.getItem(key);\r\n        if (!value) {\r\n            // If temp cache item not found in session/memory, check local storage for items set by old versions\r\n            if (this.cacheConfig.cacheLocation === BrowserCacheLocation.LocalStorage) {\r\n                const item = this.browserStorage.getItem(key);\r\n                if (item) {\r\n                    this.logger.trace(\"BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage\");\r\n                    return item;\r\n                }\r\n            }\r\n            this.logger.trace(\"BrowserCacheManager.getTemporaryCache: No cache item found in local storage\");\r\n            return null;\r\n        }\r\n        this.logger.trace(\"BrowserCacheManager.getTemporaryCache: Temporary cache item returned\");\r\n        return value;\r\n    }\r\n\r\n    /**\r\n     * Sets the cache item with the key and value given.\r\n     * Stores in cookie if storeAuthStateInCookie is set to true.\r\n     * This can cause cookie overflow if used incorrectly.\r\n     * @param key\r\n     * @param value\r\n     */\r\n    setTemporaryCache(cacheKey: string, value: string, generateKey?: boolean): void {\r\n        const key = generateKey ? this.generateCacheKey(cacheKey) : cacheKey;\r\n\r\n        this.temporaryCacheStorage.setItem(key, value);\r\n        if (this.cacheConfig.storeAuthStateInCookie) {\r\n            this.logger.trace(\"BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie\");\r\n            this.setItemCookie(key, value);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes the cache item with the given key.\r\n     * Will also clear the cookie item if storeAuthStateInCookie is set to true.\r\n     * @param key\r\n     */\r\n    removeItem(key: string): void {\r\n        this.browserStorage.removeItem(key);\r\n        this.temporaryCacheStorage.removeItem(key);\r\n        if (this.cacheConfig.storeAuthStateInCookie) {\r\n            this.logger.trace(\"BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie\");\r\n            this.clearItemCookie(key);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks whether key is in cache.\r\n     * @param key\r\n     */\r\n    containsKey(key: string): boolean {\r\n        return this.browserStorage.containsKey(key) || this.temporaryCacheStorage.containsKey(key);\r\n    }\r\n\r\n    /**\r\n     * Gets all keys in window.\r\n     */\r\n    getKeys(): string[] {\r\n        return [\r\n            ...this.browserStorage.getKeys(),\r\n            ...this.temporaryCacheStorage.getKeys()\r\n        ];\r\n    }\r\n\r\n    /**\r\n     * Clears all cache entries created by MSAL.\r\n     */\r\n    async clear(): Promise<void> {\r\n        // Removes all accounts and their credentials\r\n        await this.removeAllAccounts();\r\n        this.removeAppMetadata();\r\n\r\n        // Removes all remaining MSAL cache items\r\n        this.getKeys().forEach((cacheKey: string) => {\r\n            // Check if key contains msal prefix; For now, we are clearing all the cache items created by MSAL.js\r\n            if ((this.browserStorage.containsKey(cacheKey) || this.temporaryCacheStorage.containsKey(cacheKey)) && ((cacheKey.indexOf(Constants.CACHE_PREFIX) !== -1) || (cacheKey.indexOf(this.clientId) !== -1))) {\r\n                this.removeItem(cacheKey);\r\n            }\r\n        });\r\n\r\n        this.internalStorage.clear();\r\n    }\r\n\r\n    /**\r\n     * Clears all access tokes that have claims prior to saving the current one\r\n     * @param credential \r\n     * @returns \r\n     */\r\n    async clearTokensAndKeysWithClaims(): Promise<void> {\r\n\r\n        this.logger.trace(\"BrowserCacheManager.clearTokensAndKeysWithClaims called\");\r\n        const tokenKeys = this.getTokenKeys();\r\n            \r\n        const removedAccessTokens: Array<Promise<void>> = [];\r\n        tokenKeys.accessToken.forEach((key: string) => {\r\n            // if the access token has claims in its key, remove the token key and the token\r\n            const credential = this.getAccessTokenCredential(key);\r\n            if(credential?.requestedClaimsHash && key.includes(credential.requestedClaimsHash.toLowerCase())) {\r\n                removedAccessTokens.push(this.removeAccessToken(key));\r\n            }\r\n        });\r\n        await Promise.all(removedAccessTokens);\r\n\r\n        // warn if any access tokens are removed\r\n        if(removedAccessTokens.length > 0) {\r\n            this.logger.warning(`${removedAccessTokens.length} access tokens with claims in the cache keys have been removed from the cache.`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add value to cookies\r\n     * @param cookieName\r\n     * @param cookieValue\r\n     * @param expires\r\n     */\r\n    setItemCookie(cookieName: string, cookieValue: string, expires?: number): void {\r\n        let cookieStr = `${encodeURIComponent(cookieName)}=${encodeURIComponent(cookieValue)};path=/;SameSite=Lax;`;\r\n        if (expires) {\r\n            const expireTime = this.getCookieExpirationTime(expires);\r\n            cookieStr += `expires=${expireTime};`;\r\n        }\r\n\r\n        if (this.cacheConfig.secureCookies) {\r\n            cookieStr += \"Secure;\";\r\n        }\r\n\r\n        document.cookie = cookieStr;\r\n    }\r\n\r\n    /**\r\n     * Get one item by key from cookies\r\n     * @param cookieName\r\n     */\r\n    getItemCookie(cookieName: string): string {\r\n        const name = `${encodeURIComponent(cookieName)}=`;\r\n        const cookieList = document.cookie.split(\";\");\r\n        for (let i: number = 0; i < cookieList.length; i++) {\r\n            let cookie = cookieList[i];\r\n            while (cookie.charAt(0) === \" \") {\r\n                cookie = cookie.substring(1);\r\n            }\r\n            if (cookie.indexOf(name) === 0) {\r\n                return decodeURIComponent(cookie.substring(name.length, cookie.length));\r\n            }\r\n        }\r\n        return Constants.EMPTY_STRING;\r\n    }\r\n\r\n    /**\r\n     * Clear all msal-related cookies currently set in the browser. Should only be used to clear temporary cache items.\r\n     */\r\n    clearMsalCookies(): void {\r\n        const cookiePrefix = `${Constants.CACHE_PREFIX}.${this.clientId}`;\r\n        const cookieList = document.cookie.split(\";\");\r\n        cookieList.forEach((cookie: string): void => {\r\n            while (cookie.charAt(0) === \" \") {\r\n                // eslint-disable-next-line no-param-reassign\r\n                cookie = cookie.substring(1);\r\n            }\r\n            if (cookie.indexOf(cookiePrefix) === 0) {\r\n                const cookieKey = cookie.split(\"=\")[0];\r\n                this.clearItemCookie(cookieKey);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Clear an item in the cookies by key\r\n     * @param cookieName\r\n     */\r\n    clearItemCookie(cookieName: string): void {\r\n        this.setItemCookie(cookieName, Constants.EMPTY_STRING, -1);\r\n    }\r\n\r\n    /**\r\n     * Get cookie expiration time\r\n     * @param cookieLifeDays\r\n     */\r\n    getCookieExpirationTime(cookieLifeDays: number): string {\r\n        const today = new Date();\r\n        const expr = new Date(today.getTime() + cookieLifeDays * this.COOKIE_LIFE_MULTIPLIER);\r\n        return expr.toUTCString();\r\n    }\r\n\r\n    /**\r\n     * Gets the cache object referenced by the browser\r\n     */\r\n    getCache(): object {\r\n        return this.browserStorage;\r\n    }\r\n\r\n    /**\r\n     * interface compat, we cannot overwrite browser cache; Functionality is supported by individual entities in browser\r\n     */\r\n    setCache(): void {\r\n        // sets nothing\r\n    }\r\n\r\n    /**\r\n     * Prepend msal.<client-id> to each key; Skip for any JSON object as Key (defined schemas do not need the key appended: AccessToken Keys or the upcoming schema)\r\n     * @param key\r\n     * @param addInstanceId\r\n     */\r\n    generateCacheKey(key: string): string {\r\n        const generatedKey = this.validateAndParseJson(key);\r\n        if (!generatedKey) {\r\n            if (StringUtils.startsWith(key, Constants.CACHE_PREFIX) || StringUtils.startsWith(key, PersistentCacheKeys.ADAL_ID_TOKEN)) {\r\n                return key;\r\n            }\r\n            return `${Constants.CACHE_PREFIX}.${this.clientId}.${key}`;\r\n        }\r\n\r\n        return JSON.stringify(key);\r\n    }\r\n\r\n    /**\r\n     * Create authorityKey to cache authority\r\n     * @param state\r\n     */\r\n    generateAuthorityKey(stateString: string): string {\r\n        const {\r\n            libraryState: {\r\n                id: stateId\r\n            }\r\n        } = ProtocolUtils.parseRequestState(this.cryptoImpl, stateString);\r\n\r\n        return this.generateCacheKey(`${TemporaryCacheKeys.AUTHORITY}.${stateId}`);\r\n    }\r\n\r\n    /**\r\n     * Create Nonce key to cache nonce\r\n     * @param state\r\n     */\r\n    generateNonceKey(stateString: string): string {\r\n        const {\r\n            libraryState: {\r\n                id: stateId\r\n            }\r\n        } = ProtocolUtils.parseRequestState(this.cryptoImpl, stateString);\r\n\r\n        return this.generateCacheKey(`${TemporaryCacheKeys.NONCE_IDTOKEN}.${stateId}`);\r\n    }\r\n\r\n    /**\r\n     * Creates full cache key for the request state\r\n     * @param stateString State string for the request\r\n     */\r\n    generateStateKey(stateString: string): string {\r\n        // Use the library state id to key temp storage for uniqueness for multiple concurrent requests\r\n        const {\r\n            libraryState: {\r\n                id: stateId\r\n            }\r\n        } = ProtocolUtils.parseRequestState(this.cryptoImpl, stateString);\r\n        return this.generateCacheKey(`${TemporaryCacheKeys.REQUEST_STATE}.${stateId}`);\r\n    }\r\n\r\n    /**\r\n     * Gets the cached authority based on the cached state. Returns empty if no cached state found.\r\n     */\r\n    getCachedAuthority(cachedState: string): string | null {\r\n        const stateCacheKey = this.generateStateKey(cachedState);\r\n        const state = this.getTemporaryCache(stateCacheKey);\r\n        if (!state) {\r\n            return null;\r\n        }\r\n\r\n        const authorityCacheKey = this.generateAuthorityKey(state);\r\n        return this.getTemporaryCache(authorityCacheKey);\r\n    }\r\n\r\n    /**\r\n     * Updates account, authority, and state in cache\r\n     * @param serverAuthenticationRequest\r\n     * @param account\r\n     */\r\n    updateCacheEntries(state: string, nonce: string, authorityInstance: string, loginHint: string, account: AccountInfo | null): void {\r\n        this.logger.trace(\"BrowserCacheManager.updateCacheEntries called\");\r\n        // Cache the request state\r\n        const stateCacheKey = this.generateStateKey(state);\r\n        this.setTemporaryCache(stateCacheKey, state, false);\r\n\r\n        // Cache the nonce\r\n        const nonceCacheKey = this.generateNonceKey(state);\r\n        this.setTemporaryCache(nonceCacheKey, nonce, false);\r\n\r\n        // Cache authorityKey\r\n        const authorityCacheKey = this.generateAuthorityKey(state);\r\n        this.setTemporaryCache(authorityCacheKey, authorityInstance, false);\r\n\r\n        if (account) {\r\n            const ccsCredential: CcsCredential = {\r\n                credential: account.homeAccountId,\r\n                type: CcsCredentialType.HOME_ACCOUNT_ID\r\n            };\r\n            this.setTemporaryCache(TemporaryCacheKeys.CCS_CREDENTIAL, JSON.stringify(ccsCredential), true);\r\n        } else if (!StringUtils.isEmpty(loginHint)) {\r\n            const ccsCredential: CcsCredential = {\r\n                credential: loginHint,\r\n                type: CcsCredentialType.UPN\r\n            };\r\n            this.setTemporaryCache(TemporaryCacheKeys.CCS_CREDENTIAL, JSON.stringify(ccsCredential), true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Reset all temporary cache items\r\n     * @param state\r\n     */\r\n    resetRequestCache(state: string): void {\r\n        this.logger.trace(\"BrowserCacheManager.resetRequestCache called\");\r\n        // check state and remove associated cache items\r\n        if (!StringUtils.isEmpty(state)) {\r\n            this.getKeys().forEach(key => {\r\n                if (key.indexOf(state) !== -1) {\r\n                    this.removeItem(key);\r\n                }\r\n            });\r\n        }\r\n\r\n        // delete generic interactive request parameters\r\n        if (state) {\r\n            this.removeItem(this.generateStateKey(state));\r\n            this.removeItem(this.generateNonceKey(state));\r\n            this.removeItem(this.generateAuthorityKey(state));\r\n        }\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.REQUEST_PARAMS));\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.ORIGIN_URI));\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.URL_HASH));\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.CORRELATION_ID));\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.CCS_CREDENTIAL));\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.NATIVE_REQUEST));\r\n        this.setInteractionInProgress(false);\r\n    }\r\n\r\n    /**\r\n     * Removes temporary cache for the provided state\r\n     * @param stateString\r\n     */\r\n    cleanRequestByState(stateString: string): void {\r\n        this.logger.trace(\"BrowserCacheManager.cleanRequestByState called\");\r\n        // Interaction is completed - remove interaction status.\r\n        if (stateString) {\r\n            const stateKey = this.generateStateKey(stateString);\r\n            const cachedState = this.temporaryCacheStorage.getItem(stateKey);\r\n            this.logger.infoPii(`BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: ${cachedState}`);\r\n            this.resetRequestCache(cachedState || Constants.EMPTY_STRING);\r\n        }\r\n        this.clearMsalCookies();\r\n    }\r\n\r\n    /**\r\n     * Looks in temporary cache for any state values with the provided interactionType and removes all temporary cache items for that state\r\n     * Used in scenarios where temp cache needs to be cleaned but state is not known, such as clicking browser back button.\r\n     * @param interactionType\r\n     */\r\n    cleanRequestByInteractionType(interactionType: InteractionType): void {\r\n        this.logger.trace(\"BrowserCacheManager.cleanRequestByInteractionType called\");\r\n        // Loop through all keys to find state key\r\n        this.getKeys().forEach((key) => {\r\n            // If this key is not the state key, move on\r\n            if (key.indexOf(TemporaryCacheKeys.REQUEST_STATE) === -1) {\r\n                return;\r\n            }\r\n\r\n            // Retrieve state value, return if not a valid value\r\n            const stateValue = this.temporaryCacheStorage.getItem(key);\r\n            if (!stateValue) {\r\n                return;\r\n            }\r\n            // Extract state and ensure it matches given InteractionType, then clean request cache\r\n            const parsedState = BrowserProtocolUtils.extractBrowserRequestState(this.cryptoImpl, stateValue);\r\n            if (parsedState && parsedState.interactionType === interactionType) {\r\n                this.logger.infoPii(`BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: ${stateValue}`);\r\n                this.resetRequestCache(stateValue);\r\n            }\r\n        });\r\n        this.clearMsalCookies();\r\n        this.setInteractionInProgress(false);\r\n    }\r\n\r\n    cacheCodeRequest(authCodeRequest: CommonAuthorizationCodeRequest, browserCrypto: ICrypto): void {\r\n        this.logger.trace(\"BrowserCacheManager.cacheCodeRequest called\");\r\n\r\n        const encodedValue = browserCrypto.base64Encode(JSON.stringify(authCodeRequest));\r\n        this.setTemporaryCache(TemporaryCacheKeys.REQUEST_PARAMS, encodedValue, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the token exchange parameters from the cache. Throws an error if nothing is found.\r\n     */\r\n    getCachedRequest(state: string, browserCrypto: ICrypto): CommonAuthorizationCodeRequest {\r\n        this.logger.trace(\"BrowserCacheManager.getCachedRequest called\");\r\n        // Get token request from cache and parse as TokenExchangeParameters.\r\n        const encodedTokenRequest = this.getTemporaryCache(TemporaryCacheKeys.REQUEST_PARAMS, true);\r\n        if (!encodedTokenRequest) {\r\n            throw BrowserAuthError.createNoTokenRequestCacheError();\r\n        }\r\n\r\n        const parsedRequest = this.validateAndParseJson(browserCrypto.base64Decode(encodedTokenRequest)) as CommonAuthorizationCodeRequest;\r\n        if (!parsedRequest) {\r\n            throw BrowserAuthError.createUnableToParseTokenRequestCacheError();\r\n        }\r\n        this.removeItem(this.generateCacheKey(TemporaryCacheKeys.REQUEST_PARAMS));\r\n\r\n        // Get cached authority and use if no authority is cached with request.\r\n        if (StringUtils.isEmpty(parsedRequest.authority)) {\r\n            const authorityCacheKey: string = this.generateAuthorityKey(state);\r\n            const cachedAuthority = this.getTemporaryCache(authorityCacheKey);\r\n            if (!cachedAuthority) {\r\n                throw BrowserAuthError.createNoCachedAuthorityError();\r\n            }\r\n            parsedRequest.authority = cachedAuthority;\r\n        }\r\n\r\n        return parsedRequest;\r\n    }\r\n\r\n    /**\r\n     * Gets cached native request for redirect flows\r\n     */\r\n    getCachedNativeRequest(): NativeTokenRequest | null {\r\n        this.logger.trace(\"BrowserCacheManager.getCachedNativeRequest called\");\r\n        const cachedRequest = this.getTemporaryCache(TemporaryCacheKeys.NATIVE_REQUEST, true);\r\n        if (!cachedRequest) {\r\n            this.logger.trace(\"BrowserCacheManager.getCachedNativeRequest: No cached native request found\");\r\n            return null;\r\n        }\r\n\r\n        const parsedRequest = this.validateAndParseJson(cachedRequest) as NativeTokenRequest;\r\n        if (!parsedRequest) {\r\n            this.logger.error(\"BrowserCacheManager.getCachedNativeRequest: Unable to parse native request\");\r\n            return null;\r\n        }\r\n\r\n        return parsedRequest;\r\n    }\r\n\r\n    isInteractionInProgress(matchClientId?: boolean): boolean {\r\n        const clientId = this.getInteractionInProgress();\r\n\r\n        if (matchClientId) {\r\n            return clientId === this.clientId;\r\n        } else {\r\n            return !!clientId;\r\n        }\r\n    }\r\n\r\n    getInteractionInProgress(): string | null {\r\n        const key = `${Constants.CACHE_PREFIX}.${TemporaryCacheKeys.INTERACTION_STATUS_KEY}`;\r\n        return this.getTemporaryCache(key, false);\r\n    }\r\n\r\n    setInteractionInProgress(inProgress: boolean): void {\r\n        // Ensure we don't overwrite interaction in progress for a different clientId\r\n        const key = `${Constants.CACHE_PREFIX}.${TemporaryCacheKeys.INTERACTION_STATUS_KEY}`;\r\n        if (inProgress) {\r\n            if (this.getInteractionInProgress()) {\r\n                throw BrowserAuthError.createInteractionInProgressError();\r\n            } else {\r\n                // No interaction is in progress\r\n                this.setTemporaryCache(key, this.clientId, false);\r\n            }\r\n        } else if (!inProgress && this.getInteractionInProgress() === this.clientId) {\r\n            this.removeItem(key);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns username retrieved from ADAL or MSAL v1 idToken\r\n     */\r\n    getLegacyLoginHint(): string | null {\r\n        // Only check for adal/msal token if no SSO params are being used\r\n        const adalIdTokenString = this.getTemporaryCache(PersistentCacheKeys.ADAL_ID_TOKEN);\r\n        if (adalIdTokenString) {\r\n            this.browserStorage.removeItem(PersistentCacheKeys.ADAL_ID_TOKEN);\r\n            this.logger.verbose(\"Cached ADAL id token retrieved.\");\r\n        }\r\n\r\n        // Check for cached MSAL v1 id token\r\n        const msalIdTokenString = this.getTemporaryCache(PersistentCacheKeys.ID_TOKEN, true);\r\n        if (msalIdTokenString) {\r\n            this.removeItem(this.generateCacheKey(PersistentCacheKeys.ID_TOKEN));\r\n            this.logger.verbose(\"Cached MSAL.js v1 id token retrieved\");\r\n        }\r\n\r\n        const cachedIdTokenString = msalIdTokenString || adalIdTokenString;\r\n        if (cachedIdTokenString) {\r\n            const cachedIdToken = new IdToken(cachedIdTokenString, this.cryptoImpl);\r\n            if (cachedIdToken.claims && cachedIdToken.claims.preferred_username) {\r\n                this.logger.verbose(\"No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 preferred_username as loginHint\");\r\n                return cachedIdToken.claims.preferred_username;\r\n            }\r\n            else if (cachedIdToken.claims && cachedIdToken.claims.upn) {\r\n                this.logger.verbose(\"No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 upn as loginHint\");\r\n                return cachedIdToken.claims.upn;\r\n            }\r\n            else {\r\n                this.logger.verbose(\"No SSO params used and ADAL/MSAL v1 token retrieved, however, no account hint claim found. Enable preferred_username or upn id token claim to get SSO.\");\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Updates a credential's cache key if the current cache key is outdated\r\n     */\r\n    updateCredentialCacheKey(currentCacheKey: string, credential: ValidCredentialType): string {\r\n        const updatedCacheKey = credential.generateCredentialKey();\r\n\r\n        if (currentCacheKey !== updatedCacheKey) {\r\n            const cacheItem = this.getItem(currentCacheKey);\r\n            if (cacheItem) {\r\n                this.removeItem(currentCacheKey);\r\n                this.setItem(updatedCacheKey, cacheItem);\r\n                this.logger.verbose(`Updated an outdated ${credential.credentialType} cache key`);\r\n                return updatedCacheKey;\r\n            } else {\r\n                this.logger.error(`Attempted to update an outdated ${credential.credentialType} cache key but no item matching the outdated key was found in storage`);\r\n            }\r\n        }\r\n\r\n        return currentCacheKey;\r\n    }\r\n\r\n    /**\r\n     * Returns application id as redirect context during AcquireTokenRedirect flow.\r\n     */\r\n    getRedirectRequestContext(): string | null {\r\n        return this.getTemporaryCache(TemporaryCacheKeys.REDIRECT_CONTEXT, true);\r\n    }\r\n\r\n    /**\r\n     * Sets application id as the redirect context during AcquireTokenRedirect flow.\r\n     * @param value\r\n     */\r\n    setRedirectRequestContext(value: string): void {\r\n        this.setTemporaryCache(TemporaryCacheKeys.REDIRECT_CONTEXT, value, true);\r\n    }\r\n\r\n    /**\r\n     * Builds credential entities from AuthenticationResult object and saves the resulting credentials to the cache\r\n     * @param result\r\n     * @param request\r\n     */\r\n    async hydrateCache(\r\n        result: AuthenticationResult,\r\n        request: SilentRequest\r\n        | SsoSilentRequest\r\n        | RedirectRequest\r\n        | PopupRequest\r\n    ): Promise<void> {\r\n        const idTokenEntity = IdTokenEntity.createIdTokenEntity(\r\n            result.account?.homeAccountId || \"\" ,\r\n            result.account?.environment || \"\",\r\n            result.idToken,\r\n            this.clientId,\r\n            result.tenantId\r\n        );\r\n\r\n        let claimsHash;\r\n        if (request.claims) {\r\n            claimsHash = await this.cryptoImpl.hashString(request.claims);\r\n        }\r\n        const accessTokenEntity = AccessTokenEntity.createAccessTokenEntity(\r\n            result.account?.homeAccountId || \"\",\r\n            result.account?.environment || \"\",\r\n            result.accessToken,\r\n            this.clientId,\r\n            result.tenantId,\r\n            result.scopes.join(\" \"),\r\n            result.expiresOn?.getTime() || 0,\r\n            result.extExpiresOn?.getTime() || 0,\r\n            this.cryptoImpl,\r\n            undefined, // refreshOn\r\n            result.tokenType as AuthenticationScheme,\r\n            undefined, // userAssertionHash\r\n            request.sshKid,\r\n            request.claims,\r\n            claimsHash\r\n        );\r\n\r\n        const cacheRecord = new CacheRecord(\r\n            undefined,\r\n            idTokenEntity,\r\n            accessTokenEntity\r\n        );\r\n        return this.saveCacheRecord(cacheRecord);\r\n    }\r\n}\r\n\r\nexport const DEFAULT_BROWSER_CACHE_MANAGER = (clientId: string, logger: Logger): BrowserCacheManager => {\r\n    const cacheOptions: Required<CacheOptions> = {\r\n        cacheLocation: BrowserCacheLocation.MemoryStorage,\r\n        temporaryCacheLocation: BrowserCacheLocation.MemoryStorage,\r\n        storeAuthStateInCookie: false,\r\n        secureCookies: false,\r\n        cacheMigrationEnabled: false,\r\n        claimsBasedCachingEnabled: true\r\n    };\r\n    return new BrowserCacheManager(clientId, cacheOptions, DEFAULT_CRYPTO_IMPLEMENTATION, logger);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAgBH;;;;AAIG;AACH,IAAA,mBAAA,kBAAA,UAAA,MAAA,EAAA;IAAyC,SAAY,CAAA,mBAAA,EAAA,MAAA,CAAA,CAAA;AAgBjD,IAAA,SAAA,mBAAA,CAAY,QAAgB,EAAE,WAAmC,EAAE,UAAmB,EAAE,MAAc,EAAA;AAAtG,QAAA,IAAA,KAAA,GACI,kBAAM,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,IAYtC,IAAA,CAAA;;QAfkB,KAAsB,CAAA,sBAAA,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAI5D,QAAA,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,KAAI,CAAC,eAAe,GAAG,IAAI,aAAa,EAAE,CAAC;AAC3C,QAAA,KAAI,CAAC,cAAc,GAAG,KAAI,CAAC,mBAAmB,CAAC,KAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAC/E,QAAA,KAAI,CAAC,qBAAqB,GAAG,KAAI,CAAC,0BAA0B,CAAC,KAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,KAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;QAGtI,IAAI,WAAW,CAAC,qBAAqB,EAAE;YACnC,KAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,KAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;;KACJ;AAED;;;AAGG;IACO,mBAAmB,CAAA,SAAA,CAAA,mBAAA,GAA7B,UAA8B,aAA4C,EAAA;AACtE,QAAA,QAAQ,aAAa;YACjB,KAAK,oBAAoB,CAAC,YAAY,CAAC;YACvC,KAAK,oBAAoB,CAAC,cAAc;gBACpC,IAAI;AACA,oBAAA,OAAO,IAAI,cAAc,CAAC,aAAa,CAAC,CAAC;AAC5C,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACvB,MAAM;AACT,iBAAA;AAIR,SAAA;QACD,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC;QACpE,OAAO,IAAI,aAAa,EAAE,CAAC;KAC9B,CAAA;AAED;;;;AAIG;AACO,IAAA,mBAAA,CAAA,SAAA,CAAA,0BAA0B,GAApC,UAAqC,sBAAqD,EAAE,aAA4C,EAAA;AACpI,QAAA,QAAQ,aAAa;YACjB,KAAK,oBAAoB,CAAC,YAAY,CAAC;YACvC,KAAK,oBAAoB,CAAC,cAAc;gBACpC,IAAI;AACA;;;AAGG;oBACH,OAAO,IAAI,cAAc,CAAC,sBAAsB,IAAI,oBAAoB,CAAC,cAAc,CAAC,CAAC;AAC5F,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACvB,OAAO,IAAI,CAAC,eAAe,CAAC;AAC/B,iBAAA;YACL,KAAK,oBAAoB,CAAC,aAAa,CAAC;AACxC,YAAA;gBACI,OAAO,IAAI,CAAC,eAAe,CAAC;AACnC,SAAA;KACJ,CAAA;AAED;;;AAGG;AACO,IAAA,mBAAA,CAAA,SAAA,CAAA,mBAAmB,GAA7B,YAAA;QAAA,IAeC,KAAA,GAAA,IAAA,CAAA;QAdG,IAAM,UAAU,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,mBAAmB,CAAC,QAAU,CAAC;QAC/E,IAAM,aAAa,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,mBAAmB,CAAC,WAAa,CAAC;QACrF,IAAM,QAAQ,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,mBAAmB,CAAC,KAAO,CAAC;QAC1E,IAAM,YAAY,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,mBAAmB,CAAC,UAAY,CAAC;QAEnF,IAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7D,IAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACnE,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEjE,IAAM,MAAM,GAAG,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AAC3E,QAAA,IAAM,aAAa,GAAG,CAAC,mBAAmB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,WAAW,EAAE,mBAAmB,CAAC,KAAK,EAAE,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAEjJ,aAAa,CAAC,OAAO,CAAC,UAAC,QAAgB,EAAE,KAAa,EAAK,EAAA,OAAA,KAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,CAAC,CAAC;KAC/G,CAAA;AAED;;;;;AAKG;AACO,IAAA,mBAAA,CAAA,SAAA,CAAA,iBAAiB,GAA3B,UAA4B,MAAc,EAAE,KAAoB,EAAA;AAC5D,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/C,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACK,IAAA,mBAAA,CAAA,SAAA,CAAA,aAAa,GAArB,YAAA;QAAA,IA8EC,KAAA,GAAA,IAAA,CAAA;AA7EG,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AAC/D,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAI,eAAe,CAAC,UAAU,GAAI,GAAA,GAAA,IAAI,CAAC,QAAU,CAAC,CAAC;QACjF,IAAI,WAAW,IAAI,SAAS,EAAE;AAC1B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mGAAmG,CAAC,CAAC;;YAEzH,OAAO;AACV,SAAA;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;AAC9C,QAAA,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;AAChB,YAAA,IAAI,KAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;;gBAE3B,IAAM,KAAK,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC,gBAAA,IAAI,KAAK,EAAE;oBACP,IAAM,OAAO,GAAG,KAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;oBACjD,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE;AACrD,wBAAA,QAAQ,OAAO,CAAC,gBAAgB,CAAC;4BAC7B,KAAK,cAAc,CAAC,QAAQ;AACxB,gCAAA,IAAI,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;AACxC,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gFAAgF,CAAC,CAAC;oCACpG,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,wDAAyD,GAAA,GAAG,GAAqC,qCAAA,CAAC,CAAC;AACxH,oCAAA,IAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,aAAa,EAAE,EAAE,OAAO,CAAC,CAAC;oCAC1E,IAAM,MAAM,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;oCACjE,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;oCAClD,OAAO;AACV,iCAAA;AAAM,qCAAA;AACH,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+KAA+K,CAAC,CAAC;oCACnM,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,wEAAyE,GAAA,GAAK,CAAC,CAAC;AACxG,iCAAA;gCACD,MAAM;4BACV,KAAK,cAAc,CAAC,YAAY,CAAC;4BACjC,KAAK,cAAc,CAAC,6BAA6B;AAC7C,gCAAA,IAAI,iBAAiB,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;AAChD,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oFAAoF,CAAC,CAAC;oCACxG,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,4DAA6D,GAAA,GAAG,GAAqC,qCAAA,CAAC,CAAC;AAC5H,oCAAA,IAAM,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,iBAAiB,EAAE,EAAE,OAAO,CAAC,CAAC;oCAClF,IAAM,MAAM,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;oCACrE,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;oCACtD,OAAO;AACV,iCAAA;AAAM,qCAAA;AACH,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2LAA2L,CAAC,CAAC;oCAC/M,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,4EAA6E,GAAA,GAAK,CAAC,CAAC;AAC5G,iCAAA;gCACD,MAAM;4BACV,KAAK,cAAc,CAAC,aAAa;AAC7B,gCAAA,IAAI,kBAAkB,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;AAClD,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;oCACzG,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,6DAA8D,GAAA,GAAG,GAAqC,qCAAA,CAAC,CAAC;AAC7H,oCAAA,IAAM,kBAAkB,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,kBAAkB,EAAE,EAAE,OAAO,CAAC,CAAC;oCACpF,IAAM,MAAM,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;oCACtE,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;oCACvD,OAAO;AACV,iCAAA;AAAM,qCAAA;AACH,oCAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8LAA8L,CAAC,CAAC;oCAClN,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,6EAA8E,GAAA,GAAK,CAAC,CAAC;AAC7G,iCAAA;gCACD,MAAM;;AAGb,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;gBACxB,IAAM,KAAK,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC,gBAAA,IAAI,KAAK,EAAE;oBACP,IAAM,UAAU,GAAG,KAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;oBACpD,IAAI,UAAU,IAAI,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;AACzD,wBAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;wBACtG,KAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,wDAAyD,GAAA,GAAG,GAAuC,uCAAA,CAAC,CAAC;AAC1H,wBAAA,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAChC,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;AAGG;IACO,mBAAoB,CAAA,SAAA,CAAA,oBAAA,GAA9B,UAA+B,SAAiB,EAAA;QAC5C,IAAI;YACA,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACzC;;;;;AAKG;AACH,YAAA,OAAO,CAAC,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC;AAC7E,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACZ,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,mBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,GAAW,EAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC3C,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,GAAW,EAAE,KAAa,EAAA;QAC9B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC3C,CAAA;AAED;;;AAGG;IACH,mBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,UAAkB,EAAA;AACzB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE;AACV,YAAA,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACzC,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE;AACjE,YAAA,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACzC,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,OAAO,YAAY,CAAC,QAAQ,CAAgB,IAAI,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;KACnF,CAAA;AAED;;;;AAIG;IACH,mBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,OAAsB,EAAA;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC3D,QAAA,IAAM,GAAG,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;KAChC,CAAA;AAED;;;AAGG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AAC/D,QAAA,IAAI,WAAW,EAAE;AACb,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4DAA4D,CAAC,CAAC;AAClF,QAAA,OAAO,EAAE,CAAC;KACb,CAAA;AAED;;;AAGG;IACH,mBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,GAAW,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,0DAA2D,GAAA,GAAK,CAAC,CAAC;AACvF,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;;AAEjC,YAAA,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtB,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0DAA0D,CAAC,CAAC;AACnF,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0EAA0E,CAAC,CAAC;AACnG,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,mBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,GAAW,EAAA;AAC/B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,+DAAgE,GAAA,GAAK,CAAC,CAAC;AAC5F,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE;AACnB,YAAA,WAAW,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;AACxF,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2EAA2E,CAAC,CAAC;AAClG,SAAA;KACJ,CAAA;AAED;;;AAGG;IACG,mBAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UAAoB,GAAW,EAAA;;;AAC3B,gBAAA,MAAA,CAAA,SAAA,CAAM,aAAa,CAAA,IAAA,CAAA,IAAA,EAAC,GAAG,CAAC,CAAC;AACzB,gBAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;;;;AACrC,KAAA,CAAA;AAED;;;AAGG;IACH,mBAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,GAAW,EAAA;AACrB,QAAA,MAAA,CAAA,SAAA,CAAM,aAAa,CAAA,IAAA,CAAA,IAAA,EAAC,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;KACrD,CAAA;AAED;;;AAGG;IACG,mBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAvB,UAAwB,GAAW,EAAA;;;AAC/B,gBAAA,MAAA,CAAA,SAAA,CAAM,iBAAiB,CAAA,IAAA,CAAA,IAAA,EAAC,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;;;;AACzD,KAAA,CAAA;AAED;;;AAGG;IACH,mBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,GAAW,EAAA;AAC1B,QAAA,MAAA,CAAA,SAAA,CAAM,kBAAkB,CAAA,IAAA,CAAA,IAAA,EAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;KAC1D,CAAA;AAED;;;AAGG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC7D,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAI,eAAe,CAAC,UAAU,GAAI,GAAA,GAAA,IAAI,CAAC,QAAU,CAAC,CAAC;AAC5E,QAAA,IAAI,IAAI,EAAE;YACN,IAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAClD,YAAA,IAAI,SAAS;AACT,gBAAA,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC;AACnC,gBAAA,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC;AACvC,gBAAA,SAAS,CAAC,cAAc,CAAC,cAAc,CAAC,EAC1C;AACE,gBAAA,OAAO,SAAsB,CAAC;AACjC,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wGAAwG,CAAC,CAAC;AAC/H,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;AACjF,SAAA;QAED,OAAO;AACH,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,WAAW,EAAE,EAAE;AACf,YAAA,YAAY,EAAE,EAAE;SACnB,CAAC;KACL,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UAAY,GAAW,EAAE,IAAoB,EAAA;AACzC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAEtC,QAAA,QAAQ,IAAI;YACR,KAAK,cAAc,CAAC,QAAQ;gBACxB,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;AAC5E,oBAAA,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,iBAAA;gBACD,MAAM;YACV,KAAK,cAAc,CAAC,YAAY;gBAC5B,IAAI,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AAC3C,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;AAChF,oBAAA,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,iBAAA;gBACD,MAAM;YACV,KAAK,cAAc,CAAC,aAAa;gBAC7B,IAAI,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5C,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;AACjF,oBAAA,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,iBAAA;gBACD,MAAM;AACV,YAAA;gBACI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qFAAsF,GAAA,IAAM,CAAC,CAAC;gBAChH,eAAe,CAAC,mCAAmC,EAAE,CAAC;AAC7D,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAI,eAAe,CAAC,UAAU,SAAI,IAAI,CAAC,QAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;KAC7F,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,GAAW,EAAE,IAAoB,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAC/D,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAEtC,QAAA,QAAQ,IAAI;YACR,KAAK,cAAc,CAAC,QAAQ;gBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+EAAgF,GAAA,GAAG,GAAW,WAAA,CAAC,CAAC;gBACpH,IAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACjD,gBAAA,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE;AAChB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;oBACnF,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC1C,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8HAA8H,CAAC,CAAC;AACpJ,iBAAA;gBACD,MAAM;YACV,KAAK,cAAc,CAAC,YAAY;gBAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mFAAoF,GAAA,GAAG,GAAW,WAAA,CAAC,CAAC;gBACxH,IAAM,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACzD,gBAAA,IAAI,aAAa,GAAG,CAAC,CAAC,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;oBACvF,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AAClD,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kIAAkI,CAAC,CAAC;AACxJ,iBAAA;gBACD,MAAM;YACV,KAAK,cAAc,CAAC,aAAa;gBAC7B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oFAAqF,GAAA,GAAG,GAAW,WAAA,CAAC,CAAC;gBACzH,IAAM,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC3D,gBAAA,IAAI,cAAc,GAAG,CAAC,CAAC,EAAE;AACrB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;oBACxF,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;AACpD,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mIAAmI,CAAC,CAAC;AACzJ,iBAAA;gBACD,MAAM;AACV,YAAA;gBACI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wFAAyF,GAAA,IAAM,CAAC,CAAC;gBACnH,eAAe,CAAC,mCAAmC,EAAE,CAAC;AAC7D,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAI,eAAe,CAAC,UAAU,SAAI,IAAI,CAAC,QAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;KAC7F,CAAA;AAED;;;AAGG;IACH,mBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,UAAkB,EAAA;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YACpF,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;AACzD,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE;AACjE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YACpF,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;AACzD,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;KACpE,CAAA;AAED;;;AAGG;IACH,mBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,OAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACrE,QAAA,IAAM,UAAU,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;AAEnD,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAElD,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;KACzD,CAAA;AAED;;;AAGG;IACH,mBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,cAAsB,EAAA;QAC3C,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;YACxF,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;AACjE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,IAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;AACjF,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;YACxF,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;AACjE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC7E,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,iBAAiB,EAAE,EAAE,iBAAiB,CAAC,CAAC;KAC5E,CAAA;AAED;;;AAGG;IACH,mBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,WAA8B,EAAA;AACnD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;AACzE,QAAA,IAAM,cAAc,GAAG,WAAW,CAAC,qBAAqB,EAAE,CAAC;AAC3D,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;QAE1D,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;KACjE,CAAA;AAED;;;AAGG;IACH,mBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,eAAuB,EAAA;QAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACzF,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AACnE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,IAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,EAAE;AACrF,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACzF,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AACnE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,kBAAkB,EAAE,EAAE,kBAAkB,CAAC,CAAC;KAC9E,CAAA;AAED;;;AAGG;IACH,mBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,YAAgC,EAAA;AACtD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAC1E,QAAA,IAAM,eAAe,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;AAC7D,QAAA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;QAE5D,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;KACnE,CAAA;AAED;;;AAGG;IACH,mBAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,cAAsB,EAAA;QACjC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;AAC9E,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,cAAc,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE;AAC3F,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;AAC9E,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,iBAAiB,EAAE,EAAE,cAAc,CAAC,CAAC;KACzE,CAAA;AAED;;;AAGG;IACH,mBAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAA8B,EAAA;AACzC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAC/D,QAAA,IAAM,cAAc,GAAG,WAAW,CAAC,sBAAsB,EAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;KAC7D,CAAA;AAED;;;AAGG;IACH,mBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,kBAA0B,EAAA;QACzC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAClF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,IAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,cAAc,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,cAAc,CAAC,EAAE;AACvG,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAClF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,qBAAqB,EAAE,EAAE,cAAc,CAAC,CAAC;KAC7E,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,UAAmB,kBAA0B,EAAE,eAAsC,EAAA;AACjF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;KACrE,CAAA;AAED;;AAEG;IACH,mBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,GAAW,EAAA;QAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;AACpF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,IAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACxD,IAAI,cAAc,IAAI,uBAAuB,CAAC,yBAAyB,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE;AAC1F,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACzE,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,uBAAuB,EAAE,EAAE,cAAc,CAAC,CAAC;AAC/E,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QAAA,IAKC,KAAA,GAAA,IAAA,CAAA;QAJG,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;AAC/C,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAA;AACtB,YAAA,OAAO,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;AACzC,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,UAAmB,UAAkB,EAAE,cAAsB,EAAA;QACzD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;KAC/E,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;AACI,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;AAClG,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;AACtG,QAAA,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACzB,CAAA;AAED;;;AAGG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,UAAqB,GAAW,EAAE,MAA+B,EAAA;AAC7D,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;KAC7D,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;QACI,IAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;QAClG,IAAM,yBAAyB,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACxE,IAAI,CAAC,yBAAyB,EAAE;;AAE5B,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+GAA+G,CAAC,CAAC;YACnI,IAAM,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YACxF,IAAM,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACpE,IAAI,CAAC,uBAAuB,EAAE;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;AACnF,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACD,YAAA,IAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,cAAc,EAAE,uBAAuB,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAC1G,YAAA,IAAI,aAAa,EAAE;AACf,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gFAAgF,CAAC,CAAC;AACpG,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;AACtG,gBAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACrC,gBAAA,OAAO,aAAa,CAAC;AACxB,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,IAAM,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAgB,CAAC;AAClG,QAAA,IAAI,qBAAqB,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2EAA2E,CAAC,CAAC;YAC/F,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBAC/B,aAAa,EAAE,qBAAqB,CAAC,aAAa;gBAClD,cAAc,EAAE,qBAAqB,CAAC,cAAc;AACvD,aAAA,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AACjB,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;AACnF,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACH,mBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,OAA2B,EAAA;QACxC,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;QAC3F,IAAM,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;AACxF,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5D,YAAA,IAAM,kBAAkB,GAAyB;gBAC7C,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,cAAc,EAAE,OAAO,CAAC,cAAc;aACzC,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAClF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,qBAAqB,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AAC9E,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6DAA6D,CAAC,CAAC;AACnF,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;AACzD,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,mBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,aAAmE,EAAA;AACtF,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAqD,GAAA,WAAW,CAAC,MAAM,GAAiB,iBAAA,CAAC,CAAC;AAE5G,QAAA,OAAO,WAAW,CAAC,MAAM,CAAC,UAAC,UAAU,EAAA;AACjC,YAAA,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE;AACtG,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;YAED,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa,KAAK,UAAU,CAAC,aAAa,EAAE;AACzF,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;YAED,IAAI,aAAa,CAAC,cAAc,IAAI,aAAa,CAAC,cAAc,KAAK,UAAU,CAAC,cAAc,EAAE;AAC5F,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;YAED,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE;AAC1E,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;YAED,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,EAAE;AACnF,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AAChB,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,UAAsB,SAAkB,EAAE,GAAY,EAAA;QAClD,IAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,UAAC,WAAW,EAAA;AAC9D,YAAA,IAAI,GAAG,EAAE;AACL,gBAAA,IAAM,UAAU,GAAG,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACjF,OAAO,GAAG,KAAK,UAAU,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,OAAO,SAAS,KAAK,WAAW,CAAC,QAAQ,CAAC;AAC7C,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;AACjB,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,YAAA,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAA;AAAM,aAAA,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,YAAA,MAAM,eAAe,CAAC,0CAA0C,EAAE,CAAC;AACtE,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACH,mBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,kBAA0B,EAAA;QACzC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAClF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,EAAE;AAC3G,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAClF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,OAAO,YAAY,CAAC,QAAQ,CAAC,IAAI,gBAAgB,EAAE,EAAE,qBAAqB,CAAC,CAAC;KAC/E,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,UAAmB,kBAA0B,EAAE,eAAiC,EAAA;AAC5E,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;KACrE,CAAA;AAED;;;;AAIG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UAAkB,QAAgB,EAAE,WAAqB,EAAA;AACrD,QAAA,IAAM,GAAG,GAAG,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACrE,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE;YACzC,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC3C,YAAA,IAAI,UAAU,EAAE;AACZ,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qGAAqG,CAAC,CAAC;AACzH,gBAAA,OAAO,UAAU,CAAC;AACrB,aAAA;AACJ,SAAA;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,EAAE;;YAER,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,KAAK,oBAAoB,CAAC,YAAY,EAAE;gBACtE,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,gBAAA,IAAI,IAAI,EAAE;AACN,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oFAAoF,CAAC,CAAC;AACxG,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACJ,aAAA;AACD,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;AACjG,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;AAC1F,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;;;AAMG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UAAkB,QAAgB,EAAE,KAAa,EAAE,WAAqB,EAAA;AACpE,QAAA,IAAM,GAAG,GAAG,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;QAErE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC/C,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE;AACzC,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gGAAgG,CAAC,CAAC;AACpH,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;KACJ,CAAA;AAED;;;;AAIG;IACH,mBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAW,EAAA;AAClB,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC3C,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE;AACzC,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC;AAC1G,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,mBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,GAAW,EAAA;AACnB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KAC9F,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AACI,QAAA,OAAA,QAAA,CACO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CACzC,CAAA;KACL,CAAA;AAED;;AAEG;AACG,IAAA,mBAAA,CAAA,SAAA,CAAA,KAAK,GAAX,YAAA;;;;;;;AAEI,oBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA,CAAA;;;AAA9B,wBAAA,EAAA,CAAA,IAAA,EAA8B,CAAC;wBAC/B,IAAI,CAAC,iBAAiB,EAAE,CAAC;;AAGzB,wBAAA,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAC,QAAgB,EAAA;;4BAEpC,IAAI,CAAC,KAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,KAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACpM,gCAAA,KAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC7B,6BAAA;AACL,yBAAC,CAAC,CAAC;AAEH,wBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;;;;;AAChC,KAAA,CAAA;AAED;;;;AAIG;AACG,IAAA,mBAAA,CAAA,SAAA,CAAA,4BAA4B,GAAlC,YAAA;;;;;;;AAEI,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;AACvE,wBAAA,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBAEhC,mBAAmB,GAAyB,EAAE,CAAC;AACrD,wBAAA,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,GAAW,EAAA;;4BAEtC,IAAM,UAAU,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;4BACtD,IAAG,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,uBAAV,UAAU,CAAE,mBAAmB,KAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC,EAAE;gCAC9F,mBAAmB,CAAC,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,6BAAA;AACL,yBAAC,CAAC,CAAC;AACH,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA,CAAA;;AAAtC,wBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;;AAGvC,wBAAA,IAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAI,mBAAmB,CAAC,MAAM,GAAgF,gFAAA,CAAC,CAAC;AACtI,yBAAA;;;;;AACJ,KAAA,CAAA;AAED;;;;;AAKG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,UAAkB,EAAE,WAAmB,EAAE,OAAgB,EAAA;AACnE,QAAA,IAAI,SAAS,GAAM,kBAAkB,CAAC,UAAU,CAAC,GAAI,GAAA,GAAA,kBAAkB,CAAC,WAAW,CAAC,GAAA,uBAAuB,CAAC;AAC5G,QAAA,IAAI,OAAO,EAAE;YACT,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AACzD,YAAA,SAAS,IAAI,UAAA,GAAW,UAAU,GAAA,GAAG,CAAC;AACzC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YAChC,SAAS,IAAI,SAAS,CAAC;AAC1B,SAAA;AAED,QAAA,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;KAC/B,CAAA;AAED;;;AAGG;IACH,mBAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAkB,EAAA;AAC5B,QAAA,IAAM,IAAI,GAAM,kBAAkB,CAAC,UAAU,CAAC,MAAG,CAAC;QAClD,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,IAAI,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC3B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC7B,gBAAA,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAChC,aAAA;YACD,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC5B,gBAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3E,aAAA;AACJ,SAAA;QACD,OAAO,SAAS,CAAC,YAAY,CAAC;KACjC,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;QAAA,IAaC,KAAA,GAAA,IAAA,CAAA;QAZG,IAAM,YAAY,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,IAAI,CAAC,QAAU,CAAC;QAClE,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,UAAU,CAAC,OAAO,CAAC,UAAC,MAAc,EAAA;YAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;AAE7B,gBAAA,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAChC,aAAA;YACD,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;gBACpC,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,gBAAA,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AACnC,aAAA;AACL,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;AAGG;IACH,mBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,UAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAA;AAED;;;AAGG;IACH,mBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,cAAsB,EAAA;AAC1C,QAAA,IAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;AACzB,QAAA,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACtF,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC7B,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;KAC9B,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;;KAEC,CAAA;AAED;;;;AAIG;IACH,mBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,GAAW,EAAA;QACxB,IAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACf,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,EAAE,mBAAmB,CAAC,aAAa,CAAC,EAAE;AACvH,gBAAA,OAAO,GAAG,CAAC;AACd,aAAA;YACD,OAAU,SAAS,CAAC,YAAY,GAAA,GAAA,GAAI,IAAI,CAAC,QAAQ,GAAI,GAAA,GAAA,GAAK,CAAC;AAC9D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KAC9B,CAAA;AAED;;;AAGG;IACH,mBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,WAAmB,EAAA;AAG5B,QAAA,IAAI,OAAO,GAEf,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,gBAF9C,CAE+C;QAElE,OAAO,IAAI,CAAC,gBAAgB,CAAI,kBAAkB,CAAC,SAAS,GAAA,GAAA,GAAI,OAAS,CAAC,CAAC;KAC9E,CAAA;AAED;;;AAGG;IACH,mBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,WAAmB,EAAA;AAGxB,QAAA,IAAI,OAAO,GAEf,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,gBAF9C,CAE+C;QAElE,OAAO,IAAI,CAAC,gBAAgB,CAAI,kBAAkB,CAAC,aAAa,GAAA,GAAA,GAAI,OAAS,CAAC,CAAC;KAClF,CAAA;AAED;;;AAGG;IACH,mBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,WAAmB,EAAA;;AAIxB,QAAA,IAAI,OAAO,GAEf,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,gBAF9C,CAE+C;QAClE,OAAO,IAAI,CAAC,gBAAgB,CAAI,kBAAkB,CAAC,aAAa,GAAA,GAAA,GAAI,OAAS,CAAC,CAAC;KAClF,CAAA;AAED;;AAEG;IACH,mBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,WAAmB,EAAA;QAClC,IAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACzD,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;KACpD,CAAA;AAED;;;;AAIG;IACH,mBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,KAAa,EAAE,KAAa,EAAE,iBAAyB,EAAE,SAAiB,EAAE,OAA2B,EAAA;AACtH,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;QAEnE,IAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;;QAGpD,IAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;;QAGpD,IAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAEpE,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAM,aAAa,GAAkB;gBACjC,UAAU,EAAE,OAAO,CAAC,aAAa;gBACjC,IAAI,EAAE,iBAAiB,CAAC,eAAe;aAC1C,CAAC;AACF,YAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC;AAClG,SAAA;AAAM,aAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACxC,YAAA,IAAM,aAAa,GAAkB;AACjC,gBAAA,UAAU,EAAE,SAAS;gBACrB,IAAI,EAAE,iBAAiB,CAAC,GAAG;aAC9B,CAAC;AACF,YAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC;AAClG,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,mBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,KAAa,EAAA;QAA/B,IAwBC,KAAA,GAAA,IAAA,CAAA;AAvBG,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;;AAElE,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAA,GAAG,EAAA;gBACtB,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;AAC3B,oBAAA,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACxB,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;;AAGD,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;AACrD,SAAA;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpE,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;KACxC,CAAA;AAED;;;AAGG;IACH,mBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,WAAmB,EAAA;AACnC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;;AAEpE,QAAA,IAAI,WAAW,EAAE;YACb,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACpD,IAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qFAAsF,GAAA,WAAa,CAAC,CAAC;YACzH,IAAI,CAAC,iBAAiB,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;AACjE,SAAA;QACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;KAC3B,CAAA;AAED;;;;AAIG;IACH,mBAA6B,CAAA,SAAA,CAAA,6BAAA,GAA7B,UAA8B,eAAgC,EAAA;QAA9D,IAuBC,KAAA,GAAA,IAAA,CAAA;AAtBG,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;;AAE9E,QAAA,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;;YAEvB,IAAI,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtD,OAAO;AACV,aAAA;;YAGD,IAAM,UAAU,GAAG,KAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3D,IAAI,CAAC,UAAU,EAAE;gBACb,OAAO;AACV,aAAA;;AAED,YAAA,IAAM,WAAW,GAAG,oBAAoB,CAAC,0BAA0B,CAAC,KAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AACjG,YAAA,IAAI,WAAW,IAAI,WAAW,CAAC,eAAe,KAAK,eAAe,EAAE;gBAChE,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+FAAgG,GAAA,UAAY,CAAC,CAAC;AAClI,gBAAA,KAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AACtC,aAAA;AACL,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;KACxC,CAAA;AAED,IAAA,mBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,eAA+C,EAAE,aAAsB,EAAA;AACpF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAEjE,QAAA,IAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;KACjF,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,KAAa,EAAE,aAAsB,EAAA;AAClD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;;AAEjE,QAAA,IAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC5F,IAAI,CAAC,mBAAmB,EAAE;AACtB,YAAA,MAAM,gBAAgB,CAAC,8BAA8B,EAAE,CAAC;AAC3D,SAAA;AAED,QAAA,IAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAmC,CAAC;QACnI,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,MAAM,gBAAgB,CAAC,yCAAyC,EAAE,CAAC;AACtE,SAAA;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;;QAG1E,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;YAC9C,IAAM,iBAAiB,GAAW,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACnE,IAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAClE,IAAI,CAAC,eAAe,EAAE;AAClB,gBAAA,MAAM,gBAAgB,CAAC,4BAA4B,EAAE,CAAC;AACzD,aAAA;AACD,YAAA,aAAa,CAAC,SAAS,GAAG,eAAe,CAAC;AAC7C,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,YAAA;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;AACvE,QAAA,IAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;AAChG,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAuB,CAAC;QACrF,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;AAChG,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;IAED,mBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,aAAuB,EAAA;AAC3C,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAEjD,QAAA,IAAI,aAAa,EAAE;AACf,YAAA,OAAO,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;AACrC,SAAA;AAAM,aAAA;YACH,OAAO,CAAC,CAAC,QAAQ,CAAC;AACrB,SAAA;KACJ,CAAA;AAED,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QACI,IAAM,GAAG,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,kBAAkB,CAAC,sBAAwB,CAAC;QACrF,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC7C,CAAA;IAED,mBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,UAAmB,EAAA;;QAExC,IAAM,GAAG,GAAM,SAAS,CAAC,YAAY,GAAI,GAAA,GAAA,kBAAkB,CAAC,sBAAwB,CAAC;AACrF,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE;AACjC,gBAAA,MAAM,gBAAgB,CAAC,gCAAgC,EAAE,CAAC;AAC7D,aAAA;AAAM,iBAAA;;gBAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrD,aAAA;AACJ,SAAA;aAAM,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,wBAAwB,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE;AACzE,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACxB,SAAA;KACJ,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;;QAEI,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;AACpF,QAAA,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC1D,SAAA;;AAGD,QAAA,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACrF,QAAA,IAAI,iBAAiB,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC/D,SAAA;AAED,QAAA,IAAM,mBAAmB,GAAG,iBAAiB,IAAI,iBAAiB,CAAC;AACnE,QAAA,IAAI,mBAAmB,EAAE;YACrB,IAAM,aAAa,GAAG,IAAI,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxE,IAAI,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,kBAAkB,EAAE;AACjE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2GAA2G,CAAC,CAAC;AACjI,gBAAA,OAAO,aAAa,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAClD,aAAA;iBACI,IAAI,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE;AACvD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4FAA4F,CAAC,CAAC;AAClH,gBAAA,OAAO,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC;AACnC,aAAA;AACI,iBAAA;AACD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wJAAwJ,CAAC,CAAC;AACjL,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,UAAyB,eAAuB,EAAE,UAA+B,EAAA;AAC7E,QAAA,IAAM,eAAe,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;QAE3D,IAAI,eAAe,KAAK,eAAe,EAAE;YACrC,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAChD,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAuB,GAAA,UAAU,CAAC,cAAc,GAAY,YAAA,CAAC,CAAC;AAClF,gBAAA,OAAO,eAAe,CAAC;AAC1B,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAmC,GAAA,UAAU,CAAC,cAAc,GAAuE,uEAAA,CAAC,CAAC;AAC1J,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,eAAe,CAAC;KAC1B,CAAA;AAED;;AAEG;AACH,IAAA,mBAAA,CAAA,SAAA,CAAA,yBAAyB,GAAzB,YAAA;QACI,OAAO,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;KAC5E,CAAA;AAED;;;AAGG;IACH,mBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,KAAa,EAAA;QACnC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;KAC5E,CAAA;AAED;;;;AAIG;AACG,IAAA,mBAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,UACI,MAA4B,EAC5B,OAGc,EAAA;;;;;;;AAER,wBAAA,aAAa,GAAG,aAAa,CAAC,mBAAmB,CACnD,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,0CAAE,aAAa,KAAI,EAAE,EACnC,OAAA,MAAM,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,KAAI,EAAE,EACjC,MAAM,CAAC,OAAO,EACd,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,QAAQ,CAClB,CAAC;6BAGE,OAAO,CAAC,MAAM,EAAd,OAAc,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,CAAA;;wBAA7D,UAAU,GAAG,SAAgD,CAAC;;;AAE5D,wBAAA,iBAAiB,GAAG,iBAAiB,CAAC,uBAAuB,CAC/D,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAa,KAAI,EAAE,EACnC,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,KAAI,EAAE,EACjC,MAAM,CAAC,WAAW,EAClB,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EACvB,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAM,KAAA,CAAC,EAChC,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAM,KAAA,CAAC,EACnC,IAAI,CAAC,UAAU,EACf,SAAS;AACT,wBAAA,MAAM,CAAC,SAAiC,EACxC,SAAS;wBACT,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,MAAM,EACd,UAAU,CACb,CAAC;wBAEI,WAAW,GAAG,IAAI,WAAW,CAC/B,SAAS,EACT,aAAa,EACb,iBAAiB,CACpB,CAAC;AACF,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAA;;;;AAC5C,KAAA,CAAA;IACL,OAAC,mBAAA,CAAA;AAAD,CAl6CA,CAAyC,YAAY,CAk6CpD,EAAA;AAEY,IAAA,6BAA6B,GAAG,UAAC,QAAgB,EAAE,MAAc,EAAA;AAC1E,IAAA,IAAM,YAAY,GAA2B;QACzC,aAAa,EAAE,oBAAoB,CAAC,aAAa;QACjD,sBAAsB,EAAE,oBAAoB,CAAC,aAAa;AAC1D,QAAA,sBAAsB,EAAE,KAAK;AAC7B,QAAA,aAAa,EAAE,KAAK;AACpB,QAAA,qBAAqB,EAAE,KAAK;AAC5B,QAAA,yBAAyB,EAAE,IAAI;KAClC,CAAC;IACF,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,YAAY,EAAE,6BAA6B,EAAE,MAAM,CAAC,CAAC;AAClG;;;;"}