{"version": 3, "file": "AsyncMemoryStorage.js", "sources": ["../../src/cache/AsyncMemoryStorage.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Logger } from \"@azure/msal-common\";\r\nimport { BrowserAuthError, BrowserAuthErrorMessage } from \"../error/BrowserAuthError\";\r\nimport { DatabaseStorage } from \"./DatabaseStorage\";\r\nimport { IAsyncStorage } from \"./IAsyncMemoryStorage\";\r\nimport { MemoryStorage } from \"./MemoryStorage\";\r\n\r\n/**\r\n * This class allows MSAL to store artifacts asynchronously using the DatabaseStorage IndexedDB wrapper,\r\n * backed up with the more volatile MemoryStorage object for cases in which IndexedDB may be unavailable.\r\n */\r\nexport class AsyncMemoryStorage<T> implements IAsyncStorage<T> {\r\n    private inMemoryCache: MemoryStorage<T>;\r\n    private indexedDBCache: DatabaseStorage<T>;\r\n    private logger: Logger;\r\n    private storeName: string;\r\n\r\n    constructor(logger: Logger, storeName: string) {\r\n        this.inMemoryCache = new MemoryStorage<T>();\r\n        this.indexedDBCache = new DatabaseStorage<T>();\r\n        this.logger = logger;\r\n        this.storeName = storeName;\r\n    }\r\n\r\n    private handleDatabaseAccessError(error: unknown): void {\r\n        if (error instanceof BrowserAuthError && error.errorCode === BrowserAuthErrorMessage.databaseUnavailable.code) {\r\n            this.logger.error(\"Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.\");\r\n        } else {\r\n            throw error;\r\n        }\r\n    }\r\n    /**\r\n     * Get the item matching the given key. Tries in-memory cache first, then in the asynchronous\r\n     * storage object if item isn't found in-memory.\r\n     * @param key \r\n     */\r\n    async getItem(key: string): Promise<T | null> {\r\n        const item = this.inMemoryCache.getItem(key);\r\n        if(!item) {\r\n            try {\r\n                this.logger.verbose(\"Queried item not found in in-memory cache, now querying persistent storage.\");\r\n                return await this.indexedDBCache.getItem(key);\r\n            } catch (e) {\r\n                this.handleDatabaseAccessError(e);\r\n            }\r\n        }\r\n        return item;\r\n    }\r\n\r\n    /**\r\n     * Sets the item in the in-memory cache and then tries to set it in the asynchronous\r\n     * storage object with the given key.\r\n     * @param key \r\n     * @param value \r\n     */\r\n    async setItem(key: string, value: T): Promise<void> {\r\n        this.inMemoryCache.setItem(key, value);\r\n        try {\r\n            await this.indexedDBCache.setItem(key, value);\r\n        } catch (e) {\r\n            this.handleDatabaseAccessError(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes the item matching the key from the in-memory cache, then tries to remove it from the asynchronous storage object.\r\n     * @param key \r\n     */\r\n    async removeItem(key: string): Promise<void> {\r\n        this.inMemoryCache.removeItem(key);\r\n        try {\r\n            await this.indexedDBCache.removeItem(key);\r\n        } catch (e) {\r\n            this.handleDatabaseAccessError(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get all the keys from the in-memory cache as an iterable array of strings. If no keys are found, query the keys in the \r\n     * asynchronous storage object.\r\n     */\r\n    async getKeys(): Promise<string[]> {\r\n        const cacheKeys = this.inMemoryCache.getKeys();\r\n        if (cacheKeys.length === 0) {\r\n            try {\r\n                this.logger.verbose(\"In-memory cache is empty, now querying persistent storage.\");\r\n                return await this.indexedDBCache.getKeys();\r\n            } catch (e) {\r\n                this.handleDatabaseAccessError(e);\r\n            }\r\n        }\r\n        return cacheKeys;\r\n    }\r\n\r\n    /**\r\n     * Returns true or false if the given key is present in the cache.\r\n     * @param key \r\n     */\r\n    async containsKey(key: string): Promise<boolean> {\r\n        const containsKey = this.inMemoryCache.containsKey(key);\r\n        if(!containsKey) {\r\n            try {\r\n                this.logger.verbose(\"Key not found in in-memory cache, now querying persistent storage.\");\r\n                return await this.indexedDBCache.containsKey(key);\r\n            } catch (e) {\r\n                this.handleDatabaseAccessError(e);\r\n            }\r\n        }\r\n        return containsKey;\r\n    }\r\n\r\n    /**\r\n     * Clears in-memory Map\r\n     */\r\n    clearInMemory(): void {\r\n        // InMemory cache is a Map instance, clear is straightforward\r\n        this.logger.verbose(`Deleting in-memory keystore ${this.storeName}`);\r\n        this.inMemoryCache.clear();\r\n        this.logger.verbose(`In-memory keystore ${this.storeName} deleted`);\r\n    }\r\n\r\n    /**\r\n     * Tries to delete the IndexedDB database\r\n     * @returns\r\n     */\r\n    async clearPersistent(): Promise<boolean> {\r\n        try {\r\n            this.logger.verbose(\"Deleting persistent keystore\");\r\n            const dbDeleted = await this.indexedDBCache.deleteDatabase();\r\n            if (dbDeleted) {\r\n                this.logger.verbose(\"Persistent keystore deleted\");\r\n            }\r\n            \r\n            return dbDeleted;\r\n        } catch (e) {\r\n            this.handleDatabaseAccessError(e);\r\n            return false;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAQH;;;AAGG;AACH,IAAA,kBAAA,kBAAA,YAAA;IAMI,SAAY,kBAAA,CAAA,MAAc,EAAE,SAAiB,EAAA;AACzC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,eAAe,EAAK,CAAC;AAC/C,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC9B;IAEO,kBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAjC,UAAkC,KAAc,EAAA;AAC5C,QAAA,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,CAAC,SAAS,KAAK,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,EAAE;AAC3G,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6IAA6I,CAAC,CAAC;AACpK,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ,CAAA;AACD;;;;AAIG;IACG,kBAAO,CAAA,SAAA,CAAA,OAAA,GAAb,UAAc,GAAW,EAAA;;;;;;wBACf,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;6BAC1C,CAAC,IAAI,EAAL,OAAK,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;AAEA,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6EAA6E,CAAC,CAAC;wBAC5F,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,CAAA;AAA7C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAsC,CAAC,CAAA;;;AAE9C,wBAAA,IAAI,CAAC,yBAAyB,CAAC,GAAC,CAAC,CAAC;;AAG1C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AACf,KAAA,CAAA;AAED;;;;;AAKG;AACG,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAb,UAAc,GAAW,EAAE,KAAQ,EAAA;;;;;;wBAC/B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;;;;wBAEnC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA,CAAA;;AAA7C,wBAAA,EAAA,CAAA,IAAA,EAA6C,CAAC;;;;AAE9C,wBAAA,IAAI,CAAC,yBAAyB,CAAC,GAAC,CAAC,CAAC;;;;;;AAEzC,KAAA,CAAA;AAED;;;AAGG;IACG,kBAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UAAiB,GAAW,EAAA;;;;;;AACxB,wBAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;;;wBAE/B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAA;;AAAzC,wBAAA,EAAA,CAAA,IAAA,EAAyC,CAAC;;;;AAE1C,wBAAA,IAAI,CAAC,yBAAyB,CAAC,GAAC,CAAC,CAAC;;;;;;AAEzC,KAAA,CAAA;AAED;;;AAGG;AACG,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;;;AACU,wBAAA,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC3C,wBAAA,IAAA,EAAA,SAAS,CAAC,MAAM,KAAK,CAAC,CAAA,EAAtB,OAAsB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;AAElB,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4DAA4D,CAAC,CAAC;AAC3E,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAA,CAAA;AAA1C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAmC,CAAC,CAAA;;;AAE3C,wBAAA,IAAI,CAAC,yBAAyB,CAAC,GAAC,CAAC,CAAC;;AAG1C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;;;;AACpB,KAAA,CAAA;AAED;;;AAGG;IACG,kBAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,GAAW,EAAA;;;;;;wBACnB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;6BACrD,CAAC,WAAW,EAAZ,OAAY,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;AAEP,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oEAAoE,CAAC,CAAC;wBACnF,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,CAAA;AAAjD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA0C,CAAC,CAAA;;;AAElD,wBAAA,IAAI,CAAC,yBAAyB,CAAC,GAAC,CAAC,CAAC;;AAG1C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,WAAW,CAAC,CAAA;;;;AACtB,KAAA,CAAA;AAED;;AAEG;AACH,IAAA,kBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;;QAEI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAA+B,IAAI,CAAC,SAAW,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAsB,GAAA,IAAI,CAAC,SAAS,GAAU,UAAA,CAAC,CAAC;KACvE,CAAA;AAED;;;AAGG;AACG,IAAA,kBAAA,CAAA,SAAA,CAAA,eAAe,GAArB,YAAA;;;;;;;AAEQ,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClC,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAA,CAAA;;AAAtD,wBAAA,SAAS,GAAG,EAA0C,CAAA,IAAA,EAAA,CAAA;AAC5D,wBAAA,IAAI,SAAS,EAAE;AACX,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtD,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;;;AAEjB,wBAAA,IAAI,CAAC,yBAAyB,CAAC,GAAC,CAAC,CAAC;AAClC,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;;AAEpB,KAAA,CAAA;IACL,OAAC,kBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}