{"version": 3, "file": "CryptoKeyStore.js", "sources": ["../../src/cache/CryptoKeyStore.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Logger } from \"@azure/msal-common\";\r\nimport { CachedKeyPair } from \"../crypto/CryptoOps\";\r\nimport { AsyncMemoryStorage } from \"./AsyncMemoryStorage\";\r\n\r\nexport enum CryptoKeyStoreNames {\r\n    asymmetricKeys = \"asymmetricKeys\",\r\n    symmetricKeys = \"symmetricKeys\"\r\n}\r\n/**\r\n * MSAL CryptoKeyStore DB Version 2\r\n */\r\nexport class CryptoKeyStore {\r\n    public asymmetricKeys: AsyncMemoryStorage<CachedKeyPair>;\r\n    public symmetricKeys: AsyncMemoryStorage<CryptoKey>;\r\n    public logger: Logger;\r\n\r\n    constructor(logger: Logger){\r\n        this.logger = logger;\r\n        this.asymmetricKeys = new AsyncMemoryStorage<CachedKeyPair>(this.logger, CryptoKeyStoreNames.asymmetricKeys);\r\n        this.symmetricKeys = new AsyncMemoryStorage<CryptoKey>(this.logger, CryptoKeyStoreNames.symmetricKeys);\r\n    }\r\n\r\n    async clear(): Promise<boolean> {\r\n        // Delete in-memory keystores\r\n        this.asymmetricKeys.clearInMemory();\r\n\t    this.symmetricKeys.clearInMemory();\r\n\t\t\r\n        /**\r\n         * There is only one database, so calling clearPersistent on asymmetric keystore takes care of\r\n         * every persistent keystore\r\n         */\r\n        try {\r\n            await this.asymmetricKeys.clearPersistent();\r\n            return true;\r\n        } catch (e) {\r\n            if (e instanceof Error) {\r\n                this.logger.error(`Clearing keystore failed with error: ${e.message}`);\r\n            } else {\r\n                this.logger.error(\"Clearing keystore failed with unknown error\");\r\n            }\r\n            \r\n            return false;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;IAMS,oBAGX;AAHD,CAAA,UAAY,mBAAmB,EAAA;AAC3B,IAAA,mBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AACnC,CAAC,EAHW,mBAAmB,KAAnB,mBAAmB,GAG9B,EAAA,CAAA,CAAA,CAAA;AACD;;AAEG;AACH,IAAA,cAAA,kBAAA,YAAA;AAKI,IAAA,SAAA,cAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,kBAAkB,CAAgB,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC,cAAc,CAAC,CAAC;AAC7G,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAkB,CAAY,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC,aAAa,CAAC,CAAC;KAC1G;AAEK,IAAA,cAAA,CAAA,SAAA,CAAA,KAAK,GAAX,YAAA;;;;;;;AAEI,wBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;AACvC,wBAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;;;;AAO5B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAA,CAAA;;AAA3C,wBAAA,EAAA,CAAA,IAAA,EAA2C,CAAC;AAC5C,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;wBAEZ,IAAI,GAAC,YAAY,KAAK,EAAE;4BACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAAwC,GAAC,CAAC,OAAS,CAAC,CAAC;AAC1E,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;AACpE,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;;AAEpB,KAAA,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA;;;;"}