/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __awaiter, __generator } from '../_virtual/_tslib.js';
import { AsyncMemoryStorage } from './AsyncMemoryStorage.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var CryptoKeyStoreNames;
(function (CryptoKeyStoreNames) {
    CryptoKeyStoreNames["asymmetricKeys"] = "asymmetricKeys";
    CryptoKeyStoreNames["symmetricKeys"] = "symmetricKeys";
})(CryptoKeyStoreNames || (CryptoKeyStoreNames = {}));
/**
 * MSAL CryptoKeyStore DB Version 2
 */
var CryptoKeyStore = /** @class */ (function () {
    function CryptoKeyStore(logger) {
        this.logger = logger;
        this.asymmetricKeys = new AsyncMemoryStorage(this.logger, CryptoKeyStoreNames.asymmetricKeys);
        this.symmetricKeys = new AsyncMemoryStorage(this.logger, CryptoKeyStoreNames.symmetricKeys);
    }
    CryptoKeyStore.prototype.clear = function () {
        return __awaiter(this, void 0, void 0, function () {
            var e_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Delete in-memory keystores
                        this.asymmetricKeys.clearInMemory();
                        this.symmetricKeys.clearInMemory();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.asymmetricKeys.clearPersistent()];
                    case 2:
                        _a.sent();
                        return [2 /*return*/, true];
                    case 3:
                        e_1 = _a.sent();
                        if (e_1 instanceof Error) {
                            this.logger.error("Clearing keystore failed with error: " + e_1.message);
                        }
                        else {
                            this.logger.error("Clearing keystore failed with unknown error");
                        }
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return CryptoKeyStore;
}());

export { CryptoKeyStore, CryptoKeyStoreNames };
//# sourceMappingURL=CryptoKeyStore.js.map
