/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { __awaiter, __generator, __assign } from '../_virtual/_tslib.js';
import { CryptoOps } from '../crypto/CryptoOps.js';
import { PerformanceEvents, InteractionRequiredAuthError, PromptValue, Constants, Logger, StubPerformanceClient, DEFAULT_CRYPTO_IMPLEMENTATION, AuthError, ClientAuthError } from '@azure/msal-common';
import { BrowserCacheManager, DEFAULT_BROWSER_CACHE_MANAGER } from '../cache/BrowserCacheManager.js';
import { buildConfiguration } from '../config/Configuration.js';
import { InteractionType, ApiId, BrowserCacheLocation, TemporaryCacheKeys, CacheLookupPolicy } from '../utils/BrowserConstants.js';
import { BrowserUtils } from '../utils/BrowserUtils.js';
import { name, version } from '../packageMetadata.js';
import { EventType } from '../event/EventType.js';
import { BrowserConfigurationAuthError } from '../error/BrowserConfigurationAuthError.js';
import { EventHandler } from '../event/EventHandler.js';
import { PopupClient } from '../interaction_client/PopupClient.js';
import { RedirectClient } from '../interaction_client/RedirectClient.js';
import { SilentIframeClient } from '../interaction_client/SilentIframeClient.js';
import { SilentRefreshClient } from '../interaction_client/SilentRefreshClient.js';
import { TokenCache } from '../cache/TokenCache.js';
import { NativeInteractionClient } from '../interaction_client/NativeInteractionClient.js';
import { NativeMessageHandler } from '../broker/nativeBroker/NativeMessageHandler.js';
import { NativeAuthError } from '../error/NativeAuthError.js';
import { SilentCacheClient } from '../interaction_client/SilentCacheClient.js';
import { SilentAuthCodeClient } from '../interaction_client/SilentAuthCodeClient.js';
import { BrowserAuthError } from '../error/BrowserAuthError.js';
import { BrowserPerformanceClient } from '../telemetry/BrowserPerformanceClient.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var ClientApplication = /** @class */ (function () {
    /**
     * @constructor
     * Constructor for the PublicClientApplication used to instantiate the PublicClientApplication object
     *
     * Important attributes in the Configuration object for auth are:
     * - clientID: the application ID of your application. You can obtain one by registering your application with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview
     * - authority: the authority URL for your application.
     * - redirect_uri: the uri of your application registered in the portal.
     *
     * In Azure AD, authority is a URL indicating the Azure active directory that MSAL uses to obtain tokens.
     * It is of the form https://login.microsoftonline.com/{Enter_the_Tenant_Info_Here}
     * If your application supports Accounts in one organizational directory, replace "Enter_the_Tenant_Info_Here" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).
     * If your application supports Accounts in any organizational directory, replace "Enter_the_Tenant_Info_Here" value with organizations.
     * If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace "Enter_the_Tenant_Info_Here" value with common.
     * To restrict support to Personal Microsoft accounts only, replace "Enter_the_Tenant_Info_Here" value with consumers.
     *
     * In Azure B2C, authority is of the form https://{instance}/tfp/{tenant}/{policyName}/
     * Full B2C functionality will be available in this library in future versions.
     *
     * @param configuration Object for the MSAL PublicClientApplication instance
     */
    function ClientApplication(configuration) {
        /*
         * If loaded in an environment where window is not available,
         * set internal flag to false so that further requests fail.
         * This is to support server-side rendering environments.
         */
        this.isBrowserEnvironment = typeof window !== "undefined";
        // Set the configuration.
        this.config = buildConfiguration(configuration, this.isBrowserEnvironment);
        this.initialized = false;
        // Initialize logger
        this.logger = new Logger(this.config.system.loggerOptions, name, version);
        // Initialize the network module class.
        this.networkClient = this.config.system.networkClient;
        // Initialize the navigation client class.
        this.navigationClient = this.config.system.navigationClient;
        // Initialize redirectResponse Map
        this.redirectResponse = new Map();
        // Initial hybrid spa map
        this.hybridAuthCodeResponses = new Map();
        // Initialize performance client
        this.performanceClient = this.isBrowserEnvironment ?
            new BrowserPerformanceClient(this.config.auth.clientId, this.config.auth.authority, this.logger, name, version, this.config.telemetry.application, this.config.system.cryptoOptions) :
            new StubPerformanceClient(this.config.auth.clientId, this.config.auth.authority, this.logger, name, version, this.config.telemetry.application);
        // Initialize the crypto class.
        this.browserCrypto = this.isBrowserEnvironment ? new CryptoOps(this.logger, this.performanceClient, this.config.system.cryptoOptions) : DEFAULT_CRYPTO_IMPLEMENTATION;
        this.eventHandler = new EventHandler(this.logger, this.browserCrypto);
        // Initialize the browser storage class.
        this.browserStorage = this.isBrowserEnvironment ?
            new BrowserCacheManager(this.config.auth.clientId, this.config.cache, this.browserCrypto, this.logger) :
            DEFAULT_BROWSER_CACHE_MANAGER(this.config.auth.clientId, this.logger);
        // initialize in memory storage for native flows
        var nativeCacheOptions = {
            cacheLocation: BrowserCacheLocation.MemoryStorage,
            temporaryCacheLocation: BrowserCacheLocation.MemoryStorage,
            storeAuthStateInCookie: false,
            secureCookies: false,
            cacheMigrationEnabled: false,
            claimsBasedCachingEnabled: true
        };
        this.nativeInternalStorage = new BrowserCacheManager(this.config.auth.clientId, nativeCacheOptions, this.browserCrypto, this.logger);
        // Initialize the token cache
        this.tokenCache = new TokenCache(this.config, this.browserStorage, this.logger, this.browserCrypto);
        // Register listener functions
        this.trackPageVisibilityWithMeasurement = this.trackPageVisibilityWithMeasurement.bind(this);
    }
    /**
     * Initializer function to perform async startup tasks such as connecting to WAM extension
     */
    ClientApplication.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var allowNativeBroker, initMeasurement, _a, e_1, claimsTokensRemovalMeasurement;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.logger.trace("initialize called");
                        if (this.initialized) {
                            this.logger.info("initialize has already been called, exiting early.");
                            return [2 /*return*/];
                        }
                        allowNativeBroker = this.config.system.allowNativeBroker;
                        initMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.InitializeClientApplication);
                        this.eventHandler.emitEvent(EventType.INITIALIZE_START);
                        if (!allowNativeBroker) return [3 /*break*/, 4];
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 3, , 4]);
                        _a = this;
                        return [4 /*yield*/, NativeMessageHandler.createProvider(this.logger, this.config.system.nativeBrokerHandshakeTimeout, this.performanceClient)];
                    case 2:
                        _a.nativeExtensionProvider = _b.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        e_1 = _b.sent();
                        this.logger.verbose(e_1);
                        return [3 /*break*/, 4];
                    case 4:
                        if (!!this.config.cache.claimsBasedCachingEnabled) return [3 /*break*/, 6];
                        this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims");
                        claimsTokensRemovalMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.ClearTokensAndKeysWithClaims);
                        return [4 /*yield*/, this.browserStorage.clearTokensAndKeysWithClaims()];
                    case 5:
                        _b.sent();
                        claimsTokensRemovalMeasurement.endMeasurement({ success: true });
                        _b.label = 6;
                    case 6:
                        this.initialized = true;
                        this.eventHandler.emitEvent(EventType.INITIALIZE_END);
                        initMeasurement.endMeasurement({ allowNativeBroker: allowNativeBroker, success: true });
                        return [2 /*return*/];
                }
            });
        });
    };
    // #region Redirect Flow
    /**
     * Event handler function which allows users to fire events after the PublicClientApplication object
     * has loaded during redirect flows. This should be invoked on all page loads involved in redirect
     * auth flows.
     * @param hash Hash to process. Defaults to the current value of window.location.hash. Only needs to be provided explicitly if the response to be handled is not contained in the current value.
     * @returns Token response or null. If the return value is null, then no auth redirect was detected.
     */
    ClientApplication.prototype.handleRedirectPromise = function (hash) {
        return __awaiter(this, void 0, void 0, function () {
            var loggedInAccounts, redirectResponseKey, response, request, redirectResponse, nativeClient, correlationId, redirectClient;
            var _this = this;
            return __generator(this, function (_a) {
                this.logger.verbose("handleRedirectPromise called");
                // Block token acquisition before initialize has been called if native brokering is enabled
                BrowserUtils.blockNativeBrokerCalledBeforeInitialized(this.config.system.allowNativeBroker, this.initialized);
                loggedInAccounts = this.getAllAccounts();
                if (this.isBrowserEnvironment) {
                    redirectResponseKey = hash || Constants.EMPTY_STRING;
                    response = this.redirectResponse.get(redirectResponseKey);
                    if (typeof response === "undefined") {
                        this.eventHandler.emitEvent(EventType.HANDLE_REDIRECT_START, InteractionType.Redirect);
                        this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise");
                        request = this.browserStorage.getCachedNativeRequest();
                        redirectResponse = void 0;
                        if (request && NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider) && this.nativeExtensionProvider && !hash) {
                            this.logger.trace("handleRedirectPromise - acquiring token from native platform");
                            nativeClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.handleRedirectPromise, this.performanceClient, this.nativeExtensionProvider, request.accountId, this.nativeInternalStorage, request.correlationId);
                            redirectResponse = nativeClient.handleRedirectPromise();
                        }
                        else {
                            this.logger.trace("handleRedirectPromise - acquiring token from web flow");
                            correlationId = this.browserStorage.getTemporaryCache(TemporaryCacheKeys.CORRELATION_ID, true) || Constants.EMPTY_STRING;
                            redirectClient = this.createRedirectClient(correlationId);
                            redirectResponse = redirectClient.handleRedirectPromise(hash);
                        }
                        response = redirectResponse.then(function (result) {
                            if (result) {
                                // Emit login event if number of accounts change
                                var isLoggingIn = loggedInAccounts.length < _this.getAllAccounts().length;
                                if (isLoggingIn) {
                                    _this.eventHandler.emitEvent(EventType.LOGIN_SUCCESS, InteractionType.Redirect, result);
                                    _this.logger.verbose("handleRedirectResponse returned result, login success");
                                }
                                else {
                                    _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_SUCCESS, InteractionType.Redirect, result);
                                    _this.logger.verbose("handleRedirectResponse returned result, acquire token success");
                                }
                            }
                            _this.eventHandler.emitEvent(EventType.HANDLE_REDIRECT_END, InteractionType.Redirect);
                            return result;
                        }).catch(function (e) {
                            // Emit login event if there is an account
                            if (loggedInAccounts.length > 0) {
                                _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Redirect, null, e);
                            }
                            else {
                                _this.eventHandler.emitEvent(EventType.LOGIN_FAILURE, InteractionType.Redirect, null, e);
                            }
                            _this.eventHandler.emitEvent(EventType.HANDLE_REDIRECT_END, InteractionType.Redirect);
                            throw e;
                        });
                        this.redirectResponse.set(redirectResponseKey, response);
                    }
                    else {
                        this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call");
                    }
                    return [2 /*return*/, response];
                }
                this.logger.verbose("handleRedirectPromise returns null, not browser environment");
                return [2 /*return*/, null];
            });
        });
    };
    /**
     * Use when you want to obtain an access_token for your API by redirecting the user's browser window to the authorization endpoint. This function redirects
     * the page, so any code that follows this function will not execute.
     *
     * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current
     * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.
     *
     * @param request
     */
    ClientApplication.prototype.acquireTokenRedirect = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var correlationId, isLoggedIn, result, nativeClient, redirectClient;
            var _this = this;
            return __generator(this, function (_a) {
                correlationId = this.getRequestCorrelationId(request);
                this.logger.verbose("acquireTokenRedirect called", correlationId);
                this.preflightBrowserEnvironmentCheck(InteractionType.Redirect);
                isLoggedIn = this.getAllAccounts().length > 0;
                if (isLoggedIn) {
                    this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_START, InteractionType.Redirect, request);
                }
                else {
                    this.eventHandler.emitEvent(EventType.LOGIN_START, InteractionType.Redirect, request);
                }
                if (this.nativeExtensionProvider && this.canUseNative(request)) {
                    nativeClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.acquireTokenRedirect, this.performanceClient, this.nativeExtensionProvider, this.getNativeAccountId(request), this.nativeInternalStorage, request.correlationId);
                    result = nativeClient.acquireTokenRedirect(request).catch(function (e) {
                        if (e instanceof NativeAuthError && e.isFatal()) {
                            _this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt
                            var redirectClient = _this.createRedirectClient(request.correlationId);
                            return redirectClient.acquireToken(request);
                        }
                        else if (e instanceof InteractionRequiredAuthError) {
                            _this.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow");
                            var redirectClient = _this.createRedirectClient(request.correlationId);
                            return redirectClient.acquireToken(request);
                        }
                        _this.browserStorage.setInteractionInProgress(false);
                        throw e;
                    });
                }
                else {
                    redirectClient = this.createRedirectClient(request.correlationId);
                    result = redirectClient.acquireToken(request);
                }
                return [2 /*return*/, result.catch(function (e) {
                        // If logged in, emit acquire token events
                        if (isLoggedIn) {
                            _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Redirect, null, e);
                        }
                        else {
                            _this.eventHandler.emitEvent(EventType.LOGIN_FAILURE, InteractionType.Redirect, null, e);
                        }
                        throw e;
                    })];
            });
        });
    };
    // #endregion
    // #region Popup Flow
    /**
     * Use when you want to obtain an access_token for your API via opening a popup window in the user's browser
     *
     * @param request
     *
     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
     */
    ClientApplication.prototype.acquireTokenPopup = function (request) {
        var _this = this;
        var correlationId = this.getRequestCorrelationId(request);
        var atPopupMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenPopup, correlationId);
        try {
            this.logger.verbose("acquireTokenPopup called", correlationId);
            this.preflightBrowserEnvironmentCheck(InteractionType.Popup);
        }
        catch (e) {
            // Since this function is syncronous we need to reject
            return Promise.reject(e);
        }
        // If logged in, emit acquire token events
        var loggedInAccounts = this.getAllAccounts();
        if (loggedInAccounts.length > 0) {
            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_START, InteractionType.Popup, request);
        }
        else {
            this.eventHandler.emitEvent(EventType.LOGIN_START, InteractionType.Popup, request);
        }
        var result;
        if (this.canUseNative(request)) {
            result = this.acquireTokenNative(request, ApiId.acquireTokenPopup).then(function (response) {
                _this.browserStorage.setInteractionInProgress(false);
                atPopupMeasurement.endMeasurement({
                    success: true,
                    isNativeBroker: true,
                    requestId: response.requestId
                });
                return response;
            }).catch(function (e) {
                if (e instanceof NativeAuthError && e.isFatal()) {
                    _this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt
                    var popupClient = _this.createPopupClient(request.correlationId);
                    return popupClient.acquireToken(request);
                }
                else if (e instanceof InteractionRequiredAuthError) {
                    _this.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow");
                    var popupClient = _this.createPopupClient(request.correlationId);
                    return popupClient.acquireToken(request);
                }
                _this.browserStorage.setInteractionInProgress(false);
                throw e;
            });
        }
        else {
            var popupClient = this.createPopupClient(request.correlationId);
            result = popupClient.acquireToken(request);
        }
        return result.then(function (result) {
            /*
             *  If logged in, emit acquire token events
             */
            var isLoggingIn = loggedInAccounts.length < _this.getAllAccounts().length;
            if (isLoggingIn) {
                _this.eventHandler.emitEvent(EventType.LOGIN_SUCCESS, InteractionType.Popup, result);
            }
            else {
                _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_SUCCESS, InteractionType.Popup, result);
            }
            atPopupMeasurement.addStaticFields({
                accessTokenSize: result.accessToken.length,
                idTokenSize: result.idToken.length
            });
            atPopupMeasurement.endMeasurement({
                success: true,
                requestId: result.requestId
            });
            return result;
        }).catch(function (e) {
            if (loggedInAccounts.length > 0) {
                _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Popup, null, e);
            }
            else {
                _this.eventHandler.emitEvent(EventType.LOGIN_FAILURE, InteractionType.Popup, null, e);
            }
            atPopupMeasurement.endMeasurement({
                errorCode: e.errorCode,
                subErrorCode: e.subError,
                success: false
            });
            // Since this function is syncronous we need to reject
            return Promise.reject(e);
        });
    };
    ClientApplication.prototype.trackPageVisibilityWithMeasurement = function () {
        var measurement = this.ssoSilentMeasurement || this.acquireTokenByCodeAsyncMeasurement;
        if (!measurement) {
            return;
        }
        this.logger.info("Perf: Visibility change detected in ", measurement.event.name);
        measurement.increment({
            visibilityChangeCount: 1,
        });
    };
    // #endregion
    // #region Silent Flow
    /**
     * This function uses a hidden iframe to fetch an authorization code from the eSTS. There are cases where this may not work:
     * - Any browser using a form of Intelligent Tracking Prevention
     * - If there is not an established session with the service
     *
     * In these cases, the request must be done inside a popup or full frame redirect.
     *
     * For the cases where interaction is required, you cannot send a request with prompt=none.
     *
     * If your refresh token has expired, you can use this function to fetch a new set of tokens silently as long as
     * you session on the server still exists.
     * @param request {@link SsoSilentRequest}
     *
     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
     */
    ClientApplication.prototype.ssoSilent = function (request) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var correlationId, validRequest, result, silentIframeClient;
            var _this = this;
            return __generator(this, function (_b) {
                correlationId = this.getRequestCorrelationId(request);
                validRequest = __assign(__assign({}, request), { 
                    // will be PromptValue.NONE or PromptValue.NO_SESSION
                    prompt: request.prompt, correlationId: correlationId });
                this.preflightBrowserEnvironmentCheck(InteractionType.Silent);
                this.ssoSilentMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SsoSilent, correlationId);
                (_a = this.ssoSilentMeasurement) === null || _a === void 0 ? void 0 : _a.increment({
                    visibilityChangeCount: 0
                });
                document.addEventListener("visibilitychange", this.trackPageVisibilityWithMeasurement);
                this.logger.verbose("ssoSilent called", correlationId);
                this.eventHandler.emitEvent(EventType.SSO_SILENT_START, InteractionType.Silent, validRequest);
                if (this.canUseNative(validRequest)) {
                    result = this.acquireTokenNative(validRequest, ApiId.ssoSilent).catch(function (e) {
                        // If native token acquisition fails for availability reasons fallback to standard flow
                        if (e instanceof NativeAuthError && e.isFatal()) {
                            _this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt
                            var silentIframeClient = _this.createSilentIframeClient(validRequest.correlationId);
                            return silentIframeClient.acquireToken(validRequest);
                        }
                        throw e;
                    });
                }
                else {
                    silentIframeClient = this.createSilentIframeClient(validRequest.correlationId);
                    result = silentIframeClient.acquireToken(validRequest);
                }
                return [2 /*return*/, result.then(function (response) {
                        var _a, _b;
                        _this.eventHandler.emitEvent(EventType.SSO_SILENT_SUCCESS, InteractionType.Silent, response);
                        (_a = _this.ssoSilentMeasurement) === null || _a === void 0 ? void 0 : _a.addStaticFields({
                            accessTokenSize: response.accessToken.length,
                            idTokenSize: response.idToken.length
                        });
                        (_b = _this.ssoSilentMeasurement) === null || _b === void 0 ? void 0 : _b.endMeasurement({
                            success: true,
                            isNativeBroker: response.fromNativeBroker,
                            requestId: response.requestId
                        });
                        return response;
                    }).catch(function (e) {
                        var _a;
                        _this.eventHandler.emitEvent(EventType.SSO_SILENT_FAILURE, InteractionType.Silent, null, e);
                        (_a = _this.ssoSilentMeasurement) === null || _a === void 0 ? void 0 : _a.endMeasurement({
                            errorCode: e.errorCode,
                            subErrorCode: e.subError,
                            success: false
                        });
                        throw e;
                    }).finally(function () {
                        document.removeEventListener("visibilitychange", _this.trackPageVisibilityWithMeasurement);
                    })];
            });
        });
    };
    /**
     * This function redeems an authorization code (passed as code) from the eSTS token endpoint.
     * This authorization code should be acquired server-side using a confidential client to acquire a spa_code.
     * This API is not indended for normal authorization code acquisition and redemption.
     *
     * Redemption of this authorization code will not require PKCE, as it was acquired by a confidential client.
     *
     * @param request {@link AuthorizationCodeRequest}
     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.
     */
    ClientApplication.prototype.acquireTokenByCode = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var correlationId, atbcMeasurement, hybridAuthCode_1, response;
            var _this = this;
            return __generator(this, function (_a) {
                correlationId = this.getRequestCorrelationId(request);
                this.preflightBrowserEnvironmentCheck(InteractionType.Silent);
                this.logger.trace("acquireTokenByCode called", correlationId);
                this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_START, InteractionType.Silent, request);
                atbcMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenByCode, request.correlationId);
                try {
                    if (request.code && request.nativeAccountId) {
                        // Throw error in case server returns both spa_code and spa_accountid in exchange for auth code.
                        throw BrowserAuthError.createSpaCodeAndNativeAccountIdPresentError();
                    }
                    else if (request.code) {
                        hybridAuthCode_1 = request.code;
                        response = this.hybridAuthCodeResponses.get(hybridAuthCode_1);
                        if (!response) {
                            this.logger.verbose("Initiating new acquireTokenByCode request", correlationId);
                            response = this.acquireTokenByCodeAsync(__assign(__assign({}, request), { correlationId: correlationId }))
                                .then(function (result) {
                                _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_SUCCESS, InteractionType.Silent, result);
                                _this.hybridAuthCodeResponses.delete(hybridAuthCode_1);
                                atbcMeasurement.addStaticFields({
                                    accessTokenSize: result.accessToken.length,
                                    idTokenSize: result.idToken.length
                                });
                                atbcMeasurement.endMeasurement({
                                    success: true,
                                    isNativeBroker: result.fromNativeBroker,
                                    requestId: result.requestId
                                });
                                return result;
                            })
                                .catch(function (error) {
                                _this.hybridAuthCodeResponses.delete(hybridAuthCode_1);
                                _this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_FAILURE, InteractionType.Silent, null, error);
                                atbcMeasurement.endMeasurement({
                                    errorCode: error.errorCode,
                                    subErrorCode: error.subError,
                                    success: false
                                });
                                throw error;
                            });
                            this.hybridAuthCodeResponses.set(hybridAuthCode_1, response);
                        }
                        else {
                            this.logger.verbose("Existing acquireTokenByCode request found", request.correlationId);
                            atbcMeasurement.discardMeasurement();
                        }
                        return [2 /*return*/, response];
                    }
                    else if (request.nativeAccountId) {
                        if (this.canUseNative(request, request.nativeAccountId)) {
                            return [2 /*return*/, this.acquireTokenNative(request, ApiId.acquireTokenByCode, request.nativeAccountId).catch(function (e) {
                                    // If native token acquisition fails for availability reasons fallback to standard flow
                                    if (e instanceof NativeAuthError && e.isFatal()) {
                                        _this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt
                                    }
                                    throw e;
                                })];
                        }
                        else {
                            throw BrowserAuthError.createUnableToAcquireTokenFromNativePlatformError();
                        }
                    }
                    else {
                        throw BrowserAuthError.createAuthCodeOrNativeAccountIdRequiredError();
                    }
                }
                catch (e) {
                    this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_FAILURE, InteractionType.Silent, null, e);
                    atbcMeasurement.endMeasurement({
                        errorCode: e instanceof AuthError && e.errorCode || undefined,
                        subErrorCode: e instanceof AuthError && e.subError || undefined,
                        success: false
                    });
                    throw e;
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * Creates a SilentAuthCodeClient to redeem an authorization code.
     * @param request
     * @returns Result of the operation to redeem the authorization code
     */
    ClientApplication.prototype.acquireTokenByCodeAsync = function (request) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var silentAuthCodeClient, silentTokenResult;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.logger.trace("acquireTokenByCodeAsync called", request.correlationId);
                        this.acquireTokenByCodeAsyncMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenByCodeAsync, request.correlationId);
                        (_a = this.acquireTokenByCodeAsyncMeasurement) === null || _a === void 0 ? void 0 : _a.increment({
                            visibilityChangeCount: 0
                        });
                        document.addEventListener("visibilitychange", this.trackPageVisibilityWithMeasurement);
                        silentAuthCodeClient = this.createSilentAuthCodeClient(request.correlationId);
                        return [4 /*yield*/, silentAuthCodeClient.acquireToken(request).then(function (response) {
                                var _a;
                                (_a = _this.acquireTokenByCodeAsyncMeasurement) === null || _a === void 0 ? void 0 : _a.endMeasurement({
                                    success: true,
                                    fromCache: response.fromCache,
                                    isNativeBroker: response.fromNativeBroker,
                                    requestId: response.requestId
                                });
                                return response;
                            }).catch(function (tokenRenewalError) {
                                var _a;
                                (_a = _this.acquireTokenByCodeAsyncMeasurement) === null || _a === void 0 ? void 0 : _a.endMeasurement({
                                    errorCode: tokenRenewalError.errorCode,
                                    subErrorCode: tokenRenewalError.subError,
                                    success: false
                                });
                                throw tokenRenewalError;
                            }).finally(function () {
                                document.removeEventListener("visibilitychange", _this.trackPageVisibilityWithMeasurement);
                            })];
                    case 1:
                        silentTokenResult = _b.sent();
                        return [2 /*return*/, silentTokenResult];
                }
            });
        });
    };
    /**
     * Attempt to acquire an access token from the cache
     * @param silentCacheClient SilentCacheClient
     * @param commonRequest CommonSilentFlowRequest
     * @param silentRequest SilentRequest
     * @returns A promise that, when resolved, returns the access token
     */
    ClientApplication.prototype.acquireTokenFromCache = function (silentCacheClient, commonRequest, silentRequest) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenFromCache, commonRequest.correlationId);
                switch (silentRequest.cacheLookupPolicy) {
                    case CacheLookupPolicy.Default:
                    case CacheLookupPolicy.AccessToken:
                    case CacheLookupPolicy.AccessTokenAndRefreshToken:
                        return [2 /*return*/, silentCacheClient.acquireToken(commonRequest)];
                    default:
                        throw ClientAuthError.createRefreshRequiredError();
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * Attempt to acquire an access token via a refresh token
     * @param commonRequest CommonSilentFlowRequest
     * @param silentRequest SilentRequest
     * @returns A promise that, when resolved, returns the access token
     */
    ClientApplication.prototype.acquireTokenByRefreshToken = function (commonRequest, silentRequest) {
        return __awaiter(this, void 0, void 0, function () {
            var silentRefreshClient;
            return __generator(this, function (_a) {
                this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenByRefreshToken, commonRequest.correlationId);
                switch (silentRequest.cacheLookupPolicy) {
                    case CacheLookupPolicy.Default:
                    case CacheLookupPolicy.AccessTokenAndRefreshToken:
                    case CacheLookupPolicy.RefreshToken:
                    case CacheLookupPolicy.RefreshTokenAndNetwork:
                        silentRefreshClient = this.createSilentRefreshClient(commonRequest.correlationId);
                        this.performanceClient.setPreQueueTime(PerformanceEvents.SilentRefreshClientAcquireToken, commonRequest.correlationId);
                        return [2 /*return*/, silentRefreshClient.acquireToken(commonRequest)];
                    default:
                        throw ClientAuthError.createRefreshRequiredError();
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * Attempt to acquire an access token via an iframe
     * @param request CommonSilentFlowRequest
     * @returns A promise that, when resolved, returns the access token
     */
    ClientApplication.prototype.acquireTokenBySilentIframe = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var silentIframeClient;
            return __generator(this, function (_a) {
                this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenBySilentIframe, request.correlationId);
                silentIframeClient = this.createSilentIframeClient(request.correlationId);
                this.performanceClient.setPreQueueTime(PerformanceEvents.SilentIframeClientAcquireToken, request.correlationId);
                return [2 /*return*/, silentIframeClient.acquireToken(request)];
            });
        });
    };
    // #endregion
    // #region Logout
    /**
     * Deprecated logout function. Use logoutRedirect or logoutPopup instead
     * @param logoutRequest
     * @deprecated
     */
    ClientApplication.prototype.logout = function (logoutRequest) {
        return __awaiter(this, void 0, void 0, function () {
            var correlationId;
            return __generator(this, function (_a) {
                correlationId = this.getRequestCorrelationId(logoutRequest);
                this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.", correlationId);
                return [2 /*return*/, this.logoutRedirect(__assign({ correlationId: correlationId }, logoutRequest))];
            });
        });
    };
    /**
     * Use to log out the current user, and redirect the user to the postLogoutRedirectUri.
     * Default behaviour is to redirect the user to `window.location.href`.
     * @param logoutRequest
     */
    ClientApplication.prototype.logoutRedirect = function (logoutRequest) {
        return __awaiter(this, void 0, void 0, function () {
            var correlationId, redirectClient;
            return __generator(this, function (_a) {
                correlationId = this.getRequestCorrelationId(logoutRequest);
                this.preflightBrowserEnvironmentCheck(InteractionType.Redirect);
                redirectClient = this.createRedirectClient(correlationId);
                return [2 /*return*/, redirectClient.logout(logoutRequest)];
            });
        });
    };
    /**
     * Clears local cache for the current user then opens a popup window prompting the user to sign-out of the server
     * @param logoutRequest
     */
    ClientApplication.prototype.logoutPopup = function (logoutRequest) {
        try {
            var correlationId = this.getRequestCorrelationId(logoutRequest);
            this.preflightBrowserEnvironmentCheck(InteractionType.Popup);
            var popupClient = this.createPopupClient(correlationId);
            return popupClient.logout(logoutRequest);
        }
        catch (e) {
            // Since this function is syncronous we need to reject
            return Promise.reject(e);
        }
    };
    // #endregion
    // #region Account APIs
    /**
     * Returns all accounts that MSAL currently has data for.
     * (the account object is created at the time of successful login)
     * or empty array when no accounts are found
     * @returns Array of account objects in cache
     */
    ClientApplication.prototype.getAllAccounts = function () {
        this.logger.verbose("getAllAccounts called");
        return this.isBrowserEnvironment ? this.browserStorage.getAllAccounts() : [];
    };
    /**
     * Returns the signed in account matching username.
     * (the account object is created at the time of successful login)
     * or null when no matching account is found.
     * This API is provided for convenience but getAccountById should be used for best reliability
     * @param username
     * @returns The account object stored in MSAL
     */
    ClientApplication.prototype.getAccountByUsername = function (username) {
        this.logger.trace("getAccountByUsername called");
        if (!username) {
            this.logger.warning("getAccountByUsername: No username provided");
            return null;
        }
        var account = this.browserStorage.getAccountInfoFilteredBy({ username: username });
        if (account) {
            this.logger.verbose("getAccountByUsername: Account matching username found, returning");
            this.logger.verbosePii("getAccountByUsername: Returning signed-in accounts matching username: " + username);
            return account;
        }
        else {
            this.logger.verbose("getAccountByUsername: No matching account found, returning null");
            return null;
        }
    };
    /**
     * Returns the signed in account matching homeAccountId.
     * (the account object is created at the time of successful login)
     * or null when no matching account is found
     * @param homeAccountId
     * @returns The account object stored in MSAL
     */
    ClientApplication.prototype.getAccountByHomeId = function (homeAccountId) {
        this.logger.trace("getAccountByHomeId called");
        if (!homeAccountId) {
            this.logger.warning("getAccountByHomeId: No homeAccountId provided");
            return null;
        }
        var account = this.browserStorage.getAccountInfoFilteredBy({ homeAccountId: homeAccountId });
        if (account) {
            this.logger.verbose("getAccountByHomeId: Account matching homeAccountId found, returning");
            this.logger.verbosePii("getAccountByHomeId: Returning signed-in accounts matching homeAccountId: " + homeAccountId);
            return account;
        }
        else {
            this.logger.verbose("getAccountByHomeId: No matching account found, returning null");
            return null;
        }
    };
    /**
     * Returns the signed in account matching localAccountId.
     * (the account object is created at the time of successful login)
     * or null when no matching account is found
     * @param localAccountId
     * @returns The account object stored in MSAL
     */
    ClientApplication.prototype.getAccountByLocalId = function (localAccountId) {
        this.logger.trace("getAccountByLocalId called");
        if (!localAccountId) {
            this.logger.warning("getAccountByLocalId: No localAccountId provided");
            return null;
        }
        var account = this.browserStorage.getAccountInfoFilteredBy({ localAccountId: localAccountId });
        if (account) {
            this.logger.verbose("getAccountByLocalId: Account matching localAccountId found, returning");
            this.logger.verbosePii("getAccountByLocalId: Returning signed-in accounts matching localAccountId: " + localAccountId);
            return account;
        }
        else {
            this.logger.verbose("getAccountByLocalId: No matching account found, returning null");
            return null;
        }
    };
    /**
     * Sets the account to use as the active account. If no account is passed to the acquireToken APIs, then MSAL will use this active account.
     * @param account
     */
    ClientApplication.prototype.setActiveAccount = function (account) {
        this.browserStorage.setActiveAccount(account);
    };
    /**
     * Gets the currently active account
     */
    ClientApplication.prototype.getActiveAccount = function () {
        return this.browserStorage.getActiveAccount();
    };
    // #endregion
    // #region Helpers
    /**
     * Helper to validate app environment before making an auth request
     *
     * @protected
     * @param {InteractionType} interactionType What kind of interaction is being used
     * @param {boolean} [setInteractionInProgress=true] Whether to set interaction in progress temp cache flag
     */
    ClientApplication.prototype.preflightBrowserEnvironmentCheck = function (interactionType, setInteractionInProgress) {
        if (setInteractionInProgress === void 0) { setInteractionInProgress = true; }
        this.logger.verbose("preflightBrowserEnvironmentCheck started");
        // Block request if not in browser environment
        BrowserUtils.blockNonBrowserEnvironment(this.isBrowserEnvironment);
        // Block redirects if in an iframe
        BrowserUtils.blockRedirectInIframe(interactionType, this.config.system.allowRedirectInIframe);
        // Block auth requests inside a hidden iframe
        BrowserUtils.blockReloadInHiddenIframes();
        // Block redirectUri opened in a popup from calling MSAL APIs
        BrowserUtils.blockAcquireTokenInPopups();
        // Block token acquisition before initialize has been called if native brokering is enabled
        BrowserUtils.blockNativeBrokerCalledBeforeInitialized(this.config.system.allowNativeBroker, this.initialized);
        // Block redirects if memory storage is enabled but storeAuthStateInCookie is not
        if (interactionType === InteractionType.Redirect &&
            this.config.cache.cacheLocation === BrowserCacheLocation.MemoryStorage &&
            !this.config.cache.storeAuthStateInCookie) {
            throw BrowserConfigurationAuthError.createInMemoryRedirectUnavailableError();
        }
        if (interactionType === InteractionType.Redirect || interactionType === InteractionType.Popup) {
            this.preflightInteractiveRequest(setInteractionInProgress);
        }
    };
    /**
     * Preflight check for interactive requests
     *
     * @protected
     * @param {boolean} setInteractionInProgress Whether to set interaction in progress temp cache flag
     */
    ClientApplication.prototype.preflightInteractiveRequest = function (setInteractionInProgress) {
        this.logger.verbose("preflightInteractiveRequest called, validating app environment");
        // block the reload if it occurred inside a hidden iframe
        BrowserUtils.blockReloadInHiddenIframes();
        // Set interaction in progress temporary cache or throw if alread set.
        if (setInteractionInProgress) {
            this.browserStorage.setInteractionInProgress(true);
        }
    };
    /**
     * Acquire a token from native device (e.g. WAM)
     * @param request
     */
    ClientApplication.prototype.acquireTokenNative = function (request, apiId, accountId) {
        return __awaiter(this, void 0, void 0, function () {
            var nativeClient;
            return __generator(this, function (_a) {
                this.logger.trace("acquireTokenNative called");
                if (!this.nativeExtensionProvider) {
                    throw BrowserAuthError.createNativeConnectionNotEstablishedError();
                }
                nativeClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, apiId, this.performanceClient, this.nativeExtensionProvider, accountId || this.getNativeAccountId(request), this.nativeInternalStorage, request.correlationId);
                return [2 /*return*/, nativeClient.acquireToken(request)];
            });
        });
    };
    /**
     * Returns boolean indicating if this request can use the native broker
     * @param request
     */
    ClientApplication.prototype.canUseNative = function (request, accountId) {
        this.logger.trace("canUseNative called");
        if (!NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider, request.authenticationScheme)) {
            this.logger.trace("canUseNative: isNativeAvailable returned false, returning false");
            return false;
        }
        if (request.prompt) {
            switch (request.prompt) {
                case PromptValue.NONE:
                case PromptValue.CONSENT:
                case PromptValue.LOGIN:
                    this.logger.trace("canUseNative: prompt is compatible with native flow");
                    break;
                default:
                    this.logger.trace("canUseNative: prompt = " + request.prompt + " is not compatible with native flow, returning false");
                    return false;
            }
        }
        if (!accountId && !this.getNativeAccountId(request)) {
            this.logger.trace("canUseNative: nativeAccountId is not available, returning false");
            return false;
        }
        return true;
    };
    /**
     * Get the native accountId from the account
     * @param request
     * @returns
     */
    ClientApplication.prototype.getNativeAccountId = function (request) {
        var account = request.account || this.browserStorage.getAccountInfoByHints(request.loginHint, request.sid) || this.getActiveAccount();
        return account && account.nativeAccountId || "";
    };
    /**
     * Returns new instance of the Popup Interaction Client
     * @param correlationId
     */
    ClientApplication.prototype.createPopupClient = function (correlationId) {
        return new PopupClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, correlationId);
    };
    /**
     * Returns new instance of the Redirect Interaction Client
     * @param correlationId
     */
    ClientApplication.prototype.createRedirectClient = function (correlationId) {
        return new RedirectClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, correlationId);
    };
    /**
     * Returns new instance of the Silent Iframe Interaction Client
     * @param correlationId
     */
    ClientApplication.prototype.createSilentIframeClient = function (correlationId) {
        return new SilentIframeClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.ssoSilent, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, correlationId);
    };
    /**
     * Returns new instance of the Silent Cache Interaction Client
     */
    ClientApplication.prototype.createSilentCacheClient = function (correlationId) {
        return new SilentCacheClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeExtensionProvider, correlationId);
    };
    /**
     * Returns new instance of the Silent Refresh Interaction Client
     */
    ClientApplication.prototype.createSilentRefreshClient = function (correlationId) {
        return new SilentRefreshClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeExtensionProvider, correlationId);
    };
    /**
     * Returns new instance of the Silent AuthCode Interaction Client
     */
    ClientApplication.prototype.createSilentAuthCodeClient = function (correlationId) {
        return new SilentAuthCodeClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.acquireTokenByCode, this.performanceClient, this.nativeExtensionProvider, correlationId);
    };
    /**
     * Adds event callbacks to array
     * @param callback
     */
    ClientApplication.prototype.addEventCallback = function (callback) {
        return this.eventHandler.addEventCallback(callback);
    };
    /**
     * Removes callback with provided id from callback array
     * @param callbackId
     */
    ClientApplication.prototype.removeEventCallback = function (callbackId) {
        this.eventHandler.removeEventCallback(callbackId);
    };
    /**
     * Registers a callback to receive performance events.
     *
     * @param {PerformanceCallbackFunction} callback
     * @returns {string}
     */
    ClientApplication.prototype.addPerformanceCallback = function (callback) {
        return this.performanceClient.addPerformanceCallback(callback);
    };
    /**
     * Removes a callback registered with addPerformanceCallback.
     *
     * @param {string} callbackId
     * @returns {boolean}
     */
    ClientApplication.prototype.removePerformanceCallback = function (callbackId) {
        return this.performanceClient.removePerformanceCallback(callbackId);
    };
    /**
     * Adds event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
     */
    ClientApplication.prototype.enableAccountStorageEvents = function () {
        this.eventHandler.enableAccountStorageEvents();
    };
    /**
     * Removes event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window
     */
    ClientApplication.prototype.disableAccountStorageEvents = function () {
        this.eventHandler.disableAccountStorageEvents();
    };
    /**
     * Gets the token cache for the application.
     */
    ClientApplication.prototype.getTokenCache = function () {
        return this.tokenCache;
    };
    /**
     * Returns the logger instance
     */
    ClientApplication.prototype.getLogger = function () {
        return this.logger;
    };
    /**
     * Replaces the default logger set in configurations with new Logger with new configurations
     * @param logger Logger instance
     */
    ClientApplication.prototype.setLogger = function (logger) {
        this.logger = logger;
    };
    /**
     * Called by wrapper libraries (Angular & React) to set SKU and Version passed down to telemetry, logger, etc.
     * @param sku
     * @param version
     */
    ClientApplication.prototype.initializeWrapperLibrary = function (sku, version) {
        // Validate the SKU passed in is one we expect
        this.browserStorage.setWrapperMetadata(sku, version);
    };
    /**
     * Sets navigation client
     * @param navigationClient
     */
    ClientApplication.prototype.setNavigationClient = function (navigationClient) {
        this.navigationClient = navigationClient;
    };
    /**
     * Returns the configuration object
     */
    ClientApplication.prototype.getConfiguration = function () {
        return this.config;
    };
    /**
     * Generates a correlation id for a request if none is provided.
     *
     * @protected
     * @param {?Partial<BaseAuthRequest>} [request]
     * @returns {string}
     */
    ClientApplication.prototype.getRequestCorrelationId = function (request) {
        if (request === null || request === void 0 ? void 0 : request.correlationId) {
            return request.correlationId;
        }
        if (this.isBrowserEnvironment) {
            return this.browserCrypto.createNewGuid();
        }
        /*
         * Included for fallback for non-browser environments,
         * and to ensure this method always returns a string.
         */
        return Constants.EMPTY_STRING;
    };
    return ClientApplication;
}());

export { ClientApplication };
//# sourceMappingURL=ClientApplication.js.map
