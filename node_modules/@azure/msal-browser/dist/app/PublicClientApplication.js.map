{"version": 3, "file": "PublicClientApplication.js", "sources": ["../../src/app/PublicClientApplication.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AccountInfo, AuthenticationResult, Constants, RequestThumbprint, AuthError, PerformanceEvents, ServerError, InteractionRequiredAuthError, InProgressPerformanceEvent, InteractionRequiredAuthErrorMessage, AccountEntity } from \"@azure/msal-common\";\r\nimport { Configuration } from \"../config/Configuration\";\r\nimport { DEFAULT_REQUEST, InteractionType, ApiId, CacheLookupPolicy, BrowserConstants } from \"../utils/BrowserConstants\";\r\nimport { IPublicClientApplication } from \"./IPublicClientApplication\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { ClientApplication } from \"./ClientApplication\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { EventType } from \"../event/EventType\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { NativeAuthError } from \"../error/NativeAuthError\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\n\r\n/**\r\n * The PublicClientApplication class is the object exposed by the library to perform authentication and authorization functions in Single Page Applications\r\n * to obtain JWT tokens as described in the OAuth 2.0 Authorization Code Flow with PKCE specification.\r\n */\r\nexport class PublicClientApplication extends ClientApplication implements IPublicClientApplication {\r\n\r\n    // Active requests\r\n    private activeSilentTokenRequests: Map<string, Promise<AuthenticationResult>>;\r\n    private astsAsyncMeasurement?: InProgressPerformanceEvent = undefined;\r\n\r\n    /**\r\n     * @constructor\r\n     * Constructor for the PublicClientApplication used to instantiate the PublicClientApplication object\r\n     *\r\n     * Important attributes in the Configuration object for auth are:\r\n     * - clientID: the application ID of your application. You can obtain one by registering your application with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview\r\n     * - authority: the authority URL for your application.\r\n     * - redirect_uri: the uri of your application registered in the portal.\r\n     *\r\n     * In Azure AD, authority is a URL indicating the Azure active directory that MSAL uses to obtain tokens.\r\n     * It is of the form https://login.microsoftonline.com/{Enter_the_Tenant_Info_Here}\r\n     * If your application supports Accounts in one organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).\r\n     * If your application supports Accounts in any organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with organizations.\r\n     * If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace \"Enter_the_Tenant_Info_Here\" value with common.\r\n     * To restrict support to Personal Microsoft accounts only, replace \"Enter_the_Tenant_Info_Here\" value with consumers.\r\n     *\r\n     * In Azure B2C, authority is of the form https://{instance}/tfp/{tenant}/{policyName}/\r\n     * Full B2C functionality will be available in this library in future versions.\r\n     *\r\n     * @param configuration object for the MSAL PublicClientApplication instance\r\n     */\r\n    constructor(configuration: Configuration) {\r\n        super(configuration);\r\n\r\n        this.activeSilentTokenRequests = new Map();\r\n        // Register listener functions\r\n        this.trackPageVisibility = this.trackPageVisibility.bind(this);\r\n    }\r\n\r\n    /**\r\n     * Use when initiating the login process by redirecting the user's browser to the authorization endpoint. This function redirects the page, so\r\n     * any code that follows this function will not execute.\r\n     *\r\n     * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current\r\n     * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.\r\n     *\r\n     * @param request\r\n     */\r\n    async loginRedirect(request?: RedirectRequest): Promise<void> {\r\n        const correlationId: string = this.getRequestCorrelationId(request);\r\n        this.logger.verbose(\"loginRedirect called\", correlationId);\r\n        return this.acquireTokenRedirect({\r\n            correlationId,\r\n            ...(request || DEFAULT_REQUEST)\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Use when initiating the login process via opening a popup window in the user's browser\r\n     *\r\n     * @param request\r\n     *\r\n     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.\r\n     */\r\n    loginPopup(request?: PopupRequest): Promise<AuthenticationResult> {\r\n        const correlationId: string = this.getRequestCorrelationId(request);\r\n        this.logger.verbose(\"loginPopup called\", correlationId);\r\n        return this.acquireTokenPopup({\r\n            correlationId,\r\n            ...(request || DEFAULT_REQUEST)\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Silently acquire an access token for a given set of scopes. Returns currently processing promise if parallel requests are made.\r\n     *\r\n     * @param {@link (SilentRequest:type)}\r\n     * @returns {Promise.<AuthenticationResult>} - a promise that is fulfilled when this function has completed, or rejected if an error was raised. Returns the {@link AuthResponse} object\r\n     */\r\n    async acquireTokenSilent(request: SilentRequest): Promise<AuthenticationResult> {\r\n        const correlationId = this.getRequestCorrelationId(request);\r\n        const atsMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenSilent, correlationId);\r\n        atsMeasurement.addStaticFields({\r\n            cacheLookupPolicy: request.cacheLookupPolicy\r\n        });\r\n\r\n        this.preflightBrowserEnvironmentCheck(InteractionType.Silent);\r\n        this.logger.verbose(\"acquireTokenSilent called\", correlationId);\r\n\r\n        const account = request.account || this.getActiveAccount();\r\n        if (!account) {\r\n            throw BrowserAuthError.createNoAccountError();\r\n        }\r\n\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: this.config.auth.clientId,\r\n            authority: request.authority || Constants.EMPTY_STRING,\r\n            scopes: request.scopes,\r\n            homeAccountIdentifier: account.homeAccountId,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n        const silentRequestKey = JSON.stringify(thumbprint);\r\n\r\n        const cachedResponse = this.activeSilentTokenRequests.get(silentRequestKey);\r\n        if (typeof cachedResponse === \"undefined\") {\r\n            this.logger.verbose(\"acquireTokenSilent called for the first time, storing active request\", correlationId);\r\n\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.AcquireTokenSilentAsync, correlationId);\r\n            const response = this.acquireTokenSilentAsync({\r\n                ...request,\r\n                correlationId\r\n            }, account)\r\n                .then((result) => {\r\n                    this.activeSilentTokenRequests.delete(silentRequestKey);\r\n                    atsMeasurement.addStaticFields({\r\n                        accessTokenSize: result.accessToken.length,\r\n                        idTokenSize: result.idToken.length\r\n                    });\r\n                    atsMeasurement.endMeasurement({\r\n                        success: true,\r\n                        fromCache: result.fromCache,\r\n                        isNativeBroker: result.fromNativeBroker,\r\n                        cacheLookupPolicy: request.cacheLookupPolicy,\r\n                        requestId: result.requestId,\r\n                    });\r\n                    return result;\r\n                })\r\n                .catch((error: AuthError) => {\r\n                    this.activeSilentTokenRequests.delete(silentRequestKey);\r\n                    atsMeasurement.endMeasurement({\r\n                        errorCode: error.errorCode,\r\n                        subErrorCode: error.subError,\r\n                        success: false\r\n                    });\r\n                    throw error;\r\n                });\r\n            this.activeSilentTokenRequests.set(silentRequestKey, response);\r\n            return response;\r\n        } else {\r\n            this.logger.verbose(\"acquireTokenSilent has been called previously, returning the result from the first call\", correlationId);\r\n            // Discard measurements for memoized calls, as they are usually only a couple of ms and will artificially deflate metrics\r\n            atsMeasurement.discardMeasurement();\r\n            return cachedResponse;\r\n        }\r\n    }\r\n\r\n    private trackPageVisibility():void {\r\n        if (!this.astsAsyncMeasurement) {\r\n            return;\r\n        }\r\n        this.logger.info(\"Perf: Visibility change detected\");\r\n        this.astsAsyncMeasurement.increment({\r\n            visibilityChangeCount: 1,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Silently acquire an access token for a given set of scopes. Will use cached token if available, otherwise will attempt to acquire a new token from the network via refresh token.\r\n     * @param {@link (SilentRequest:type)}\r\n     * @param {@link (AccountInfo:type)}\r\n     * @returns {Promise.<AuthenticationResult>} - a promise that is fulfilled when this function has completed, or rejected if an error was raised. Returns the {@link AuthResponse}\r\n     */\r\n    protected async acquireTokenSilentAsync(request: SilentRequest, account: AccountInfo): Promise<AuthenticationResult>{\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenSilentAsync, request.correlationId);\r\n\r\n        this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_START, InteractionType.Silent, request);\r\n        this.astsAsyncMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenSilentAsync, request.correlationId);\r\n        this.astsAsyncMeasurement?.increment({\r\n            visibilityChangeCount: 0\r\n        });\r\n        document.addEventListener(\"visibilitychange\",this.trackPageVisibility);\r\n        let result: Promise<AuthenticationResult>;\r\n        if (NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider, request.authenticationScheme) && account.nativeAccountId) {\r\n            this.logger.verbose(\"acquireTokenSilent - attempting to acquire token from native platform\");\r\n            const silentRequest: SilentRequest = {\r\n                ...request,\r\n                account\r\n            };\r\n            result = this.acquireTokenNative(silentRequest, ApiId.acquireTokenSilent_silentFlow).catch(async (e: AuthError) => {\r\n                // If native token acquisition fails for availability reasons fallback to web flow\r\n                if (e instanceof NativeAuthError && e.isFatal()) {\r\n                    this.logger.verbose(\"acquireTokenSilent - native platform unavailable, falling back to web flow\");\r\n                    this.nativeExtensionProvider = undefined; // Prevent future requests from continuing to attempt\r\n\r\n                    // Cache will not contain tokens, given that previous WAM requests succeeded. Skip cache and RT renewal and go straight to iframe renewal\r\n                    const silentIframeClient = this.createSilentIframeClient(request.correlationId);\r\n                    return silentIframeClient.acquireToken(request);\r\n                }\r\n                throw e;\r\n            });\r\n        } else {\r\n            this.logger.verbose(\"acquireTokenSilent - attempting to acquire token from web flow\");\r\n\r\n            const silentCacheClient = this.createSilentCacheClient(request.correlationId);\r\n\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.InitializeSilentRequest, request.correlationId);\r\n            const silentRequest = await silentCacheClient.initializeSilentRequest(request, account);\r\n\r\n            const requestWithCLP = {\r\n                ...request,\r\n                // set the request's CacheLookupPolicy to Default if it was not optionally passed in\r\n                cacheLookupPolicy: request.cacheLookupPolicy || CacheLookupPolicy.Default\r\n            };\r\n\r\n            this.performanceClient.setPreQueueTime(PerformanceEvents.AcquireTokenFromCache, silentRequest.correlationId);\r\n            result = this.acquireTokenFromCache(silentCacheClient, silentRequest, requestWithCLP).catch((cacheError: AuthError) => {\r\n                if (requestWithCLP.cacheLookupPolicy === CacheLookupPolicy.AccessToken) {\r\n                    throw cacheError;\r\n                }\r\n\r\n                // block the reload if it occurred inside a hidden iframe\r\n                BrowserUtils.blockReloadInHiddenIframes();\r\n                this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_NETWORK_START, InteractionType.Silent, silentRequest);\r\n\r\n                this.performanceClient.setPreQueueTime(PerformanceEvents.AcquireTokenByRefreshToken, silentRequest.correlationId);\r\n                return this.acquireTokenByRefreshToken(silentRequest, requestWithCLP).catch((refreshTokenError: AuthError) => {\r\n                    const isServerError = refreshTokenError instanceof ServerError;\r\n                    const isInteractionRequiredError = refreshTokenError instanceof InteractionRequiredAuthError;\r\n                    const rtNotFound = (refreshTokenError.errorCode === InteractionRequiredAuthErrorMessage.noTokensFoundError.code);\r\n                    const isInvalidGrantError = (refreshTokenError.errorCode === BrowserConstants.INVALID_GRANT_ERROR);\r\n\r\n                    if ((!isServerError ||\r\n                            !isInvalidGrantError ||\r\n                            isInteractionRequiredError ||\r\n                            requestWithCLP.cacheLookupPolicy === CacheLookupPolicy.AccessTokenAndRefreshToken ||\r\n                            requestWithCLP.cacheLookupPolicy === CacheLookupPolicy.RefreshToken)\r\n                        && (requestWithCLP.cacheLookupPolicy !== CacheLookupPolicy.Skip)\r\n                        && !rtNotFound\r\n                    ) {\r\n                        throw refreshTokenError;\r\n                    }\r\n\r\n                    this.logger.verbose(\"Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.\", request.correlationId);\r\n                    this.performanceClient.setPreQueueTime(PerformanceEvents.AcquireTokenBySilentIframe, silentRequest.correlationId);\r\n                    return this.acquireTokenBySilentIframe(silentRequest);\r\n                });\r\n            });\r\n        }\r\n\r\n        return result.then((response) => {\r\n            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_SUCCESS, InteractionType.Silent, response);\r\n            this.astsAsyncMeasurement?.endMeasurement({\r\n                success: true,\r\n                fromCache: response.fromCache,\r\n                isNativeBroker: response.fromNativeBroker,\r\n                requestId: response.requestId\r\n            });\r\n            return response;\r\n        }).catch((tokenRenewalError: AuthError) => {\r\n            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Silent, null, tokenRenewalError);\r\n            this.astsAsyncMeasurement?.endMeasurement({\r\n                errorCode: tokenRenewalError.errorCode,\r\n                subErrorCode: tokenRenewalError.subError,\r\n                success: false\r\n            });\r\n            throw tokenRenewalError;\r\n        }).finally(() => {\r\n            document.removeEventListener(\"visibilitychange\",this.trackPageVisibility);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Hydrates cache with the tokens and account in the AuthenticationResult object\r\n     * @param result\r\n     * @param request - The request object that was used to obtain the AuthenticationResult\r\n     * @returns\r\n     */\r\n    async hydrateCache(\r\n        result: AuthenticationResult,\r\n        request: SilentRequest\r\n        | SsoSilentRequest\r\n        | RedirectRequest\r\n        | PopupRequest\r\n    ): Promise<void> {\r\n        this.logger.verbose(\"hydrateCache called\");\r\n\r\n        if(result.account) {\r\n            // Account gets saved to browser storage regardless of native or not\r\n            const accountEntity = AccountEntity.createFromAccountInfo(\r\n                result.account,\r\n                result.cloudGraphHostName,\r\n                result.msGraphHost\r\n            );\r\n            this.browserStorage.setAccount(accountEntity);\r\n\r\n            if (result.fromNativeBroker) {\r\n                this.logger.verbose(\r\n                    \"Response was from native broker, storing in-memory\"\r\n                );\r\n                // Tokens from native broker are stored in-memory\r\n                return this.nativeInternalStorage.hydrateCache(result, request);\r\n            } else {\r\n                return this.browserStorage.hydrateCache(result, request);\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;AAiBH;;;AAGG;AACH,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IAA6C,SAAiB,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;AAM1D;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,IAAA,SAAA,uBAAA,CAAY,aAA4B,EAAA;QAAxC,IACI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,aAAa,CAAC,IAKvB,IAAA,CAAA;QA7BO,KAAoB,CAAA,oBAAA,GAAgC,SAAS,CAAC;AA0BlE,QAAA,KAAI,CAAC,yBAAyB,GAAG,IAAI,GAAG,EAAE,CAAC;;QAE3C,KAAI,CAAC,mBAAmB,GAAG,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;;KAClE;AAED;;;;;;;;AAQG;IACG,uBAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UAAoB,OAAyB,EAAA;;;;AACnC,gBAAA,aAAa,GAAW,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;gBACpE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC;AAC3D,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,oBAAoB,CAAA,QAAA,CAAA,EAC5B,aAAa,EAAA,aAAA,EACV,GAAC,OAAO,IAAI,eAAe,GAChC,CAAC,CAAA;;;AACN,KAAA,CAAA;AAED;;;;;;AAMG;IACH,uBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,OAAsB,EAAA;QAC7B,IAAM,aAAa,GAAW,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AACxD,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAA,QAAA,CAAA,EACzB,aAAa,EAAA,aAAA,EACV,GAAC,OAAO,IAAI,eAAe,GAChC,CAAC;KACN,CAAA;AAED;;;;;AAKG;IACG,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAxB,UAAyB,OAAsB,EAAA;;;;;AACrC,gBAAA,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AACtD,gBAAA,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;gBACpH,cAAc,CAAC,eAAe,CAAC;oBAC3B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;AAC/C,iBAAA,CAAC,CAAC;AAEH,gBAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2BAA2B,EAAE,aAAa,CAAC,CAAC;gBAE1D,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3D,IAAI,CAAC,OAAO,EAAE;AACV,oBAAA,MAAM,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;AACjD,iBAAA;AAEK,gBAAA,UAAU,GAAsB;AAClC,oBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,oBAAA,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY;oBACtD,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,qBAAqB,EAAE,OAAO,CAAC,aAAa;oBAC5C,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;oBAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;oBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;oBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;iBACzB,CAAC;AACI,gBAAA,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;gBAE9C,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAC5E,gBAAA,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;oBACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sEAAsE,EAAE,aAAa,CAAC,CAAC;oBAE3G,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;oBAC3F,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACtC,OAAO,CAAA,EAAA,EACV,aAAa,EAAA,aAAA,EACd,CAAA,EAAA,OAAO,CAAC;yBACN,IAAI,CAAC,UAAC,MAAM,EAAA;AACT,wBAAA,KAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;wBACxD,cAAc,CAAC,eAAe,CAAC;AAC3B,4BAAA,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;AAC1C,4BAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;AACrC,yBAAA,CAAC,CAAC;wBACH,cAAc,CAAC,cAAc,CAAC;AAC1B,4BAAA,OAAO,EAAE,IAAI;4BACb,SAAS,EAAE,MAAM,CAAC,SAAS;4BAC3B,cAAc,EAAE,MAAM,CAAC,gBAAgB;4BACvC,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;4BAC5C,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,yBAAA,CAAC,CAAC;AACH,wBAAA,OAAO,MAAM,CAAC;AAClB,qBAAC,CAAC;yBACD,KAAK,CAAC,UAAC,KAAgB,EAAA;AACpB,wBAAA,KAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;wBACxD,cAAc,CAAC,cAAc,CAAC;4BAC1B,SAAS,EAAE,KAAK,CAAC,SAAS;4BAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,4BAAA,OAAO,EAAE,KAAK;AACjB,yBAAA,CAAC,CAAC;AACH,wBAAA,MAAM,KAAK,CAAC;AAChB,qBAAC,CAAC,CAAC;oBACP,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;AAC/D,oBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;AACnB,iBAAA;AAAM,qBAAA;oBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yFAAyF,EAAE,aAAa,CAAC,CAAC;;oBAE9H,cAAc,CAAC,kBAAkB,EAAE,CAAC;AACpC,oBAAA,OAAA,CAAA,CAAA,aAAO,cAAc,CAAC,CAAA;AACzB,iBAAA;;;AACJ,KAAA,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,mBAAmB,GAA3B,YAAA;AACI,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,OAAO;AACV,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;AAChC,YAAA,qBAAqB,EAAE,CAAC;AAC3B,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;;;;AAKG;AACa,IAAA,uBAAA,CAAA,SAAA,CAAA,uBAAuB,GAAvC,UAAwC,OAAsB,EAAE,OAAoB,EAAA;;;;;;;;AAChF,wBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAE7G,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC5F,wBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACtI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAC;AACjC,4BAAA,qBAAqB,EAAE,CAAC;yBAC3B,CAAE,CAAA;wBACH,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;8BAEnE,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC,eAAe,CAAA,EAAvJ,OAAuJ,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACvJ,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;AACvF,wBAAA,aAAa,yBACZ,OAAO,CAAA,EAAA,EACV,OAAO,EAAA,OAAA,GACV,CAAC;AACF,wBAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,UAAO,CAAY,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;gCAE1G,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AAC7C,oCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4EAA4E,CAAC,CAAC;AAClG,oCAAA,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;oCAGnC,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAChF,oCAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAA;AACnD,iCAAA;AACD,gCAAA,MAAM,CAAC,CAAC;;AACX,yBAAA,CAAA,CAAA,EAAA,CAAC,CAAC;;;AAEH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gEAAgE,CAAC,CAAC;wBAEhF,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE9E,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBACnF,OAAM,CAAA,CAAA,YAAA,iBAAiB,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAjF,wBAAA,eAAA,GAAgB,EAAiE,CAAA,IAAA,EAAA,CAAA;AAEjF,wBAAA,gBAAA,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACC,OAAO,CAAA,EAAA;;4BAEV,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,OAAO,EAAA,CAC5E,CAAC;AAEF,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,eAAa,CAAC,aAAa,CAAC,CAAC;AAC7G,wBAAA,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,eAAa,EAAE,gBAAc,CAAC,CAAC,KAAK,CAAC,UAAC,UAAqB,EAAA;AAC9G,4BAAA,IAAI,gBAAc,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,WAAW,EAAE;AACpE,gCAAA,MAAM,UAAU,CAAC;AACpB,6BAAA;;4BAGD,YAAY,CAAC,0BAA0B,EAAE,CAAC;AAC1C,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,2BAA2B,EAAE,eAAe,CAAC,MAAM,EAAE,eAAa,CAAC,CAAC;AAE1G,4BAAA,KAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,eAAa,CAAC,aAAa,CAAC,CAAC;AAClH,4BAAA,OAAO,KAAI,CAAC,0BAA0B,CAAC,eAAa,EAAE,gBAAc,CAAC,CAAC,KAAK,CAAC,UAAC,iBAA4B,EAAA;AACrG,gCAAA,IAAM,aAAa,GAAG,iBAAiB,YAAY,WAAW,CAAC;AAC/D,gCAAA,IAAM,0BAA0B,GAAG,iBAAiB,YAAY,4BAA4B,CAAC;AAC7F,gCAAA,IAAM,UAAU,IAAI,iBAAiB,CAAC,SAAS,KAAK,mCAAmC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gCACjH,IAAM,mBAAmB,IAAI,iBAAiB,CAAC,SAAS,KAAK,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;gCAEnG,IAAI,CAAC,CAAC,aAAa;AACX,oCAAA,CAAC,mBAAmB;oCACpB,0BAA0B;AAC1B,oCAAA,gBAAc,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,0BAA0B;AACjF,oCAAA,gBAAc,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,YAAY;AACpE,wCAAC,gBAAc,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,IAAI,CAAC;AAC7D,uCAAA,CAAC,UAAU,EAChB;AACE,oCAAA,MAAM,iBAAiB,CAAC;AAC3B,iCAAA;gCAED,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wGAAwG,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACrJ,gCAAA,KAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,eAAa,CAAC,aAAa,CAAC,CAAC;AAClH,gCAAA,OAAO,KAAI,CAAC,0BAA0B,CAAC,eAAa,CAAC,CAAC;AAC1D,6BAAC,CAAC,CAAC;AACP,yBAAC,CAAC,CAAC;;AAGP,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,IAAI,CAAC,UAAC,QAAQ,EAAA;;AACxB,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC/F,4BAAA,CAAA,EAAA,GAAA,KAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAC;AACtC,gCAAA,OAAO,EAAE,IAAI;gCACb,SAAS,EAAE,QAAQ,CAAC,SAAS;gCAC7B,cAAc,EAAE,QAAQ,CAAC,gBAAgB;gCACzC,SAAS,EAAE,QAAQ,CAAC,SAAS;6BAChC,CAAE,CAAA;AACH,4BAAA,OAAO,QAAQ,CAAC;AACpB,yBAAC,CAAC,CAAC,KAAK,CAAC,UAAC,iBAA4B,EAAA;;AAClC,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;AAC9G,4BAAA,CAAA,EAAA,GAAA,KAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAC;gCACtC,SAAS,EAAE,iBAAiB,CAAC,SAAS;gCACtC,YAAY,EAAE,iBAAiB,CAAC,QAAQ;AACxC,gCAAA,OAAO,EAAE,KAAK;6BACjB,CAAE,CAAA;AACH,4BAAA,MAAM,iBAAiB,CAAC;yBAC3B,CAAC,CAAC,OAAO,CAAC,YAAA;4BACP,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAC,KAAI,CAAC,mBAAmB,CAAC,CAAC;AAC9E,yBAAC,CAAC,CAAC,CAAA;;;;AACN,KAAA,CAAA;AAED;;;;;AAKG;AACG,IAAA,uBAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,UACI,MAA4B,EAC5B,OAGc,EAAA;;;;AAEd,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;gBAE3C,IAAG,MAAM,CAAC,OAAO,EAAE;AAET,oBAAA,aAAa,GAAG,aAAa,CAAC,qBAAqB,CACrD,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,WAAW,CACrB,CAAC;AACF,oBAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;oBAE9C,IAAI,MAAM,CAAC,gBAAgB,EAAE;AACzB,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;;wBAEF,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA;AACnE,qBAAA;AAAM,yBAAA;wBACH,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA;AAC5D,qBAAA;AACJ,iBAAA;;;;AACJ,KAAA,CAAA;IACL,OAAC,uBAAA,CAAA;AAAD,CAzSA,CAA6C,iBAAiB,CAyS7D;;;;"}