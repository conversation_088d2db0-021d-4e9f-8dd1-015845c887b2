{"version": 3, "file": "ClientApplication.js", "sources": ["../../src/app/ClientApplication.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CryptoOps } from \"../crypto/CryptoOps\";\r\nimport { InteractionRequiredAuthError, AccountInfo, Constants, INetworkModule, AuthenticationResult, Logger, CommonSilentFlowRequest, ICrypto, DEFAULT_CRYPTO_IMPLEMENTATION, AuthError, PerformanceEvents, PerformanceCallbackFunction, StubPerformanceClient, IPerformanceClient, BaseAuthRequest, PromptValue, ClientAuthError, InProgressPerformanceEvent } from \"@azure/msal-common\";\r\nimport { BrowserCacheManager, DEFAULT_BROWSER_CACHE_MANAGER } from \"../cache/BrowserCacheManager\";\r\nimport { BrowserConfiguration, buildConfiguration, CacheOptions, Configuration } from \"../config/Configuration\";\r\nimport { InteractionType, ApiId, BrowserCacheLocation, WrapperSKU, TemporaryCacheKeys, CacheLookupPolicy } from \"../utils/BrowserConstants\";\r\nimport { BrowserUtils } from \"../utils/BrowserUtils\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\nimport { version, name } from \"../packageMetadata\";\r\nimport { EventCallbackFunction } from \"../event/EventMessage\";\r\nimport { EventType } from \"../event/EventType\";\r\nimport { EndSessionRequest } from \"../request/EndSessionRequest\";\r\nimport { BrowserConfigurationAuthError } from \"../error/BrowserConfigurationAuthError\";\r\nimport { EndSessionPopupRequest } from \"../request/EndSessionPopupRequest\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { EventHandler } from \"../event/EventHandler\";\r\nimport { PopupClient } from \"../interaction_client/PopupClient\";\r\nimport { RedirectClient } from \"../interaction_client/RedirectClient\";\r\nimport { SilentIframeClient } from \"../interaction_client/SilentIframeClient\";\r\nimport { SilentRefreshClient } from \"../interaction_client/SilentRefreshClient\";\r\nimport { TokenCache } from \"../cache/TokenCache\";\r\nimport { ITokenCache } from \"../cache/ITokenCache\";\r\nimport { NativeInteractionClient } from \"../interaction_client/NativeInteractionClient\";\r\nimport { NativeMessageHandler } from \"../broker/nativeBroker/NativeMessageHandler\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { NativeAuthError } from \"../error/NativeAuthError\";\r\nimport { SilentCacheClient } from \"../interaction_client/SilentCacheClient\";\r\nimport { SilentAuthCodeClient } from \"../interaction_client/SilentAuthCodeClient\";\r\nimport { BrowserAuthError } from \"../error/BrowserAuthError\";\r\nimport { AuthorizationCodeRequest } from \"../request/AuthorizationCodeRequest\";\r\nimport { NativeTokenRequest } from \"../broker/nativeBroker/NativeRequest\";\r\nimport { BrowserPerformanceClient } from \"../telemetry/BrowserPerformanceClient\";\r\n\r\nexport abstract class ClientApplication {\r\n\r\n    // Crypto interface implementation\r\n    protected readonly browserCrypto: ICrypto;\r\n\r\n    // Storage interface implementation\r\n    protected readonly browserStorage: BrowserCacheManager;\r\n\r\n    // Native Cache in memory storage implementation\r\n    protected readonly nativeInternalStorage: BrowserCacheManager;\r\n\r\n    // Network interface implementation\r\n    protected readonly networkClient: INetworkModule;\r\n\r\n    // Navigation interface implementation\r\n    protected navigationClient: INavigationClient;\r\n\r\n    // Input configuration by developer/user\r\n    protected config: BrowserConfiguration;\r\n\r\n    // Token cache implementation\r\n    private tokenCache: TokenCache;\r\n\r\n    // Logger\r\n    protected logger: Logger;\r\n\r\n    // Flag to indicate if in browser environment\r\n    protected isBrowserEnvironment: boolean;\r\n\r\n    protected eventHandler: EventHandler;\r\n\r\n    // Redirect Response Object\r\n    protected redirectResponse: Map<string, Promise<AuthenticationResult | null>>;\r\n\r\n    // Native Extension Provider\r\n    protected nativeExtensionProvider: NativeMessageHandler | undefined;\r\n\r\n    // Hybrid auth code responses\r\n    private hybridAuthCodeResponses: Map<string, Promise<AuthenticationResult>>;\r\n\r\n    // Performance telemetry client\r\n    protected performanceClient: IPerformanceClient;\r\n\r\n    // Flag representing whether or not the initialize API has been called and completed\r\n    protected initialized: boolean;\r\n\r\n    private ssoSilentMeasurement?: InProgressPerformanceEvent;\r\n    private acquireTokenByCodeAsyncMeasurement?: InProgressPerformanceEvent;\r\n    /**\r\n     * @constructor\r\n     * Constructor for the PublicClientApplication used to instantiate the PublicClientApplication object\r\n     *\r\n     * Important attributes in the Configuration object for auth are:\r\n     * - clientID: the application ID of your application. You can obtain one by registering your application with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview\r\n     * - authority: the authority URL for your application.\r\n     * - redirect_uri: the uri of your application registered in the portal.\r\n     *\r\n     * In Azure AD, authority is a URL indicating the Azure active directory that MSAL uses to obtain tokens.\r\n     * It is of the form https://login.microsoftonline.com/{Enter_the_Tenant_Info_Here}\r\n     * If your application supports Accounts in one organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).\r\n     * If your application supports Accounts in any organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with organizations.\r\n     * If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace \"Enter_the_Tenant_Info_Here\" value with common.\r\n     * To restrict support to Personal Microsoft accounts only, replace \"Enter_the_Tenant_Info_Here\" value with consumers.\r\n     *\r\n     * In Azure B2C, authority is of the form https://{instance}/tfp/{tenant}/{policyName}/\r\n     * Full B2C functionality will be available in this library in future versions.\r\n     *\r\n     * @param configuration Object for the MSAL PublicClientApplication instance\r\n     */\r\n    constructor(configuration: Configuration) {\r\n        /*\r\n         * If loaded in an environment where window is not available,\r\n         * set internal flag to false so that further requests fail.\r\n         * This is to support server-side rendering environments.\r\n         */\r\n        this.isBrowserEnvironment = typeof window !== \"undefined\";\r\n        // Set the configuration.\r\n        this.config = buildConfiguration(configuration, this.isBrowserEnvironment);\r\n        this.initialized = false;\r\n\r\n        // Initialize logger\r\n        this.logger = new Logger(this.config.system.loggerOptions, name, version);\r\n\r\n        // Initialize the network module class.\r\n        this.networkClient = this.config.system.networkClient;\r\n\r\n        // Initialize the navigation client class.\r\n        this.navigationClient = this.config.system.navigationClient;\r\n\r\n        // Initialize redirectResponse Map\r\n        this.redirectResponse = new Map();\r\n\r\n        // Initial hybrid spa map\r\n        this.hybridAuthCodeResponses = new Map();\r\n\r\n        // Initialize performance client\r\n        this.performanceClient = this.isBrowserEnvironment ?\r\n            new BrowserPerformanceClient(this.config.auth.clientId, this.config.auth.authority, this.logger, name, version, this.config.telemetry.application, this.config.system.cryptoOptions) :\r\n            new StubPerformanceClient(this.config.auth.clientId, this.config.auth.authority, this.logger, name, version, this.config.telemetry.application);\r\n\r\n        // Initialize the crypto class.\r\n        this.browserCrypto = this.isBrowserEnvironment ? new CryptoOps(this.logger, this.performanceClient, this.config.system.cryptoOptions) : DEFAULT_CRYPTO_IMPLEMENTATION;\r\n\r\n        this.eventHandler = new EventHandler(this.logger, this.browserCrypto);\r\n\r\n        // Initialize the browser storage class.\r\n        this.browserStorage = this.isBrowserEnvironment ?\r\n            new BrowserCacheManager(this.config.auth.clientId, this.config.cache, this.browserCrypto, this.logger) :\r\n            DEFAULT_BROWSER_CACHE_MANAGER(this.config.auth.clientId, this.logger);\r\n\r\n        // initialize in memory storage for native flows\r\n        const nativeCacheOptions: Required<CacheOptions> = {\r\n            cacheLocation: BrowserCacheLocation.MemoryStorage,\r\n            temporaryCacheLocation: BrowserCacheLocation.MemoryStorage,\r\n            storeAuthStateInCookie: false,\r\n            secureCookies: false,\r\n            cacheMigrationEnabled: false,\r\n            claimsBasedCachingEnabled: true\r\n        };\r\n        this.nativeInternalStorage = new BrowserCacheManager(this.config.auth.clientId, nativeCacheOptions, this.browserCrypto, this.logger);\r\n\r\n        // Initialize the token cache\r\n        this.tokenCache = new TokenCache(this.config, this.browserStorage, this.logger, this.browserCrypto);\r\n        // Register listener functions\r\n        this.trackPageVisibilityWithMeasurement = this.trackPageVisibilityWithMeasurement.bind(this);\r\n    }\r\n\r\n    /**\r\n     * Initializer function to perform async startup tasks such as connecting to WAM extension\r\n     */\r\n    async initialize(): Promise<void> {\r\n        this.logger.trace(\"initialize called\");\r\n        if (this.initialized) {\r\n            this.logger.info(\"initialize has already been called, exiting early.\");\r\n            return;\r\n        }\r\n\r\n        const allowNativeBroker = this.config.system.allowNativeBroker;\r\n        const initMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.InitializeClientApplication);\r\n        this.eventHandler.emitEvent(EventType.INITIALIZE_START);\r\n\r\n        if (allowNativeBroker) {\r\n            try {\r\n                this.nativeExtensionProvider = await NativeMessageHandler.createProvider(this.logger, this.config.system.nativeBrokerHandshakeTimeout, this.performanceClient);\r\n            } catch (e) {\r\n                this.logger.verbose(e);\r\n            }\r\n        }\r\n\r\n        if(!this.config.cache.claimsBasedCachingEnabled) {\r\n            this.logger.verbose(\"Claims-based caching is disabled. Clearing the previous cache with claims\");\r\n            const claimsTokensRemovalMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.ClearTokensAndKeysWithClaims);\r\n            await this.browserStorage.clearTokensAndKeysWithClaims();\r\n            claimsTokensRemovalMeasurement.endMeasurement({success: true});\r\n        }\r\n\r\n        this.initialized = true;\r\n        this.eventHandler.emitEvent(EventType.INITIALIZE_END);\r\n\r\n        initMeasurement.endMeasurement({allowNativeBroker, success: true});\r\n    }\r\n\r\n    // #region Redirect Flow\r\n\r\n    /**\r\n     * Event handler function which allows users to fire events after the PublicClientApplication object\r\n     * has loaded during redirect flows. This should be invoked on all page loads involved in redirect\r\n     * auth flows.\r\n     * @param hash Hash to process. Defaults to the current value of window.location.hash. Only needs to be provided explicitly if the response to be handled is not contained in the current value.\r\n     * @returns Token response or null. If the return value is null, then no auth redirect was detected.\r\n     */\r\n    async handleRedirectPromise(hash?: string): Promise<AuthenticationResult | null> {\r\n        this.logger.verbose(\"handleRedirectPromise called\");\r\n        // Block token acquisition before initialize has been called if native brokering is enabled\r\n        BrowserUtils.blockNativeBrokerCalledBeforeInitialized(this.config.system.allowNativeBroker, this.initialized);\r\n\r\n        const loggedInAccounts = this.getAllAccounts();\r\n        if (this.isBrowserEnvironment) {\r\n            /**\r\n             * Store the promise on the PublicClientApplication instance if this is the first invocation of handleRedirectPromise,\r\n             * otherwise return the promise from the first invocation. Prevents race conditions when handleRedirectPromise is called\r\n             * several times concurrently.\r\n             */\r\n            const redirectResponseKey = hash || Constants.EMPTY_STRING;\r\n            let response = this.redirectResponse.get(redirectResponseKey);\r\n            if (typeof response === \"undefined\") {\r\n                this.eventHandler.emitEvent(EventType.HANDLE_REDIRECT_START, InteractionType.Redirect);\r\n                this.logger.verbose(\"handleRedirectPromise has been called for the first time, storing the promise\");\r\n\r\n                const request: NativeTokenRequest | null = this.browserStorage.getCachedNativeRequest();\r\n                let redirectResponse: Promise<AuthenticationResult | null>;\r\n                if (request && NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider) && this.nativeExtensionProvider && !hash) {\r\n                    this.logger.trace(\"handleRedirectPromise - acquiring token from native platform\");\r\n                    const nativeClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.handleRedirectPromise, this.performanceClient, this.nativeExtensionProvider, request.accountId, this.nativeInternalStorage, request.correlationId);\r\n                    redirectResponse = nativeClient.handleRedirectPromise();\r\n                } else {\r\n                    this.logger.trace(\"handleRedirectPromise - acquiring token from web flow\");\r\n                    const correlationId = this.browserStorage.getTemporaryCache(TemporaryCacheKeys.CORRELATION_ID, true) || Constants.EMPTY_STRING;\r\n                    const redirectClient = this.createRedirectClient(correlationId);\r\n                    redirectResponse = redirectClient.handleRedirectPromise(hash);\r\n                }\r\n\r\n                response = redirectResponse.then((result: AuthenticationResult | null) => {\r\n                    if (result) {\r\n                        // Emit login event if number of accounts change\r\n\r\n                        const isLoggingIn = loggedInAccounts.length < this.getAllAccounts().length;\r\n                        if (isLoggingIn) {\r\n                            this.eventHandler.emitEvent(EventType.LOGIN_SUCCESS, InteractionType.Redirect, result);\r\n                            this.logger.verbose(\"handleRedirectResponse returned result, login success\");\r\n                        } else {\r\n                            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_SUCCESS, InteractionType.Redirect, result);\r\n                            this.logger.verbose(\"handleRedirectResponse returned result, acquire token success\");\r\n                        }\r\n                    }\r\n                    this.eventHandler.emitEvent(EventType.HANDLE_REDIRECT_END, InteractionType.Redirect);\r\n\r\n                    return result;\r\n                }).catch((e) => {\r\n                    // Emit login event if there is an account\r\n                    if (loggedInAccounts.length > 0) {\r\n                        this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Redirect, null, e);\r\n                    } else {\r\n                        this.eventHandler.emitEvent(EventType.LOGIN_FAILURE, InteractionType.Redirect, null, e);\r\n                    }\r\n                    this.eventHandler.emitEvent(EventType.HANDLE_REDIRECT_END, InteractionType.Redirect);\r\n\r\n                    throw e;\r\n                });\r\n                this.redirectResponse.set(redirectResponseKey, response);\r\n            } else {\r\n                this.logger.verbose(\"handleRedirectPromise has been called previously, returning the result from the first call\");\r\n            }\r\n\r\n            return response;\r\n        }\r\n        this.logger.verbose(\"handleRedirectPromise returns null, not browser environment\");\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Use when you want to obtain an access_token for your API by redirecting the user's browser window to the authorization endpoint. This function redirects\r\n     * the page, so any code that follows this function will not execute.\r\n     *\r\n     * IMPORTANT: It is NOT recommended to have code that is dependent on the resolution of the Promise. This function will navigate away from the current\r\n     * browser window. It currently returns a Promise in order to reflect the asynchronous nature of the code running in this function.\r\n     *\r\n     * @param request\r\n     */\r\n    async acquireTokenRedirect(request: RedirectRequest): Promise<void> {\r\n        // Preflight request\r\n        const correlationId = this.getRequestCorrelationId(request);\r\n        this.logger.verbose(\"acquireTokenRedirect called\", correlationId);\r\n        this.preflightBrowserEnvironmentCheck(InteractionType.Redirect);\r\n\r\n        // If logged in, emit acquire token events\r\n        const isLoggedIn = this.getAllAccounts().length > 0;\r\n        if (isLoggedIn) {\r\n            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_START, InteractionType.Redirect, request);\r\n        } else {\r\n            this.eventHandler.emitEvent(EventType.LOGIN_START, InteractionType.Redirect, request);\r\n        }\r\n\r\n        let result: Promise<void>;\r\n\r\n        if (this.nativeExtensionProvider && this.canUseNative(request)) {\r\n            const nativeClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.acquireTokenRedirect, this.performanceClient, this.nativeExtensionProvider, this.getNativeAccountId(request), this.nativeInternalStorage, request.correlationId);\r\n            result = nativeClient.acquireTokenRedirect(request).catch((e: AuthError) => {\r\n                if (e instanceof NativeAuthError && e.isFatal()) {\r\n                    this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt\r\n                    const redirectClient = this.createRedirectClient(request.correlationId);\r\n                    return redirectClient.acquireToken(request);\r\n                } else if (e instanceof InteractionRequiredAuthError) {\r\n                    this.logger.verbose(\"acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow\");\r\n                    const redirectClient = this.createRedirectClient(request.correlationId);\r\n                    return redirectClient.acquireToken(request);\r\n                }\r\n                this.browserStorage.setInteractionInProgress(false);\r\n                throw e;\r\n            });\r\n        } else {\r\n            const redirectClient = this.createRedirectClient(request.correlationId);\r\n            result = redirectClient.acquireToken(request);\r\n        }\r\n\r\n        return result.catch((e) => {\r\n            // If logged in, emit acquire token events\r\n            if (isLoggedIn) {\r\n                this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Redirect, null, e);\r\n            } else {\r\n                this.eventHandler.emitEvent(EventType.LOGIN_FAILURE, InteractionType.Redirect, null, e);\r\n            }\r\n            throw e;\r\n        });\r\n    }\r\n\r\n    // #endregion\r\n\r\n    // #region Popup Flow\r\n\r\n    /**\r\n     * Use when you want to obtain an access_token for your API via opening a popup window in the user's browser\r\n     *\r\n     * @param request\r\n     *\r\n     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.\r\n     */\r\n    acquireTokenPopup(request: PopupRequest): Promise<AuthenticationResult> {\r\n        const correlationId = this.getRequestCorrelationId(request);\r\n        const atPopupMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenPopup, correlationId);\r\n\r\n        try {\r\n            this.logger.verbose(\"acquireTokenPopup called\", correlationId);\r\n            this.preflightBrowserEnvironmentCheck(InteractionType.Popup);\r\n        } catch (e) {\r\n            // Since this function is syncronous we need to reject\r\n            return Promise.reject(e);\r\n        }\r\n\r\n        // If logged in, emit acquire token events\r\n        const loggedInAccounts = this.getAllAccounts();\r\n        if (loggedInAccounts.length > 0) {\r\n            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_START, InteractionType.Popup, request);\r\n        } else {\r\n            this.eventHandler.emitEvent(EventType.LOGIN_START, InteractionType.Popup, request);\r\n        }\r\n\r\n        let result: Promise<AuthenticationResult>;\r\n\r\n        if (this.canUseNative(request)) {\r\n            result = this.acquireTokenNative(request, ApiId.acquireTokenPopup).then((response) => {\r\n                this.browserStorage.setInteractionInProgress(false);\r\n                atPopupMeasurement.endMeasurement({\r\n                    success: true,\r\n                    isNativeBroker: true,\r\n                    requestId: response.requestId\r\n                });\r\n                return response;\r\n            }).catch((e: AuthError) => {\r\n                if (e instanceof NativeAuthError && e.isFatal()) {\r\n                    this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt\r\n                    const popupClient = this.createPopupClient(request.correlationId);\r\n                    return popupClient.acquireToken(request);\r\n                } else if (e instanceof InteractionRequiredAuthError) {\r\n                    this.logger.verbose(\"acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow\");\r\n                    const popupClient = this.createPopupClient(request.correlationId);\r\n                    return popupClient.acquireToken(request);\r\n                }\r\n                this.browserStorage.setInteractionInProgress(false);\r\n                throw e;\r\n            });\r\n        } else {\r\n            const popupClient = this.createPopupClient(request.correlationId);\r\n            result = popupClient.acquireToken(request);\r\n        }\r\n\r\n        return result.then((result) => {\r\n\r\n            /*\r\n             *  If logged in, emit acquire token events\r\n             */\r\n            const isLoggingIn = loggedInAccounts.length < this.getAllAccounts().length;\r\n            if (isLoggingIn) {\r\n                this.eventHandler.emitEvent(EventType.LOGIN_SUCCESS, InteractionType.Popup, result);\r\n            } else {\r\n                this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_SUCCESS, InteractionType.Popup, result);\r\n            }\r\n\r\n            atPopupMeasurement.addStaticFields({\r\n                accessTokenSize: result.accessToken.length,\r\n                idTokenSize: result.idToken.length\r\n            });\r\n            atPopupMeasurement.endMeasurement({\r\n                success: true,\r\n                requestId: result.requestId\r\n            });\r\n            return result;\r\n        }).catch((e: AuthError) => {\r\n            if (loggedInAccounts.length > 0) {\r\n                this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_FAILURE, InteractionType.Popup, null, e);\r\n            } else {\r\n                this.eventHandler.emitEvent(EventType.LOGIN_FAILURE, InteractionType.Popup, null, e);\r\n            }\r\n\r\n            atPopupMeasurement.endMeasurement({\r\n                errorCode: e.errorCode,\r\n                subErrorCode: e.subError,\r\n                success: false\r\n            });\r\n            // Since this function is syncronous we need to reject\r\n            return Promise.reject(e);\r\n        });\r\n    }\r\n\r\n    private trackPageVisibilityWithMeasurement():void {\r\n        const measurement = this.ssoSilentMeasurement || this.acquireTokenByCodeAsyncMeasurement;\r\n        if(!measurement) {\r\n            return;\r\n        }\r\n\r\n        this.logger.info(\"Perf: Visibility change detected in \", measurement.event.name);\r\n        measurement.increment({\r\n            visibilityChangeCount: 1,\r\n        });\r\n    }\r\n    // #endregion\r\n\r\n    // #region Silent Flow\r\n\r\n    /**\r\n     * This function uses a hidden iframe to fetch an authorization code from the eSTS. There are cases where this may not work:\r\n     * - Any browser using a form of Intelligent Tracking Prevention\r\n     * - If there is not an established session with the service\r\n     *\r\n     * In these cases, the request must be done inside a popup or full frame redirect.\r\n     *\r\n     * For the cases where interaction is required, you cannot send a request with prompt=none.\r\n     *\r\n     * If your refresh token has expired, you can use this function to fetch a new set of tokens silently as long as\r\n     * you session on the server still exists.\r\n     * @param request {@link SsoSilentRequest}\r\n     *\r\n     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.\r\n     */\r\n    async ssoSilent(request: SsoSilentRequest): Promise<AuthenticationResult> {\r\n        const correlationId = this.getRequestCorrelationId(request);\r\n        const validRequest = {\r\n            ...request,\r\n            // will be PromptValue.NONE or PromptValue.NO_SESSION\r\n            prompt: request.prompt,\r\n            correlationId: correlationId\r\n        };\r\n        this.preflightBrowserEnvironmentCheck(InteractionType.Silent);\r\n        this.ssoSilentMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.SsoSilent, correlationId);\r\n        this.ssoSilentMeasurement?.increment({\r\n            visibilityChangeCount: 0\r\n        });\r\n        document.addEventListener(\"visibilitychange\",this.trackPageVisibilityWithMeasurement);\r\n        this.logger.verbose(\"ssoSilent called\", correlationId);\r\n        this.eventHandler.emitEvent(EventType.SSO_SILENT_START, InteractionType.Silent, validRequest);\r\n\r\n        let result: Promise<AuthenticationResult>;\r\n\r\n        if (this.canUseNative(validRequest)) {\r\n            result = this.acquireTokenNative(validRequest, ApiId.ssoSilent).catch((e: AuthError) => {\r\n                // If native token acquisition fails for availability reasons fallback to standard flow\r\n                if (e instanceof NativeAuthError && e.isFatal()) {\r\n                    this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt\r\n                    const silentIframeClient = this.createSilentIframeClient(validRequest.correlationId);\r\n                    return silentIframeClient.acquireToken(validRequest);\r\n                }\r\n                throw e;\r\n            });\r\n        } else {\r\n            const silentIframeClient = this.createSilentIframeClient(validRequest.correlationId);\r\n            result = silentIframeClient.acquireToken(validRequest);\r\n        }\r\n\r\n        return result.then((response) => {\r\n            this.eventHandler.emitEvent(EventType.SSO_SILENT_SUCCESS, InteractionType.Silent, response);\r\n            this.ssoSilentMeasurement?.addStaticFields({\r\n                accessTokenSize: response.accessToken.length,\r\n                idTokenSize: response.idToken.length\r\n            });\r\n            this.ssoSilentMeasurement?.endMeasurement({\r\n                success: true,\r\n                isNativeBroker: response.fromNativeBroker,\r\n                requestId: response.requestId\r\n            });\r\n            return response;\r\n        }).catch((e: AuthError) => {\r\n            this.eventHandler.emitEvent(EventType.SSO_SILENT_FAILURE, InteractionType.Silent, null, e);\r\n            this.ssoSilentMeasurement?.endMeasurement({\r\n                errorCode: e.errorCode,\r\n                subErrorCode: e.subError,\r\n                success: false\r\n            });\r\n            throw e;\r\n        }).finally(() => {\r\n            document.removeEventListener(\"visibilitychange\",this.trackPageVisibilityWithMeasurement);\r\n        });\r\n\r\n    }\r\n\r\n    /**\r\n     * This function redeems an authorization code (passed as code) from the eSTS token endpoint.\r\n     * This authorization code should be acquired server-side using a confidential client to acquire a spa_code.\r\n     * This API is not indended for normal authorization code acquisition and redemption.\r\n     *\r\n     * Redemption of this authorization code will not require PKCE, as it was acquired by a confidential client.\r\n     *\r\n     * @param request {@link AuthorizationCodeRequest}\r\n     * @returns A promise that is fulfilled when this function has completed, or rejected if an error was raised.\r\n     */\r\n    async acquireTokenByCode(request: AuthorizationCodeRequest): Promise<AuthenticationResult> {\r\n        const correlationId = this.getRequestCorrelationId(request);\r\n        this.preflightBrowserEnvironmentCheck(InteractionType.Silent);\r\n        this.logger.trace(\"acquireTokenByCode called\", correlationId);\r\n        this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_START, InteractionType.Silent, request);\r\n        const atbcMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenByCode, request.correlationId);\r\n\r\n        try {\r\n            if (request.code && request.nativeAccountId) {\r\n                // Throw error in case server returns both spa_code and spa_accountid in exchange for auth code.\r\n                throw BrowserAuthError.createSpaCodeAndNativeAccountIdPresentError();\r\n            }\r\n            else if (request.code) {\r\n                const hybridAuthCode = request.code;\r\n                let response = this.hybridAuthCodeResponses.get(hybridAuthCode);\r\n                if (!response) {\r\n                    this.logger.verbose(\"Initiating new acquireTokenByCode request\", correlationId);\r\n                    response = this.acquireTokenByCodeAsync({\r\n                        ...request,\r\n                        correlationId\r\n                    })\r\n                        .then((result: AuthenticationResult) => {\r\n                            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_SUCCESS, InteractionType.Silent, result);\r\n                            this.hybridAuthCodeResponses.delete(hybridAuthCode);\r\n                            atbcMeasurement.addStaticFields({\r\n                                accessTokenSize: result.accessToken.length,\r\n                                idTokenSize: result.idToken.length\r\n                            });\r\n                            atbcMeasurement.endMeasurement({\r\n                                success: true,\r\n                                isNativeBroker: result.fromNativeBroker,\r\n                                requestId: result.requestId\r\n                            });\r\n                            return result;\r\n                        })\r\n                        .catch((error: AuthError) => {\r\n                            this.hybridAuthCodeResponses.delete(hybridAuthCode);\r\n                            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_FAILURE, InteractionType.Silent, null, error);\r\n                            atbcMeasurement.endMeasurement({\r\n                                errorCode: error.errorCode,\r\n                                subErrorCode: error.subError,\r\n                                success: false\r\n                            });\r\n                            throw error;\r\n                        });\r\n                    this.hybridAuthCodeResponses.set(hybridAuthCode, response);\r\n                } else {\r\n                    this.logger.verbose(\"Existing acquireTokenByCode request found\", request.correlationId);\r\n                    atbcMeasurement.discardMeasurement();\r\n                }\r\n                return response;\r\n            } else if (request.nativeAccountId) {\r\n                if (this.canUseNative(request, request.nativeAccountId)) {\r\n                    return this.acquireTokenNative(request, ApiId.acquireTokenByCode, request.nativeAccountId).catch((e: AuthError) => {\r\n                        // If native token acquisition fails for availability reasons fallback to standard flow\r\n                        if (e instanceof NativeAuthError && e.isFatal()) {\r\n                            this.nativeExtensionProvider = undefined; // If extension gets uninstalled during session prevent future requests from continuing to attempt\r\n                        }\r\n                        throw e;\r\n                    });\r\n                } else {\r\n                    throw BrowserAuthError.createUnableToAcquireTokenFromNativePlatformError();\r\n                }\r\n            } else {\r\n                throw BrowserAuthError.createAuthCodeOrNativeAccountIdRequiredError();\r\n            }\r\n\r\n        } catch (e) {\r\n            this.eventHandler.emitEvent(EventType.ACQUIRE_TOKEN_BY_CODE_FAILURE, InteractionType.Silent, null, e);\r\n            atbcMeasurement.endMeasurement({\r\n                errorCode: e instanceof AuthError && e.errorCode || undefined,\r\n                subErrorCode: e instanceof AuthError && e.subError || undefined,\r\n                success: false\r\n            });\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a SilentAuthCodeClient to redeem an authorization code.\r\n     * @param request\r\n     * @returns Result of the operation to redeem the authorization code\r\n     */\r\n    private async acquireTokenByCodeAsync(request: AuthorizationCodeRequest): Promise<AuthenticationResult> {\r\n        this.logger.trace(\"acquireTokenByCodeAsync called\", request.correlationId);\r\n        this.acquireTokenByCodeAsyncMeasurement = this.performanceClient.startMeasurement(PerformanceEvents.AcquireTokenByCodeAsync, request.correlationId);\r\n        this.acquireTokenByCodeAsyncMeasurement?.increment({\r\n            visibilityChangeCount: 0\r\n        });\r\n        document.addEventListener(\"visibilitychange\",this.trackPageVisibilityWithMeasurement);\r\n        const silentAuthCodeClient = this.createSilentAuthCodeClient(request.correlationId);\r\n        const silentTokenResult = await silentAuthCodeClient.acquireToken(request).then((response) => {\r\n            this.acquireTokenByCodeAsyncMeasurement?.endMeasurement({\r\n                success: true,\r\n                fromCache: response.fromCache,\r\n                isNativeBroker: response.fromNativeBroker,\r\n                requestId: response.requestId\r\n            });\r\n            return response;\r\n        }).catch((tokenRenewalError: AuthError) => {\r\n            this.acquireTokenByCodeAsyncMeasurement?.endMeasurement({\r\n                errorCode: tokenRenewalError.errorCode,\r\n                subErrorCode: tokenRenewalError.subError,\r\n                success: false\r\n            });\r\n            throw tokenRenewalError;\r\n        }).finally(() => {\r\n            document.removeEventListener(\"visibilitychange\",this.trackPageVisibilityWithMeasurement);\r\n        });\r\n        return silentTokenResult;\r\n    }\r\n\r\n    /**\r\n     * Attempt to acquire an access token from the cache\r\n     * @param silentCacheClient SilentCacheClient\r\n     * @param commonRequest CommonSilentFlowRequest\r\n     * @param silentRequest SilentRequest\r\n     * @returns A promise that, when resolved, returns the access token\r\n     */\r\n    protected async acquireTokenFromCache(\r\n        silentCacheClient: SilentCacheClient,\r\n        commonRequest: CommonSilentFlowRequest,\r\n        silentRequest: SilentRequest\r\n    ): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenFromCache, commonRequest.correlationId);\r\n        switch(silentRequest.cacheLookupPolicy) {\r\n            case CacheLookupPolicy.Default:\r\n            case CacheLookupPolicy.AccessToken:\r\n            case CacheLookupPolicy.AccessTokenAndRefreshToken:\r\n                return silentCacheClient.acquireToken(commonRequest);\r\n            default:\r\n                throw ClientAuthError.createRefreshRequiredError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attempt to acquire an access token via a refresh token\r\n     * @param commonRequest CommonSilentFlowRequest\r\n     * @param silentRequest SilentRequest\r\n     * @returns A promise that, when resolved, returns the access token\r\n     */\r\n    protected async acquireTokenByRefreshToken(\r\n        commonRequest: CommonSilentFlowRequest,\r\n        silentRequest: SilentRequest\r\n    ): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenByRefreshToken, commonRequest.correlationId);\r\n        switch(silentRequest.cacheLookupPolicy) {\r\n            case CacheLookupPolicy.Default:\r\n            case CacheLookupPolicy.AccessTokenAndRefreshToken:\r\n            case CacheLookupPolicy.RefreshToken:\r\n            case CacheLookupPolicy.RefreshTokenAndNetwork:\r\n                const silentRefreshClient = this.createSilentRefreshClient(commonRequest.correlationId);\r\n\r\n                this.performanceClient.setPreQueueTime(PerformanceEvents.SilentRefreshClientAcquireToken, commonRequest.correlationId);\r\n                return silentRefreshClient.acquireToken(commonRequest);\r\n            default:\r\n                throw ClientAuthError.createRefreshRequiredError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attempt to acquire an access token via an iframe\r\n     * @param request CommonSilentFlowRequest\r\n     * @returns A promise that, when resolved, returns the access token\r\n     */\r\n    protected async acquireTokenBySilentIframe(\r\n        request: CommonSilentFlowRequest\r\n    ): Promise<AuthenticationResult> {\r\n        this.performanceClient.addQueueMeasurement(PerformanceEvents.AcquireTokenBySilentIframe, request.correlationId);\r\n\r\n        const silentIframeClient = this.createSilentIframeClient(request.correlationId);\r\n\r\n        this.performanceClient.setPreQueueTime(PerformanceEvents.SilentIframeClientAcquireToken, request.correlationId);\r\n        return silentIframeClient.acquireToken(request);\r\n    }\r\n\r\n    // #endregion\r\n\r\n    // #region Logout\r\n\r\n    /**\r\n     * Deprecated logout function. Use logoutRedirect or logoutPopup instead\r\n     * @param logoutRequest\r\n     * @deprecated\r\n     */\r\n    async logout(logoutRequest?: EndSessionRequest): Promise<void> {\r\n        const correlationId = this.getRequestCorrelationId(logoutRequest);\r\n        this.logger.warning(\"logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.\", correlationId);\r\n        return this.logoutRedirect({\r\n            correlationId,\r\n            ...logoutRequest\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Use to log out the current user, and redirect the user to the postLogoutRedirectUri.\r\n     * Default behaviour is to redirect the user to `window.location.href`.\r\n     * @param logoutRequest\r\n     */\r\n    async logoutRedirect(logoutRequest?: EndSessionRequest): Promise<void> {\r\n        const correlationId = this.getRequestCorrelationId(logoutRequest);\r\n        this.preflightBrowserEnvironmentCheck(InteractionType.Redirect);\r\n\r\n        const redirectClient = this.createRedirectClient(correlationId);\r\n        return redirectClient.logout(logoutRequest);\r\n    }\r\n\r\n    /**\r\n     * Clears local cache for the current user then opens a popup window prompting the user to sign-out of the server\r\n     * @param logoutRequest\r\n     */\r\n    logoutPopup(logoutRequest?: EndSessionPopupRequest): Promise<void> {\r\n        try {\r\n            const correlationId = this.getRequestCorrelationId(logoutRequest);\r\n            this.preflightBrowserEnvironmentCheck(InteractionType.Popup);\r\n            const popupClient = this.createPopupClient(correlationId);\r\n            return popupClient.logout(logoutRequest);\r\n        } catch (e) {\r\n            // Since this function is syncronous we need to reject\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    // #endregion\r\n\r\n    // #region Account APIs\r\n\r\n    /**\r\n     * Returns all accounts that MSAL currently has data for.\r\n     * (the account object is created at the time of successful login)\r\n     * or empty array when no accounts are found\r\n     * @returns Array of account objects in cache\r\n     */\r\n    getAllAccounts(): AccountInfo[] {\r\n        this.logger.verbose(\"getAllAccounts called\");\r\n        return this.isBrowserEnvironment ? this.browserStorage.getAllAccounts() : [];\r\n    }\r\n\r\n    /**\r\n     * Returns the signed in account matching username.\r\n     * (the account object is created at the time of successful login)\r\n     * or null when no matching account is found.\r\n     * This API is provided for convenience but getAccountById should be used for best reliability\r\n     * @param username\r\n     * @returns The account object stored in MSAL\r\n     */\r\n    getAccountByUsername(username: string): AccountInfo | null {\r\n        this.logger.trace(\"getAccountByUsername called\");\r\n        if (!username) {\r\n            this.logger.warning(\"getAccountByUsername: No username provided\");\r\n            return null;\r\n        }\r\n\r\n        const account = this.browserStorage.getAccountInfoFilteredBy({username});\r\n        if (account) {\r\n            this.logger.verbose(\"getAccountByUsername: Account matching username found, returning\");\r\n            this.logger.verbosePii(`getAccountByUsername: Returning signed-in accounts matching username: ${username}`);\r\n            return account;\r\n        } else {\r\n            this.logger.verbose(\"getAccountByUsername: No matching account found, returning null\");\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the signed in account matching homeAccountId.\r\n     * (the account object is created at the time of successful login)\r\n     * or null when no matching account is found\r\n     * @param homeAccountId\r\n     * @returns The account object stored in MSAL\r\n     */\r\n    getAccountByHomeId(homeAccountId: string): AccountInfo | null {\r\n        this.logger.trace(\"getAccountByHomeId called\");\r\n        if (!homeAccountId) {\r\n            this.logger.warning(\"getAccountByHomeId: No homeAccountId provided\");\r\n            return null;\r\n        }\r\n\r\n        const account = this.browserStorage.getAccountInfoFilteredBy({homeAccountId});\r\n        if (account) {\r\n            this.logger.verbose(\"getAccountByHomeId: Account matching homeAccountId found, returning\");\r\n            this.logger.verbosePii(`getAccountByHomeId: Returning signed-in accounts matching homeAccountId: ${homeAccountId}`);\r\n            return account;\r\n        } else {\r\n            this.logger.verbose(\"getAccountByHomeId: No matching account found, returning null\");\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the signed in account matching localAccountId.\r\n     * (the account object is created at the time of successful login)\r\n     * or null when no matching account is found\r\n     * @param localAccountId\r\n     * @returns The account object stored in MSAL\r\n     */\r\n    getAccountByLocalId(localAccountId: string): AccountInfo | null {\r\n        this.logger.trace(\"getAccountByLocalId called\");\r\n        if (!localAccountId) {\r\n            this.logger.warning(\"getAccountByLocalId: No localAccountId provided\");\r\n            return null;\r\n        }\r\n\r\n        const account = this.browserStorage.getAccountInfoFilteredBy({localAccountId});\r\n        if (account) {\r\n            this.logger.verbose(\"getAccountByLocalId: Account matching localAccountId found, returning\");\r\n            this.logger.verbosePii(`getAccountByLocalId: Returning signed-in accounts matching localAccountId: ${localAccountId}`);\r\n            return account;\r\n        } else {\r\n            this.logger.verbose(\"getAccountByLocalId: No matching account found, returning null\");\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the account to use as the active account. If no account is passed to the acquireToken APIs, then MSAL will use this active account.\r\n     * @param account\r\n     */\r\n    setActiveAccount(account: AccountInfo | null): void {\r\n        this.browserStorage.setActiveAccount(account);\r\n    }\r\n\r\n    /**\r\n     * Gets the currently active account\r\n     */\r\n    getActiveAccount(): AccountInfo | null {\r\n        return this.browserStorage.getActiveAccount();\r\n    }\r\n\r\n    // #endregion\r\n\r\n    // #region Helpers\r\n\r\n    /**\r\n     * Helper to validate app environment before making an auth request\r\n     *\r\n     * @protected\r\n     * @param {InteractionType} interactionType What kind of interaction is being used\r\n     * @param {boolean} [setInteractionInProgress=true] Whether to set interaction in progress temp cache flag\r\n     */\r\n    protected preflightBrowserEnvironmentCheck(interactionType: InteractionType, setInteractionInProgress: boolean = true): void {\r\n        this.logger.verbose(\"preflightBrowserEnvironmentCheck started\");\r\n        // Block request if not in browser environment\r\n        BrowserUtils.blockNonBrowserEnvironment(this.isBrowserEnvironment);\r\n\r\n        // Block redirects if in an iframe\r\n        BrowserUtils.blockRedirectInIframe(interactionType, this.config.system.allowRedirectInIframe);\r\n\r\n        // Block auth requests inside a hidden iframe\r\n        BrowserUtils.blockReloadInHiddenIframes();\r\n\r\n        // Block redirectUri opened in a popup from calling MSAL APIs\r\n        BrowserUtils.blockAcquireTokenInPopups();\r\n\r\n        // Block token acquisition before initialize has been called if native brokering is enabled\r\n        BrowserUtils.blockNativeBrokerCalledBeforeInitialized(this.config.system.allowNativeBroker, this.initialized);\r\n\r\n        // Block redirects if memory storage is enabled but storeAuthStateInCookie is not\r\n        if (interactionType === InteractionType.Redirect &&\r\n            this.config.cache.cacheLocation === BrowserCacheLocation.MemoryStorage &&\r\n            !this.config.cache.storeAuthStateInCookie) {\r\n            throw BrowserConfigurationAuthError.createInMemoryRedirectUnavailableError();\r\n        }\r\n\r\n        if (interactionType === InteractionType.Redirect || interactionType === InteractionType.Popup) {\r\n            this.preflightInteractiveRequest(setInteractionInProgress);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Preflight check for interactive requests\r\n     *\r\n     * @protected\r\n     * @param {boolean} setInteractionInProgress Whether to set interaction in progress temp cache flag\r\n     */\r\n    protected preflightInteractiveRequest(setInteractionInProgress: boolean): void {\r\n        this.logger.verbose(\"preflightInteractiveRequest called, validating app environment\");\r\n        // block the reload if it occurred inside a hidden iframe\r\n        BrowserUtils.blockReloadInHiddenIframes();\r\n\r\n        // Set interaction in progress temporary cache or throw if alread set.\r\n        if (setInteractionInProgress) {\r\n            this.browserStorage.setInteractionInProgress(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquire a token from native device (e.g. WAM)\r\n     * @param request\r\n     */\r\n    protected async acquireTokenNative(request: PopupRequest | SilentRequest | SsoSilentRequest, apiId: ApiId, accountId?: string): Promise<AuthenticationResult> {\r\n        this.logger.trace(\"acquireTokenNative called\");\r\n        if (!this.nativeExtensionProvider) {\r\n            throw BrowserAuthError.createNativeConnectionNotEstablishedError();\r\n        }\r\n\r\n        const nativeClient = new NativeInteractionClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, apiId, this.performanceClient, this.nativeExtensionProvider, accountId || this.getNativeAccountId(request), this.nativeInternalStorage, request.correlationId);\r\n\r\n        return nativeClient.acquireToken(request);\r\n    }\r\n\r\n    /**\r\n     * Returns boolean indicating if this request can use the native broker\r\n     * @param request\r\n     */\r\n    protected canUseNative(request: RedirectRequest | PopupRequest | SsoSilentRequest, accountId?: string): boolean {\r\n        this.logger.trace(\"canUseNative called\");\r\n        if (!NativeMessageHandler.isNativeAvailable(this.config, this.logger, this.nativeExtensionProvider, request.authenticationScheme)) {\r\n            this.logger.trace(\"canUseNative: isNativeAvailable returned false, returning false\");\r\n            return false;\r\n        }\r\n\r\n        if (request.prompt) {\r\n            switch (request.prompt) {\r\n                case PromptValue.NONE:\r\n                case PromptValue.CONSENT:\r\n                case PromptValue.LOGIN:\r\n                    this.logger.trace(\"canUseNative: prompt is compatible with native flow\");\r\n                    break;\r\n                default:\r\n                    this.logger.trace(`canUseNative: prompt = ${request.prompt} is not compatible with native flow, returning false`);\r\n                    return false;\r\n            }\r\n        }\r\n\r\n        if (!accountId && !this.getNativeAccountId(request)) {\r\n            this.logger.trace(\"canUseNative: nativeAccountId is not available, returning false\");\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get the native accountId from the account\r\n     * @param request\r\n     * @returns\r\n     */\r\n    protected getNativeAccountId(request: RedirectRequest | PopupRequest | SsoSilentRequest): string {\r\n        const account = request.account || this.browserStorage.getAccountInfoByHints(request.loginHint, request.sid) || this.getActiveAccount();\r\n\r\n        return account && account.nativeAccountId || \"\";\r\n    }\r\n\r\n    /**\r\n     * Returns new instance of the Popup Interaction Client\r\n     * @param correlationId\r\n     */\r\n    protected createPopupClient(correlationId?: string): PopupClient {\r\n        return new PopupClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Returns new instance of the Redirect Interaction Client\r\n     * @param correlationId\r\n     */\r\n    protected createRedirectClient(correlationId?: string): RedirectClient {\r\n        return new RedirectClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Returns new instance of the Silent Iframe Interaction Client\r\n     * @param correlationId\r\n     */\r\n    protected createSilentIframeClient(correlationId?: string): SilentIframeClient {\r\n        return new SilentIframeClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.ssoSilent, this.performanceClient, this.nativeInternalStorage, this.nativeExtensionProvider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Returns new instance of the Silent Cache Interaction Client\r\n     */\r\n    protected createSilentCacheClient(correlationId?: string): SilentCacheClient {\r\n        return new SilentCacheClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeExtensionProvider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Returns new instance of the Silent Refresh Interaction Client\r\n     */\r\n    protected createSilentRefreshClient(correlationId?: string): SilentRefreshClient {\r\n        return new SilentRefreshClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, this.performanceClient, this.nativeExtensionProvider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Returns new instance of the Silent AuthCode Interaction Client\r\n     */\r\n    protected createSilentAuthCodeClient(correlationId?: string): SilentAuthCodeClient {\r\n        return new SilentAuthCodeClient(this.config, this.browserStorage, this.browserCrypto, this.logger, this.eventHandler, this.navigationClient, ApiId.acquireTokenByCode, this.performanceClient, this.nativeExtensionProvider, correlationId);\r\n    }\r\n\r\n    /**\r\n     * Adds event callbacks to array\r\n     * @param callback\r\n     */\r\n    addEventCallback(callback: EventCallbackFunction): string | null {\r\n        return this.eventHandler.addEventCallback(callback);\r\n    }\r\n\r\n    /**\r\n     * Removes callback with provided id from callback array\r\n     * @param callbackId\r\n     */\r\n    removeEventCallback(callbackId: string): void {\r\n        this.eventHandler.removeEventCallback(callbackId);\r\n    }\r\n\r\n    /**\r\n     * Registers a callback to receive performance events.\r\n     *\r\n     * @param {PerformanceCallbackFunction} callback\r\n     * @returns {string}\r\n     */\r\n    addPerformanceCallback(callback: PerformanceCallbackFunction): string {\r\n        return this.performanceClient.addPerformanceCallback(callback);\r\n    }\r\n\r\n    /**\r\n     * Removes a callback registered with addPerformanceCallback.\r\n     *\r\n     * @param {string} callbackId\r\n     * @returns {boolean}\r\n     */\r\n    removePerformanceCallback(callbackId: string): boolean {\r\n        return this.performanceClient.removePerformanceCallback(callbackId);\r\n    }\r\n\r\n    /**\r\n     * Adds event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window\r\n     */\r\n    enableAccountStorageEvents(): void {\r\n        this.eventHandler.enableAccountStorageEvents();\r\n    }\r\n\r\n    /**\r\n     * Removes event listener that emits an event when a user account is added or removed from localstorage in a different browser tab or window\r\n     */\r\n    disableAccountStorageEvents(): void {\r\n        this.eventHandler.disableAccountStorageEvents();\r\n    }\r\n\r\n    /**\r\n     * Gets the token cache for the application.\r\n     */\r\n    getTokenCache(): ITokenCache {\r\n        return this.tokenCache;\r\n    }\r\n\r\n    /**\r\n     * Returns the logger instance\r\n     */\r\n    getLogger(): Logger {\r\n        return this.logger;\r\n    }\r\n\r\n    /**\r\n     * Replaces the default logger set in configurations with new Logger with new configurations\r\n     * @param logger Logger instance\r\n     */\r\n    setLogger(logger: Logger): void {\r\n        this.logger = logger;\r\n    }\r\n\r\n    /**\r\n     * Called by wrapper libraries (Angular & React) to set SKU and Version passed down to telemetry, logger, etc.\r\n     * @param sku\r\n     * @param version\r\n     */\r\n    initializeWrapperLibrary(sku: WrapperSKU, version: string): void {\r\n        // Validate the SKU passed in is one we expect\r\n        this.browserStorage.setWrapperMetadata(sku, version);\r\n    }\r\n\r\n    /**\r\n     * Sets navigation client\r\n     * @param navigationClient\r\n     */\r\n    setNavigationClient(navigationClient: INavigationClient): void {\r\n        this.navigationClient = navigationClient;\r\n    }\r\n\r\n    /**\r\n     * Returns the configuration object\r\n     */\r\n    getConfiguration(): BrowserConfiguration {\r\n        return this.config;\r\n    }\r\n\r\n    /**\r\n     * Generates a correlation id for a request if none is provided.\r\n     *\r\n     * @protected\r\n     * @param {?Partial<BaseAuthRequest>} [request]\r\n     * @returns {string}\r\n     */\r\n    protected getRequestCorrelationId(request?: Partial<BaseAuthRequest>): string {\r\n        if (request?.correlationId) {\r\n            return request.correlationId;\r\n        }\r\n\r\n        if (this.isBrowserEnvironment) {\r\n            return this.browserCrypto.createNewGuid();\r\n        }\r\n\r\n        /*\r\n         * Included for fallback for non-browser environments,\r\n         * and to ensure this method always returns a string.\r\n         */\r\n        return Constants.EMPTY_STRING;\r\n    }\r\n\r\n    // #endregion\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAoCH,IAAA,iBAAA,kBAAA,YAAA;AAgDI;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,IAAA,SAAA,iBAAA,CAAY,aAA4B,EAAA;AACpC;;;;AAIG;AACH,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;;QAE1D,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;;AAGzB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;;QAG1E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;;QAGtD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;;AAG5D,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;;AAGlC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;;AAGzC,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB;AAC9C,YAAA,IAAI,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AACpL,YAAA,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;;AAGpJ,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,6BAA6B,CAAC;AAEtK,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;AAGtE,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB;YAC3C,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC;AACtG,YAAA,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;AAG1E,QAAA,IAAM,kBAAkB,GAA2B;YAC/C,aAAa,EAAE,oBAAoB,CAAC,aAAa;YACjD,sBAAsB,EAAE,oBAAoB,CAAC,aAAa;AAC1D,YAAA,sBAAsB,EAAE,KAAK;AAC7B,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,qBAAqB,EAAE,KAAK;AAC5B,YAAA,yBAAyB,EAAE,IAAI;SAClC,CAAC;QACF,IAAI,CAAC,qBAAqB,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;QAGrI,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;QAEpG,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChG;AAED;;AAEG;AACG,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;;AACI,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;wBACvC,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;4BACvE,OAAO,CAAA,CAAA,YAAA,CAAA;AACV,yBAAA;wBAEK,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC;wBACzD,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,2BAA2B,CAAC,CAAC;wBAC/G,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;AAEpD,wBAAA,IAAA,CAAA,iBAAiB,EAAjB,OAAiB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;AAEb,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAA2B,OAAM,CAAA,CAAA,YAAA,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,4BAA4B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAA,CAAA;;wBAA9J,EAAK,CAAA,uBAAuB,GAAG,EAAA,CAAA,IAAA,EAA+H,CAAC;;;;AAE/J,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAC,CAAC,CAAC;;;6BAI5B,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAA5C,OAA4C,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3C,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2EAA2E,CAAC,CAAC;wBAC3F,8BAA8B,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC;AAC/H,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,CAAC,4BAA4B,EAAE,CAAA,CAAA;;AAAxD,wBAAA,EAAA,CAAA,IAAA,EAAwD,CAAC;wBACzD,8BAA8B,CAAC,cAAc,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;;;AAGnE,wBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;wBACxB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AAEtD,wBAAA,eAAe,CAAC,cAAc,CAAC,EAAC,iBAAiB,EAAA,iBAAA,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;;;;;AACtE,KAAA,CAAA;;AAID;;;;;;AAMG;IACG,iBAAqB,CAAA,SAAA,CAAA,qBAAA,GAA3B,UAA4B,IAAa,EAAA;;;;;AACrC,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;;AAEpD,gBAAA,YAAY,CAAC,wCAAwC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAExG,gBAAA,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAMrB,oBAAA,mBAAmB,GAAG,IAAI,IAAI,SAAS,CAAC,YAAY,CAAC;oBACvD,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AAC9D,oBAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACjC,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AACvF,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+EAA+E,CAAC,CAAC;AAE/F,wBAAA,OAAO,GAA8B,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;AACpF,wBAAA,gBAAgB,SAAsC,CAAC;wBAC3D,IAAI,OAAO,IAAI,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,EAAE;AACpJ,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;4BAC5E,YAAY,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACvT,4BAAA,gBAAgB,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;AAC3D,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;AACrE,4BAAA,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;AACzH,4BAAA,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;AAChE,4BAAA,gBAAgB,GAAG,cAAc,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACjE,yBAAA;AAED,wBAAA,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAC,MAAmC,EAAA;AACjE,4BAAA,IAAI,MAAM,EAAE;;AAGR,gCAAA,IAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,GAAG,KAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;AAC3E,gCAAA,IAAI,WAAW,EAAE;AACb,oCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACvF,oCAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAChF,iCAAA;AAAM,qCAAA;AACH,oCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC/F,oCAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;AACxF,iCAAA;AACJ,6BAAA;AACD,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AAErF,4BAAA,OAAO,MAAM,CAAC;AAClB,yBAAC,CAAC,CAAC,KAAK,CAAC,UAAC,CAAC,EAAA;;AAEP,4BAAA,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,gCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACnG,6BAAA;AAAM,iCAAA;AACH,gCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC3F,6BAAA;AACD,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AAErF,4BAAA,MAAM,CAAC,CAAC;AACZ,yBAAC,CAAC,CAAC;wBACH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;AAC5D,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4FAA4F,CAAC,CAAC;AACrH,qBAAA;AAED,oBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;AACnB,iBAAA;AACD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6DAA6D,CAAC,CAAC;AACnF,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;AACf,KAAA,CAAA;AAED;;;;;;;;AAQG;IACG,iBAAoB,CAAA,SAAA,CAAA,oBAAA,GAA1B,UAA2B,OAAwB,EAAA;;;;;AAEzC,gBAAA,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6BAA6B,EAAE,aAAa,CAAC,CAAC;AAClE,gBAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAG1D,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD,gBAAA,IAAI,UAAU,EAAE;AACZ,oBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjG,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzF,iBAAA;gBAID,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;oBACtD,YAAY,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;oBACrU,MAAM,GAAG,YAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAC,CAAY,EAAA;wBACnE,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AAC7C,4BAAA,KAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;4BACzC,IAAM,cAAc,GAAG,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxE,4BAAA,OAAO,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC/C,yBAAA;6BAAM,IAAI,CAAC,YAAY,4BAA4B,EAAE;AAClD,4BAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iHAAiH,CAAC,CAAC;4BACvI,IAAM,cAAc,GAAG,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxE,4BAAA,OAAO,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC/C,yBAAA;AACD,wBAAA,KAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,wBAAA,MAAM,CAAC,CAAC;AACZ,qBAAC,CAAC,CAAC;AACN,iBAAA;AAAM,qBAAA;oBACG,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxE,oBAAA,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACjD,iBAAA;AAED,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,KAAK,CAAC,UAAC,CAAC,EAAA;;AAElB,wBAAA,IAAI,UAAU,EAAE;AACZ,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACnG,yBAAA;AAAM,6BAAA;AACH,4BAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC3F,yBAAA;AACD,wBAAA,MAAM,CAAC,CAAC;AACZ,qBAAC,CAAC,CAAC,CAAA;;;AACN,KAAA,CAAA;;;AAMD;;;;;;AAMG;IACH,iBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,OAAqB,EAAA;QAAvC,IAqFC,KAAA,GAAA,IAAA,CAAA;QApFG,IAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC5D,QAAA,IAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAEvH,IAAI;YACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAC;AAC/D,YAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;;AAGD,QAAA,IAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAC/C,QAAA,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC9F,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtF,SAAA;AAED,QAAA,IAAI,MAAqC,CAAC;AAE1C,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;AAC5B,YAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ,EAAA;AAC7E,gBAAA,KAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACpD,kBAAkB,CAAC,cAAc,CAAC;AAC9B,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,cAAc,EAAE,IAAI;oBACpB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAChC,iBAAA,CAAC,CAAC;AACH,gBAAA,OAAO,QAAQ,CAAC;AACpB,aAAC,CAAC,CAAC,KAAK,CAAC,UAAC,CAAY,EAAA;gBAClB,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AAC7C,oBAAA,KAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;oBACzC,IAAM,WAAW,GAAG,KAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAClE,oBAAA,OAAO,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC5C,iBAAA;qBAAM,IAAI,CAAC,YAAY,4BAA4B,EAAE;AAClD,oBAAA,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8GAA8G,CAAC,CAAC;oBACpI,IAAM,WAAW,GAAG,KAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAClE,oBAAA,OAAO,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC5C,iBAAA;AACD,gBAAA,KAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,gBAAA,MAAM,CAAC,CAAC;AACZ,aAAC,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;YACH,IAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAClE,YAAA,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC9C,SAAA;AAED,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,UAAC,MAAM,EAAA;AAEtB;;AAEG;AACH,YAAA,IAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,GAAG,KAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;AAC3E,YAAA,IAAI,WAAW,EAAE;AACb,gBAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACvF,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC/F,aAAA;YAED,kBAAkB,CAAC,eAAe,CAAC;AAC/B,gBAAA,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;AAC1C,gBAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;AACrC,aAAA,CAAC,CAAC;YACH,kBAAkB,CAAC,cAAc,CAAC;AAC9B,gBAAA,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,MAAM,CAAC;AAClB,SAAC,CAAC,CAAC,KAAK,CAAC,UAAC,CAAY,EAAA;AAClB,YAAA,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,gBAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,qBAAqB,EAAE,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAChG,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACxF,aAAA;YAED,kBAAkB,CAAC,cAAc,CAAC;gBAC9B,SAAS,EAAE,CAAC,CAAC,SAAS;gBACtB,YAAY,EAAE,CAAC,CAAC,QAAQ;AACxB,gBAAA,OAAO,EAAE,KAAK;AACjB,aAAA,CAAC,CAAC;;AAEH,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,SAAC,CAAC,CAAC;KACN,CAAA;AAEO,IAAA,iBAAA,CAAA,SAAA,CAAA,kCAAkC,GAA1C,YAAA;QACI,IAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kCAAkC,CAAC;QACzF,IAAG,CAAC,WAAW,EAAE;YACb,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjF,WAAW,CAAC,SAAS,CAAC;AAClB,YAAA,qBAAqB,EAAE,CAAC;AAC3B,SAAA,CAAC,CAAC;KACN,CAAA;;;AAKD;;;;;;;;;;;;;;AAcG;IACG,iBAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,OAAyB,EAAA;;;;;;AAC/B,gBAAA,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AACtD,gBAAA,YAAY,yBACX,OAAO,CAAA,EAAA;;oBAEV,MAAM,EAAE,OAAO,CAAC,MAAM,EACtB,aAAa,EAAE,aAAa,EAAA,CAC/B,CAAC;AACF,gBAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC9D,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAChH,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAC;AACjC,oBAAA,qBAAqB,EAAE,CAAC;iBAC3B,CAAE,CAAA;gBACH,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBACtF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;AACvD,gBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,gBAAgB,EAAE,eAAe,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAI9F,gBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;AACjC,oBAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,UAAC,CAAY,EAAA;;wBAE/E,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AAC7C,4BAAA,KAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;4BACzC,IAAM,kBAAkB,GAAG,KAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACrF,4BAAA,OAAO,kBAAkB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AACxD,yBAAA;AACD,wBAAA,MAAM,CAAC,CAAC;AACZ,qBAAC,CAAC,CAAC;AACN,iBAAA;AAAM,qBAAA;oBACG,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACrF,oBAAA,MAAM,GAAG,kBAAkB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1D,iBAAA;AAED,gBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,IAAI,CAAC,UAAC,QAAQ,EAAA;;AACxB,wBAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5F,wBAAA,CAAA,EAAA,GAAA,KAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC;AACvC,4BAAA,eAAe,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM;AAC5C,4BAAA,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;yBACvC,CAAE,CAAA;AACH,wBAAA,CAAA,EAAA,GAAA,KAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAC;AACtC,4BAAA,OAAO,EAAE,IAAI;4BACb,cAAc,EAAE,QAAQ,CAAC,gBAAgB;4BACzC,SAAS,EAAE,QAAQ,CAAC,SAAS;yBAChC,CAAE,CAAA;AACH,wBAAA,OAAO,QAAQ,CAAC;AACpB,qBAAC,CAAC,CAAC,KAAK,CAAC,UAAC,CAAY,EAAA;;AAClB,wBAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC3F,wBAAA,CAAA,EAAA,GAAA,KAAI,CAAC,oBAAoB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAC;4BACtC,SAAS,EAAE,CAAC,CAAC,SAAS;4BACtB,YAAY,EAAE,CAAC,CAAC,QAAQ;AACxB,4BAAA,OAAO,EAAE,KAAK;yBACjB,CAAE,CAAA;AACH,wBAAA,MAAM,CAAC,CAAC;qBACX,CAAC,CAAC,OAAO,CAAC,YAAA;wBACP,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAC,KAAI,CAAC,kCAAkC,CAAC,CAAC;AAC7F,qBAAC,CAAC,CAAC,CAAA;;;AAEN,KAAA,CAAA;AAED;;;;;;;;;AASG;IACG,iBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAxB,UAAyB,OAAiC,EAAA;;;;;AAChD,gBAAA,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC5D,gBAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,aAAa,CAAC,CAAC;AAC9D,gBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,2BAA2B,EAAE,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9F,gBAAA,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;gBAE7H,IAAI;AACA,oBAAA,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,eAAe,EAAE;;AAEzC,wBAAA,MAAM,gBAAgB,CAAC,2CAA2C,EAAE,CAAC;AACxE,qBAAA;yBACI,IAAI,OAAO,CAAC,IAAI,EAAE;wBACb,gBAAiB,GAAA,OAAO,CAAC,IAAI,CAAC;wBAChC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,gBAAc,CAAC,CAAC;wBAChE,IAAI,CAAC,QAAQ,EAAE;4BACX,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2CAA2C,EAAE,aAAa,CAAC,CAAC;4BAChF,QAAQ,GAAG,IAAI,CAAC,uBAAuB,uBAChC,OAAO,CAAA,EAAA,EACV,aAAa,EAAA,aAAA,EACf,CAAA,CAAA;iCACG,IAAI,CAAC,UAAC,MAA4B,EAAA;AAC/B,gCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,6BAA6B,EAAE,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACrG,gCAAA,KAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,gBAAc,CAAC,CAAC;gCACpD,eAAe,CAAC,eAAe,CAAC;AAC5B,oCAAA,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;AAC1C,oCAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;AACrC,iCAAA,CAAC,CAAC;gCACH,eAAe,CAAC,cAAc,CAAC;AAC3B,oCAAA,OAAO,EAAE,IAAI;oCACb,cAAc,EAAE,MAAM,CAAC,gBAAgB;oCACvC,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,iCAAA,CAAC,CAAC;AACH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACD,KAAK,CAAC,UAAC,KAAgB,EAAA;AACpB,gCAAA,KAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,gBAAc,CAAC,CAAC;AACpD,gCAAA,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,6BAA6B,EAAE,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gCAC1G,eAAe,CAAC,cAAc,CAAC;oCAC3B,SAAS,EAAE,KAAK,CAAC,SAAS;oCAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oCAAA,OAAO,EAAE,KAAK;AACjB,iCAAA,CAAC,CAAC;AACH,gCAAA,MAAM,KAAK,CAAC;AAChB,6BAAC,CAAC,CAAC;4BACP,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,gBAAc,EAAE,QAAQ,CAAC,CAAC;AAC9D,yBAAA;AAAM,6BAAA;4BACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2CAA2C,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;4BACxF,eAAe,CAAC,kBAAkB,EAAE,CAAC;AACxC,yBAAA;AACD,wBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;AACnB,qBAAA;yBAAM,IAAI,OAAO,CAAC,eAAe,EAAE;wBAChC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC,EAAE;AACrD,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,UAAC,CAAY,EAAA;;oCAE1G,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AAC7C,wCAAA,KAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;AAC5C,qCAAA;AACD,oCAAA,MAAM,CAAC,CAAC;AACZ,iCAAC,CAAC,CAAC,CAAA;AACN,yBAAA;AAAM,6BAAA;AACH,4BAAA,MAAM,gBAAgB,CAAC,iDAAiD,EAAE,CAAC;AAC9E,yBAAA;AACJ,qBAAA;AAAM,yBAAA;AACH,wBAAA,MAAM,gBAAgB,CAAC,4CAA4C,EAAE,CAAC;AACzE,qBAAA;AAEJ,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,6BAA6B,EAAE,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;oBACtG,eAAe,CAAC,cAAc,CAAC;wBAC3B,SAAS,EAAE,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS;wBAC7D,YAAY,EAAE,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS;AAC/D,wBAAA,OAAO,EAAE,KAAK;AACjB,qBAAA,CAAC,CAAC;AACH,oBAAA,MAAM,CAAC,CAAC;AACX,iBAAA;;;;AACJ,KAAA,CAAA;AAED;;;;AAIG;IACW,iBAAuB,CAAA,SAAA,CAAA,uBAAA,GAArC,UAAsC,OAAiC,EAAA;;;;;;;;wBACnE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC3E,wBAAA,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACpJ,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kCAAkC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAC;AAC/C,4BAAA,qBAAqB,EAAE,CAAC;yBAC3B,CAAE,CAAA;wBACH,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;wBAChF,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC1D,OAAM,CAAA,CAAA,YAAA,oBAAoB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ,EAAA;;AACrF,gCAAA,CAAA,EAAA,GAAA,KAAI,CAAC,kCAAkC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAC;AACpD,oCAAA,OAAO,EAAE,IAAI;oCACb,SAAS,EAAE,QAAQ,CAAC,SAAS;oCAC7B,cAAc,EAAE,QAAQ,CAAC,gBAAgB;oCACzC,SAAS,EAAE,QAAQ,CAAC,SAAS;iCAChC,CAAE,CAAA;AACH,gCAAA,OAAO,QAAQ,CAAC;AACpB,6BAAC,CAAC,CAAC,KAAK,CAAC,UAAC,iBAA4B,EAAA;;AAClC,gCAAA,CAAA,EAAA,GAAA,KAAI,CAAC,kCAAkC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,cAAc,CAAC;oCACpD,SAAS,EAAE,iBAAiB,CAAC,SAAS;oCACtC,YAAY,EAAE,iBAAiB,CAAC,QAAQ;AACxC,oCAAA,OAAO,EAAE,KAAK;iCACjB,CAAE,CAAA;AACH,gCAAA,MAAM,iBAAiB,CAAC;6BAC3B,CAAC,CAAC,OAAO,CAAC,YAAA;gCACP,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAC,KAAI,CAAC,kCAAkC,CAAC,CAAC;AAC7F,6BAAC,CAAC,CAAA,CAAA;;AAjBI,wBAAA,iBAAiB,GAAG,EAiBxB,CAAA,IAAA,EAAA,CAAA;AACF,wBAAA,OAAA,CAAA,CAAA,aAAO,iBAAiB,CAAC,CAAA;;;;AAC5B,KAAA,CAAA;AAED;;;;;;AAMG;AACa,IAAA,iBAAA,CAAA,SAAA,CAAA,qBAAqB,GAArC,UACI,iBAAoC,EACpC,aAAsC,EACtC,aAA4B,EAAA;;;AAE5B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;gBACjH,QAAO,aAAa,CAAC,iBAAiB;oBAClC,KAAK,iBAAiB,CAAC,OAAO,CAAC;oBAC/B,KAAK,iBAAiB,CAAC,WAAW,CAAC;oBACnC,KAAK,iBAAiB,CAAC,0BAA0B;AAC7C,wBAAA,OAAA,CAAA,CAAA,aAAO,iBAAiB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAA;AACzD,oBAAA;AACI,wBAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AAC1D,iBAAA;;;;AACJ,KAAA,CAAA;AAED;;;;;AAKG;AACa,IAAA,iBAAA,CAAA,SAAA,CAAA,0BAA0B,GAA1C,UACI,aAAsC,EACtC,aAA4B,EAAA;;;;AAE5B,gBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;gBACtH,QAAO,aAAa,CAAC,iBAAiB;oBAClC,KAAK,iBAAiB,CAAC,OAAO,CAAC;oBAC/B,KAAK,iBAAiB,CAAC,0BAA0B,CAAC;oBAClD,KAAK,iBAAiB,CAAC,YAAY,CAAC;oBACpC,KAAK,iBAAiB,CAAC,sBAAsB;wBACnC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAExF,wBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AACvH,wBAAA,OAAA,CAAA,CAAA,aAAO,mBAAmB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAA;AAC3D,oBAAA;AACI,wBAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AAC1D,iBAAA;;;;AACJ,KAAA,CAAA;AAED;;;;AAIG;IACa,iBAA0B,CAAA,SAAA,CAAA,0BAAA,GAA1C,UACI,OAAgC,EAAA;;;;AAEhC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;gBAE1G,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAEhF,gBAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAChH,gBAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAA;;;AACnD,KAAA,CAAA;;;AAMD;;;;AAIG;IACG,iBAAM,CAAA,SAAA,CAAA,MAAA,GAAZ,UAAa,aAAiC,EAAA;;;;AACpC,gBAAA,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;gBAClE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kGAAkG,EAAE,aAAa,CAAC,CAAC;gBACvI,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,cAAc,CAAA,QAAA,CAAA,EACtB,aAAa,EAAA,aAAA,EAAA,EACV,aAAa,CAAA,CAClB,CAAC,CAAA;;;AACN,KAAA,CAAA;AAED;;;;AAIG;IACG,iBAAc,CAAA,SAAA,CAAA,cAAA,GAApB,UAAqB,aAAiC,EAAA;;;;AAC5C,gBAAA,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;AAClE,gBAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AAE1D,gBAAA,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;AAChE,gBAAA,OAAA,CAAA,CAAA,aAAO,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;;;AAC/C,KAAA,CAAA;AAED;;;AAGG;IACH,iBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,aAAsC,EAAA;QAC9C,IAAI;YACA,IAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC7D,IAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;AAC1D,YAAA,OAAO,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC5C,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;KACJ,CAAA;;;AAMD;;;;;AAKG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;AACI,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC7C,QAAA,OAAO,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC;KAChF,CAAA;AAED;;;;;;;AAOG;IACH,iBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,QAAgB,EAAA;AACjC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,EAAE;AACX,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;AAClE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAC,QAAQ,EAAA,QAAA,EAAC,CAAC,CAAC;AACzE,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kEAAkE,CAAC,CAAC;YACxF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,wEAAyE,GAAA,QAAU,CAAC,CAAC;AAC5G,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iEAAiE,CAAC,CAAC;AACvF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;IACH,iBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,aAAqB,EAAA;AACpC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;AACrE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAC,aAAa,EAAA,aAAA,EAAC,CAAC,CAAC;AAC9E,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qEAAqE,CAAC,CAAC;YAC3F,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,2EAA4E,GAAA,aAAe,CAAC,CAAC;AACpH,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC;AACrF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ,CAAA;AAED;;;;;;AAMG;IACH,iBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,cAAsB,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,EAAE;AACjB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;AACvE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAC,cAAc,EAAA,cAAA,EAAC,CAAC,CAAC;AAC/E,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;YAC7F,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,6EAA8E,GAAA,cAAgB,CAAC,CAAC;AACvH,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gEAAgE,CAAC,CAAC;AACtF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,iBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,OAA2B,EAAA;AACxC,QAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;KACjD,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACI,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;KACjD,CAAA;;;AAMD;;;;;;AAMG;AACO,IAAA,iBAAA,CAAA,SAAA,CAAA,gCAAgC,GAA1C,UAA2C,eAAgC,EAAE,wBAAwC,EAAA;AAAxC,QAAA,IAAA,wBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,wBAAwC,GAAA,IAAA,CAAA,EAAA;AACjH,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;;AAEhE,QAAA,YAAY,CAAC,0BAA0B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;;AAGnE,QAAA,YAAY,CAAC,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;;QAG9F,YAAY,CAAC,0BAA0B,EAAE,CAAC;;QAG1C,YAAY,CAAC,yBAAyB,EAAE,CAAC;;AAGzC,QAAA,YAAY,CAAC,wCAAwC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;;AAG9G,QAAA,IAAI,eAAe,KAAK,eAAe,CAAC,QAAQ;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,oBAAoB,CAAC,aAAa;AACtE,YAAA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;AAC3C,YAAA,MAAM,6BAA6B,CAAC,sCAAsC,EAAE,CAAC;AAChF,SAAA;QAED,IAAI,eAAe,KAAK,eAAe,CAAC,QAAQ,IAAI,eAAe,KAAK,eAAe,CAAC,KAAK,EAAE;AAC3F,YAAA,IAAI,CAAC,2BAA2B,CAAC,wBAAwB,CAAC,CAAC;AAC9D,SAAA;KACJ,CAAA;AAED;;;;;AAKG;IACO,iBAA2B,CAAA,SAAA,CAAA,2BAAA,GAArC,UAAsC,wBAAiC,EAAA;AACnE,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gEAAgE,CAAC,CAAC;;QAEtF,YAAY,CAAC,0BAA0B,EAAE,CAAC;;AAG1C,QAAA,IAAI,wBAAwB,EAAE;AAC1B,YAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AACtD,SAAA;KACJ,CAAA;AAED;;;AAGG;AACa,IAAA,iBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlC,UAAmC,OAAwD,EAAE,KAAY,EAAE,SAAkB,EAAA;;;;AACzH,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC/C,gBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AAC/B,oBAAA,MAAM,gBAAgB,CAAC,yCAAyC,EAAE,CAAC;AACtE,iBAAA;gBAEK,YAAY,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAE7T,gBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAA;;;AAC7C,KAAA,CAAA;AAED;;;AAGG;AACO,IAAA,iBAAA,CAAA,SAAA,CAAA,YAAY,GAAtB,UAAuB,OAA0D,EAAE,SAAkB,EAAA;AACjG,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,oBAAoB,CAAC,EAAE;AAC/H,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;AACrF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,QAAQ,OAAO,CAAC,MAAM;gBAClB,KAAK,WAAW,CAAC,IAAI,CAAC;gBACtB,KAAK,WAAW,CAAC,OAAO,CAAC;gBACzB,KAAK,WAAW,CAAC,KAAK;AAClB,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;oBACzE,MAAM;AACV,gBAAA;oBACI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAA0B,GAAA,OAAO,CAAC,MAAM,GAAsD,sDAAA,CAAC,CAAC;AAClH,oBAAA,OAAO,KAAK,CAAC;AACpB,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;AACrF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACO,iBAAkB,CAAA,SAAA,CAAA,kBAAA,GAA5B,UAA6B,OAA0D,EAAA;QACnF,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExI,QAAA,OAAO,OAAO,IAAI,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;KACnD,CAAA;AAED;;;AAGG;IACO,iBAAiB,CAAA,SAAA,CAAA,iBAAA,GAA3B,UAA4B,aAAsB,EAAA;AAC9C,QAAA,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;KACxO,CAAA;AAED;;;AAGG;IACO,iBAAoB,CAAA,SAAA,CAAA,oBAAA,GAA9B,UAA+B,aAAsB,EAAA;AACjD,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;KAC3O,CAAA;AAED;;;AAGG;IACO,iBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAlC,UAAmC,aAAsB,EAAA;QACrD,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;KAChQ,CAAA;AAED;;AAEG;IACO,iBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAjC,UAAkC,aAAsB,EAAA;AACpD,QAAA,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;KAClN,CAAA;AAED;;AAEG;IACO,iBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAnC,UAAoC,aAAsB,EAAA;AACtD,QAAA,OAAO,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;KACpN,CAAA;AAED;;AAEG;IACO,iBAA0B,CAAA,SAAA,CAAA,0BAAA,GAApC,UAAqC,aAAsB,EAAA;AACvD,QAAA,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;KAC/O,CAAA;AAED;;;AAGG;IACH,iBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,QAA+B,EAAA;QAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KACvD,CAAA;AAED;;;AAGG;IACH,iBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,UAAkB,EAAA;AAClC,QAAA,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AAED;;;;;AAKG;IACH,iBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,QAAqC,EAAA;QACxD,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;KAClE,CAAA;AAED;;;;;AAKG;IACH,iBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,UAAkB,EAAA;QACxC,OAAO,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;KACvE,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,0BAA0B,GAA1B,YAAA;AACI,QAAA,IAAI,CAAC,YAAY,CAAC,0BAA0B,EAAE,CAAC;KAClD,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,2BAA2B,GAA3B,YAAA;AACI,QAAA,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,CAAC;KACnD,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;QACI,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QACI,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB,CAAA;AAED;;;AAGG;IACH,iBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAAc,EAAA;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB,CAAA;AAED;;;;AAIG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,UAAyB,GAAe,EAAE,OAAe,EAAA;;QAErD,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACxD,CAAA;AAED;;;AAGG;IACH,iBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,gBAAmC,EAAA;AACnD,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC5C,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;QACI,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB,CAAA;AAED;;;;;;AAMG;IACO,iBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAjC,UAAkC,OAAkC,EAAA;AAChE,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,aAAa,EAAE;YACxB,OAAO,OAAO,CAAC,aAAa,CAAC;AAChC,SAAA;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AAC7C,SAAA;AAED;;;AAGG;QACH,OAAO,SAAS,CAAC,YAAY,CAAC;KACjC,CAAA;IAGL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}