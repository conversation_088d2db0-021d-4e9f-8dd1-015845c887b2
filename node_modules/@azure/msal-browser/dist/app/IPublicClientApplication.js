/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
import { BrowserConfigurationAuthError } from '../error/BrowserConfigurationAuthError.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var stubbedPublicClientApplication = {
    initialize: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    acquireTokenPopup: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    acquireTokenRedirect: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    acquireTokenSilent: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    acquireTokenByCode: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    getAllAccounts: function () {
        return [];
    },
    getAccountByHomeId: function () {
        return null;
    },
    getAccountByUsername: function () {
        return null;
    },
    getAccountByLocalId: function () {
        return null;
    },
    handleRedirectPromise: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    loginPopup: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    loginRedirect: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    logout: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    logoutRedirect: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    logoutPopup: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    ssoSilent: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
    addEventCallback: function () {
        return null;
    },
    removeEventCallback: function () {
        return;
    },
    addPerformanceCallback: function () {
        return "";
    },
    removePerformanceCallback: function () {
        return false;
    },
    enableAccountStorageEvents: function () {
        return;
    },
    disableAccountStorageEvents: function () {
        return;
    },
    getTokenCache: function () {
        throw BrowserConfigurationAuthError.createStubPcaInstanceCalledError();
    },
    getLogger: function () {
        throw BrowserConfigurationAuthError.createStubPcaInstanceCalledError();
    },
    setLogger: function () {
        return;
    },
    setActiveAccount: function () {
        return;
    },
    getActiveAccount: function () {
        return null;
    },
    initializeWrapperLibrary: function () {
        return;
    },
    setNavigationClient: function () {
        return;
    },
    getConfiguration: function () {
        throw BrowserConfigurationAuthError.createStubPcaInstanceCalledError();
    },
    hydrateCache: function () {
        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());
    },
};

export { stubbedPublicClientApplication };
//# sourceMappingURL=IPublicClientApplication.js.map
