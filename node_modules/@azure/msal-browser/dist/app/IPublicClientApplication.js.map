{"version": 3, "file": "IPublicClientApplication.js", "sources": ["../../src/app/IPublicClientApplication.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthenticationResult, AccountInfo, Logger, PerformanceCallbackFunction } from \"@azure/msal-common\";\r\nimport { RedirectRequest } from \"../request/RedirectRequest\";\r\nimport { PopupRequest } from \"../request/PopupRequest\";\r\nimport { SilentRequest } from \"../request/SilentRequest\";\r\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\r\nimport { EndSessionRequest } from \"../request/EndSessionRequest\";\r\nimport { BrowserConfigurationAuthError } from \"../error/BrowserConfigurationAuthError\";\r\nimport { WrapperSKU } from \"../utils/BrowserConstants\";\r\nimport { INavigationClient } from \"../navigation/INavigationClient\";\r\nimport { EndSessionPopupRequest } from \"../request/EndSessionPopupRequest\";\r\nimport { ITokenCache } from \"../cache/ITokenCache\";\r\nimport { AuthorizationCodeRequest } from \"../request/AuthorizationCodeRequest\";\r\nimport { BrowserConfiguration } from \"../config/Configuration\";\r\n\r\nexport interface IPublicClientApplication {\r\n    initialize(): Promise<void>;\r\n    acquireTokenPopup(request: PopupRequest): Promise<AuthenticationResult>;\r\n    acquireTokenRedirect(request: RedirectRequest): Promise<void>;\r\n    acquireTokenSilent(silentRequest: SilentRequest): Promise<AuthenticationResult>;\r\n    acquireTokenByCode(request: AuthorizationCodeRequest): Promise<AuthenticationResult>;\r\n    addEventCallback(callback: Function): string | null;\r\n    removeEventCallback(callbackId: string): void;\r\n    addPerformanceCallback(callback: PerformanceCallbackFunction): string;\r\n    removePerformanceCallback(callbackId: string): boolean;\r\n    enableAccountStorageEvents(): void;\r\n    disableAccountStorageEvents(): void;\r\n    getAccountByHomeId(homeAccountId: string): AccountInfo | null;\r\n    getAccountByLocalId(localId: string): AccountInfo | null;\r\n    getAccountByUsername(userName: string): AccountInfo | null;\r\n    getAllAccounts(): AccountInfo[];\r\n    handleRedirectPromise(hash?: string): Promise<AuthenticationResult | null>;\r\n    loginPopup(request?: PopupRequest): Promise<AuthenticationResult>;\r\n    loginRedirect(request?: RedirectRequest): Promise<void>;\r\n    logout(logoutRequest?: EndSessionRequest): Promise<void>;\r\n    logoutRedirect(logoutRequest?: EndSessionRequest): Promise<void>;\r\n    logoutPopup(logoutRequest?: EndSessionPopupRequest): Promise<void>;\r\n    ssoSilent(request: SsoSilentRequest): Promise<AuthenticationResult>;\r\n    getTokenCache(): ITokenCache;\r\n    getLogger(): Logger;\r\n    setLogger(logger: Logger): void;\r\n    setActiveAccount(account: AccountInfo | null): void;\r\n    getActiveAccount(): AccountInfo | null;\r\n    initializeWrapperLibrary(sku: WrapperSKU, version: string): void;\r\n    setNavigationClient(navigationClient: INavigationClient): void;\r\n    getConfiguration(): BrowserConfiguration;\r\n    hydrateCache(\r\n        result: AuthenticationResult,\r\n        request: SilentRequest\r\n        | SsoSilentRequest\r\n        | RedirectRequest\r\n        | PopupRequest\r\n    ): Promise<void>;\r\n}\r\n\r\nexport const stubbedPublicClientApplication: IPublicClientApplication = {\r\n    initialize: () => {\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\r\n    },\r\n    acquireTokenPopup: () => {\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\r\n    },\r\n    acquireTokenRedirect: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\t\r\n    acquireTokenSilent: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\r\n    acquireTokenByCode: () => {\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\r\n    },\r\n    getAllAccounts: () => {\r\n        return [];\t\r\n    },\t\r\n    getAccountByHomeId: () => {\r\n        return null;\r\n    },\r\n    getAccountByUsername: () => {\t\r\n        return null;\t\r\n    },\t\r\n    getAccountByLocalId: () => {\r\n        return null;\r\n    },\r\n    handleRedirectPromise: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\t\r\n    loginPopup: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\t\r\n    loginRedirect: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\t\r\n    logout: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\t\r\n    logoutRedirect: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\r\n    logoutPopup: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\r\n    ssoSilent: () => {\t\r\n        return Promise.reject(BrowserConfigurationAuthError.createStubPcaInstanceCalledError());\t\r\n    },\r\n    addEventCallback: () => {\r\n        return null;\r\n    },\r\n    removeEventCallback: () => {\r\n        return;\r\n    },\r\n    addPerformanceCallback: () => {\r\n        return \"\";\r\n    },\r\n    removePerformanceCallback: () => {\r\n        return false;\r\n    },\r\n    enableAccountStorageEvents: () => {\r\n        return;\r\n    },\r\n    disableAccountStorageEvents: () => {\r\n        return;\r\n    },\r\n    getTokenCache: () => {\r\n        throw BrowserConfigurationAuthError.createStubPcaInstanceCalledError();\r\n    },\r\n    getLogger: () => {\r\n        throw BrowserConfigurationAuthError.createStubPcaInstanceCalledError();\r\n    },\r\n    setLogger: () => {\r\n        return;\r\n    },\r\n    setActiveAccount: () => {\r\n        return;\r\n    },\r\n    getActiveAccount: () => {\r\n        return null;\r\n    },\r\n    initializeWrapperLibrary: () => {\r\n        return;\r\n    },\r\n    setNavigationClient: () => {\r\n        return;\r\n    },\r\n    getConfiguration: () => {\r\n        throw BrowserConfigurationAuthError.createStubPcaInstanceCalledError();\r\n    },\r\n    hydrateCache: () => {\r\n        return Promise.reject(\r\n            BrowserConfigurationAuthError.createStubPcaInstanceCalledError()\r\n        );\r\n    },\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAwDU,IAAA,8BAA8B,GAA6B;AACpE,IAAA,UAAU,EAAE,YAAA;QACR,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,iBAAiB,EAAE,YAAA;QACf,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,oBAAoB,EAAE,YAAA;QAClB,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,kBAAkB,EAAE,YAAA;QAChB,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,kBAAkB,EAAE,YAAA;QAChB,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,cAAc,EAAE,YAAA;AACZ,QAAA,OAAO,EAAE,CAAC;KACb;AACD,IAAA,kBAAkB,EAAE,YAAA;AAChB,QAAA,OAAO,IAAI,CAAC;KACf;AACD,IAAA,oBAAoB,EAAE,YAAA;AAClB,QAAA,OAAO,IAAI,CAAC;KACf;AACD,IAAA,mBAAmB,EAAE,YAAA;AACjB,QAAA,OAAO,IAAI,CAAC;KACf;AACD,IAAA,qBAAqB,EAAE,YAAA;QACnB,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,UAAU,EAAE,YAAA;QACR,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,aAAa,EAAE,YAAA;QACX,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,MAAM,EAAE,YAAA;QACJ,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,cAAc,EAAE,YAAA;QACZ,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,WAAW,EAAE,YAAA;QACT,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,SAAS,EAAE,YAAA;QACP,OAAO,OAAO,CAAC,MAAM,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,CAAC,CAAC;KAC3F;AACD,IAAA,gBAAgB,EAAE,YAAA;AACd,QAAA,OAAO,IAAI,CAAC;KACf;AACD,IAAA,mBAAmB,EAAE,YAAA;QACjB,OAAO;KACV;AACD,IAAA,sBAAsB,EAAE,YAAA;AACpB,QAAA,OAAO,EAAE,CAAC;KACb;AACD,IAAA,yBAAyB,EAAE,YAAA;AACvB,QAAA,OAAO,KAAK,CAAC;KAChB;AACD,IAAA,0BAA0B,EAAE,YAAA;QACxB,OAAO;KACV;AACD,IAAA,2BAA2B,EAAE,YAAA;QACzB,OAAO;KACV;AACD,IAAA,aAAa,EAAE,YAAA;AACX,QAAA,MAAM,6BAA6B,CAAC,gCAAgC,EAAE,CAAC;KAC1E;AACD,IAAA,SAAS,EAAE,YAAA;AACP,QAAA,MAAM,6BAA6B,CAAC,gCAAgC,EAAE,CAAC;KAC1E;AACD,IAAA,SAAS,EAAE,YAAA;QACP,OAAO;KACV;AACD,IAAA,gBAAgB,EAAE,YAAA;QACd,OAAO;KACV;AACD,IAAA,gBAAgB,EAAE,YAAA;AACd,QAAA,OAAO,IAAI,CAAC;KACf;AACD,IAAA,wBAAwB,EAAE,YAAA;QACtB,OAAO;KACV;AACD,IAAA,mBAAmB,EAAE,YAAA;QACjB,OAAO;KACV;AACD,IAAA,gBAAgB,EAAE,YAAA;AACd,QAAA,MAAM,6BAA6B,CAAC,gCAAgC,EAAE,CAAC;KAC1E;AACD,IAAA,YAAY,EAAE,YAAA;QACV,OAAO,OAAO,CAAC,MAAM,CACjB,6BAA6B,CAAC,gCAAgC,EAAE,CACnE,CAAC;KACL;;;;;"}