{"version": 3, "file": "Base64Encode.js", "sources": ["../../src/encode/Base64Encode.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"@azure/msal-common\";\r\nimport { BrowserStringUtils } from \"../utils/BrowserStringUtils\";\r\n\r\n/**\r\n * Class which exposes APIs to encode plaintext to base64 encoded string. See here for implementation details:\r\n * https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#Solution_2_%E2%80%93_JavaScript's_UTF-16_%3E_UTF-8_%3E_base64\r\n */\r\nexport class Base64Encode {\r\n    \r\n    /**\r\n     * Returns URL Safe b64 encoded string from a plaintext string.\r\n     * @param input \r\n     */\r\n    urlEncode(input: string): string {\r\n        return encodeURIComponent(this.encode(input)\r\n            .replace(/=/g, Constants.EMPTY_STRING)\r\n            .replace(/\\+/g, \"-\")\r\n            .replace(/\\//g, \"_\"));\r\n    }\r\n\r\n    /**\r\n     * Returns URL Safe b64 encoded string from an int8Array.\r\n     * @param inputArr \r\n     */\r\n    urlEncodeArr(inputArr: Uint8Array): string {\r\n        return this.base64EncArr(inputArr)\r\n            .replace(/=/g, Constants.EMPTY_STRING)\r\n            .replace(/\\+/g, \"-\")\r\n            .replace(/\\//g, \"_\");\r\n    }\r\n\r\n    /**\r\n     * Returns b64 encoded string from plaintext string.\r\n     * @param input \r\n     */\r\n    encode(input: string): string {\r\n        const inputUtf8Arr = BrowserStringUtils.stringToUtf8Arr(input);\r\n        return this.base64EncArr(inputUtf8Arr);\r\n    }\r\n\r\n    /**\r\n     * Base64 encode byte array\r\n     * @param aBytes \r\n     */\r\n    private base64EncArr(aBytes: Uint8Array): string {  \r\n        const eqLen = (3 - (aBytes.length % 3)) % 3;\r\n        let sB64Enc = Constants.EMPTY_STRING;\r\n      \r\n        for (let nMod3, nLen = aBytes.length, nUint24 = 0, nIdx = 0; nIdx < nLen; nIdx++) {\r\n            nMod3 = nIdx % 3;\r\n            /* Uncomment the following line in order to split the output in lines 76-character long: */\r\n            /*\r\n             *if (nIdx > 0 && (nIdx * 4 / 3) % 76 === 0) { sB64Enc += \"\\r\\n\"; }\r\n             */\r\n            nUint24 |= aBytes[nIdx] << (16 >>> nMod3 & 24);\r\n            if (nMod3 === 2 || aBytes.length - nIdx === 1) {\r\n                sB64Enc += String.fromCharCode(\r\n                    this.uint6ToB64(nUint24 >>> 18 & 63), \r\n                    this.uint6ToB64(nUint24 >>> 12 & 63), \r\n                    this.uint6ToB64(nUint24 >>> 6 & 63), \r\n                    this.uint6ToB64(nUint24 & 63)\r\n                );\r\n                nUint24 = 0;\r\n            }\r\n        }\r\n\r\n        return  eqLen === 0 ? sB64Enc : sB64Enc.substring(0, sB64Enc.length - eqLen) + (eqLen === 1 ? \"=\" : \"==\");\r\n    }\r\n\r\n    /**\r\n     * Base64 string to array encoding helper\r\n     * @param nUint6 \r\n     */\r\n    private uint6ToB64 (nUint6: number): number {\r\n        return nUint6 < 26 ?\r\n            nUint6 + 65\r\n            : nUint6 < 52 ?\r\n                nUint6 + 71\r\n                : nUint6 < 62 ?\r\n                    nUint6 - 4\r\n                    : nUint6 === 62 ?\r\n                        43\r\n                        : nUint6 === 63 ?\r\n                            47\r\n                            :\r\n                            65;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAKH;;;AAGG;AACH,IAAA,YAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,YAAA,GAAA;KAgFC;AA9EG;;;AAGG;IACH,YAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,KAAa,EAAA;AACnB,QAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AACvC,aAAA,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC;AACrC,aAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACnB,aAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;KAC7B,CAAA;AAED;;;AAGG;IACH,YAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,QAAoB,EAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC7B,aAAA,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC;AACrC,aAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACnB,aAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAC5B,CAAA;AAED;;;AAGG;IACH,YAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,KAAa,EAAA;QAChB,IAAM,YAAY,GAAG,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC/D,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;KAC1C,CAAA;AAED;;;AAGG;IACK,YAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,MAAkB,EAAA;AACnC,QAAA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAA,IAAI,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC;QAErC,KAAK,IAAI,KAAK,GAAA,KAAA,CAAA,EAAE,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE;AAC9E,YAAA,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;;AAEjB;;AAEG;AACH,YAAA,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,KAAK,GAAG,EAAE,CAAC,CAAC;YAC/C,IAAI,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE;gBAC3C,OAAO,IAAI,MAAM,CAAC,YAAY,CAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE,GAAG,EAAE,CAAC,EACpC,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE,GAAG,EAAE,CAAC,EACpC,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,GAAG,EAAE,CAAC,EACnC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC,CAChC,CAAC;gBACF,OAAO,GAAG,CAAC,CAAC;AACf,aAAA;AACJ,SAAA;AAED,QAAA,OAAQ,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;KAC7G,CAAA;AAED;;;AAGG;IACK,YAAU,CAAA,SAAA,CAAA,UAAA,GAAlB,UAAoB,MAAc,EAAA;AAC9B,QAAA,OAAO,MAAM,GAAG,EAAE;AACd,YAAA,MAAM,GAAG,EAAE;AACX,cAAE,MAAM,GAAG,EAAE;AACT,gBAAA,MAAM,GAAG,EAAE;AACX,kBAAE,MAAM,GAAG,EAAE;AACT,oBAAA,MAAM,GAAG,CAAC;AACV,sBAAE,MAAM,KAAK,EAAE;wBACX,EAAE;AACF,0BAAE,MAAM,KAAK,EAAE;4BACX,EAAE;;AAEF,gCAAA,EAAE,CAAC;KAC1B,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA;;;;"}