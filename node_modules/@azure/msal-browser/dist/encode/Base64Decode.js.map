{"version": 3, "file": "Base64Decode.js", "sources": ["../../src/encode/Base64Decode.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"@azure/msal-common\";\r\nimport { BrowserStringUtils } from \"../utils/BrowserStringUtils\";\r\n\r\n/**\r\n * Class which exposes APIs to decode base64 strings to plaintext. See here for implementation details:\r\n * https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#Solution_2_%E2%80%93_JavaScript's_UTF-16_%3E_UTF-8_%3E_base64\r\n */\r\nexport class Base64Decode {\r\n\r\n    /**\r\n     * Returns a URL-safe plaintext decoded string from b64 encoded input.\r\n     * @param input \r\n     */\r\n    decode(input: string): string {\r\n        let encodedString = input.replace(/-/g, \"+\").replace(/_/g, \"/\");\r\n        switch (encodedString.length % 4) {\r\n            case 0:\r\n                break;\r\n            case 2:\r\n                encodedString += \"==\";\r\n                break;\r\n            case 3:\r\n                encodedString += \"=\";\r\n                break;\r\n            default:\r\n                throw new Error(\"Invalid base64 string\");\r\n        }\r\n\r\n        const inputUtf8Arr = this.base64DecToArr(encodedString);\r\n        return BrowserStringUtils.utf8ArrToString(inputUtf8Arr);\r\n    }\r\n\r\n    /**\r\n     * Decodes base64 into Uint8Array\r\n     * @param base64String \r\n     * @param nBlockSize \r\n     */\r\n    private base64DecToArr(base64String: string, nBlockSize?: number): Uint8Array {\r\n        const sB64Enc = base64String.replace(/[^A-Za-z0-9\\+\\/]/g, Constants.EMPTY_STRING);\r\n        const nInLen = sB64Enc.length;\r\n        const nOutLen = nBlockSize ? Math.ceil((nInLen * 3 + 1 >>> 2) / nBlockSize) * nBlockSize : nInLen * 3 + 1 >>> 2;\r\n        const aBytes = new Uint8Array(nOutLen);\r\n\r\n        for (let nMod3, nMod4, nUint24 = 0, nOutIdx = 0, nInIdx = 0; nInIdx < nInLen; nInIdx++) {\r\n            nMod4 = nInIdx & 3;\r\n            nUint24 |= this.b64ToUint6(sB64Enc.charCodeAt(nInIdx)) << 18 - 6 * nMod4;\r\n            if (nMod4 === 3 || nInLen - nInIdx === 1) {\r\n                for (nMod3 = 0; nMod3 < 3 && nOutIdx < nOutLen; nMod3++, nOutIdx++) {\r\n                    aBytes[nOutIdx] = nUint24 >>> (16 >>> nMod3 & 24) & 255;\r\n                }\r\n                nUint24 = 0;\r\n            }\r\n        }\r\n\r\n        return aBytes;\r\n    }\r\n\r\n    /**\r\n     * Base64 string to array decoding helper\r\n     * @param charNum \r\n     */\r\n    private b64ToUint6(charNum: number): number {\r\n        return charNum > 64 && charNum < 91 ?\r\n            charNum - 65\r\n            : charNum > 96 && charNum < 123 ? \r\n                charNum - 71\r\n                : charNum > 47 && charNum < 58 ?\r\n                    charNum + 4\r\n                    : charNum === 43 ?\r\n                        62\r\n                        : charNum === 47 ?\r\n                            63\r\n                            :\r\n                            0;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAKH;;;AAGG;AACH,IAAA,YAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,YAAA,GAAA;KAoEC;AAlEG;;;AAGG;IACH,YAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,KAAa,EAAA;AAChB,QAAA,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAChE,QAAA,QAAQ,aAAa,CAAC,MAAM,GAAG,CAAC;AAC5B,YAAA,KAAK,CAAC;gBACF,MAAM;AACV,YAAA,KAAK,CAAC;gBACF,aAAa,IAAI,IAAI,CAAC;gBACtB,MAAM;AACV,YAAA,KAAK,CAAC;gBACF,aAAa,IAAI,GAAG,CAAC;gBACrB,MAAM;AACV,YAAA;AACI,gBAAA,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAChD,SAAA;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AACxD,QAAA,OAAO,kBAAkB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;KAC3D,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,UAAuB,YAAoB,EAAE,UAAmB,EAAA;AAC5D,QAAA,IAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,mBAAmB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;AAClF,QAAA,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC9B,QAAA,IAAM,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChH,QAAA,IAAM,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QAEvC,KAAK,IAAI,KAAK,GAAA,KAAA,CAAA,EAAE,KAAK,SAAA,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,EAAE;AACpF,YAAA,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;AACnB,YAAA,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;YACzE,IAAI,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE;AACtC,gBAAA,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,OAAO,GAAG,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE;AAChE,oBAAA,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,MAAM,EAAE,KAAK,KAAK,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;AAC3D,iBAAA;gBACD,OAAO,GAAG,CAAC,CAAC;AACf,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;AAGG;IACK,YAAU,CAAA,SAAA,CAAA,UAAA,GAAlB,UAAmB,OAAe,EAAA;QAC9B,OAAO,OAAO,GAAG,EAAE,IAAI,OAAO,GAAG,EAAE;AAC/B,YAAA,OAAO,GAAG,EAAE;cACV,OAAO,GAAG,EAAE,IAAI,OAAO,GAAG,GAAG;AAC3B,gBAAA,OAAO,GAAG,EAAE;kBACV,OAAO,GAAG,EAAE,IAAI,OAAO,GAAG,EAAE;AAC1B,oBAAA,OAAO,GAAG,CAAC;AACX,sBAAE,OAAO,KAAK,EAAE;wBACZ,EAAE;AACF,0BAAE,OAAO,KAAK,EAAE;4BACZ,EAAE;;AAEF,gCAAA,CAAC,CAAC;KACzB,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA;;;;"}