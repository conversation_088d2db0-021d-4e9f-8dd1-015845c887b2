{"version": 3, "file": "BrowserPerformanceClient.js", "sources": ["../../src/telemetry/BrowserPerformanceClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport {\r\n    Logger,\r\n    PerformanceEvent,\r\n    PerformanceEvents,\r\n    IPerformanceClient,\r\n    PerformanceClient,\r\n    IPerformanceMeasurement,\r\n    InProgressPerformanceEvent,\r\n    ApplicationTelemetry,\r\n    SubMeasurement,\r\n    PreQueueEvent\r\n} from \"@azure/msal-common\";\r\nimport { CryptoOptions } from \"../config/Configuration\";\r\nimport { BrowserCrypto } from \"../crypto/BrowserCrypto\";\r\nimport { GuidGenerator } from \"../crypto/GuidGenerator\";\r\nimport { BrowserPerformanceMeasurement } from \"./BrowserPerformanceMeasurement\";\r\n\r\nexport class BrowserPerformanceClient extends PerformanceClient implements IPerformanceClient {\r\n    private browserCrypto: BrowserCrypto;\r\n    private guidGenerator: GuidGenerator;\r\n\r\n    constructor(clientId: string, authority: string, logger: Logger, libraryName: string, libraryVersion: string, applicationTelemetry: ApplicationTelemetry, cryptoOptions: CryptoOptions) {\r\n        super(clientId, authority, logger, libraryName, libraryVersion, applicationTelemetry);\r\n        this.browserCrypto = new BrowserCrypto(this.logger, cryptoOptions);\r\n        this.guidGenerator = new GuidGenerator(this.browserCrypto);\r\n    }\r\n\r\n    startPerformanceMeasuremeant(measureName: string, correlationId: string): IPerformanceMeasurement {\r\n        return new BrowserPerformanceMeasurement(measureName, correlationId);\r\n    }\r\n\r\n    generateId() : string {\r\n        return this.guidGenerator.generateGuid();\r\n    }\r\n\r\n    private getPageVisibility(): string | null {\r\n        return document.visibilityState?.toString() || null;\r\n    }\r\n\r\n    private deleteIncompleteSubMeasurements(inProgressEvent: InProgressPerformanceEvent): void {\r\n        const rootEvent = this.eventsByCorrelationId.get(inProgressEvent.event.correlationId);\r\n        const isRootEvent = rootEvent && rootEvent.eventId === inProgressEvent.event.eventId;\r\n        const incompleteMeasurements: SubMeasurement[] = [];\r\n        if (isRootEvent && rootEvent?.incompleteSubMeasurements) {\r\n            rootEvent.incompleteSubMeasurements.forEach((subMeasurement) => {\r\n                incompleteMeasurements.push({...subMeasurement});\r\n            });\r\n        }\r\n        // Clean up remaining marks for incomplete sub-measurements\r\n        if (incompleteMeasurements.length > 0) {\r\n            BrowserPerformanceMeasurement.flushMeasurements(inProgressEvent.event.correlationId, incompleteMeasurements);\r\n        }\r\n    }\r\n\r\n    supportsBrowserPerformanceNow(): boolean {\r\n        return typeof window !== \"undefined\" &&\r\n            typeof window.performance !== \"undefined\" &&\r\n            typeof window.performance.now === \"function\";\r\n    }\r\n\r\n    /**\r\n     * Starts measuring performance for a given operation. Returns a function that should be used to end the measurement.\r\n     * Also captures browser page visibilityState.\r\n     *\r\n     * @param {PerformanceEvents} measureName\r\n     * @param {?string} [correlationId]\r\n     * @returns {((event?: Partial<PerformanceEvent>) => PerformanceEvent| null)}\r\n     */\r\n    startMeasurement(measureName: PerformanceEvents, correlationId?: string): InProgressPerformanceEvent {\r\n        // Capture page visibilityState and then invoke start/end measurement\r\n        const startPageVisibility = this.getPageVisibility();\r\n\r\n        const inProgressEvent = super.startMeasurement(measureName, correlationId);\r\n\r\n        return {\r\n            ...inProgressEvent,\r\n            endMeasurement: (event?: Partial<PerformanceEvent>): PerformanceEvent | null => {\r\n                const res = inProgressEvent.endMeasurement({\r\n                    startPageVisibility,\r\n                    endPageVisibility: this.getPageVisibility(),\r\n                    ...event\r\n                });\r\n                this.deleteIncompleteSubMeasurements(inProgressEvent);\r\n\r\n                return res;\r\n            },\r\n            discardMeasurement: () => {\r\n                inProgressEvent.discardMeasurement();\r\n                this.deleteIncompleteSubMeasurements(inProgressEvent);\r\n                inProgressEvent.measurement.flushMeasurement();\r\n            }\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Adds pre-queue time to preQueueTimeByCorrelationId map.\r\n     * @param {PerformanceEvents} eventName\r\n     * @param {?string} correlationId\r\n     * @returns\r\n     */\r\n    setPreQueueTime(eventName: PerformanceEvents, correlationId?: string): void {\r\n        if (!this.supportsBrowserPerformanceNow()) {\r\n            this.logger.trace(`BrowserPerformanceClient: window performance API not available, unable to set telemetry queue time for ${eventName}`);\r\n            return;\r\n        }\r\n\r\n        if (!correlationId) {\r\n            this.logger.trace(`BrowserPerformanceClient: correlationId for ${eventName} not provided, unable to set telemetry queue time`);\r\n            return;\r\n        }\r\n\r\n        const preQueueEvent: PreQueueEvent | undefined = this.preQueueTimeByCorrelationId.get(correlationId);\r\n        /**\r\n         * Manually complete queue measurement if there is an incomplete pre-queue event.\r\n         * Incomplete pre-queue events are instrumentation bugs that should be fixed.\r\n         */\r\n        if (preQueueEvent) {\r\n            this.logger.trace(`BrowserPerformanceClient: Incomplete pre-queue ${preQueueEvent.name} found`, correlationId);\r\n            this.addQueueMeasurement(preQueueEvent.name, correlationId, undefined, true);\r\n        }\r\n        this.preQueueTimeByCorrelationId.set(correlationId, { name: eventName, time: window.performance.now() });\r\n    }\r\n\r\n    /**\r\n     * Calculates and adds queue time measurement for given performance event.\r\n     *\r\n     * @param {PerformanceEvents} eventName\r\n     * @param {?string} correlationId\r\n     * @param {?number} queueTime\r\n     * @param {?boolean} manuallyCompleted - indicator for manually completed queue measurements\r\n     * @returns\r\n     */\r\n    addQueueMeasurement(eventName: PerformanceEvents, correlationId?: string, queueTime?: number, manuallyCompleted?: boolean): void {\r\n        if (!this.supportsBrowserPerformanceNow()) {\r\n            this.logger.trace(`BrowserPerformanceClient: window performance API not available, unable to add queue measurement for ${eventName}`);\r\n            return;\r\n        }\r\n\r\n        if (!correlationId) {\r\n            this.logger.trace(`BrowserPerformanceClient: correlationId for ${eventName} not provided, unable to add queue measurement`);\r\n            return;\r\n        }\r\n\r\n        const preQueueTime = super.getPreQueueTime(eventName, correlationId);\r\n        if (!preQueueTime) {\r\n            return;\r\n        }\r\n\r\n        const currentTime = window.performance.now();\r\n        const resQueueTime = queueTime || super.calculateQueuedTime(preQueueTime, currentTime);\r\n\r\n        return super.addQueueMeasurement(eventName, correlationId, resQueueTime, manuallyCompleted);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAmBH,IAAA,wBAAA,kBAAA,UAAA,MAAA,EAAA;IAA8C,SAAiB,CAAA,wBAAA,EAAA,MAAA,CAAA,CAAA;AAI3D,IAAA,SAAA,wBAAA,CAAY,QAAgB,EAAE,SAAiB,EAAE,MAAc,EAAE,WAAmB,EAAE,cAAsB,EAAE,oBAA0C,EAAE,aAA4B,EAAA;AAAtL,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,CAAC,IAGxF,IAAA,CAAA;AAFG,QAAA,KAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,KAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QACnE,KAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,KAAI,CAAC,aAAa,CAAC,CAAC;;KAC9D;AAED,IAAA,wBAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UAA6B,WAAmB,EAAE,aAAqB,EAAA;AACnE,QAAA,OAAO,IAAI,6BAA6B,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;KACxE,CAAA;AAED,IAAA,wBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;AACI,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;KAC5C,CAAA;AAEO,IAAA,wBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAzB,YAAA;;QACI,OAAO,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,eAAe,0CAAE,QAAQ,EAAA,KAAM,IAAI,CAAC;KACvD,CAAA;IAEO,wBAA+B,CAAA,SAAA,CAAA,+BAAA,GAAvC,UAAwC,eAA2C,EAAA;AAC/E,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACtF,QAAA,IAAM,WAAW,GAAG,SAAS,IAAI,SAAS,CAAC,OAAO,KAAK,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC;QACrF,IAAM,sBAAsB,GAAqB,EAAE,CAAC;QACpD,IAAI,WAAW,KAAI,SAAS,KAAT,IAAA,IAAA,SAAS,uBAAT,SAAS,CAAE,yBAAyB,CAAA,EAAE;AACrD,YAAA,SAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,UAAC,cAAc,EAAA;AACvD,gBAAA,sBAAsB,CAAC,IAAI,CAAK,QAAA,CAAA,EAAA,EAAA,cAAc,EAAE,CAAC;AACrD,aAAC,CAAC,CAAC;AACN,SAAA;;AAED,QAAA,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,6BAA6B,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,sBAAsB,CAAC,CAAC;AAChH,SAAA;KACJ,CAAA;AAED,IAAA,wBAAA,CAAA,SAAA,CAAA,6BAA6B,GAA7B,YAAA;QACI,OAAO,OAAO,MAAM,KAAK,WAAW;AAChC,YAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;AACzC,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,UAAU,CAAC;KACpD,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,wBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,WAA8B,EAAE,aAAsB,EAAA;QAAvE,IAwBC,KAAA,GAAA,IAAA,CAAA;;AAtBG,QAAA,IAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAErD,IAAM,eAAe,GAAG,MAAM,CAAA,SAAA,CAAA,gBAAgB,YAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AAE3E,QAAA,OAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACO,eAAe,CAAA,EAAA,EAClB,cAAc,EAAE,UAAC,KAAiC,EAAA;AAC9C,gBAAA,IAAM,GAAG,GAAG,eAAe,CAAC,cAAc,CAAA,QAAA,CAAA,EACtC,mBAAmB,EAAA,mBAAA,EACnB,iBAAiB,EAAE,KAAI,CAAC,iBAAiB,EAAE,EACxC,EAAA,KAAK,EACV,CAAC;AACH,gBAAA,KAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,CAAC;AAEtD,gBAAA,OAAO,GAAG,CAAC;aACd,EACD,kBAAkB,EAAE,YAAA;gBAChB,eAAe,CAAC,kBAAkB,EAAE,CAAC;AACrC,gBAAA,KAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,CAAC;AACtD,gBAAA,eAAe,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;AACnD,aAAC,EACH,CAAA,CAAA;KACL,CAAA;AAED;;;;;AAKG;AACH,IAAA,wBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,SAA4B,EAAE,aAAsB,EAAA;AAChE,QAAA,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yGAA0G,GAAA,SAAW,CAAC,CAAC;YACzI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA+C,GAAA,SAAS,GAAmD,mDAAA,CAAC,CAAC;YAC/H,OAAO;AACV,SAAA;QAED,IAAM,aAAa,GAA8B,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACrG;;;AAGG;AACH,QAAA,IAAI,aAAa,EAAE;AACf,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAA,GAAkD,aAAa,CAAC,IAAI,GAAA,QAAQ,EAAE,aAAa,CAAC,CAAC;AAC/G,YAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAChF,SAAA;QACD,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;KAC5G,CAAA;AAED;;;;;;;;AAQG;IACH,wBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,SAA4B,EAAE,aAAsB,EAAE,SAAkB,EAAE,iBAA2B,EAAA;AACrH,QAAA,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sGAAuG,GAAA,SAAW,CAAC,CAAC;YACtI,OAAO;AACV,SAAA;QAED,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA+C,GAAA,SAAS,GAAgD,gDAAA,CAAC,CAAC;YAC5H,OAAO;AACV,SAAA;QAED,IAAM,YAAY,GAAG,MAAM,CAAA,SAAA,CAAA,eAAe,YAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAC7C,IAAM,YAAY,GAAG,SAAS,IAAI,MAAA,CAAA,SAAA,CAAM,mBAAmB,CAAA,IAAA,CAAA,IAAA,EAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAEvF,OAAO,MAAA,CAAA,SAAA,CAAM,mBAAmB,CAAA,IAAA,CAAA,IAAA,EAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;KAC/F,CAAA;IACL,OAAC,wBAAA,CAAA;AAAD,CAxIA,CAA8C,iBAAiB,CAwI9D;;;;"}