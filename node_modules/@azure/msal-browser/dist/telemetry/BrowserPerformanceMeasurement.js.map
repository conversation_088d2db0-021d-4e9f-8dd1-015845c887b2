{"version": 3, "file": "BrowserPerformanceMeasurement.js", "sources": ["../../src/telemetry/BrowserPerformanceMeasurement.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { IPerformanceMeasurement, SubMeasurement } from \"@azure/msal-common\";\r\n\r\nexport class BrowserPerformanceMeasurement implements IPerformanceMeasurement {\r\n    private readonly measureName: string;\r\n    private readonly correlationId: string;\r\n    private readonly startMark: string;\r\n    private readonly endMark: string;\r\n\r\n    constructor(name: string, correlationId: string) {\r\n        this.correlationId = correlationId;\r\n        this.measureName = BrowserPerformanceMeasurement.makeMeasureName(name, correlationId);\r\n        this.startMark = BrowserPerformanceMeasurement.makeStartMark(name, correlationId);\r\n        this.endMark = BrowserPerformanceMeasurement.makeEndMark(name, correlationId);\r\n    }\r\n\r\n    private static makeMeasureName(name: string, correlationId: string) {\r\n        return `msal.measure.${name}.${correlationId}`;\r\n    }\r\n\r\n    private static makeStartMark(name: string, correlationId: string) {\r\n        return `msal.start.${name}.${correlationId}`;\r\n    }\r\n\r\n    private static makeEndMark(name: string, correlationId: string) {\r\n        return `msal.end.${name}.${correlationId}`;\r\n    }\r\n\r\n    static supportsBrowserPerformance(): boolean {\r\n        return typeof window !== \"undefined\" &&\r\n            typeof window.performance !== \"undefined\" &&\r\n            typeof window.performance.mark === \"function\" &&\r\n            typeof window.performance.measure === \"function\" &&\r\n            typeof window.performance.clearMarks === \"function\" &&\r\n            typeof window.performance.clearMeasures === \"function\" &&\r\n            typeof window.performance.getEntriesByName === \"function\";\r\n    }\r\n\r\n    /**\r\n     * Flush browser marks and measurements.\r\n     * @param {string} correlationId\r\n     * @param {SubMeasurement} measurements\r\n     */\r\n    public static flushMeasurements(correlationId: string, measurements: SubMeasurement[]): void {\r\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\r\n            try {\r\n                measurements.forEach((measurement) => {\r\n                    const measureName = BrowserPerformanceMeasurement.makeMeasureName(measurement.name, correlationId);\r\n                    const entriesForMeasurement = window.performance.getEntriesByName(measureName, \"measure\");\r\n                    if (entriesForMeasurement.length > 0) {\r\n                        window.performance.clearMeasures(measureName);\r\n                        window.performance.clearMarks(BrowserPerformanceMeasurement.makeStartMark(measureName, correlationId));\r\n                        window.performance.clearMarks(BrowserPerformanceMeasurement.makeEndMark(measureName, correlationId));\r\n                    }\r\n                });\r\n            } catch (e) {\r\n                // Silently catch and return null\r\n            }\r\n        }\r\n    }\r\n\r\n    startMeasurement(): void {\r\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\r\n            try {\r\n                window.performance.mark(this.startMark);\r\n            } catch (e) {\r\n                // Silently catch\r\n            }\r\n        }\r\n    }\r\n\r\n    endMeasurement():void {\r\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\r\n            try {\r\n                window.performance.mark(this.endMark);\r\n                window.performance.measure(this.measureName, this.startMark, this.endMark);\r\n            } catch (e) {\r\n                // Silently catch\r\n            }\r\n        }\r\n    }\r\n\r\n    flushMeasurement(): number | null {\r\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\r\n            try {\r\n                const entriesForMeasurement = window.performance.getEntriesByName(this.measureName, \"measure\");\r\n                if (entriesForMeasurement.length > 0) {\r\n                    const durationMs = entriesForMeasurement[0].duration;\r\n                    window.performance.clearMeasures(this.measureName);\r\n                    window.performance.clearMarks(this.startMark);\r\n                    window.performance.clearMarks(this.endMark);\r\n                    return durationMs;\r\n                }\r\n            } catch (e) {\r\n                // Silently catch and return null\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAIH,IAAA,6BAAA,kBAAA,YAAA;IAMI,SAAY,6BAAA,CAAA,IAAY,EAAE,aAAqB,EAAA;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,6BAA6B,CAAC,eAAe,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACtF,IAAI,CAAC,SAAS,GAAG,6BAA6B,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAClF,IAAI,CAAC,OAAO,GAAG,6BAA6B,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;KACjF;AAEc,IAAA,6BAAA,CAAA,eAAe,GAA9B,UAA+B,IAAY,EAAE,aAAqB,EAAA;AAC9D,QAAA,OAAO,eAAgB,GAAA,IAAI,GAAI,GAAA,GAAA,aAAe,CAAC;KAClD,CAAA;AAEc,IAAA,6BAAA,CAAA,aAAa,GAA5B,UAA6B,IAAY,EAAE,aAAqB,EAAA;AAC5D,QAAA,OAAO,aAAc,GAAA,IAAI,GAAI,GAAA,GAAA,aAAe,CAAC;KAChD,CAAA;AAEc,IAAA,6BAAA,CAAA,WAAW,GAA1B,UAA2B,IAAY,EAAE,aAAqB,EAAA;AAC1D,QAAA,OAAO,WAAY,GAAA,IAAI,GAAI,GAAA,GAAA,aAAe,CAAC;KAC9C,CAAA;AAEM,IAAA,6BAAA,CAAA,0BAA0B,GAAjC,YAAA;QACI,OAAO,OAAO,MAAM,KAAK,WAAW;AAChC,YAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;AACzC,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,KAAK,UAAU;AAC7C,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,KAAK,UAAU;AAChD,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,UAAU;AACnD,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,aAAa,KAAK,UAAU;AACtD,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB,KAAK,UAAU,CAAC;KACjE,CAAA;AAED;;;;AAIG;AACW,IAAA,6BAAA,CAAA,iBAAiB,GAA/B,UAAgC,aAAqB,EAAE,YAA8B,EAAA;AACjF,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;AACA,gBAAA,YAAY,CAAC,OAAO,CAAC,UAAC,WAAW,EAAA;AAC7B,oBAAA,IAAM,WAAW,GAAG,6BAA6B,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AACnG,oBAAA,IAAM,qBAAqB,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC1F,oBAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,wBAAA,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAC9C,wBAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,6BAA6B,CAAC,aAAa,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;AACvG,wBAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,6BAA6B,CAAC,WAAW,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;AACxG,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;KACJ,CAAA;AAED,IAAA,6BAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACI,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;gBACA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3C,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;KACJ,CAAA;AAED,IAAA,6BAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;AACI,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;gBACA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtC,gBAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9E,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;KACJ,CAAA;AAED,IAAA,6BAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACI,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;AACA,gBAAA,IAAM,qBAAqB,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC/F,gBAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClC,IAAM,UAAU,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACrD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC9C,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,oBAAA,OAAO,UAAU,CAAC;AACrB,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IACL,OAAC,6BAAA,CAAA;AAAD,CAAC,EAAA;;;;"}