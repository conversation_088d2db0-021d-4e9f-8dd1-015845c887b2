/*! @azure/msal-browser v2.39.0 2024-06-06 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var BrowserPerformanceMeasurement = /** @class */ (function () {
    function BrowserPerformanceMeasurement(name, correlationId) {
        this.correlationId = correlationId;
        this.measureName = BrowserPerformanceMeasurement.makeMeasureName(name, correlationId);
        this.startMark = BrowserPerformanceMeasurement.makeStartMark(name, correlationId);
        this.endMark = BrowserPerformanceMeasurement.makeEndMark(name, correlationId);
    }
    BrowserPerformanceMeasurement.makeMeasureName = function (name, correlationId) {
        return "msal.measure." + name + "." + correlationId;
    };
    BrowserPerformanceMeasurement.makeStartMark = function (name, correlationId) {
        return "msal.start." + name + "." + correlationId;
    };
    BrowserPerformanceMeasurement.makeEndMark = function (name, correlationId) {
        return "msal.end." + name + "." + correlationId;
    };
    BrowserPerformanceMeasurement.supportsBrowserPerformance = function () {
        return typeof window !== "undefined" &&
            typeof window.performance !== "undefined" &&
            typeof window.performance.mark === "function" &&
            typeof window.performance.measure === "function" &&
            typeof window.performance.clearMarks === "function" &&
            typeof window.performance.clearMeasures === "function" &&
            typeof window.performance.getEntriesByName === "function";
    };
    /**
     * Flush browser marks and measurements.
     * @param {string} correlationId
     * @param {SubMeasurement} measurements
     */
    BrowserPerformanceMeasurement.flushMeasurements = function (correlationId, measurements) {
        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {
            try {
                measurements.forEach(function (measurement) {
                    var measureName = BrowserPerformanceMeasurement.makeMeasureName(measurement.name, correlationId);
                    var entriesForMeasurement = window.performance.getEntriesByName(measureName, "measure");
                    if (entriesForMeasurement.length > 0) {
                        window.performance.clearMeasures(measureName);
                        window.performance.clearMarks(BrowserPerformanceMeasurement.makeStartMark(measureName, correlationId));
                        window.performance.clearMarks(BrowserPerformanceMeasurement.makeEndMark(measureName, correlationId));
                    }
                });
            }
            catch (e) {
                // Silently catch and return null
            }
        }
    };
    BrowserPerformanceMeasurement.prototype.startMeasurement = function () {
        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {
            try {
                window.performance.mark(this.startMark);
            }
            catch (e) {
                // Silently catch
            }
        }
    };
    BrowserPerformanceMeasurement.prototype.endMeasurement = function () {
        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {
            try {
                window.performance.mark(this.endMark);
                window.performance.measure(this.measureName, this.startMark, this.endMark);
            }
            catch (e) {
                // Silently catch
            }
        }
    };
    BrowserPerformanceMeasurement.prototype.flushMeasurement = function () {
        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {
            try {
                var entriesForMeasurement = window.performance.getEntriesByName(this.measureName, "measure");
                if (entriesForMeasurement.length > 0) {
                    var durationMs = entriesForMeasurement[0].duration;
                    window.performance.clearMeasures(this.measureName);
                    window.performance.clearMarks(this.startMark);
                    window.performance.clearMarks(this.endMark);
                    return durationMs;
                }
            }
            catch (e) {
                // Silently catch and return null
            }
        }
        return null;
    };
    return BrowserPerformanceMeasurement;
}());

export { BrowserPerformanceMeasurement };
//# sourceMappingURL=BrowserPerformanceMeasurement.js.map
