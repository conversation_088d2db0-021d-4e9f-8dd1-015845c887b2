{"name": "@azure/msal-browser", "author": {"name": "Microsoft", "email": "<EMAIL>", "url": "https://www.microsoft.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/AzureAD/microsoft-authentication-library-for-js.git"}, "version": "2.39.0", "description": "Microsoft Authentication Library for js", "keywords": ["implicit", "authorization code", "PKCE", "js", "AAD", "msal", "o<PERSON>h"], "sideEffects": false, "main": "./dist/index.cjs.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=0.8.0"}, "beachball": {"disallowedChangeTypes": ["major"]}, "directories": {"test": "test"}, "files": ["dist", "lib/msal-browser.js", "lib/msal-browser.js.map", "lib/msal-browser.min.js"], "scripts": {"cdn": "npm run build:all && npm run cdn:upload && npm run cdn:sri", "cdn:upload": "node ./cdn-upload.js", "cdn:sri": "node ./cdn-sri.js", "clean": "shx rm -rf dist lib", "clean:coverage": "rimraf ../../.nyc_output/*", "lint": "cd ../../ && npm run lint:browser", "lint:fix": "npm run lint -- -- --fix", "test": "jest", "test:coverage": "jest --coverage", "test:coverage:only": "npm run clean:coverage && npm run test:coverage", "build:all": "npm run build:common && npm run build", "build:common": "cd ../msal-common && npm run build", "build:modules": "rollup -c", "build:modules:watch": "rollup -cw", "build": "npm run clean && npm run build:modules", "link:localDeps": "npx lerna bootstrap --scope @azure/msal-common --scope @azure/msal-browser", "prepack": "npm run build:all"}, "devDependencies": {"@azure/storage-blob": "^12.2.1", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/preset-env": "^7.7.1", "@babel/preset-typescript": "^7.7.2", "@rollup/plugin-node-resolve": "^11.2.1", "@types/jest": "^27.5.2", "@types/sinon": "^7.5.0", "dotenv": "^8.2.0", "fake-indexeddb": "^3.1.3", "husky": "^3.0.9", "jest": "^27.0.4", "jsdom": "^20.0.0", "jsdom-global": "^3.0.2", "rimraf": "^3.0.0", "rollup": "^2.46.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "shx": "^0.3.2", "sinon": "^7.5.0", "ssri": "^8.0.1", "ts-jest": "^27.0.2", "tslib": "^1.10.0", "tslint": "^5.20.0", "typescript": "^3.8.3"}, "dependencies": {"@azure/msal-common": "13.3.3"}}