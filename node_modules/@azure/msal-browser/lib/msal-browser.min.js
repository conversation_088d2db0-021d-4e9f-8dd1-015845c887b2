/*! @azure/msal-browser v2.39.0 2024-06-06 */
"use strict";!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).msal={})}(this,(function(e){
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */
var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},t(e,r)};function r(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}var n=function(){return n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}function i(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function a(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function s(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a}function c(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(s(arguments[t]));return e}
/*! @azure/msal-common v13.3.3 2024-06-06 */
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var u=function(e,t){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},u(e,t)};function l(e,t){function r(){this.constructor=e}u(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},d.apply(this,arguments)};function h(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function p(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function g(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),o=0;for(t=0;t<r;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,o++)n[o]=i[a];return n}
/*! @azure/msal-common v13.3.3 2024-06-06 */var f,m,v,y,C,E={LIBRARY_NAME:"MSAL.JS",SKU:"msal.js.common",CACHE_PREFIX:"msal",DEFAULT_AUTHORITY:"https://login.microsoftonline.com/common/",DEFAULT_AUTHORITY_HOST:"login.microsoftonline.com",DEFAULT_COMMON_TENANT:"common",ADFS:"adfs",DSTS:"dstsv2",AAD_INSTANCE_DISCOVERY_ENDPT:"https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",CIAM_AUTH_URL:".ciamlogin.com",AAD_TENANT_DOMAIN_SUFFIX:".onmicrosoft.com",RESOURCE_DELIM:"|",NO_ACCOUNT:"NO_ACCOUNT",CLAIMS:"claims",CONSUMER_UTID:"9188040d-6c67-4c5b-b112-36a304b66dad",OPENID_SCOPE:"openid",PROFILE_SCOPE:"profile",OFFLINE_ACCESS_SCOPE:"offline_access",EMAIL_SCOPE:"email",CODE_RESPONSE_TYPE:"code",CODE_GRANT_TYPE:"authorization_code",RT_GRANT_TYPE:"refresh_token",FRAGMENT_RESPONSE_MODE:"fragment",S256_CODE_CHALLENGE_METHOD:"S256",URL_FORM_CONTENT_TYPE:"application/x-www-form-urlencoded;charset=utf-8",AUTHORIZATION_PENDING:"authorization_pending",NOT_DEFINED:"not_defined",EMPTY_STRING:"",NOT_APPLICABLE:"N/A",FORWARD_SLASH:"/",IMDS_ENDPOINT:"http://***************/metadata/instance/compute/location",IMDS_VERSION:"2020-06-01",IMDS_TIMEOUT:2e3,AZURE_REGION_AUTO_DISCOVER_FLAG:"TryAutoDetect",REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX:"login.microsoft.com",REGIONAL_AUTH_NON_MSI_QUERY_STRING:"allowestsrnonmsi=true",KNOWN_PUBLIC_CLOUDS:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"],TOKEN_RESPONSE_TYPE:"token",ID_TOKEN_RESPONSE_TYPE:"id_token",SHR_NONCE_VALIDITY:240,INVALID_INSTANCE:"invalid_instance"},T=[E.OPENID_SCOPE,E.PROFILE_SCOPE,E.OFFLINE_ACCESS_SCOPE],_=g(T,[E.EMAIL_SCOPE]);!function(e){e.CONTENT_TYPE="Content-Type",e.RETRY_AFTER="Retry-After",e.CCS_HEADER="X-AnchorMailbox",e.WWWAuthenticate="WWW-Authenticate",e.AuthenticationInfo="Authentication-Info",e.X_MS_REQUEST_ID="x-ms-request-id",e.X_MS_HTTP_VERSION="x-ms-httpver"}(f||(f={})),function(e){e.ID_TOKEN="idtoken",e.CLIENT_INFO="client.info",e.ADAL_ID_TOKEN="adal.idtoken",e.ERROR="error",e.ERROR_DESC="error.description",e.ACTIVE_ACCOUNT="active-account",e.ACTIVE_ACCOUNT_FILTERS="active-account-filters"}(m||(m={})),function(e){e.COMMON="common",e.ORGANIZATIONS="organizations",e.CONSUMERS="consumers"}(v||(v={})),function(e){e.CLIENT_ID="client_id",e.REDIRECT_URI="redirect_uri",e.RESPONSE_TYPE="response_type",e.RESPONSE_MODE="response_mode",e.GRANT_TYPE="grant_type",e.CLAIMS="claims",e.SCOPE="scope",e.ERROR="error",e.ERROR_DESCRIPTION="error_description",e.ACCESS_TOKEN="access_token",e.ID_TOKEN="id_token",e.REFRESH_TOKEN="refresh_token",e.EXPIRES_IN="expires_in",e.STATE="state",e.NONCE="nonce",e.PROMPT="prompt",e.SESSION_STATE="session_state",e.CLIENT_INFO="client_info",e.CODE="code",e.CODE_CHALLENGE="code_challenge",e.CODE_CHALLENGE_METHOD="code_challenge_method",e.CODE_VERIFIER="code_verifier",e.CLIENT_REQUEST_ID="client-request-id",e.X_CLIENT_SKU="x-client-SKU",e.X_CLIENT_VER="x-client-VER",e.X_CLIENT_OS="x-client-OS",e.X_CLIENT_CPU="x-client-CPU",e.X_CLIENT_CURR_TELEM="x-client-current-telemetry",e.X_CLIENT_LAST_TELEM="x-client-last-telemetry",e.X_MS_LIB_CAPABILITY="x-ms-lib-capability",e.X_APP_NAME="x-app-name",e.X_APP_VER="x-app-ver",e.POST_LOGOUT_URI="post_logout_redirect_uri",e.ID_TOKEN_HINT="id_token_hint",e.DEVICE_CODE="device_code",e.CLIENT_SECRET="client_secret",e.CLIENT_ASSERTION="client_assertion",e.CLIENT_ASSERTION_TYPE="client_assertion_type",e.TOKEN_TYPE="token_type",e.REQ_CNF="req_cnf",e.OBO_ASSERTION="assertion",e.REQUESTED_TOKEN_USE="requested_token_use",e.ON_BEHALF_OF="on_behalf_of",e.FOCI="foci",e.CCS_HEADER="X-AnchorMailbox",e.RETURN_SPA_CODE="return_spa_code",e.NATIVE_BROKER="nativebroker",e.LOGOUT_HINT="logout_hint"}(y||(y={})),function(e){e.ACCESS_TOKEN="access_token",e.XMS_CC="xms_cc"}(C||(C={}));var I,w={LOGIN:"login",SELECT_ACCOUNT:"select_account",CONSENT:"consent",NONE:"none",CREATE:"create",NO_SESSION:"no_session"};!function(e){e.ACCOUNT="account",e.SID="sid",e.LOGIN_HINT="login_hint",e.ID_TOKEN="id_token",e.DOMAIN_HINT="domain_hint",e.ORGANIZATIONS="organizations",e.CONSUMERS="consumers",e.ACCOUNT_ID="accountIdentifier",e.HOMEACCOUNT_ID="homeAccountIdentifier"}(I||(I={}));var S,A,k,R,b,P,N={PLAIN:"plain",S256:"S256"};!function(e){e.QUERY="query",e.FRAGMENT="fragment",e.FORM_POST="form_post"}(S||(S={})),function(e){e.IMPLICIT_GRANT="implicit",e.AUTHORIZATION_CODE_GRANT="authorization_code",e.CLIENT_CREDENTIALS_GRANT="client_credentials",e.RESOURCE_OWNER_PASSWORD_GRANT="password",e.REFRESH_TOKEN_GRANT="refresh_token",e.DEVICE_CODE_GRANT="device_code",e.JWT_BEARER="urn:ietf:params:oauth:grant-type:jwt-bearer"}(A||(A={})),function(e){e.MSSTS_ACCOUNT_TYPE="MSSTS",e.ADFS_ACCOUNT_TYPE="ADFS",e.MSAV1_ACCOUNT_TYPE="MSA",e.GENERIC_ACCOUNT_TYPE="Generic"}(k||(k={})),function(e){e.CACHE_KEY_SEPARATOR="-",e.CLIENT_INFO_SEPARATOR="."}(R||(R={})),function(e){e.ID_TOKEN="IdToken",e.ACCESS_TOKEN="AccessToken",e.ACCESS_TOKEN_WITH_AUTH_SCHEME="AccessToken_With_AuthScheme",e.REFRESH_TOKEN="RefreshToken"}(b||(b={})),function(e){e[e.ADFS=1001]="ADFS",e[e.MSA=1002]="MSA",e[e.MSSTS=1003]="MSSTS",e[e.GENERIC=1004]="GENERIC",e[e.ACCESS_TOKEN=2001]="ACCESS_TOKEN",e[e.REFRESH_TOKEN=2002]="REFRESH_TOKEN",e[e.ID_TOKEN=2003]="ID_TOKEN",e[e.APP_METADATA=3001]="APP_METADATA",e[e.UNDEFINED=9999]="UNDEFINED"}(P||(P={}));var M,O="appmetadata",q="1",U="authority-metadata",H=86400;!function(e){e.CONFIG="config",e.CACHE="cache",e.NETWORK="network",e.HARDCODED_VALUES="hardcoded_values"}(M||(M={}));var L,D={SCHEMA_VERSION:5,MAX_CUR_HEADER_BYTES:80,MAX_LAST_HEADER_BYTES:330,MAX_CACHED_ERRORS:50,CACHE_KEY:"server-telemetry",CATEGORY_SEPARATOR:"|",VALUE_SEPARATOR:",",OVERFLOW_TRUE:"1",OVERFLOW_FALSE:"0",UNKNOWN_ERROR:"unknown_error"};e.AuthenticationScheme=void 0,(L=e.AuthenticationScheme||(e.AuthenticationScheme={})).BEARER="Bearer",L.POP="pop",L.SSH="ssh-cert";var K,F,B,x,G,z,Q=60,j=3600,Y="throttling",W="retry-after, h429",V="invalid_grant",J="client_mismatch";!function(e){e.username="username",e.password="password"}(K||(K={})),function(e){e[e.httpSuccess=200]="httpSuccess",e[e.httpBadRequest=400]="httpBadRequest"}(F||(F={})),function(e){e.FAILED_AUTO_DETECTION="1",e.INTERNAL_CACHE="2",e.ENVIRONMENT_VARIABLE="3",e.IMDS="4"}(B||(B={})),function(e){e.CONFIGURED_MATCHES_DETECTED="1",e.CONFIGURED_NO_AUTO_DETECTION="2",e.CONFIGURED_NOT_DETECTED="3",e.AUTO_DETECTION_REQUESTED_SUCCESSFUL="4",e.AUTO_DETECTION_REQUESTED_FAILED="5"}(x||(x={})),function(e){e.NO_CACHE_HIT="0",e.FORCE_REFRESH="1",e.NO_CACHED_ACCESS_TOKEN="2",e.CACHED_ACCESS_TOKEN_EXPIRED="3",e.REFRESH_CACHED_ACCESS_TOKEN="4",e.CLAIMS_REQUESTED_CACHE_SKIPPED="5"}(G||(G={})),function(e){e.Jwt="JWT",e.Jwk="JWK",e.Pop="pop"}(z||(z={}));
/*! @azure/msal-common v13.3.3 2024-06-06 */
var X,Z={unexpectedError:{code:"unexpected_error",desc:"Unexpected error in authentication."},postRequestFailed:{code:"post_request_failed",desc:"Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."}},$=function(e){function t(r,n,o){var i=this,a=n?r+": "+n:r;return i=e.call(this,a)||this,Object.setPrototypeOf(i,t.prototype),i.errorCode=r||E.EMPTY_STRING,i.errorMessage=n||E.EMPTY_STRING,i.subError=o||E.EMPTY_STRING,i.name="AuthError",i}return l(t,e),t.prototype.setCorrelationId=function(e){this.correlationId=e},t.createUnexpectedError=function(e){return new t(Z.unexpectedError.code,Z.unexpectedError.desc+": "+e)},t.createPostRequestFailed=function(e){return new t(Z.postRequestFailed.code,Z.postRequestFailed.desc+": "+e)},t}(Error),ee={createNewGuid:function(){throw $.createUnexpectedError("Crypto interface - createNewGuid() has not been implemented")},base64Decode:function(){throw $.createUnexpectedError("Crypto interface - base64Decode() has not been implemented")},base64Encode:function(){throw $.createUnexpectedError("Crypto interface - base64Encode() has not been implemented")},generatePkceCodes:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Crypto interface - generatePkceCodes() has not been implemented",$.createUnexpectedError("Crypto interface - generatePkceCodes() has not been implemented")}))}))},getPublicKeyThumbprint:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Crypto interface - getPublicKeyThumbprint() has not been implemented",$.createUnexpectedError("Crypto interface - getPublicKeyThumbprint() has not been implemented")}))}))},removeTokenBindingKey:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Crypto interface - removeTokenBindingKey() has not been implemented",$.createUnexpectedError("Crypto interface - removeTokenBindingKey() has not been implemented")}))}))},clearKeystore:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Crypto interface - clearKeystore() has not been implemented",$.createUnexpectedError("Crypto interface - clearKeystore() has not been implemented")}))}))},signJwt:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Crypto interface - signJwt() has not been implemented",$.createUnexpectedError("Crypto interface - signJwt() has not been implemented")}))}))},hashString:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Crypto interface - hashString() has not been implemented",$.createUnexpectedError("Crypto interface - hashString() has not been implemented")}))}))}},te={clientInfoDecodingError:{code:"client_info_decoding_error",desc:"The client info could not be parsed/decoded correctly. Please review the trace to determine the root cause."},clientInfoEmptyError:{code:"client_info_empty_error",desc:"The client info was empty. Please review the trace to determine the root cause."},tokenParsingError:{code:"token_parsing_error",desc:"Token cannot be parsed. Please review stack trace to determine root cause."},nullOrEmptyToken:{code:"null_or_empty_token",desc:"The token is null or empty. Please review the trace to determine the root cause."},endpointResolutionError:{code:"endpoints_resolution_error",desc:"Error: could not resolve endpoints. Please check network and try again."},networkError:{code:"network_error",desc:"Network request failed. Please check network trace to determine root cause."},unableToGetOpenidConfigError:{code:"openid_config_error",desc:"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints."},hashNotDeserialized:{code:"hash_not_deserialized",desc:"The hash parameters could not be deserialized. Please review the trace to determine the root cause."},blankGuidGenerated:{code:"blank_guid_generated",desc:"The guid generated was blank. Please review the trace to determine the root cause."},invalidStateError:{code:"invalid_state",desc:"State was not the expected format. Please check the logs to determine whether the request was sent using ProtocolUtils.setRequestState()."},stateMismatchError:{code:"state_mismatch",desc:"State mismatch error. Please check your network. Continued requests may cause cache overflow."},stateNotFoundError:{code:"state_not_found",desc:"State not found"},nonceMismatchError:{code:"nonce_mismatch",desc:"Nonce mismatch error. This may be caused by a race condition in concurrent requests."},nonceNotFoundError:{code:"nonce_not_found",desc:"nonce not found"},authTimeNotFoundError:{code:"auth_time_not_found",desc:"Max Age was requested and the ID token is missing the auth_time variable. auth_time is an optional claim and is not enabled by default - it must be enabled. See https://aka.ms/msaljs/optional-claims for more information."},maxAgeTranspiredError:{code:"max_age_transpired",desc:"Max Age is set to 0, or too much time has elapsed since the last end-user authentication."},noTokensFoundError:{code:"no_tokens_found",desc:"No tokens were found for the given scopes, and no authorization code was passed to acquireToken. You must retrieve an authorization code before making a call to acquireToken()."},multipleMatchingTokens:{code:"multiple_matching_tokens",desc:"The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account."},multipleMatchingAccounts:{code:"multiple_matching_accounts",desc:"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account"},multipleMatchingAppMetadata:{code:"multiple_matching_appMetadata",desc:"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata"},tokenRequestCannotBeMade:{code:"request_cannot_be_made",desc:"Token request cannot be made without authorization code or refresh token."},appendEmptyScopeError:{code:"cannot_append_empty_scope",desc:"Cannot append null or empty scope to ScopeSet. Please check the stack trace for more info."},removeEmptyScopeError:{code:"cannot_remove_empty_scope",desc:"Cannot remove null or empty scope from ScopeSet. Please check the stack trace for more info."},appendScopeSetError:{code:"cannot_append_scopeset",desc:"Cannot append ScopeSet due to error."},emptyInputScopeSetError:{code:"empty_input_scopeset",desc:"Empty input ScopeSet cannot be processed."},DeviceCodePollingCancelled:{code:"device_code_polling_cancelled",desc:"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true."},DeviceCodeExpired:{code:"device_code_expired",desc:"Device code is expired."},DeviceCodeUnknownError:{code:"device_code_unknown_error",desc:"Device code stopped polling for unknown reasons."},NoAccountInSilentRequest:{code:"no_account_in_silent_request",desc:"Please pass an account object, silent flow is not supported without account information"},invalidCacheRecord:{code:"invalid_cache_record",desc:"Cache record object was null or undefined."},invalidCacheEnvironment:{code:"invalid_cache_environment",desc:"Invalid environment when attempting to create cache entry"},noAccountFound:{code:"no_account_found",desc:"No account found in cache for given key."},CachePluginError:{code:"no cache plugin set on CacheManager",desc:"ICachePlugin needs to be set before using readFromStorage or writeFromStorage"},noCryptoObj:{code:"no_crypto_object",desc:"No crypto object detected. This is required for the following operation: "},invalidCacheType:{code:"invalid_cache_type",desc:"Invalid cache type"},unexpectedAccountType:{code:"unexpected_account_type",desc:"Unexpected account type."},unexpectedCredentialType:{code:"unexpected_credential_type",desc:"Unexpected credential type."},invalidAssertion:{code:"invalid_assertion",desc:"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515"},invalidClientCredential:{code:"invalid_client_credential",desc:"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential"},tokenRefreshRequired:{code:"token_refresh_required",desc:"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired."},userTimeoutReached:{code:"user_timeout_reached",desc:"User defined timeout for device code polling reached"},tokenClaimsRequired:{code:"token_claims_cnf_required_for_signedjwt",desc:"Cannot generate a POP jwt if the token_claims are not populated"},noAuthorizationCodeFromServer:{code:"authorization_code_missing_from_server_response",desc:"Server response does not contain an authorization code to proceed"},noAzureRegionDetected:{code:"no_azure_region_detected",desc:"No azure region was detected and no fallback was made available"},accessTokenEntityNullError:{code:"access_token_entity_null",desc:"Access token entity is null, please check logs and cache to ensure a valid access token is present."},bindingKeyNotRemovedError:{code:"binding_key_not_removed",desc:"Could not remove the credential's binding key from storage."},logoutNotSupported:{code:"end_session_endpoint_not_supported",desc:"Provided authority does not support logout."},keyIdMissing:{code:"key_id_missing",desc:"A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key."},noNetworkConnectivity:{code:"no_network_connectivity",desc:"No network connectivity. Check your internet connection."},userCanceledError:{code:"user_canceled",desc:"User canceled the flow."}},re=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="ClientAuthError",Object.setPrototypeOf(o,t.prototype),o}return l(t,e),t.createClientInfoDecodingError=function(e){return new t(te.clientInfoDecodingError.code,te.clientInfoDecodingError.desc+" Failed with error: "+e)},t.createClientInfoEmptyError=function(){return new t(te.clientInfoEmptyError.code,""+te.clientInfoEmptyError.desc)},t.createTokenParsingError=function(e){return new t(te.tokenParsingError.code,te.tokenParsingError.desc+" Failed with error: "+e)},t.createTokenNullOrEmptyError=function(e){return new t(te.nullOrEmptyToken.code,te.nullOrEmptyToken.desc+" Raw Token Value: "+e)},t.createEndpointDiscoveryIncompleteError=function(e){return new t(te.endpointResolutionError.code,te.endpointResolutionError.desc+" Detail: "+e)},t.createNetworkError=function(e,r){return new t(te.networkError.code,te.networkError.desc+" | Fetch client threw: "+r+" | Attempted to reach: "+e.split("?")[0])},t.createUnableToGetOpenidConfigError=function(e){return new t(te.unableToGetOpenidConfigError.code,te.unableToGetOpenidConfigError.desc+" Attempted to retrieve endpoints from: "+e)},t.createHashNotDeserializedError=function(e){return new t(te.hashNotDeserialized.code,te.hashNotDeserialized.desc+" Given Object: "+e)},t.createInvalidStateError=function(e,r){return new t(te.invalidStateError.code,te.invalidStateError.desc+" Invalid State: "+e+", Root Err: "+r)},t.createStateMismatchError=function(){return new t(te.stateMismatchError.code,te.stateMismatchError.desc)},t.createStateNotFoundError=function(e){return new t(te.stateNotFoundError.code,te.stateNotFoundError.desc+":  "+e)},t.createNonceMismatchError=function(){return new t(te.nonceMismatchError.code,te.nonceMismatchError.desc)},t.createAuthTimeNotFoundError=function(){return new t(te.authTimeNotFoundError.code,te.authTimeNotFoundError.desc)},t.createMaxAgeTranspiredError=function(){return new t(te.maxAgeTranspiredError.code,te.maxAgeTranspiredError.desc)},t.createNonceNotFoundError=function(e){return new t(te.nonceNotFoundError.code,te.nonceNotFoundError.desc+":  "+e)},t.createMultipleMatchingTokensInCacheError=function(){return new t(te.multipleMatchingTokens.code,te.multipleMatchingTokens.desc+".")},t.createMultipleMatchingAccountsInCacheError=function(){return new t(te.multipleMatchingAccounts.code,te.multipleMatchingAccounts.desc)},t.createMultipleMatchingAppMetadataInCacheError=function(){return new t(te.multipleMatchingAppMetadata.code,te.multipleMatchingAppMetadata.desc)},t.createTokenRequestCannotBeMadeError=function(){return new t(te.tokenRequestCannotBeMade.code,te.tokenRequestCannotBeMade.desc)},t.createAppendEmptyScopeToSetError=function(e){return new t(te.appendEmptyScopeError.code,te.appendEmptyScopeError.desc+" Given Scope: "+e)},t.createRemoveEmptyScopeFromSetError=function(e){return new t(te.removeEmptyScopeError.code,te.removeEmptyScopeError.desc+" Given Scope: "+e)},t.createAppendScopeSetError=function(e){return new t(te.appendScopeSetError.code,te.appendScopeSetError.desc+" Detail Error: "+e)},t.createEmptyInputScopeSetError=function(){return new t(te.emptyInputScopeSetError.code,""+te.emptyInputScopeSetError.desc)},t.createDeviceCodeCancelledError=function(){return new t(te.DeviceCodePollingCancelled.code,""+te.DeviceCodePollingCancelled.desc)},t.createDeviceCodeExpiredError=function(){return new t(te.DeviceCodeExpired.code,""+te.DeviceCodeExpired.desc)},t.createDeviceCodeUnknownError=function(){return new t(te.DeviceCodeUnknownError.code,""+te.DeviceCodeUnknownError.desc)},t.createNoAccountInSilentRequestError=function(){return new t(te.NoAccountInSilentRequest.code,""+te.NoAccountInSilentRequest.desc)},t.createNullOrUndefinedCacheRecord=function(){return new t(te.invalidCacheRecord.code,te.invalidCacheRecord.desc)},t.createInvalidCacheEnvironmentError=function(){return new t(te.invalidCacheEnvironment.code,te.invalidCacheEnvironment.desc)},t.createNoAccountFoundError=function(){return new t(te.noAccountFound.code,te.noAccountFound.desc)},t.createCachePluginError=function(){return new t(te.CachePluginError.code,""+te.CachePluginError.desc)},t.createNoCryptoObjectError=function(e){return new t(te.noCryptoObj.code,""+te.noCryptoObj.desc+e)},t.createInvalidCacheTypeError=function(){return new t(te.invalidCacheType.code,""+te.invalidCacheType.desc)},t.createUnexpectedAccountTypeError=function(){return new t(te.unexpectedAccountType.code,""+te.unexpectedAccountType.desc)},t.createUnexpectedCredentialTypeError=function(){return new t(te.unexpectedCredentialType.code,""+te.unexpectedCredentialType.desc)},t.createInvalidAssertionError=function(){return new t(te.invalidAssertion.code,""+te.invalidAssertion.desc)},t.createInvalidCredentialError=function(){return new t(te.invalidClientCredential.code,""+te.invalidClientCredential.desc)},t.createRefreshRequiredError=function(){return new t(te.tokenRefreshRequired.code,te.tokenRefreshRequired.desc)},t.createUserTimeoutReachedError=function(){return new t(te.userTimeoutReached.code,te.userTimeoutReached.desc)},t.createTokenClaimsRequiredError=function(){return new t(te.tokenClaimsRequired.code,te.tokenClaimsRequired.desc)},t.createNoAuthCodeInServerResponseError=function(){return new t(te.noAuthorizationCodeFromServer.code,te.noAuthorizationCodeFromServer.desc)},t.createBindingKeyNotRemovedError=function(){return new t(te.bindingKeyNotRemovedError.code,te.bindingKeyNotRemovedError.desc)},t.createLogoutNotSupportedError=function(){return new t(te.logoutNotSupported.code,te.logoutNotSupported.desc)},t.createKeyIdMissingError=function(){return new t(te.keyIdMissing.code,te.keyIdMissing.desc)},t.createNoNetworkConnectivityError=function(){return new t(te.noNetworkConnectivity.code,te.noNetworkConnectivity.desc)},t.createUserCanceledError=function(){return new t(te.userCanceledError.code,te.userCanceledError.desc)},t}($),ne=function(){function e(){}return e.decodeAuthToken=function(t){if(e.isEmpty(t))throw re.createTokenNullOrEmptyError(t);var r=/^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(t);if(!r||r.length<4)throw re.createTokenParsingError("Given token is malformed: "+JSON.stringify(t));return{header:r[1],JWSPayload:r[2],JWSSig:r[3]}},e.isEmpty=function(e){return void 0===e||!e||0===e.length},e.isEmptyObj=function(t){if(t&&!e.isEmpty(t))try{var r=JSON.parse(t);return 0===Object.keys(r).length}catch(e){}return!0},e.startsWith=function(e,t){return 0===e.indexOf(t)},e.endsWith=function(e,t){return e.length>=t.length&&e.lastIndexOf(t)===e.length-t.length},e.queryStringToObject=function(e){var t={},r=e.split("&"),n=function(e){return decodeURIComponent(e.replace(/\+/g," "))};return r.forEach((function(e){if(e.trim()){var r=e.split(/=(.+)/g,2),o=r[0],i=r[1];o&&i&&(t[n(o)]=n(i))}})),t},e.trimArrayEntries=function(e){return e.map((function(e){return e.trim()}))},e.removeEmptyStringsFromArray=function(t){return t.filter((function(t){return!e.isEmpty(t)}))},e.jsonParseHelper=function(e){try{return JSON.parse(e)}catch(e){return null}},e.matchPattern=function(e,t){return new RegExp(e.replace(/\\/g,"\\\\").replace(/\*/g,"[^ ]*").replace(/\?/g,"\\?")).test(t)},e}();
/*! @azure/msal-common v13.3.3 2024-06-06 */
e.LogLevel=void 0,(X=e.LogLevel||(e.LogLevel={}))[X.Error=0]="Error",X[X.Warning=1]="Warning",X[X.Info=2]="Info",X[X.Verbose=3]="Verbose",X[X.Trace=4]="Trace";var oe,ie=function(){function t(r,n,o){this.level=e.LogLevel.Info;var i=r||t.createDefaultLoggerOptions();this.localCallback=i.loggerCallback||function(){},this.piiLoggingEnabled=i.piiLoggingEnabled||!1,this.level="number"==typeof i.logLevel?i.logLevel:e.LogLevel.Info,this.correlationId=i.correlationId||E.EMPTY_STRING,this.packageName=n||E.EMPTY_STRING,this.packageVersion=o||E.EMPTY_STRING}return t.createDefaultLoggerOptions=function(){return{loggerCallback:function(){},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info}},t.prototype.clone=function(e,r,n){return new t({loggerCallback:this.localCallback,piiLoggingEnabled:this.piiLoggingEnabled,logLevel:this.level,correlationId:n||this.correlationId},e,r)},t.prototype.logMessage=function(t,r){if(!(r.logLevel>this.level||!this.piiLoggingEnabled&&r.containsPii)){var n=(new Date).toUTCString(),o=(ne.isEmpty(r.correlationId)?ne.isEmpty(this.correlationId)?"["+n+"]":"["+n+"] : ["+this.correlationId+"]":"["+n+"] : ["+r.correlationId+"]")+" : "+this.packageName+"@"+this.packageVersion+" : "+e.LogLevel[r.logLevel]+" - "+t;this.executeCallback(r.logLevel,o,r.containsPii||!1)}},t.prototype.executeCallback=function(e,t,r){this.localCallback&&this.localCallback(e,t,r)},t.prototype.error=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Error,containsPii:!1,correlationId:r||E.EMPTY_STRING})},t.prototype.errorPii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Error,containsPii:!0,correlationId:r||E.EMPTY_STRING})},t.prototype.warning=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Warning,containsPii:!1,correlationId:r||E.EMPTY_STRING})},t.prototype.warningPii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Warning,containsPii:!0,correlationId:r||E.EMPTY_STRING})},t.prototype.info=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Info,containsPii:!1,correlationId:r||E.EMPTY_STRING})},t.prototype.infoPii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Info,containsPii:!0,correlationId:r||E.EMPTY_STRING})},t.prototype.verbose=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Verbose,containsPii:!1,correlationId:r||E.EMPTY_STRING})},t.prototype.verbosePii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Verbose,containsPii:!0,correlationId:r||E.EMPTY_STRING})},t.prototype.trace=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Trace,containsPii:!1,correlationId:r||E.EMPTY_STRING})},t.prototype.tracePii=function(t,r){this.logMessage(t,{logLevel:e.LogLevel.Trace,containsPii:!0,correlationId:r||E.EMPTY_STRING})},t.prototype.isPiiLoggingEnabled=function(){return this.piiLoggingEnabled||!1},t}(),ae="@azure/msal-common",se="13.3.3";
/*! @azure/msal-common v13.3.3 2024-06-06 */
/*! @azure/msal-common v13.3.3 2024-06-06 */
e.AzureCloudInstance=void 0,(oe=e.AzureCloudInstance||(e.AzureCloudInstance={}))[oe.None=0]="None",oe.AzurePublic="https://login.microsoftonline.com",oe.AzurePpe="https://login.windows-ppe.net",oe.AzureChina="https://login.chinacloudapi.cn",oe.AzureGermany="https://login.microsoftonline.de",oe.AzureUsGovernment="https://login.microsoftonline.us";
/*! @azure/msal-common v13.3.3 2024-06-06 */
var ce,ue,le={redirectUriNotSet:{code:"redirect_uri_empty",desc:"A redirect URI is required for all calls, and none has been set."},postLogoutUriNotSet:{code:"post_logout_uri_empty",desc:"A post logout redirect has not been set."},claimsRequestParsingError:{code:"claims_request_parsing_error",desc:"Could not parse the given claims request object."},authorityUriInsecure:{code:"authority_uri_insecure",desc:"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options"},urlParseError:{code:"url_parse_error",desc:"URL could not be parsed into appropriate segments."},urlEmptyError:{code:"empty_url_error",desc:"URL was empty or null."},emptyScopesError:{code:"empty_input_scopes_error",desc:"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token."},nonArrayScopesError:{code:"nonarray_input_scopes_error",desc:"Scopes cannot be passed as non-array."},clientIdSingleScopeError:{code:"clientid_input_scopes_error",desc:"Client ID can only be provided as a single scope."},invalidPrompt:{code:"invalid_prompt_value",desc:"Supported prompt values are 'login', 'select_account', 'consent', 'create', 'none' and 'no_session'.  Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest"},invalidClaimsRequest:{code:"invalid_claims",desc:"Given claims parameter must be a stringified JSON object."},tokenRequestEmptyError:{code:"token_request_empty",desc:"Token request was empty and not found in cache."},logoutRequestEmptyError:{code:"logout_request_empty",desc:"The logout request was null or undefined."},invalidCodeChallengeMethod:{code:"invalid_code_challenge_method",desc:'code_challenge_method passed is invalid. Valid values are "plain" and "S256".'},invalidCodeChallengeParams:{code:"pkce_params_missing",desc:"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request"},invalidCloudDiscoveryMetadata:{code:"invalid_cloud_discovery_metadata",desc:"Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields"},invalidAuthorityMetadata:{code:"invalid_authority_metadata",desc:"Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields."},untrustedAuthority:{code:"untrusted_authority",desc:"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter."},invalidAzureCloudInstance:{code:"invalid_azure_cloud_instance",desc:"Invalid AzureCloudInstance provided. Please refer MSAL JS docs: aks.ms/msaljs/azure_cloud_instance for valid values"},missingSshJwk:{code:"missing_ssh_jwk",desc:"Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme."},missingSshKid:{code:"missing_ssh_kid",desc:"Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme."},missingNonceAuthenticationHeader:{code:"missing_nonce_authentication_header",desc:"Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce."},invalidAuthenticationHeader:{code:"invalid_authentication_header",desc:"Invalid authentication header provided"},authorityMismatch:{code:"authority_mismatch",desc:"Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority."}},de=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="ClientConfigurationError",Object.setPrototypeOf(o,t.prototype),o}return l(t,e),t.createRedirectUriEmptyError=function(){return new t(le.redirectUriNotSet.code,le.redirectUriNotSet.desc)},t.createPostLogoutRedirectUriEmptyError=function(){return new t(le.postLogoutUriNotSet.code,le.postLogoutUriNotSet.desc)},t.createClaimsRequestParsingError=function(e){return new t(le.claimsRequestParsingError.code,le.claimsRequestParsingError.desc+" Given value: "+e)},t.createInsecureAuthorityUriError=function(e){return new t(le.authorityUriInsecure.code,le.authorityUriInsecure.desc+" Given URI: "+e)},t.createUrlParseError=function(e){return new t(le.urlParseError.code,le.urlParseError.desc+" Given Error: "+e)},t.createUrlEmptyError=function(){return new t(le.urlEmptyError.code,le.urlEmptyError.desc)},t.createEmptyScopesArrayError=function(){return new t(le.emptyScopesError.code,""+le.emptyScopesError.desc)},t.createClientIdSingleScopeError=function(e){return new t(le.clientIdSingleScopeError.code,le.clientIdSingleScopeError.desc+" Given Scopes: "+e)},t.createInvalidPromptError=function(e){return new t(le.invalidPrompt.code,le.invalidPrompt.desc+" Given value: "+e)},t.createInvalidClaimsRequestError=function(){return new t(le.invalidClaimsRequest.code,le.invalidClaimsRequest.desc)},t.createEmptyLogoutRequestError=function(){return new t(le.logoutRequestEmptyError.code,le.logoutRequestEmptyError.desc)},t.createEmptyTokenRequestError=function(){return new t(le.tokenRequestEmptyError.code,le.tokenRequestEmptyError.desc)},t.createInvalidCodeChallengeMethodError=function(){return new t(le.invalidCodeChallengeMethod.code,le.invalidCodeChallengeMethod.desc)},t.createInvalidCodeChallengeParamsError=function(){return new t(le.invalidCodeChallengeParams.code,le.invalidCodeChallengeParams.desc)},t.createInvalidCloudDiscoveryMetadataError=function(){return new t(le.invalidCloudDiscoveryMetadata.code,le.invalidCloudDiscoveryMetadata.desc)},t.createInvalidAuthorityMetadataError=function(){return new t(le.invalidAuthorityMetadata.code,le.invalidAuthorityMetadata.desc)},t.createUntrustedAuthorityError=function(){return new t(le.untrustedAuthority.code,le.untrustedAuthority.desc)},t.createInvalidAzureCloudInstanceError=function(){return new t(le.invalidAzureCloudInstance.code,le.invalidAzureCloudInstance.desc)},t.createMissingSshJwkError=function(){return new t(le.missingSshJwk.code,le.missingSshJwk.desc)},t.createMissingSshKidError=function(){return new t(le.missingSshKid.code,le.missingSshKid.desc)},t.createMissingNonceAuthenticationHeadersError=function(){return new t(le.missingNonceAuthenticationHeader.code,le.missingNonceAuthenticationHeader.desc)},t.createInvalidAuthenticationHeaderError=function(e,r){return new t(le.invalidAuthenticationHeader.code,le.invalidAuthenticationHeader.desc+". Invalid header: "+e+". Details: "+r)},t.createAuthorityMismatchError=function(){return new t(le.authorityMismatch.code,le.authorityMismatch.desc)},t}(re),he=function(){function e(e){var t=this,r=e?ne.trimArrayEntries(g(e)):[],n=r?ne.removeEmptyStringsFromArray(r):[];this.validateInputScopes(n),this.scopes=new Set,n.forEach((function(e){return t.scopes.add(e)}))}return e.fromString=function(t){return new e((t||E.EMPTY_STRING).split(" "))},e.createSearchScopes=function(t){var r=new e(t);return r.containsOnlyOIDCScopes()?r.removeScope(E.OFFLINE_ACCESS_SCOPE):r.removeOIDCScopes(),r},e.prototype.validateInputScopes=function(e){if(!e||e.length<1)throw de.createEmptyScopesArrayError()},e.prototype.containsScope=function(t){var r=new e(this.printScopesLowerCase().split(" "));return!ne.isEmpty(t)&&r.scopes.has(t.toLowerCase())},e.prototype.containsScopeSet=function(e){var t=this;return!(!e||e.scopes.size<=0)&&(this.scopes.size>=e.scopes.size&&e.asArray().every((function(e){return t.containsScope(e)})))},e.prototype.containsOnlyOIDCScopes=function(){var e=this,t=0;return _.forEach((function(r){e.containsScope(r)&&(t+=1)})),this.scopes.size===t},e.prototype.appendScope=function(e){ne.isEmpty(e)||this.scopes.add(e.trim())},e.prototype.appendScopes=function(e){var t=this;try{e.forEach((function(e){return t.appendScope(e)}))}catch(e){throw re.createAppendScopeSetError(e)}},e.prototype.removeScope=function(e){if(ne.isEmpty(e))throw re.createRemoveEmptyScopeFromSetError(e);this.scopes.delete(e.trim())},e.prototype.removeOIDCScopes=function(){var e=this;_.forEach((function(t){e.scopes.delete(t)}))},e.prototype.unionScopeSets=function(e){if(!e)throw re.createEmptyInputScopeSetError();var t=new Set;return e.scopes.forEach((function(e){return t.add(e.toLowerCase())})),this.scopes.forEach((function(e){return t.add(e.toLowerCase())})),t},e.prototype.intersectingScopeSets=function(e){if(!e)throw re.createEmptyInputScopeSetError();e.containsOnlyOIDCScopes()||e.removeOIDCScopes();var t=this.unionScopeSets(e),r=e.getScopeCount(),n=this.getScopeCount();return t.size<n+r},e.prototype.getScopeCount=function(){return this.scopes.size},e.prototype.asArray=function(){var e=[];return this.scopes.forEach((function(t){return e.push(t)})),e},e.prototype.printScopes=function(){return this.scopes?this.asArray().join(" "):E.EMPTY_STRING},e.prototype.printScopesLowerCase=function(){return this.printScopes().toLowerCase()},e}();
/*! @azure/msal-common v13.3.3 2024-06-06 */
function pe(e,t){if(ne.isEmpty(e))throw re.createClientInfoEmptyError();try{var r=t.base64Decode(e);return JSON.parse(r)}catch(e){throw re.createClientInfoDecodingError(e.message)}}function ge(e){if(ne.isEmpty(e))throw re.createClientInfoDecodingError("Home account ID was empty.");var t=e.split(R.CLIENT_INFO_SEPARATOR,2);return{uid:t[0],utid:t.length<2?E.EMPTY_STRING:t[1]}}
/*! @azure/msal-common v13.3.3 2024-06-06 */!function(e){e[e.Default=0]="Default",e[e.Adfs=1]="Adfs",e[e.Dsts=2]="Dsts",e[e.Ciam=3]="Ciam"}(ce||(ce={})),
/*! @azure/msal-common v13.3.3 2024-06-06 */
e.ProtocolMode=void 0,(ue=e.ProtocolMode||(e.ProtocolMode={})).AAD="AAD",ue.OIDC="OIDC";
/*! @azure/msal-common v13.3.3 2024-06-06 */
var fe=function(){function t(){}return t.prototype.generateAccountId=function(){return[this.homeAccountId,this.environment].join(R.CACHE_KEY_SEPARATOR).toLowerCase()},t.prototype.generateAccountKey=function(){return t.generateAccountCacheKey({homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId})},t.prototype.getAccountInfo=function(){return{homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId,name:this.name,idTokenClaims:this.idTokenClaims,nativeAccountId:this.nativeAccountId,authorityType:this.authorityType}},t.generateAccountCacheKey=function(e){return[e.homeAccountId,e.environment||E.EMPTY_STRING,e.tenantId||E.EMPTY_STRING].join(R.CACHE_KEY_SEPARATOR).toLowerCase()},t.createAccount=function(r,n){var o=new t;n.authorityType===ce.Adfs?o.authorityType=k.ADFS_ACCOUNT_TYPE:n.protocolMode===e.ProtocolMode.AAD?o.authorityType=k.MSSTS_ACCOUNT_TYPE:o.authorityType=k.GENERIC_ACCOUNT_TYPE,o.clientInfo=r.clientInfo,o.homeAccountId=r.homeAccountId,o.nativeAccountId=r.nativeAccountId;var i=r.environment||n&&n.getPreferredCache();if(!i)throw re.createInvalidCacheEnvironmentError();if(o.environment=i,o.realm=r.idTokenClaims.tid||E.EMPTY_STRING,o.idTokenClaims=r.idTokenClaims,o.localAccountId=r.idTokenClaims.oid||r.idTokenClaims.sub||E.EMPTY_STRING,o.authorityType===k.MSSTS_ACCOUNT_TYPE){var a=r.idTokenClaims.preferred_username,s=r.idTokenClaims.emails?r.idTokenClaims.emails[0]:null;o.username=a||s||""}else o.username=r.idTokenClaims.upn||"";return o.name=r.idTokenClaims.name,o.cloudGraphHostName=r.cloudGraphHostName,o.msGraphHost=r.msGraphHost,o},t.createFromAccountInfo=function(e,r,n){var o=new t;return o.authorityType=e.authorityType||k.GENERIC_ACCOUNT_TYPE,o.homeAccountId=e.homeAccountId,o.localAccountId=e.localAccountId,o.nativeAccountId=e.nativeAccountId,o.realm=e.tenantId,o.environment=e.environment,o.username=e.username,o.name=e.name,o.idTokenClaims=e.idTokenClaims,o.cloudGraphHostName=r,o.msGraphHost=n,o},t.generateHomeAccountId=function(e,t,r,n,o){var i=(null==o?void 0:o.sub)?o.sub:E.EMPTY_STRING;if(t===ce.Adfs||t===ce.Dsts)return i;if(e)try{var a=pe(e,n);if(!ne.isEmpty(a.uid)&&!ne.isEmpty(a.utid))return""+a.uid+R.CLIENT_INFO_SEPARATOR+a.utid}catch(e){}return r.verbose("No client info in response"),i},t.isAccountEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("localAccountId")&&e.hasOwnProperty("username")&&e.hasOwnProperty("authorityType"))},t.accountInfoIsEqual=function(e,t,r){if(!e||!t)return!1;var n=!0;if(r){var o=e.idTokenClaims||{},i=t.idTokenClaims||{};n=o.iat===i.iat&&o.nonce===i.nonce}return e.homeAccountId===t.homeAccountId&&e.localAccountId===t.localAccountId&&e.username===t.username&&e.tenantId===t.tenantId&&e.environment===t.environment&&e.nativeAccountId===t.nativeAccountId&&n},t}(),me=function(){function e(t,r){if(ne.isEmpty(t))throw re.createTokenNullOrEmptyError(t);this.rawToken=t,this.claims=e.extractTokenClaims(t,r)}return e.extractTokenClaims=function(e,t){var r=ne.decodeAuthToken(e);try{var n=r.JWSPayload,o=t.base64Decode(n);return JSON.parse(o)}catch(e){throw re.createTokenParsingError(e)}},e.checkMaxAge=function(e,t){if(0===t||Date.now()-3e5>e+t)throw re.createMaxAgeTranspiredError()},e}(),ve=function(){function t(e,t,r){this.clientId=e,this.cryptoImpl=t,this.commonLogger=r.clone(ae,se)}return t.prototype.getAllAccounts=function(){var e=this,t=this.getAccountKeys();if(t.length<1)return[];var r=t.reduce((function(t,r){var n=e.getAccount(r);return n?(t.push(n),t):t}),[]);return r.length<1?[]:r.map((function(t){return e.getAccountInfoFromEntity(t)}))},t.prototype.getAccountInfoFilteredBy=function(e){var t=this.getAccountsFilteredBy(e);return t.length>0?this.getAccountInfoFromEntity(t[0]):null},t.prototype.getAccountInfoFromEntity=function(e){var t=e.getAccountInfo(),r=this.getIdToken(t);return r&&(t.idToken=r.secret,t.idTokenClaims=new me(r.secret,this.cryptoImpl).claims),t},t.prototype.saveCacheRecord=function(e){return h(this,void 0,void 0,(function(){return p(this,(function(t){switch(t.label){case 0:if(!e)throw re.createNullOrUndefinedCacheRecord();return e.account&&this.setAccount(e.account),e.idToken&&this.setIdTokenCredential(e.idToken),e.accessToken?[4,this.saveAccessToken(e.accessToken)]:[3,2];case 1:t.sent(),t.label=2;case 2:return e.refreshToken&&this.setRefreshTokenCredential(e.refreshToken),e.appMetadata&&this.setAppMetadata(e.appMetadata),[2]}}))}))},t.prototype.saveAccessToken=function(e){return h(this,void 0,void 0,(function(){var t,r,n,o,i=this;return p(this,(function(a){switch(a.label){case 0:return t={clientId:e.clientId,credentialType:e.credentialType,environment:e.environment,homeAccountId:e.homeAccountId,realm:e.realm,tokenType:e.tokenType,requestedClaimsHash:e.requestedClaimsHash},r=this.getTokenKeys(),n=he.fromString(e.target),o=[],r.accessToken.forEach((function(e){if(i.accessTokenKeyMatchesFilter(e,t,!1)){var r=i.getAccessTokenCredential(e);if(r&&i.credentialMatchesFilter(r,t))he.fromString(r.target).intersectingScopeSets(n)&&o.push(i.removeAccessToken(e))}})),[4,Promise.all(o)];case 1:return a.sent(),this.setAccessTokenCredential(e),[2]}}))}))},t.prototype.getAccountsFilteredBy=function(e){var t=this,r=this.getAccountKeys(),n=[];return r.forEach((function(r){if(t.isAccountKey(r,e.homeAccountId,e.realm)){var o=t.getAccount(r);o&&(e.homeAccountId&&!t.matchHomeAccountId(o,e.homeAccountId)||e.localAccountId&&!t.matchLocalAccountId(o,e.localAccountId)||e.username&&!t.matchUsername(o,e.username)||e.environment&&!t.matchEnvironment(o,e.environment)||e.realm&&!t.matchRealm(o,e.realm)||e.nativeAccountId&&!t.matchNativeAccountId(o,e.nativeAccountId)||n.push(o))}})),n},t.prototype.isAccountKey=function(e,t,r){return!(e.split(R.CACHE_KEY_SEPARATOR).length<3)&&(!(t&&!e.toLowerCase().includes(t.toLowerCase()))&&!(r&&!e.toLowerCase().includes(r.toLowerCase())))},t.prototype.isCredentialKey=function(e){if(e.split(R.CACHE_KEY_SEPARATOR).length<6)return!1;var t=e.toLowerCase();if(-1===t.indexOf(b.ID_TOKEN.toLowerCase())&&-1===t.indexOf(b.ACCESS_TOKEN.toLowerCase())&&-1===t.indexOf(b.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase())&&-1===t.indexOf(b.REFRESH_TOKEN.toLowerCase()))return!1;if(t.indexOf(b.REFRESH_TOKEN.toLowerCase())>-1){var r=""+b.REFRESH_TOKEN+R.CACHE_KEY_SEPARATOR+this.clientId+R.CACHE_KEY_SEPARATOR,n=""+b.REFRESH_TOKEN+R.CACHE_KEY_SEPARATOR+q+R.CACHE_KEY_SEPARATOR;if(-1===t.indexOf(r.toLowerCase())&&-1===t.indexOf(n.toLowerCase()))return!1}else if(-1===t.indexOf(this.clientId.toLowerCase()))return!1;return!0},t.prototype.credentialMatchesFilter=function(t,r){if(r.clientId&&!this.matchClientId(t,r.clientId))return!1;if(r.userAssertionHash&&!this.matchUserAssertionHash(t,r.userAssertionHash))return!1;if("string"==typeof r.homeAccountId&&!this.matchHomeAccountId(t,r.homeAccountId))return!1;if(r.environment&&!this.matchEnvironment(t,r.environment))return!1;if(r.realm&&!this.matchRealm(t,r.realm))return!1;if(r.credentialType&&!this.matchCredentialType(t,r.credentialType))return!1;if(r.familyId&&!this.matchFamilyId(t,r.familyId))return!1;if(r.target&&!this.matchTarget(t,r.target))return!1;if((r.requestedClaimsHash||t.requestedClaimsHash)&&t.requestedClaimsHash!==r.requestedClaimsHash)return!1;if(t.credentialType===b.ACCESS_TOKEN_WITH_AUTH_SCHEME){if(r.tokenType&&!this.matchTokenType(t,r.tokenType))return!1;if(r.tokenType===e.AuthenticationScheme.SSH&&r.keyId&&!this.matchKeyId(t,r.keyId))return!1}return!0},t.prototype.getAppMetadataFilteredBy=function(e){return this.getAppMetadataFilteredByInternal(e.environment,e.clientId)},t.prototype.getAppMetadataFilteredByInternal=function(e,t){var r=this,n=this.getKeys(),o={};return n.forEach((function(n){if(r.isAppMetadata(n)){var i=r.getAppMetadata(n);i&&(e&&!r.matchEnvironment(i,e)||t&&!r.matchClientId(i,t)||(o[n]=i))}})),o},t.prototype.getAuthorityMetadataByAlias=function(e){var t=this,r=this.getAuthorityMetadataKeys(),n=null;return r.forEach((function(r){if(t.isAuthorityMetadata(r)&&-1!==r.indexOf(t.clientId)){var o=t.getAuthorityMetadata(r);o&&-1!==o.aliases.indexOf(e)&&(n=o)}})),n},t.prototype.removeAllAccounts=function(){return h(this,void 0,void 0,(function(){var e,t,r=this;return p(this,(function(n){switch(n.label){case 0:return e=this.getAccountKeys(),t=[],e.forEach((function(e){t.push(r.removeAccount(e))})),[4,Promise.all(t)];case 1:return n.sent(),[2]}}))}))},t.prototype.removeAccount=function(e){return h(this,void 0,void 0,(function(){var t;return p(this,(function(r){switch(r.label){case 0:if(!(t=this.getAccount(e)))throw re.createNoAccountFoundError();return[4,this.removeAccountContext(t)];case 1:return r.sent(),this.removeItem(e),[2]}}))}))},t.prototype.removeAccountContext=function(e){return h(this,void 0,void 0,(function(){var t,r,n,o=this;return p(this,(function(i){switch(i.label){case 0:return t=this.getTokenKeys(),r=e.generateAccountId(),n=[],t.idToken.forEach((function(e){0===e.indexOf(r)&&o.removeIdToken(e)})),t.accessToken.forEach((function(e){0===e.indexOf(r)&&n.push(o.removeAccessToken(e))})),t.refreshToken.forEach((function(e){0===e.indexOf(r)&&o.removeRefreshToken(e)})),[4,Promise.all(n)];case 1:return i.sent(),[2]}}))}))},t.prototype.removeAccessToken=function(t){return h(this,void 0,void 0,(function(){var r,n;return p(this,(function(o){switch(o.label){case 0:if(!(r=this.getAccessTokenCredential(t)))return[2];if(r.credentialType.toLowerCase()!==b.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase())return[3,4];if(r.tokenType!==e.AuthenticationScheme.POP)return[3,4];if(!(n=r.keyId))return[3,4];o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.cryptoImpl.removeTokenBindingKey(n)];case 2:return o.sent(),[3,4];case 3:throw o.sent(),re.createBindingKeyNotRemovedError();case 4:return[2,this.removeItem(t)]}}))}))},t.prototype.removeAppMetadata=function(){var e=this;return this.getKeys().forEach((function(t){e.isAppMetadata(t)&&e.removeItem(t)})),!0},t.prototype.readCacheRecord=function(e,t,r){var n=this.getTokenKeys(),o=this.readAccountFromCache(e),i=this.getIdToken(e,n),a=this.getAccessToken(e,t,n),s=this.getRefreshToken(e,!1,n),c=this.readAppMetadataFromCache(r);return o&&i&&(o.idTokenClaims=new me(i.secret,this.cryptoImpl).claims),{account:o,idToken:i,accessToken:a,refreshToken:s,appMetadata:c}},t.prototype.readAccountFromCache=function(e){var t=fe.generateAccountCacheKey(e);return this.getAccount(t)},t.prototype.getIdToken=function(e,t){var r=this;this.commonLogger.trace("CacheManager - getIdToken called");var n={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:b.ID_TOKEN,clientId:this.clientId,realm:e.tenantId},o=this.getIdTokensByFilter(n,t),i=o.length;return i<1?(this.commonLogger.info("CacheManager:getIdToken - No token found"),null):i>1?(this.commonLogger.info("CacheManager:getIdToken - Multiple id tokens found, clearing them"),o.forEach((function(e){r.removeIdToken(e.generateCredentialKey())})),null):(this.commonLogger.info("CacheManager:getIdToken - Returning id token"),o[0])},t.prototype.getIdTokensByFilter=function(e,t){var r=this,n=t&&t.idToken||this.getTokenKeys().idToken,o=[];return n.forEach((function(t){if(r.idTokenKeyMatchesFilter(t,d({clientId:r.clientId},e))){var n=r.getIdTokenCredential(t);n&&r.credentialMatchesFilter(n,e)&&o.push(n)}})),o},t.prototype.idTokenKeyMatchesFilter=function(e,t){var r=e.toLowerCase();return(!t.clientId||-1!==r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase()))},t.prototype.removeIdToken=function(e){this.removeItem(e)},t.prototype.removeRefreshToken=function(e){this.removeItem(e)},t.prototype.getAccessToken=function(t,r,n){var o=this;this.commonLogger.trace("CacheManager - getAccessToken called");var i=he.createSearchScopes(r.scopes),a=r.authenticationScheme||e.AuthenticationScheme.BEARER,s=a&&a.toLowerCase()!==e.AuthenticationScheme.BEARER.toLowerCase()?b.ACCESS_TOKEN_WITH_AUTH_SCHEME:b.ACCESS_TOKEN,c={homeAccountId:t.homeAccountId,environment:t.environment,credentialType:s,clientId:this.clientId,realm:t.tenantId,target:i,tokenType:a,keyId:r.sshKid,requestedClaimsHash:r.requestedClaimsHash},u=n&&n.accessToken||this.getTokenKeys().accessToken,l=[];u.forEach((function(e){if(o.accessTokenKeyMatchesFilter(e,c,!0)){var t=o.getAccessTokenCredential(e);t&&o.credentialMatchesFilter(t,c)&&l.push(t)}}));var d=l.length;return d<1?(this.commonLogger.info("CacheManager:getAccessToken - No token found"),null):d>1?(this.commonLogger.info("CacheManager:getAccessToken - Multiple access tokens found, clearing them"),l.forEach((function(e){o.removeAccessToken(e.generateCredentialKey())})),null):(this.commonLogger.info("CacheManager:getAccessToken - Returning access token"),l[0])},t.prototype.accessTokenKeyMatchesFilter=function(e,t,r){var n=e.toLowerCase();if(t.clientId&&-1===n.indexOf(t.clientId.toLowerCase()))return!1;if(t.homeAccountId&&-1===n.indexOf(t.homeAccountId.toLowerCase()))return!1;if(t.realm&&-1===n.indexOf(t.realm.toLowerCase()))return!1;if(t.requestedClaimsHash&&-1===n.indexOf(t.requestedClaimsHash.toLowerCase()))return!1;if(t.target)for(var o=t.target.asArray(),i=0;i<o.length;i++){if(r&&!n.includes(o[i].toLowerCase()))return!1;if(!r&&n.includes(o[i].toLowerCase()))return!0}return!0},t.prototype.getAccessTokensByFilter=function(e){var t=this,r=this.getTokenKeys(),n=[];return r.accessToken.forEach((function(r){if(t.accessTokenKeyMatchesFilter(r,e,!0)){var o=t.getAccessTokenCredential(r);o&&t.credentialMatchesFilter(o,e)&&n.push(o)}})),n},t.prototype.getRefreshToken=function(e,t,r){var n=this;this.commonLogger.trace("CacheManager - getRefreshToken called");var o=t?q:void 0,i={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:b.REFRESH_TOKEN,clientId:this.clientId,familyId:o},a=r&&r.refreshToken||this.getTokenKeys().refreshToken,s=[];return a.forEach((function(e){if(n.refreshTokenKeyMatchesFilter(e,i)){var t=n.getRefreshTokenCredential(e);t&&n.credentialMatchesFilter(t,i)&&s.push(t)}})),s.length<1?(this.commonLogger.info("CacheManager:getRefreshToken - No refresh token found."),null):(this.commonLogger.info("CacheManager:getRefreshToken - returning refresh token"),s[0])},t.prototype.refreshTokenKeyMatchesFilter=function(e,t){var r=e.toLowerCase();return(!t.familyId||-1!==r.indexOf(t.familyId.toLowerCase()))&&(!(!t.familyId&&t.clientId&&-1===r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase())))},t.prototype.readAppMetadataFromCache=function(e){var t={environment:e,clientId:this.clientId},r=this.getAppMetadataFilteredBy(t),n=Object.keys(r).map((function(e){return r[e]})),o=n.length;if(o<1)return null;if(o>1)throw re.createMultipleMatchingAppMetadataInCacheError();return n[0]},t.prototype.isAppMetadataFOCI=function(e){var t=this.readAppMetadataFromCache(e);return!(!t||t.familyId!==q)},t.prototype.matchHomeAccountId=function(e,t){return!("string"!=typeof e.homeAccountId||t!==e.homeAccountId)},t.prototype.matchLocalAccountId=function(e,t){return!("string"!=typeof e.localAccountId||t!==e.localAccountId)},t.prototype.matchUsername=function(e,t){return!("string"!=typeof e.username||t.toLowerCase()!==e.username.toLowerCase())},t.prototype.matchUserAssertionHash=function(e,t){return!(!e.userAssertionHash||t!==e.userAssertionHash)},t.prototype.matchEnvironment=function(e,t){var r=this.getAuthorityMetadataByAlias(t);return!!(r&&r.aliases.indexOf(e.environment)>-1)},t.prototype.matchCredentialType=function(e,t){return e.credentialType&&t.toLowerCase()===e.credentialType.toLowerCase()},t.prototype.matchClientId=function(e,t){return!(!e.clientId||t!==e.clientId)},t.prototype.matchFamilyId=function(e,t){return!(!e.familyId||t!==e.familyId)},t.prototype.matchRealm=function(e,t){return!(!e.realm||t!==e.realm)},t.prototype.matchNativeAccountId=function(e,t){return!(!e.nativeAccountId||t!==e.nativeAccountId)},t.prototype.matchTarget=function(e,t){return!(e.credentialType!==b.ACCESS_TOKEN&&e.credentialType!==b.ACCESS_TOKEN_WITH_AUTH_SCHEME||!e.target)&&he.fromString(e.target).containsScopeSet(t)},t.prototype.matchTokenType=function(e,t){return!(!e.tokenType||e.tokenType!==t)},t.prototype.matchKeyId=function(e,t){return!(!e.keyId||e.keyId!==t)},t.prototype.isAppMetadata=function(e){return-1!==e.indexOf(O)},t.prototype.isAuthorityMetadata=function(e){return-1!==e.indexOf(U)},t.prototype.generateAuthorityMetadataCacheKey=function(e){return U+"-"+this.clientId+"-"+e},t.toObject=function(e,t){for(var r in t)e[r]=t[r];return e},t}(),ye=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return l(t,e),t.prototype.setAccount=function(){throw $.createUnexpectedError("Storage interface - setAccount() has not been implemented for the cacheStorage interface.")},t.prototype.getAccount=function(){throw $.createUnexpectedError("Storage interface - getAccount() has not been implemented for the cacheStorage interface.")},t.prototype.setIdTokenCredential=function(){throw $.createUnexpectedError("Storage interface - setIdTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.getIdTokenCredential=function(){throw $.createUnexpectedError("Storage interface - getIdTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.setAccessTokenCredential=function(){throw $.createUnexpectedError("Storage interface - setAccessTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.getAccessTokenCredential=function(){throw $.createUnexpectedError("Storage interface - getAccessTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.setRefreshTokenCredential=function(){throw $.createUnexpectedError("Storage interface - setRefreshTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.getRefreshTokenCredential=function(){throw $.createUnexpectedError("Storage interface - getRefreshTokenCredential() has not been implemented for the cacheStorage interface.")},t.prototype.setAppMetadata=function(){throw $.createUnexpectedError("Storage interface - setAppMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.getAppMetadata=function(){throw $.createUnexpectedError("Storage interface - getAppMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.setServerTelemetry=function(){throw $.createUnexpectedError("Storage interface - setServerTelemetry() has not been implemented for the cacheStorage interface.")},t.prototype.getServerTelemetry=function(){throw $.createUnexpectedError("Storage interface - getServerTelemetry() has not been implemented for the cacheStorage interface.")},t.prototype.setAuthorityMetadata=function(){throw $.createUnexpectedError("Storage interface - setAuthorityMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.getAuthorityMetadata=function(){throw $.createUnexpectedError("Storage interface - getAuthorityMetadata() has not been implemented for the cacheStorage interface.")},t.prototype.getAuthorityMetadataKeys=function(){throw $.createUnexpectedError("Storage interface - getAuthorityMetadataKeys() has not been implemented for the cacheStorage interface.")},t.prototype.setThrottlingCache=function(){throw $.createUnexpectedError("Storage interface - setThrottlingCache() has not been implemented for the cacheStorage interface.")},t.prototype.getThrottlingCache=function(){throw $.createUnexpectedError("Storage interface - getThrottlingCache() has not been implemented for the cacheStorage interface.")},t.prototype.removeItem=function(){throw $.createUnexpectedError("Storage interface - removeItem() has not been implemented for the cacheStorage interface.")},t.prototype.containsKey=function(){throw $.createUnexpectedError("Storage interface - containsKey() has not been implemented for the cacheStorage interface.")},t.prototype.getKeys=function(){throw $.createUnexpectedError("Storage interface - getKeys() has not been implemented for the cacheStorage interface.")},t.prototype.getAccountKeys=function(){throw $.createUnexpectedError("Storage interface - getAccountKeys() has not been implemented for the cacheStorage interface.")},t.prototype.getTokenKeys=function(){throw $.createUnexpectedError("Storage interface - getTokenKeys() has not been implemented for the cacheStorage interface.")},t.prototype.clear=function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Storage interface - clear() has not been implemented for the cacheStorage interface.",$.createUnexpectedError("Storage interface - clear() has not been implemented for the cacheStorage interface.")}))}))},t.prototype.updateCredentialCacheKey=function(){throw $.createUnexpectedError("Storage interface - updateCredentialCacheKey() has not been implemented for the cacheStorage interface.")},t}(ve),Ce={tokenRenewalOffsetSeconds:300,preventCorsPreflight:!1},Ee={loggerCallback:function(){},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info,correlationId:E.EMPTY_STRING},Te={claimsBasedCachingEnabled:!0},_e={sendGetRequestAsync:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Network interface - sendGetRequestAsync() has not been implemented",$.createUnexpectedError("Network interface - sendGetRequestAsync() has not been implemented")}))}))},sendPostRequestAsync:function(){return h(this,void 0,void 0,(function(){return p(this,(function(e){throw"Network interface - sendPostRequestAsync() has not been implemented",$.createUnexpectedError("Network interface - sendPostRequestAsync() has not been implemented")}))}))}},Ie={sku:E.SKU,version:se,cpu:E.EMPTY_STRING,os:E.EMPTY_STRING},we={clientSecret:E.EMPTY_STRING,clientAssertion:void 0},Se={azureCloudInstance:e.AzureCloudInstance.None,tenant:""+E.DEFAULT_COMMON_TENANT},Ae={application:{appName:"",appVersion:""}};
/*! @azure/msal-common v13.3.3 2024-06-06 */
/*! @azure/msal-common v13.3.3 2024-06-06 */
var ke,Re=function(e){function t(r,n,o){var i=e.call(this,r,n,o)||this;return i.name="ServerError",Object.setPrototypeOf(i,t.prototype),i}return l(t,e),t}($),be=function(){function e(){}return e.generateThrottlingStorageKey=function(e){return Y+"."+JSON.stringify(e)},e.preProcess=function(t,r){var n,o=e.generateThrottlingStorageKey(r),i=t.getThrottlingCache(o);if(i){if(i.throttleTime<Date.now())return void t.removeItem(o);throw new Re((null===(n=i.errorCodes)||void 0===n?void 0:n.join(" "))||E.EMPTY_STRING,i.errorMessage,i.subError)}},e.postProcess=function(t,r,n){if(e.checkResponseStatus(n)||e.checkResponseForRetryAfter(n)){var o={throttleTime:e.calculateThrottleTime(parseInt(n.headers[f.RETRY_AFTER])),error:n.body.error,errorCodes:n.body.error_codes,errorMessage:n.body.error_description,subError:n.body.suberror};t.setThrottlingCache(e.generateThrottlingStorageKey(r),o)}},e.checkResponseStatus=function(e){return 429===e.status||e.status>=500&&e.status<600},e.checkResponseForRetryAfter=function(e){return!!e.headers&&(e.headers.hasOwnProperty(f.RETRY_AFTER)&&(e.status<200||e.status>=300))},e.calculateThrottleTime=function(e){var t=e<=0?0:e,r=Date.now()/1e3;return Math.floor(1e3*Math.min(r+(t||Q),r+j))},e.removeThrottle=function(e,t,r,n){var o={clientId:t,authority:r.authority,scopes:r.scopes,homeAccountIdentifier:n,claims:r.claims,authenticationScheme:r.authenticationScheme,resourceRequestMethod:r.resourceRequestMethod,resourceRequestUri:r.resourceRequestUri,shrClaims:r.shrClaims,sshKid:r.sshKid},i=this.generateThrottlingStorageKey(o);e.removeItem(i)},e}(),Pe=function(){function e(e,t){this.networkClient=e,this.cacheManager=t}return e.prototype.sendPostRequest=function(e,t,r){return h(this,void 0,void 0,(function(){var n,o;return p(this,(function(i){switch(i.label){case 0:be.preProcess(this.cacheManager,e),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.networkClient.sendPostRequestAsync(t,r)];case 2:return n=i.sent(),[3,4];case 3:throw(o=i.sent())instanceof $?o:re.createNetworkError(t,o);case 4:return be.postProcess(this.cacheManager,e,n),[2,n]}}))}))},e}();
/*! @azure/msal-common v13.3.3 2024-06-06 */!function(e){e.HOME_ACCOUNT_ID="home_account_id",e.UPN="UPN"}(ke||(ke={}));
/*! @azure/msal-common v13.3.3 2024-06-06 */
var Ne,Me,Oe=function(){function e(){}return e.validateRedirectUri=function(e){if(ne.isEmpty(e))throw de.createRedirectUriEmptyError()},e.validatePrompt=function(e){var t=[];for(var r in w)t.push(w[r]);if(t.indexOf(e)<0)throw de.createInvalidPromptError(e)},e.validateClaims=function(e){try{JSON.parse(e)}catch(e){throw de.createInvalidClaimsRequestError()}},e.validateCodeChallengeParams=function(e,t){if(ne.isEmpty(e)||ne.isEmpty(t))throw de.createInvalidCodeChallengeParamsError();this.validateCodeChallengeMethod(t)},e.validateCodeChallengeMethod=function(e){if([N.PLAIN,N.S256].indexOf(e)<0)throw de.createInvalidCodeChallengeMethodError()},e.sanitizeEQParams=function(e,t){return e?(t.forEach((function(t,r){e[r]&&delete e[r]})),Object.fromEntries(Object.entries(e).filter((function(e){return""!==e[1]})))):{}},e}(),qe=function(){function t(){this.parameters=new Map}return t.prototype.addResponseTypeCode=function(){this.parameters.set(y.RESPONSE_TYPE,encodeURIComponent(E.CODE_RESPONSE_TYPE))},t.prototype.addResponseTypeForTokenAndIdToken=function(){this.parameters.set(y.RESPONSE_TYPE,encodeURIComponent(E.TOKEN_RESPONSE_TYPE+" "+E.ID_TOKEN_RESPONSE_TYPE))},t.prototype.addResponseMode=function(e){this.parameters.set(y.RESPONSE_MODE,encodeURIComponent(e||S.QUERY))},t.prototype.addNativeBroker=function(){this.parameters.set(y.NATIVE_BROKER,encodeURIComponent("1"))},t.prototype.addScopes=function(e,t){void 0===t&&(t=!0);var r=t?g(e||[],T):e||[],n=new he(r);this.parameters.set(y.SCOPE,encodeURIComponent(n.printScopes()))},t.prototype.addClientId=function(e){this.parameters.set(y.CLIENT_ID,encodeURIComponent(e))},t.prototype.addRedirectUri=function(e){Oe.validateRedirectUri(e),this.parameters.set(y.REDIRECT_URI,encodeURIComponent(e))},t.prototype.addPostLogoutRedirectUri=function(e){Oe.validateRedirectUri(e),this.parameters.set(y.POST_LOGOUT_URI,encodeURIComponent(e))},t.prototype.addIdTokenHint=function(e){this.parameters.set(y.ID_TOKEN_HINT,encodeURIComponent(e))},t.prototype.addDomainHint=function(e){this.parameters.set(I.DOMAIN_HINT,encodeURIComponent(e))},t.prototype.addLoginHint=function(e){this.parameters.set(I.LOGIN_HINT,encodeURIComponent(e))},t.prototype.addCcsUpn=function(e){this.parameters.set(f.CCS_HEADER,encodeURIComponent("UPN:"+e))},t.prototype.addCcsOid=function(e){this.parameters.set(f.CCS_HEADER,encodeURIComponent("Oid:"+e.uid+"@"+e.utid))},t.prototype.addSid=function(e){this.parameters.set(I.SID,encodeURIComponent(e))},t.prototype.addClaims=function(e,t){var r=this.addClientCapabilitiesToClaims(e,t);Oe.validateClaims(r),this.parameters.set(y.CLAIMS,encodeURIComponent(r))},t.prototype.addCorrelationId=function(e){this.parameters.set(y.CLIENT_REQUEST_ID,encodeURIComponent(e))},t.prototype.addLibraryInfo=function(e){this.parameters.set(y.X_CLIENT_SKU,e.sku),this.parameters.set(y.X_CLIENT_VER,e.version),e.os&&this.parameters.set(y.X_CLIENT_OS,e.os),e.cpu&&this.parameters.set(y.X_CLIENT_CPU,e.cpu)},t.prototype.addApplicationTelemetry=function(e){(null==e?void 0:e.appName)&&this.parameters.set(y.X_APP_NAME,e.appName),(null==e?void 0:e.appVersion)&&this.parameters.set(y.X_APP_VER,e.appVersion)},t.prototype.addPrompt=function(e){Oe.validatePrompt(e),this.parameters.set(""+y.PROMPT,encodeURIComponent(e))},t.prototype.addState=function(e){ne.isEmpty(e)||this.parameters.set(y.STATE,encodeURIComponent(e))},t.prototype.addNonce=function(e){this.parameters.set(y.NONCE,encodeURIComponent(e))},t.prototype.addCodeChallengeParams=function(e,t){if(Oe.validateCodeChallengeParams(e,t),!e||!t)throw de.createInvalidCodeChallengeParamsError();this.parameters.set(y.CODE_CHALLENGE,encodeURIComponent(e)),this.parameters.set(y.CODE_CHALLENGE_METHOD,encodeURIComponent(t))},t.prototype.addAuthorizationCode=function(e){this.parameters.set(y.CODE,encodeURIComponent(e))},t.prototype.addDeviceCode=function(e){this.parameters.set(y.DEVICE_CODE,encodeURIComponent(e))},t.prototype.addRefreshToken=function(e){this.parameters.set(y.REFRESH_TOKEN,encodeURIComponent(e))},t.prototype.addCodeVerifier=function(e){this.parameters.set(y.CODE_VERIFIER,encodeURIComponent(e))},t.prototype.addClientSecret=function(e){this.parameters.set(y.CLIENT_SECRET,encodeURIComponent(e))},t.prototype.addClientAssertion=function(e){ne.isEmpty(e)||this.parameters.set(y.CLIENT_ASSERTION,encodeURIComponent(e))},t.prototype.addClientAssertionType=function(e){ne.isEmpty(e)||this.parameters.set(y.CLIENT_ASSERTION_TYPE,encodeURIComponent(e))},t.prototype.addOboAssertion=function(e){this.parameters.set(y.OBO_ASSERTION,encodeURIComponent(e))},t.prototype.addRequestTokenUse=function(e){this.parameters.set(y.REQUESTED_TOKEN_USE,encodeURIComponent(e))},t.prototype.addGrantType=function(e){this.parameters.set(y.GRANT_TYPE,encodeURIComponent(e))},t.prototype.addClientInfo=function(){this.parameters.set("client_info","1")},t.prototype.addExtraQueryParameters=function(e){var t=this,r=Oe.sanitizeEQParams(e,this.parameters);Object.keys(r).forEach((function(r){t.parameters.set(r,e[r])}))},t.prototype.addClientCapabilitiesToClaims=function(e,t){var r;if(e)try{r=JSON.parse(e)}catch(e){throw de.createInvalidClaimsRequestError()}else r={};return t&&t.length>0&&(r.hasOwnProperty(C.ACCESS_TOKEN)||(r[C.ACCESS_TOKEN]={}),r[C.ACCESS_TOKEN][C.XMS_CC]={values:t}),JSON.stringify(r)},t.prototype.addUsername=function(e){this.parameters.set(K.username,encodeURIComponent(e))},t.prototype.addPassword=function(e){this.parameters.set(K.password,encodeURIComponent(e))},t.prototype.addPopToken=function(t){ne.isEmpty(t)||(this.parameters.set(y.TOKEN_TYPE,e.AuthenticationScheme.POP),this.parameters.set(y.REQ_CNF,encodeURIComponent(t)))},t.prototype.addSshJwk=function(t){ne.isEmpty(t)||(this.parameters.set(y.TOKEN_TYPE,e.AuthenticationScheme.SSH),this.parameters.set(y.REQ_CNF,encodeURIComponent(t)))},t.prototype.addServerTelemetry=function(e){this.parameters.set(y.X_CLIENT_CURR_TELEM,e.generateCurrentRequestHeaderValue()),this.parameters.set(y.X_CLIENT_LAST_TELEM,e.generateLastRequestHeaderValue())},t.prototype.addThrottling=function(){this.parameters.set(y.X_MS_LIB_CAPABILITY,W)},t.prototype.addLogoutHint=function(e){this.parameters.set(y.LOGOUT_HINT,encodeURIComponent(e))},t.prototype.createQueryString=function(){var e=new Array;return this.parameters.forEach((function(t,r){e.push(r+"="+t)})),e.join("&")},t}(),Ue=function(){function e(e,t){var r,n,o,i,a,s,c,u,l,h,p,g,f,m,v,y;this.config=(o=(r=e).authOptions,i=r.systemOptions,a=r.loggerOptions,s=r.cacheOptions,c=r.storageInterface,u=r.networkInterface,l=r.cryptoInterface,h=r.clientCredentials,p=r.libraryInfo,g=r.telemetry,f=r.serverTelemetryManager,m=r.persistencePlugin,v=r.serializableCache,y=d(d({},Ee),a),{authOptions:(n=o,d({clientCapabilities:[],azureCloudOptions:Se,skipAuthorityMetadataCache:!1},n)),systemOptions:d(d({},Ce),i),loggerOptions:y,cacheOptions:d(d({},Te),s),storageInterface:c||new ye(o.clientId,ee,new ie(y)),networkInterface:u||_e,cryptoInterface:l||ee,clientCredentials:h||we,libraryInfo:d(d({},Ie),p),telemetry:d(d({},Ae),g),serverTelemetryManager:f||null,persistencePlugin:m||null,serializableCache:v||null}),this.logger=new ie(this.config.loggerOptions,ae,se),this.cryptoUtils=this.config.cryptoInterface,this.cacheManager=this.config.storageInterface,this.networkClient=this.config.networkInterface,this.networkManager=new Pe(this.networkClient,this.cacheManager),this.serverTelemetryManager=this.config.serverTelemetryManager,this.authority=this.config.authOptions.authority,this.performanceClient=t}return e.prototype.createTokenRequestHeaders=function(e){var t={};if(t[f.CONTENT_TYPE]=E.URL_FORM_CONTENT_TYPE,!this.config.systemOptions.preventCorsPreflight&&e)switch(e.type){case ke.HOME_ACCOUNT_ID:try{var r=ge(e.credential);t[f.CCS_HEADER]="Oid:"+r.uid+"@"+r.utid}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case ke.UPN:t[f.CCS_HEADER]="UPN: "+e.credential}return t},e.prototype.executePostToTokenEndpoint=function(e,t,r,n){return h(this,void 0,void 0,(function(){var o;return p(this,(function(i){switch(i.label){case 0:return[4,this.networkManager.sendPostRequest(n,e,{body:t,headers:r})];case 1:return o=i.sent(),this.config.serverTelemetryManager&&o.status<500&&429!==o.status&&this.config.serverTelemetryManager.clearTelemetryCache(),[2,o]}}))}))},e.prototype.updateAuthority=function(e){if(!e.discoveryComplete())throw re.createEndpointDiscoveryIncompleteError("Updated authority has not completed endpoint discovery.");this.authority=e},e.prototype.createTokenQueryParameters=function(e){var t=new qe;return e.tokenQueryParameters&&t.addExtraQueryParameters(e.tokenQueryParameters),t.createQueryString()},e}(),He=function(){function t(){}return t.prototype.generateAccountId=function(){return t.generateAccountIdForCacheKey(this.homeAccountId,this.environment)},t.prototype.generateCredentialId=function(){return t.generateCredentialIdForCacheKey(this.credentialType,this.clientId,this.realm,this.familyId)},t.prototype.generateTarget=function(){return t.generateTargetForCacheKey(this.target)},t.prototype.generateCredentialKey=function(){return t.generateCredentialCacheKey(this.homeAccountId,this.environment,this.credentialType,this.clientId,this.realm,this.target,this.familyId,this.tokenType,this.requestedClaimsHash)},t.prototype.generateType=function(){switch(this.credentialType){case b.ID_TOKEN:return P.ID_TOKEN;case b.ACCESS_TOKEN:case b.ACCESS_TOKEN_WITH_AUTH_SCHEME:return P.ACCESS_TOKEN;case b.REFRESH_TOKEN:return P.REFRESH_TOKEN;default:throw re.createUnexpectedCredentialTypeError()}},t.generateCredentialCacheKey=function(e,t,r,n,o,i,a,s,c){return[this.generateAccountIdForCacheKey(e,t),this.generateCredentialIdForCacheKey(r,n,o,a),this.generateTargetForCacheKey(i),this.generateClaimsHashForCacheKey(c),this.generateSchemeForCacheKey(s)].join(R.CACHE_KEY_SEPARATOR).toLowerCase()},t.generateAccountIdForCacheKey=function(e,t){return[e,t].join(R.CACHE_KEY_SEPARATOR).toLowerCase()},t.generateCredentialIdForCacheKey=function(e,t,r,n){return[e,e===b.REFRESH_TOKEN&&n||t,r||E.EMPTY_STRING].join(R.CACHE_KEY_SEPARATOR).toLowerCase()},t.generateTargetForCacheKey=function(e){return(e||E.EMPTY_STRING).toLowerCase()},t.generateClaimsHashForCacheKey=function(e){return(e||E.EMPTY_STRING).toLowerCase()},t.generateSchemeForCacheKey=function(t){return t&&t.toLowerCase()!==e.AuthenticationScheme.BEARER.toLowerCase()?t.toLowerCase():E.EMPTY_STRING},t}(),Le=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return l(t,e),t.createIdTokenEntity=function(e,r,n,o,i){var a=new t;return a.credentialType=b.ID_TOKEN,a.homeAccountId=e,a.environment=r,a.clientId=o,a.secret=n,a.realm=i,a},t.isIdTokenEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")&&e.credentialType===b.ID_TOKEN)},t}(He),De=function(){function e(){}return e.nowSeconds=function(){return Math.round((new Date).getTime()/1e3)},e.isTokenExpired=function(t,r){var n=Number(t)||0;return e.nowSeconds()+r>n},e.wasClockTurnedBack=function(t){return Number(t)>e.nowSeconds()},e.delay=function(e,t){return new Promise((function(r){return setTimeout((function(){return r(t)}),e)}))},e}(),Ke=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return l(r,t),r.createAccessTokenEntity=function(t,n,o,i,a,s,c,u,l,d,h,p,g,f,m){var v,y,C=new r;C.homeAccountId=t,C.credentialType=b.ACCESS_TOKEN,C.secret=o;var E=De.nowSeconds();if(C.cachedAt=E.toString(),C.expiresOn=c.toString(),C.extendedExpiresOn=u.toString(),d&&(C.refreshOn=d.toString()),C.environment=n,C.clientId=i,C.realm=a,C.target=s,C.userAssertionHash=p,C.tokenType=ne.isEmpty(h)?e.AuthenticationScheme.BEARER:h,f&&(C.requestedClaims=f,C.requestedClaimsHash=m),(null===(v=C.tokenType)||void 0===v?void 0:v.toLowerCase())!==e.AuthenticationScheme.BEARER.toLowerCase())switch(C.credentialType=b.ACCESS_TOKEN_WITH_AUTH_SCHEME,C.tokenType){case e.AuthenticationScheme.POP:var T=me.extractTokenClaims(o,l);if(!(null===(y=null==T?void 0:T.cnf)||void 0===y?void 0:y.kid))throw re.createTokenClaimsRequiredError();C.keyId=T.cnf.kid;break;case e.AuthenticationScheme.SSH:C.keyId=g}return C},r.isAccessTokenEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")&&e.hasOwnProperty("target")&&(e.credentialType===b.ACCESS_TOKEN||e.credentialType===b.ACCESS_TOKEN_WITH_AUTH_SCHEME))},r}(He),Fe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return l(t,e),t.createRefreshTokenEntity=function(e,r,n,o,i,a){var s=new t;return s.clientId=o,s.credentialType=b.REFRESH_TOKEN,s.environment=r,s.homeAccountId=e,s.secret=n,s.userAssertionHash=a,i&&(s.familyId=i),s},t.isRefreshTokenEntity=function(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")&&e.credentialType===b.REFRESH_TOKEN)},t}(He),Be=["interaction_required","consent_required","login_required"],xe=["message_only","additional_action","basic_action","user_password_expired","consent_required"],Ge={noTokensFoundError:{code:"no_tokens_found",desc:"No refresh token found in the cache. Please sign-in."},native_account_unavailable:{code:"native_account_unavailable",desc:"The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API."}},ze=function(e){function t(r,n,o,i,a,s,c){var u=e.call(this,r,n,o)||this;return Object.setPrototypeOf(u,t.prototype),u.timestamp=i||E.EMPTY_STRING,u.traceId=a||E.EMPTY_STRING,u.correlationId=s||E.EMPTY_STRING,u.claims=c||E.EMPTY_STRING,u.name="InteractionRequiredAuthError",u}return l(t,e),t.isInteractionRequiredError=function(e,t,r){var n=!!e&&Be.indexOf(e)>-1,o=!!r&&xe.indexOf(r)>-1,i=!!t&&Be.some((function(e){return t.indexOf(e)>-1}));return n||i||o},t.createNoTokensFoundError=function(){return new t(Ge.noTokensFoundError.code,Ge.noTokensFoundError.desc)},t.createNativeAccountUnavailableError=function(){return new t(Ge.native_account_unavailable.code,Ge.native_account_unavailable.desc)},t}($),Qe=function(e,t,r,n,o){this.account=e||null,this.idToken=t||null,this.accessToken=r||null,this.refreshToken=n||null,this.appMetadata=o||null},je=function(){function e(){}return e.setRequestState=function(t,r,n){var o=e.generateLibraryState(t,n);return ne.isEmpty(r)?o:""+o+E.RESOURCE_DELIM+r},e.generateLibraryState=function(e,t){if(!e)throw re.createNoCryptoObjectError("generateLibraryState");var r={id:e.createNewGuid()};t&&(r.meta=t);var n=JSON.stringify(r);return e.base64Encode(n)},e.parseRequestState=function(e,t){if(!e)throw re.createNoCryptoObjectError("parseRequestState");if(ne.isEmpty(t))throw re.createInvalidStateError(t,"Null, undefined or empty state");try{var r=t.split(E.RESOURCE_DELIM),n=r[0],o=r.length>1?r.slice(1).join(E.RESOURCE_DELIM):E.EMPTY_STRING,i=e.base64Decode(n),a=JSON.parse(i);return{userRequestState:ne.isEmpty(o)?E.EMPTY_STRING:o,libraryState:a}}catch(e){throw re.createInvalidStateError(t,e)}},e}(),Ye=function(){function e(t){if(this._urlString=t,ne.isEmpty(this._urlString))throw de.createUrlEmptyError();ne.isEmpty(this.getHash())&&(this._urlString=e.canonicalizeUri(t))}return Object.defineProperty(e.prototype,"urlString",{get:function(){return this._urlString},enumerable:!1,configurable:!0}),e.canonicalizeUri=function(e){if(e){var t=e.toLowerCase();return ne.endsWith(t,"?")?t=t.slice(0,-1):ne.endsWith(t,"?/")&&(t=t.slice(0,-2)),ne.endsWith(t,"/")||(t+="/"),t}return e},e.prototype.validateAsUri=function(){var e;try{e=this.getUrlComponents()}catch(e){throw de.createUrlParseError(e)}if(!e.HostNameAndPort||!e.PathSegments)throw de.createUrlParseError("Given url string: "+this.urlString);if(!e.Protocol||"https:"!==e.Protocol.toLowerCase())throw de.createInsecureAuthorityUriError(this.urlString)},e.appendQueryString=function(e,t){return ne.isEmpty(t)?e:e.indexOf("?")<0?e+"?"+t:e+"&"+t},e.removeHashFromUrl=function(t){return e.canonicalizeUri(t.split("#")[0])},e.prototype.replaceTenantPath=function(t){var r=this.getUrlComponents(),n=r.PathSegments;return!t||0===n.length||n[0]!==v.COMMON&&n[0]!==v.ORGANIZATIONS||(n[0]=t),e.constructAuthorityUriFromObject(r)},e.prototype.getHash=function(){return e.parseHash(this.urlString)},e.prototype.getUrlComponents=function(){var e=RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"),t=this.urlString.match(e);if(!t)throw de.createUrlParseError("Given url string: "+this.urlString);var r={Protocol:t[1],HostNameAndPort:t[4],AbsolutePath:t[5],QueryString:t[7]},n=r.AbsolutePath.split("/");return n=n.filter((function(e){return e&&e.length>0})),r.PathSegments=n,!ne.isEmpty(r.QueryString)&&r.QueryString.endsWith("/")&&(r.QueryString=r.QueryString.substring(0,r.QueryString.length-1)),r},e.getDomainFromUrl=function(e){var t=RegExp("^([^:/?#]+://)?([^/?#]*)"),r=e.match(t);if(!r)throw de.createUrlParseError("Given url string: "+e);return r[2]},e.getAbsoluteUrl=function(t,r){if(t[0]===E.FORWARD_SLASH){var n=new e(r).getUrlComponents();return n.Protocol+"//"+n.HostNameAndPort+t}return t},e.parseHash=function(e){var t=e.indexOf("#"),r=e.indexOf("#/");return r>-1?e.substring(r+2):t>-1?e.substring(t+1):E.EMPTY_STRING},e.parseQueryString=function(e){var t=e.indexOf("?"),r=e.indexOf("/?");return r>-1?e.substring(r+2):t>-1?e.substring(t+1):E.EMPTY_STRING},e.constructAuthorityUriFromObject=function(t){return new e(t.Protocol+"//"+t.HostNameAndPort+"/"+t.PathSegments.join("/"))},e.getDeserializedHash=function(t){if(ne.isEmpty(t))return{};var r=e.parseHash(t),n=ne.queryStringToObject(ne.isEmpty(r)?t:r);if(!n)throw re.createHashNotDeserializedError(JSON.stringify(n));return n},e.getDeserializedQueryString=function(t){if(ne.isEmpty(t))return{};var r=e.parseQueryString(t),n=ne.queryStringToObject(ne.isEmpty(r)?t:r);if(!n)throw re.createHashNotDeserializedError(JSON.stringify(n));return n},e.hashContainsKnownProperties=function(t){if(ne.isEmpty(t)||t.indexOf("=")<0)return!1;var r=e.getDeserializedHash(t);return!!(r.code||r.error_description||r.error||r.state)},e}();
/*! @azure/msal-common v13.3.3 2024-06-06 */
/*! @azure/msal-common v13.3.3 2024-06-06 */
e.PerformanceEvents=void 0,(Ne=e.PerformanceEvents||(e.PerformanceEvents={})).AcquireTokenByCode="acquireTokenByCode",Ne.AcquireTokenByRefreshToken="acquireTokenByRefreshToken",Ne.AcquireTokenSilent="acquireTokenSilent",Ne.AcquireTokenSilentAsync="acquireTokenSilentAsync",Ne.AcquireTokenPopup="acquireTokenPopup",Ne.CryptoOptsGetPublicKeyThumbprint="cryptoOptsGetPublicKeyThumbprint",Ne.CryptoOptsSignJwt="cryptoOptsSignJwt",Ne.SilentCacheClientAcquireToken="silentCacheClientAcquireToken",Ne.SilentIframeClientAcquireToken="silentIframeClientAcquireToken",Ne.SilentRefreshClientAcquireToken="silentRefreshClientAcquireToken",Ne.SsoSilent="ssoSilent",Ne.StandardInteractionClientGetDiscoveredAuthority="standardInteractionClientGetDiscoveredAuthority",Ne.FetchAccountIdWithNativeBroker="fetchAccountIdWithNativeBroker",Ne.NativeInteractionClientAcquireToken="nativeInteractionClientAcquireToken",Ne.BaseClientCreateTokenRequestHeaders="baseClientCreateTokenRequestHeaders",Ne.BrokerHandhshake="brokerHandshake",Ne.AcquireTokenByRefreshTokenInBroker="acquireTokenByRefreshTokenInBroker",Ne.AcquireTokenByBroker="acquireTokenByBroker",Ne.RefreshTokenClientExecuteTokenRequest="refreshTokenClientExecuteTokenRequest",Ne.RefreshTokenClientAcquireToken="refreshTokenClientAcquireToken",Ne.RefreshTokenClientAcquireTokenWithCachedRefreshToken="refreshTokenClientAcquireTokenWithCachedRefreshToken",Ne.RefreshTokenClientAcquireTokenByRefreshToken="refreshTokenClientAcquireTokenByRefreshToken",Ne.RefreshTokenClientCreateTokenRequestBody="refreshTokenClientCreateTokenRequestBody",Ne.AcquireTokenFromCache="acquireTokenFromCache",Ne.AcquireTokenBySilentIframe="acquireTokenBySilentIframe",Ne.InitializeBaseRequest="initializeBaseRequest",Ne.InitializeSilentRequest="initializeSilentRequest",Ne.InitializeClientApplication="initializeClientApplication",Ne.SilentIframeClientTokenHelper="silentIframeClientTokenHelper",Ne.SilentHandlerInitiateAuthRequest="silentHandlerInitiateAuthRequest",Ne.SilentHandlerMonitorIframeForHash="silentHandlerMonitorIframeForHash",Ne.SilentHandlerLoadFrame="silentHandlerLoadFrame",Ne.StandardInteractionClientCreateAuthCodeClient="standardInteractionClientCreateAuthCodeClient",Ne.StandardInteractionClientGetClientConfiguration="standardInteractionClientGetClientConfiguration",Ne.StandardInteractionClientInitializeAuthorizationRequest="standardInteractionClientInitializeAuthorizationRequest",Ne.StandardInteractionClientInitializeAuthorizationCodeRequest="standardInteractionClientInitializeAuthorizationCodeRequest",Ne.GetAuthCodeUrl="getAuthCodeUrl",Ne.HandleCodeResponseFromServer="handleCodeResponseFromServer",Ne.HandleCodeResponseFromHash="handleCodeResponseFromHash",Ne.UpdateTokenEndpointAuthority="updateTokenEndpointAuthority",Ne.AuthClientAcquireToken="authClientAcquireToken",Ne.AuthClientExecuteTokenRequest="authClientExecuteTokenRequest",Ne.AuthClientCreateTokenRequestBody="authClientCreateTokenRequestBody",Ne.AuthClientCreateQueryString="authClientCreateQueryString",Ne.PopTokenGenerateCnf="popTokenGenerateCnf",Ne.PopTokenGenerateKid="popTokenGenerateKid",Ne.HandleServerTokenResponse="handleServerTokenResponse",Ne.AuthorityFactoryCreateDiscoveredInstance="authorityFactoryCreateDiscoveredInstance",Ne.AuthorityResolveEndpointsAsync="authorityResolveEndpointsAsync",Ne.AuthorityGetCloudDiscoveryMetadataFromNetwork="authorityGetCloudDiscoveryMetadataFromNetwork",Ne.AuthorityUpdateCloudDiscoveryMetadata="authorityUpdateCloudDiscoveryMetadata",Ne.AuthorityGetEndpointMetadataFromNetwork="authorityGetEndpointMetadataFromNetwork",Ne.AuthorityUpdateEndpointMetadata="authorityUpdateEndpointMetadata",Ne.AuthorityUpdateMetadataWithRegionalInformation="authorityUpdateMetadataWithRegionalInformation",Ne.RegionDiscoveryDetectRegion="regionDiscoveryDetectRegion",Ne.RegionDiscoveryGetRegionFromIMDS="regionDiscoveryGetRegionFromIMDS",Ne.RegionDiscoveryGetCurrentVersion="regionDiscoveryGetCurrentVersion",Ne.AcquireTokenByCodeAsync="acquireTokenByCodeAsync",Ne.GetEndpointMetadataFromNetwork="getEndpointMetadataFromNetwork",Ne.GetCloudDiscoveryMetadataFromNetworkMeasurement="getCloudDiscoveryMetadataFromNetworkMeasurement",Ne.HandleRedirectPromiseMeasurement="handleRedirectPromiseMeasurement",Ne.UpdateCloudDiscoveryMetadataMeasurement="updateCloudDiscoveryMetadataMeasurement",Ne.UsernamePasswordClientAcquireToken="usernamePasswordClientAcquireToken",Ne.NativeMessageHandlerHandshake="nativeMessageHandlerHandshake",Ne.ClearTokensAndKeysWithClaims="clearTokensAndKeysWithClaims",function(e){e[e.NotStarted=0]="NotStarted",e[e.InProgress=1]="InProgress",e[e.Completed=2]="Completed"}(Me||(Me={}));var We,Ve=new Set(["accessTokenSize","durationMs","idTokenSize","matsSilentStatus","matsHttpStatus","refreshTokenSize","queuedTimeMs","startTimeMs","status"]);
/*! @azure/msal-common v13.3.3 2024-06-06 */!function(e){e.SW="sw",e.UHW="uhw"}(We||(We={}));var Je=function(){function t(e,t){this.cryptoUtils=e,this.performanceClient=t}return t.prototype.generateCnf=function(t){var r,n;return h(this,void 0,void 0,(function(){var o,i,a;return p(this,(function(s){switch(s.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.PopTokenGenerateCnf,t.correlationId),null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.PopTokenGenerateKid,t.correlationId),[4,this.generateKid(t)];case 1:return o=s.sent(),i=this.cryptoUtils.base64Encode(JSON.stringify(o)),a={kid:o.kid,reqCnfString:i},[4,this.cryptoUtils.hashString(i)];case 2:return[2,(a.reqCnfHash=s.sent(),a)]}}))}))},t.prototype.generateKid=function(t){var r;return h(this,void 0,void 0,(function(){return p(this,(function(n){switch(n.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.PopTokenGenerateKid,t.correlationId),[4,this.cryptoUtils.getPublicKeyThumbprint(t)];case 1:return[2,{kid:n.sent(),xms_ksl:We.SW}]}}))}))},t.prototype.signPopToken=function(e,t,r){return h(this,void 0,void 0,(function(){return p(this,(function(n){return[2,this.signPayload(e,t,r)]}))}))},t.prototype.signPayload=function(e,t,r,n){return h(this,void 0,void 0,(function(){var o,i,a,s,c,u;return p(this,(function(l){switch(l.label){case 0:return o=r.resourceRequestMethod,i=r.resourceRequestUri,a=r.shrClaims,s=r.shrNonce,c=i?new Ye(i):void 0,u=null==c?void 0:c.getUrlComponents(),[4,this.cryptoUtils.signJwt(d({at:e,ts:De.nowSeconds(),m:null==o?void 0:o.toUpperCase(),u:null==u?void 0:u.HostNameAndPort,nonce:s||this.cryptoUtils.createNewGuid(),p:null==u?void 0:u.AbsolutePath,q:(null==u?void 0:u.QueryString)?[[],u.QueryString]:void 0,client_claims:a||void 0},n),t,r.correlationId)];case 1:return[2,l.sent()]}}))}))},t}(),Xe=function(){function e(){}return e.prototype.generateAppMetadataKey=function(){return e.generateAppMetadataCacheKey(this.environment,this.clientId)},e.generateAppMetadataCacheKey=function(e,t){return[O,e,t].join(R.CACHE_KEY_SEPARATOR).toLowerCase()},e.createAppMetadataEntity=function(t,r,n){var o=new e;return o.clientId=t,o.environment=r,n&&(o.familyId=n),o},e.isAppMetadataEntity=function(e,t){return!!t&&(0===e.indexOf(O)&&t.hasOwnProperty("clientId")&&t.hasOwnProperty("environment"))},e}(),Ze=function(){function e(e,t){this.cache=e,this.hasChanged=t}return Object.defineProperty(e.prototype,"cacheHasChanged",{get:function(){return this.hasChanged},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tokenCache",{get:function(){return this.cache},enumerable:!1,configurable:!0}),e}(),$e=function(){function t(e,t,r,n,o,i,a){this.clientId=e,this.cacheStorage=t,this.cryptoObj=r,this.logger=n,this.serializableCache=o,this.persistencePlugin=i,this.performanceClient=a}return t.prototype.validateServerAuthorizationCodeResponse=function(e,t,r){if(!e.state||!t)throw e.state?re.createStateNotFoundError("Cached State"):re.createStateNotFoundError("Server State");if(decodeURIComponent(e.state)!==decodeURIComponent(t))throw re.createStateMismatchError();if(e.error||e.error_description||e.suberror){if(ze.isInteractionRequiredError(e.error,e.error_description,e.suberror))throw new ze(e.error||E.EMPTY_STRING,e.error_description,e.suberror,e.timestamp||E.EMPTY_STRING,e.trace_id||E.EMPTY_STRING,e.correlation_id||E.EMPTY_STRING,e.claims||E.EMPTY_STRING);throw new Re(e.error||E.EMPTY_STRING,e.error_description,e.suberror)}e.client_info&&pe(e.client_info,r)},t.prototype.validateTokenResponse=function(e){if(e.error||e.error_description||e.suberror){if(ze.isInteractionRequiredError(e.error,e.error_description,e.suberror))throw new ze(e.error,e.error_description,e.suberror,e.timestamp||E.EMPTY_STRING,e.trace_id||E.EMPTY_STRING,e.correlation_id||E.EMPTY_STRING,e.claims||E.EMPTY_STRING);var t=e.error_codes+" - ["+e.timestamp+"]: "+e.error_description+" - Correlation ID: "+e.correlation_id+" - Trace ID: "+e.trace_id;throw new Re(e.error,t,e.suberror)}},t.prototype.handleServerTokenResponse=function(r,n,o,i,a,s,c,u,l){var d;return h(this,void 0,void 0,(function(){var h,g,f,m,v,y;return p(this,(function(p){switch(p.label){case 0:if(null===(d=this.performanceClient)||void 0===d||d.addQueueMeasurement(e.PerformanceEvents.HandleServerTokenResponse,r.correlation_id),r.id_token){if(h=new me(r.id_token||E.EMPTY_STRING,this.cryptoObj),a&&!ne.isEmpty(a.nonce)&&h.claims.nonce!==a.nonce)throw re.createNonceMismatchError();if(i.maxAge||0===i.maxAge){if(!(g=h.claims.auth_time))throw re.createAuthTimeNotFoundError();me.checkMaxAge(g,i.maxAge)}}this.homeAccountIdentifier=fe.generateHomeAccountId(r.client_info||E.EMPTY_STRING,n.authorityType,this.logger,this.cryptoObj,null==h?void 0:h.claims),a&&a.state&&(f=je.parseRequestState(this.cryptoObj,a.state)),r.key_id=r.key_id||i.sshKid||void 0,m=this.generateCacheRecord(r,n,o,i,h,s,a),p.label=1;case 1:return p.trys.push([1,,5,8]),this.persistencePlugin&&this.serializableCache?(this.logger.verbose("Persistence enabled, calling beforeCacheAccess"),v=new Ze(this.serializableCache,!0),[4,this.persistencePlugin.beforeCacheAccess(v)]):[3,3];case 2:p.sent(),p.label=3;case 3:return!c||u||!m.account||(y=m.account.generateAccountKey(),this.cacheStorage.getAccount(y))?[4,this.cacheStorage.saveCacheRecord(m)]:(this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"),[2,t.generateAuthenticationResult(this.cryptoObj,n,m,!1,i,h,f,void 0,l)]);case 4:return p.sent(),[3,8];case 5:return this.persistencePlugin&&this.serializableCache&&v?(this.logger.verbose("Persistence enabled, calling afterCacheAccess"),[4,this.persistencePlugin.afterCacheAccess(v)]):[3,7];case 6:p.sent(),p.label=7;case 7:return[7];case 8:return[2,t.generateAuthenticationResult(this.cryptoObj,n,m,!1,i,h,f,r,l)]}}))}))},t.prototype.generateCacheRecord=function(e,t,r,n,o,i,a){var s,c,u=t.getPreferredCache();if(ne.isEmpty(u))throw re.createInvalidCacheEnvironmentError();!ne.isEmpty(e.id_token)&&o&&(s=Le.createIdTokenEntity(this.homeAccountIdentifier,u,e.id_token||E.EMPTY_STRING,this.clientId,o.claims.tid||E.EMPTY_STRING),c=fe.createAccount({homeAccountId:this.homeAccountIdentifier,idTokenClaims:o.claims,clientInfo:e.client_info||E.EMPTY_STRING,cloudGraphHostName:null==a?void 0:a.cloud_graph_host_name,msGraphHost:null==a?void 0:a.msgraph_host},t));var l=null;if(!ne.isEmpty(e.access_token)){var d=e.scope?he.fromString(e.scope):new he(n.scopes||[]),h=("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0,p=("string"==typeof e.ext_expires_in?parseInt(e.ext_expires_in,10):e.ext_expires_in)||0,g=("string"==typeof e.refresh_in?parseInt(e.refresh_in,10):e.refresh_in)||void 0,f=r+h,m=f+p,v=g&&g>0?r+g:void 0;l=Ke.createAccessTokenEntity(this.homeAccountIdentifier,u,e.access_token||E.EMPTY_STRING,this.clientId,o?o.claims.tid||E.EMPTY_STRING:t.tenant,d.printScopes(),f,m,this.cryptoObj,v,e.token_type,i,e.key_id,n.claims,n.requestedClaimsHash)}var y=null;ne.isEmpty(e.refresh_token)||(y=Fe.createRefreshTokenEntity(this.homeAccountIdentifier,u,e.refresh_token||E.EMPTY_STRING,this.clientId,e.foci,i));var C=null;return ne.isEmpty(e.foci)||(C=Xe.createAppMetadataEntity(this.clientId,u,e.foci)),new Qe(c,s,l,y,C)},t.generateAuthenticationResult=function(t,r,n,o,i,a,s,c,u){var l,d,g;return h(this,void 0,void 0,(function(){var h,f,m,v,y,C,T,_,I,w,S;return p(this,(function(p){switch(p.label){case 0:if(h=E.EMPTY_STRING,f=[],m=null,y=E.EMPTY_STRING,!n.accessToken)return[3,4];if(n.accessToken.tokenType!==e.AuthenticationScheme.POP)return[3,2];if(C=new Je(t),T=n.accessToken,_=T.secret,!(I=T.keyId))throw re.createKeyIdMissingError();return[4,C.signPopToken(_,I,i)];case 1:return h=p.sent(),[3,3];case 2:h=n.accessToken.secret,p.label=3;case 3:f=he.fromString(n.accessToken.target).asArray(),m=new Date(1e3*Number(n.accessToken.expiresOn)),v=new Date(1e3*Number(n.accessToken.extendedExpiresOn)),p.label=4;case 4:return n.appMetadata&&(y=n.appMetadata.familyId===q?q:E.EMPTY_STRING),w=(null==a?void 0:a.claims.oid)||(null==a?void 0:a.claims.sub)||E.EMPTY_STRING,S=(null==a?void 0:a.claims.tid)||E.EMPTY_STRING,(null==c?void 0:c.spa_accountid)&&n.account&&(n.account.nativeAccountId=null==c?void 0:c.spa_accountid),[2,{authority:r.canonicalAuthority,uniqueId:w,tenantId:S,scopes:f,account:n.account?n.account.getAccountInfo():null,idToken:a?a.rawToken:E.EMPTY_STRING,idTokenClaims:a?a.claims:{},accessToken:h,fromCache:o,expiresOn:m,correlationId:i.correlationId,requestId:u||E.EMPTY_STRING,extExpiresOn:v,familyId:y,tokenType:(null===(l=n.accessToken)||void 0===l?void 0:l.tokenType)||E.EMPTY_STRING,state:s?s.userRequestState:E.EMPTY_STRING,cloudGraphHostName:(null===(d=n.account)||void 0===d?void 0:d.cloudGraphHostName)||E.EMPTY_STRING,msGraphHost:(null===(g=n.account)||void 0===g?void 0:g.msGraphHost)||E.EMPTY_STRING,code:null==c?void 0:c.spa_code,fromNativeBroker:!1}]}}))}))},t}(),et=function(t){function r(e,r){var n=t.call(this,e,r)||this;return n.includeRedirectUri=!0,n}return l(r,t),r.prototype.getAuthCodeUrl=function(t){var r,n;return h(this,void 0,void 0,(function(){var o;return p(this,(function(i){switch(i.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.GetAuthCodeUrl,t.correlationId),null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.AuthClientCreateQueryString,t.correlationId),[4,this.createAuthCodeUrlQueryString(t)];case 1:return o=i.sent(),[2,Ye.appendQueryString(this.authority.authorizationEndpoint,o)]}}))}))},r.prototype.acquireToken=function(t,r){var n,o,i,a,s,c;return h(this,void 0,void 0,(function(){var u,l,d,h,g,m,v=this;return p(this,(function(p){switch(p.label){case 0:if(!t||!t.code)throw re.createTokenRequestCannotBeMadeError();return null===(n=this.performanceClient)||void 0===n||n.addQueueMeasurement(e.PerformanceEvents.AuthClientAcquireToken,t.correlationId),u=null===(o=this.performanceClient)||void 0===o?void 0:o.startMeasurement("AuthCodeClientAcquireToken",t.correlationId),this.logger.info("in acquireToken call in auth-code client"),l=De.nowSeconds(),null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.AuthClientExecuteTokenRequest,t.correlationId),[4,this.executeTokenRequest(this.authority,t)];case 1:return d=p.sent(),h=null===(a=d.headers)||void 0===a?void 0:a[f.X_MS_REQUEST_ID],(g=null===(s=d.headers)||void 0===s?void 0:s[f.X_MS_HTTP_VERSION])&&(null==u||u.addStaticFields({httpVerAuthority:g})),(m=new $e(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin,this.performanceClient)).validateTokenResponse(d.body),null===(c=this.performanceClient)||void 0===c||c.setPreQueueTime(e.PerformanceEvents.HandleServerTokenResponse,t.correlationId),[2,m.handleServerTokenResponse(d.body,this.authority,l,t,r,void 0,void 0,void 0,h).then((function(e){return null==u||u.endMeasurement({success:!0}),e})).catch((function(e){throw v.logger.verbose("Error in fetching token in ACC",t.correlationId),null==u||u.endMeasurement({errorCode:e.errorCode,subErrorCode:e.subError,success:!1}),e}))]}}))}))},r.prototype.handleFragmentResponse=function(e,t){var r=new $e(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,null,null),n=new Ye(e),o=Ye.getDeserializedHash(n.getHash());if(r.validateServerAuthorizationCodeResponse(o,t,this.cryptoUtils),!o.code)throw re.createNoAuthCodeInServerResponseError();return d(d({},o),{code:o.code})},r.prototype.getLogoutUri=function(e){if(!e)throw de.createEmptyLogoutRequestError();var t=this.createLogoutUrlQueryString(e);return Ye.appendQueryString(this.authority.endSessionEndpoint,t)},r.prototype.executeTokenRequest=function(t,r){var n,o;return h(this,void 0,void 0,(function(){var i,a,s,c,u,l,d;return p(this,(function(h){switch(h.label){case 0:return null===(n=this.performanceClient)||void 0===n||n.addQueueMeasurement(e.PerformanceEvents.AuthClientExecuteTokenRequest,r.correlationId),null===(o=this.performanceClient)||void 0===o||o.setPreQueueTime(e.PerformanceEvents.AuthClientCreateTokenRequestBody,r.correlationId),i=this.createTokenQueryParameters(r),a=Ye.appendQueryString(t.tokenEndpoint,i),[4,this.createTokenRequestBody(r)];case 1:if(s=h.sent(),c=void 0,r.clientInfo)try{u=pe(r.clientInfo,this.cryptoUtils),c={credential:""+u.uid+R.CLIENT_INFO_SEPARATOR+u.utid,type:ke.HOME_ACCOUNT_ID}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}return l=this.createTokenRequestHeaders(c||r.ccsCredential),d={clientId:this.config.authOptions.clientId,authority:t.canonicalAuthority,scopes:r.scopes,claims:r.claims,authenticationScheme:r.authenticationScheme,resourceRequestMethod:r.resourceRequestMethod,resourceRequestUri:r.resourceRequestUri,shrClaims:r.shrClaims,sshKid:r.sshKid},[2,this.executePostToTokenEndpoint(a,s,l,d)]}}))}))},r.prototype.createTokenRequestBody=function(t){var r,n;return h(this,void 0,void 0,(function(){var o,i,a,s,c,u,l,d;return p(this,(function(h){switch(h.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.AuthClientCreateTokenRequestBody,t.correlationId),(o=new qe).addClientId(this.config.authOptions.clientId),this.includeRedirectUri?o.addRedirectUri(t.redirectUri):Oe.validateRedirectUri(t.redirectUri),o.addScopes(t.scopes),o.addAuthorizationCode(t.code),o.addLibraryInfo(this.config.libraryInfo),o.addApplicationTelemetry(this.config.telemetry.application),o.addThrottling(),this.serverTelemetryManager&&o.addServerTelemetry(this.serverTelemetryManager),t.codeVerifier&&o.addCodeVerifier(t.codeVerifier),this.config.clientCredentials.clientSecret&&o.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion&&(i=this.config.clientCredentials.clientAssertion,o.addClientAssertion(i.assertion),o.addClientAssertionType(i.assertionType)),o.addGrantType(A.AUTHORIZATION_CODE_GRANT),o.addClientInfo(),t.authenticationScheme!==e.AuthenticationScheme.POP?[3,2]:(a=new Je(this.cryptoUtils,this.performanceClient),null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.PopTokenGenerateCnf,t.correlationId),[4,a.generateCnf(t)]);case 1:return s=h.sent(),o.addPopToken(s.reqCnfString),[3,3];case 2:if(t.authenticationScheme===e.AuthenticationScheme.SSH){if(!t.sshJwk)throw de.createMissingSshJwkError();o.addSshJwk(t.sshJwk)}h.label=3;case 3:if(c=t.correlationId||this.config.cryptoInterface.createNewGuid(),o.addCorrelationId(c),(!ne.isEmptyObj(t.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&o.addClaims(t.claims,this.config.authOptions.clientCapabilities),u=void 0,t.clientInfo)try{l=pe(t.clientInfo,this.cryptoUtils),u={credential:""+l.uid+R.CLIENT_INFO_SEPARATOR+l.utid,type:ke.HOME_ACCOUNT_ID}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}else u=t.ccsCredential;if(this.config.systemOptions.preventCorsPreflight&&u)switch(u.type){case ke.HOME_ACCOUNT_ID:try{l=ge(u.credential),o.addCcsOid(l)}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case ke.UPN:o.addCcsUpn(u.credential)}return t.tokenBodyParameters&&o.addExtraQueryParameters(t.tokenBodyParameters),!t.enableSpaAuthorizationCode||t.tokenBodyParameters&&t.tokenBodyParameters[y.RETURN_SPA_CODE]||o.addExtraQueryParameters(((d={})[y.RETURN_SPA_CODE]="1",d)),[2,o.createQueryString()]}}))}))},r.prototype.createAuthCodeUrlQueryString=function(t){var r;return h(this,void 0,void 0,(function(){var n,o,i,a,s,c,u;return p(this,(function(l){switch(l.label){case 0:if(null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.AuthClientCreateQueryString,t.correlationId),(n=new qe).addClientId(this.config.authOptions.clientId),o=g(t.scopes||[],t.extraScopesToConsent||[]),n.addScopes(o),n.addRedirectUri(t.redirectUri),i=t.correlationId||this.config.cryptoInterface.createNewGuid(),n.addCorrelationId(i),n.addResponseMode(t.responseMode),n.addResponseTypeCode(),n.addLibraryInfo(this.config.libraryInfo),n.addApplicationTelemetry(this.config.telemetry.application),n.addClientInfo(),t.codeChallenge&&t.codeChallengeMethod&&n.addCodeChallengeParams(t.codeChallenge,t.codeChallengeMethod),t.prompt&&n.addPrompt(t.prompt),t.domainHint&&n.addDomainHint(t.domainHint),t.prompt!==w.SELECT_ACCOUNT)if(t.sid&&t.prompt===w.NONE)this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"),n.addSid(t.sid);else if(t.account){if(a=this.extractAccountSid(t.account),s=this.extractLoginHint(t.account)){this.logger.verbose("createAuthCodeUrlQueryString: login_hint claim present on account"),n.addLoginHint(s);try{c=ge(t.account.homeAccountId),n.addCcsOid(c)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(a&&t.prompt===w.NONE){this.logger.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"),n.addSid(a);try{c=ge(t.account.homeAccountId),n.addCcsOid(c)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(t.loginHint)this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"),n.addLoginHint(t.loginHint),n.addCcsUpn(t.loginHint);else if(t.account.username){this.logger.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"),n.addLoginHint(t.account.username);try{c=ge(t.account.homeAccountId),n.addCcsOid(c)}catch(e){this.logger.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}}else t.loginHint&&(this.logger.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"),n.addLoginHint(t.loginHint),n.addCcsUpn(t.loginHint));else this.logger.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");return t.nonce&&n.addNonce(t.nonce),t.state&&n.addState(t.state),(!ne.isEmpty(t.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&n.addClaims(t.claims,this.config.authOptions.clientCapabilities),t.extraQueryParameters&&n.addExtraQueryParameters(t.extraQueryParameters),t.nativeBroker?(n.addNativeBroker(),t.authenticationScheme!==e.AuthenticationScheme.POP?[3,2]:[4,new Je(this.cryptoUtils).generateCnf(t)]):[3,2];case 1:u=l.sent(),n.addPopToken(u.reqCnfString),l.label=2;case 2:return[2,n.createQueryString()]}}))}))},r.prototype.createLogoutUrlQueryString=function(e){var t=new qe;return e.postLogoutRedirectUri&&t.addPostLogoutRedirectUri(e.postLogoutRedirectUri),e.correlationId&&t.addCorrelationId(e.correlationId),e.idTokenHint&&t.addIdTokenHint(e.idTokenHint),e.state&&t.addState(e.state),e.logoutHint&&t.addLogoutHint(e.logoutHint),e.extraQueryParameters&&t.addExtraQueryParameters(e.extraQueryParameters),t.createQueryString()},r.prototype.extractAccountSid=function(e){var t;return(null===(t=e.idTokenClaims)||void 0===t?void 0:t.sid)||null},r.prototype.extractLoginHint=function(e){var t;return(null===(t=e.idTokenClaims)||void 0===t?void 0:t.login_hint)||null},r}(Ue),tt=function(t){function r(e,r){return t.call(this,e,r)||this}return l(r,t),r.prototype.acquireToken=function(t){var r,n,o,i,a,s,c;return h(this,void 0,void 0,(function(){var u,l,d,h,g,m,v=this;return p(this,(function(p){switch(p.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.RefreshTokenClientAcquireToken,t.correlationId),u=null===(n=this.performanceClient)||void 0===n?void 0:n.startMeasurement(e.PerformanceEvents.RefreshTokenClientAcquireToken,t.correlationId),this.logger.verbose("RefreshTokenClientAcquireToken called",t.correlationId),l=De.nowSeconds(),null===(o=this.performanceClient)||void 0===o||o.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientExecuteTokenRequest,t.correlationId),[4,this.executeTokenRequest(t,this.authority)];case 1:return d=p.sent(),h=null===(i=d.headers)||void 0===i?void 0:i[f.X_MS_HTTP_VERSION],null==u||u.addStaticFields({refreshTokenSize:(null===(a=d.body.refresh_token)||void 0===a?void 0:a.length)||0}),h&&(null==u||u.addStaticFields({httpVerToken:h})),g=null===(s=d.headers)||void 0===s?void 0:s[f.X_MS_REQUEST_ID],(m=new $e(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin)).validateTokenResponse(d.body),null===(c=this.performanceClient)||void 0===c||c.setPreQueueTime(e.PerformanceEvents.HandleServerTokenResponse,t.correlationId),[2,m.handleServerTokenResponse(d.body,this.authority,l,t,void 0,void 0,!0,t.forceCache,g).then((function(e){return null==u||u.endMeasurement({success:!0}),e})).catch((function(e){throw v.logger.verbose("Error in fetching refresh token",t.correlationId),null==u||u.endMeasurement({errorCode:e.errorCode,subErrorCode:e.subError,success:!1}),e}))]}}))}))},r.prototype.acquireTokenByRefreshToken=function(t){var r,n,o,i;return h(this,void 0,void 0,(function(){var a,s;return p(this,(function(c){if(!t)throw de.createEmptyTokenRequestError();if(null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.RefreshTokenClientAcquireTokenByRefreshToken,t.correlationId),!t.account)throw re.createNoAccountInSilentRequestError();if(this.cacheManager.isAppMetadataFOCI(t.account.environment))try{return null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken,t.correlationId),[2,this.acquireTokenWithCachedRefreshToken(t,!0)]}catch(r){if(a=r instanceof ze&&r.errorCode===Ge.noTokensFoundError.code,s=r instanceof Re&&r.errorCode===V&&r.subError===J,a||s)return null===(o=this.performanceClient)||void 0===o||o.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken,t.correlationId),[2,this.acquireTokenWithCachedRefreshToken(t,!1)];throw r}return null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken,t.correlationId),[2,this.acquireTokenWithCachedRefreshToken(t,!1)]}))}))},r.prototype.acquireTokenWithCachedRefreshToken=function(t,r){var n,o,i;return h(this,void 0,void 0,(function(){var a,s,c;return p(this,(function(u){if(null===(n=this.performanceClient)||void 0===n||n.addQueueMeasurement(e.PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken,t.correlationId),a=null===(o=this.performanceClient)||void 0===o?void 0:o.startMeasurement(e.PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken,t.correlationId),this.logger.verbose("RefreshTokenClientAcquireTokenWithCachedRefreshToken called",t.correlationId),!(s=this.cacheManager.getRefreshToken(t.account,r)))throw null==a||a.discardMeasurement(),ze.createNoTokensFoundError();return null==a||a.endMeasurement({success:!0}),c=d(d({},t),{refreshToken:s.secret,authenticationScheme:t.authenticationScheme||e.AuthenticationScheme.BEARER,ccsCredential:{credential:t.account.homeAccountId,type:ke.HOME_ACCOUNT_ID}}),null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientAcquireToken,t.correlationId),[2,this.acquireToken(c)]}))}))},r.prototype.executeTokenRequest=function(t,r){var n,o,i;return h(this,void 0,void 0,(function(){var a,s,c,u,l,d;return p(this,(function(h){switch(h.label){case 0:return null===(n=this.performanceClient)||void 0===n||n.addQueueMeasurement(e.PerformanceEvents.RefreshTokenClientExecuteTokenRequest,t.correlationId),a=null===(o=this.performanceClient)||void 0===o?void 0:o.startMeasurement(e.PerformanceEvents.RefreshTokenClientExecuteTokenRequest,t.correlationId),null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientCreateTokenRequestBody,t.correlationId),s=this.createTokenQueryParameters(t),c=Ye.appendQueryString(r.tokenEndpoint,s),[4,this.createTokenRequestBody(t)];case 1:return u=h.sent(),l=this.createTokenRequestHeaders(t.ccsCredential),d={clientId:this.config.authOptions.clientId,authority:r.canonicalAuthority,scopes:t.scopes,claims:t.claims,authenticationScheme:t.authenticationScheme,resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,sshKid:t.sshKid},[2,this.executePostToTokenEndpoint(c,u,l,d).then((function(e){return null==a||a.endMeasurement({success:!0}),e})).catch((function(e){throw null==a||a.endMeasurement({success:!1}),e}))]}}))}))},r.prototype.createTokenRequestBody=function(t){var r,n,o;return h(this,void 0,void 0,(function(){var i,a,s,c,u,l,d;return p(this,(function(h){switch(h.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.RefreshTokenClientCreateTokenRequestBody,t.correlationId),i=t.correlationId,a=null===(n=this.performanceClient)||void 0===n?void 0:n.startMeasurement(e.PerformanceEvents.BaseClientCreateTokenRequestHeaders,i),(s=new qe).addClientId(this.config.authOptions.clientId),s.addScopes(t.scopes),s.addGrantType(A.REFRESH_TOKEN_GRANT),s.addClientInfo(),s.addLibraryInfo(this.config.libraryInfo),s.addApplicationTelemetry(this.config.telemetry.application),s.addThrottling(),this.serverTelemetryManager&&s.addServerTelemetry(this.serverTelemetryManager),s.addCorrelationId(i),s.addRefreshToken(t.refreshToken),this.config.clientCredentials.clientSecret&&s.addClientSecret(this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion&&(c=this.config.clientCredentials.clientAssertion,s.addClientAssertion(c.assertion),s.addClientAssertionType(c.assertionType)),t.authenticationScheme!==e.AuthenticationScheme.POP?[3,2]:(u=new Je(this.cryptoUtils,this.performanceClient),null===(o=this.performanceClient)||void 0===o||o.setPreQueueTime(e.PerformanceEvents.PopTokenGenerateCnf,t.correlationId),[4,u.generateCnf(t)]);case 1:return l=h.sent(),s.addPopToken(l.reqCnfString),[3,3];case 2:if(t.authenticationScheme===e.AuthenticationScheme.SSH){if(!t.sshJwk)throw null==a||a.endMeasurement({success:!1}),de.createMissingSshJwkError();s.addSshJwk(t.sshJwk)}h.label=3;case 3:if((!ne.isEmptyObj(t.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&s.addClaims(t.claims,this.config.authOptions.clientCapabilities),this.config.systemOptions.preventCorsPreflight&&t.ccsCredential)switch(t.ccsCredential.type){case ke.HOME_ACCOUNT_ID:try{d=ge(t.ccsCredential.credential),s.addCcsOid(d)}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case ke.UPN:s.addCcsUpn(t.ccsCredential.credential)}return null==a||a.endMeasurement({success:!0}),[2,s.createQueryString()]}}))}))},r}(Ue),rt=function(e){function t(t,r){return e.call(this,t,r)||this}return l(t,e),t.prototype.acquireToken=function(e){return h(this,void 0,void 0,(function(){var t;return p(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.acquireCachedToken(e)];case 1:return[2,r.sent()];case 2:if((t=r.sent())instanceof re&&t.errorCode===te.tokenRefreshRequired.code)return[2,new tt(this.config,this.performanceClient).acquireTokenByRefreshToken(e)];throw t;case 3:return[2]}}))}))},t.prototype.acquireCachedToken=function(e){var t,r,n,o,i;return h(this,void 0,void 0,(function(){var a,s;return p(this,(function(c){switch(c.label){case 0:if(!e)throw de.createEmptyTokenRequestError();if(e.forceRefresh)throw null===(t=this.serverTelemetryManager)||void 0===t||t.setCacheOutcome(G.FORCE_REFRESH),this.logger.info("SilentFlowClient:acquireCachedToken - Skipping cache because forceRefresh is true."),re.createRefreshRequiredError();if(!this.config.cacheOptions.claimsBasedCachingEnabled&&!ne.isEmptyObj(e.claims))throw null===(r=this.serverTelemetryManager)||void 0===r||r.setCacheOutcome(G.CLAIMS_REQUESTED_CACHE_SKIPPED),this.logger.info("SilentFlowClient:acquireCachedToken - Skipping cache because claims-based caching is disabled and claims were requested."),re.createRefreshRequiredError();if(!e.account)throw re.createNoAccountInSilentRequestError();if(a=e.authority||this.authority.getPreferredCache(),!(s=this.cacheManager.readCacheRecord(e.account,e,a)).accessToken)throw null===(n=this.serverTelemetryManager)||void 0===n||n.setCacheOutcome(G.NO_CACHED_ACCESS_TOKEN),this.logger.info("SilentFlowClient:acquireCachedToken - No access token found in cache for the given properties."),re.createRefreshRequiredError();if(De.wasClockTurnedBack(s.accessToken.cachedAt)||De.isTokenExpired(s.accessToken.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds))throw null===(o=this.serverTelemetryManager)||void 0===o||o.setCacheOutcome(G.CACHED_ACCESS_TOKEN_EXPIRED),this.logger.info("SilentFlowClient:acquireCachedToken - Cached access token is expired or will expire within "+this.config.systemOptions.tokenRenewalOffsetSeconds+" seconds."),re.createRefreshRequiredError();if(s.accessToken.refreshOn&&De.isTokenExpired(s.accessToken.refreshOn,0))throw null===(i=this.serverTelemetryManager)||void 0===i||i.setCacheOutcome(G.REFRESH_CACHED_ACCESS_TOKEN),this.logger.info("SilentFlowClient:acquireCachedToken - Cached access token's refreshOn property has been exceeded'."),re.createRefreshRequiredError();return this.config.serverTelemetryManager&&this.config.serverTelemetryManager.incrementCacheHits(),[4,this.generateResultFromCacheRecord(s,e)];case 1:return[2,c.sent()]}}))}))},t.prototype.generateResultFromCacheRecord=function(e,t){return h(this,void 0,void 0,(function(){var r,n;return p(this,(function(o){switch(o.label){case 0:if(e.idToken&&(r=new me(e.idToken.secret,this.config.cryptoInterface)),t.maxAge||0===t.maxAge){if(!(n=null==r?void 0:r.claims.auth_time))throw re.createAuthTimeNotFoundError();me.checkMaxAge(n,t.maxAge)}return[4,$e.generateAuthenticationResult(this.cryptoUtils,this.authority,e,!0,t,r)];case 1:return[2,o.sent()]}}))}))},t}(Ue);
/*! @azure/msal-common v13.3.3 2024-06-06 */
/*! @azure/msal-common v13.3.3 2024-06-06 */
function nt(e){return e.hasOwnProperty("authorization_endpoint")&&e.hasOwnProperty("token_endpoint")&&e.hasOwnProperty("issuer")&&e.hasOwnProperty("jwks_uri")}
/*! @azure/msal-common v13.3.3 2024-06-06 */var ot={"https://login.microsoftonline.com/common/":{token_endpoint:"https://login.microsoftonline.com/common/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.microsoftonline.com/common/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.microsoftonline.com/{tenantid}/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://graph.microsoft.com/oidc/userinfo",authorization_endpoint:"https://login.microsoftonline.com/common/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.microsoftonline.com/common/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.microsoftonline.com/common/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.microsoftonline.com/common/kerberos",tenant_region_scope:null,cloud_instance_name:"microsoftonline.com",cloud_graph_host_name:"graph.windows.net",msgraph_host:"graph.microsoft.com",rbac_url:"https://pas.windows.net"},"https://login.chinacloudapi.cn/common/":{token_endpoint:"https://login.chinacloudapi.cn/common/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.chinacloudapi.cn/common/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.partner.microsoftonline.cn/{tenantid}/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://microsoftgraph.chinacloudapi.cn/oidc/userinfo",authorization_endpoint:"https://login.chinacloudapi.cn/common/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.chinacloudapi.cn/common/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.chinacloudapi.cn/common/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.chinacloudapi.cn/common/kerberos",tenant_region_scope:null,cloud_instance_name:"partner.microsoftonline.cn",cloud_graph_host_name:"graph.chinacloudapi.cn",msgraph_host:"microsoftgraph.chinacloudapi.cn",rbac_url:"https://pas.chinacloudapi.cn"},"https://login.microsoftonline.us/common/":{token_endpoint:"https://login.microsoftonline.us/common/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.microsoftonline.us/common/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.microsoftonline.us/{tenantid}/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://graph.microsoft.com/oidc/userinfo",authorization_endpoint:"https://login.microsoftonline.us/common/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.microsoftonline.us/common/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.microsoftonline.us/common/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.microsoftonline.us/common/kerberos",tenant_region_scope:null,cloud_instance_name:"microsoftonline.us",cloud_graph_host_name:"graph.windows.net",msgraph_host:"graph.microsoft.com",rbac_url:"https://pasff.usgovcloudapi.net"},"https://login.microsoftonline.com/consumers/":{token_endpoint:"https://login.microsoftonline.com/consumers/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.microsoftonline.com/consumers/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://graph.microsoft.com/oidc/userinfo",authorization_endpoint:"https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.microsoftonline.com/consumers/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.microsoftonline.com/consumers/kerberos",tenant_region_scope:null,cloud_instance_name:"microsoftonline.com",cloud_graph_host_name:"graph.windows.net",msgraph_host:"graph.microsoft.com",rbac_url:"https://pas.windows.net"},"https://login.chinacloudapi.cn/consumers/":{token_endpoint:"https://login.chinacloudapi.cn/consumers/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.chinacloudapi.cn/consumers/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.partner.microsoftonline.cn/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://microsoftgraph.chinacloudapi.cn/oidc/userinfo",authorization_endpoint:"https://login.chinacloudapi.cn/consumers/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.chinacloudapi.cn/consumers/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.chinacloudapi.cn/consumers/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.chinacloudapi.cn/consumers/kerberos",tenant_region_scope:null,cloud_instance_name:"partner.microsoftonline.cn",cloud_graph_host_name:"graph.chinacloudapi.cn",msgraph_host:"microsoftgraph.chinacloudapi.cn",rbac_url:"https://pas.chinacloudapi.cn"},"https://login.microsoftonline.us/consumers/":{token_endpoint:"https://login.microsoftonline.us/consumers/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.microsoftonline.us/consumers/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.microsoftonline.us/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://graph.microsoft.com/oidc/userinfo",authorization_endpoint:"https://login.microsoftonline.us/consumers/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.microsoftonline.us/consumers/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.microsoftonline.us/consumers/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.microsoftonline.us/consumers/kerberos",tenant_region_scope:null,cloud_instance_name:"microsoftonline.us",cloud_graph_host_name:"graph.windows.net",msgraph_host:"graph.microsoft.com",rbac_url:"https://pasff.usgovcloudapi.net"},"https://login.microsoftonline.com/organizations/":{token_endpoint:"https://login.microsoftonline.com/organizations/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.microsoftonline.com/organizations/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.microsoftonline.com/{tenantid}/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://graph.microsoft.com/oidc/userinfo",authorization_endpoint:"https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.microsoftonline.com/organizations/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.microsoftonline.com/organizations/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.microsoftonline.com/organizations/kerberos",tenant_region_scope:null,cloud_instance_name:"microsoftonline.com",cloud_graph_host_name:"graph.windows.net",msgraph_host:"graph.microsoft.com",rbac_url:"https://pas.windows.net"},"https://login.chinacloudapi.cn/organizations/":{token_endpoint:"https://login.chinacloudapi.cn/organizations/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.chinacloudapi.cn/organizations/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.partner.microsoftonline.cn/{tenantid}/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://microsoftgraph.chinacloudapi.cn/oidc/userinfo",authorization_endpoint:"https://login.chinacloudapi.cn/organizations/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.chinacloudapi.cn/organizations/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.chinacloudapi.cn/organizations/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.chinacloudapi.cn/organizations/kerberos",tenant_region_scope:null,cloud_instance_name:"partner.microsoftonline.cn",cloud_graph_host_name:"graph.chinacloudapi.cn",msgraph_host:"microsoftgraph.chinacloudapi.cn",rbac_url:"https://pas.chinacloudapi.cn"},"https://login.microsoftonline.us/organizations/":{token_endpoint:"https://login.microsoftonline.us/organizations/oauth2/v2.0/token",token_endpoint_auth_methods_supported:["client_secret_post","private_key_jwt","client_secret_basic"],jwks_uri:"https://login.microsoftonline.us/organizations/discovery/v2.0/keys",response_modes_supported:["query","fragment","form_post"],subject_types_supported:["pairwise"],id_token_signing_alg_values_supported:["RS256"],response_types_supported:["code","id_token","code id_token","id_token token"],scopes_supported:["openid","profile","email","offline_access"],issuer:"https://login.microsoftonline.us/{tenantid}/v2.0",request_uri_parameter_supported:!1,userinfo_endpoint:"https://graph.microsoft.com/oidc/userinfo",authorization_endpoint:"https://login.microsoftonline.us/organizations/oauth2/v2.0/authorize",device_authorization_endpoint:"https://login.microsoftonline.us/organizations/oauth2/v2.0/devicecode",http_logout_supported:!0,frontchannel_logout_supported:!0,end_session_endpoint:"https://login.microsoftonline.us/organizations/oauth2/v2.0/logout",claims_supported:["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],kerberos_endpoint:"https://login.microsoftonline.us/organizations/kerberos",tenant_region_scope:null,cloud_instance_name:"microsoftonline.us",cloud_graph_host_name:"graph.windows.net",msgraph_host:"graph.microsoft.com",rbac_url:"https://pasff.usgovcloudapi.net"}},it={"https://login.microsoftonline.com/common/":{tenant_discovery_endpoint:"https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.chinacloudapi.cn/common/":{tenant_discovery_endpoint:"https://login.chinacloudapi.cn/common/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.microsoftonline.us/common/":{tenant_discovery_endpoint:"https://login.microsoftonline.us/common/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.microsoftonline.com/consumers/":{tenant_discovery_endpoint:"https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.chinacloudapi.cn/consumers/":{tenant_discovery_endpoint:"https://login.chinacloudapi.cn/consumers/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.microsoftonline.us/consumers/":{tenant_discovery_endpoint:"https://login.microsoftonline.us/consumers/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.microsoftonline.com/organizations/":{tenant_discovery_endpoint:"https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.chinacloudapi.cn/organizations/":{tenant_discovery_endpoint:"https://login.chinacloudapi.cn/organizations/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},"https://login.microsoftonline.us/organizations/":{tenant_discovery_endpoint:"https://login.microsoftonline.us/organizations/v2.0/.well-known/openid-configuration","api-version":"1.1",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]}},at=function(){function e(){this.expiresAt=De.nowSeconds()+H}return e.prototype.updateCloudDiscoveryMetadata=function(e,t){this.aliases=e.aliases,this.preferred_cache=e.preferred_cache,this.preferred_network=e.preferred_network,this.aliasesFromNetwork=t},e.prototype.updateEndpointMetadata=function(e,t){this.authorization_endpoint=e.authorization_endpoint,this.token_endpoint=e.token_endpoint,this.end_session_endpoint=e.end_session_endpoint,this.issuer=e.issuer,this.endpointsFromNetwork=t,this.jwks_uri=e.jwks_uri},e.prototype.updateCanonicalAuthority=function(e){this.canonical_authority=e},e.prototype.resetExpiresAt=function(){this.expiresAt=De.nowSeconds()+H},e.prototype.isExpired=function(){return this.expiresAt<=De.nowSeconds()},e.isAuthorityMetadataEntity=function(e,t){return!!t&&(0===e.indexOf(U)&&t.hasOwnProperty("aliases")&&t.hasOwnProperty("preferred_cache")&&t.hasOwnProperty("preferred_network")&&t.hasOwnProperty("canonical_authority")&&t.hasOwnProperty("authorization_endpoint")&&t.hasOwnProperty("token_endpoint")&&t.hasOwnProperty("issuer")&&t.hasOwnProperty("aliasesFromNetwork")&&t.hasOwnProperty("endpointsFromNetwork")&&t.hasOwnProperty("expiresAt")&&t.hasOwnProperty("jwks_uri"))},e}();
/*! @azure/msal-common v13.3.3 2024-06-06 */
var st,ct,ut,lt,dt,ht,pt,gt,ft,mt=function(){function t(e,t,r){this.networkInterface=e,this.performanceClient=t,this.correlationId=r}return t.prototype.detectRegion=function(r,n){var o,i,a,s;return h(this,void 0,void 0,(function(){var c,u,l,d,h;return p(this,(function(p){switch(p.label){case 0:if(null===(o=this.performanceClient)||void 0===o||o.addQueueMeasurement(e.PerformanceEvents.RegionDiscoveryDetectRegion,this.correlationId),c=r)return[3,8];u=t.IMDS_OPTIONS,p.label=1;case 1:return p.trys.push([1,6,,7]),null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.RegionDiscoveryGetRegionFromIMDS,this.correlationId),[4,this.getRegionFromIMDS(E.IMDS_VERSION,u)];case 2:return(l=p.sent()).status===F.httpSuccess&&(c=l.body,n.region_source=B.IMDS),l.status!==F.httpBadRequest?[3,5]:(null===(a=this.performanceClient)||void 0===a||a.setPreQueueTime(e.PerformanceEvents.RegionDiscoveryGetCurrentVersion,this.correlationId),[4,this.getCurrentVersion(u)]);case 3:return(d=p.sent())?(null===(s=this.performanceClient)||void 0===s||s.setPreQueueTime(e.PerformanceEvents.RegionDiscoveryGetRegionFromIMDS,this.correlationId),[4,this.getRegionFromIMDS(d,u)]):(n.region_source=B.FAILED_AUTO_DETECTION,[2,null]);case 4:(h=p.sent()).status===F.httpSuccess&&(c=h.body,n.region_source=B.IMDS),p.label=5;case 5:return[3,7];case 6:return p.sent(),n.region_source=B.FAILED_AUTO_DETECTION,[2,null];case 7:return[3,9];case 8:n.region_source=B.ENVIRONMENT_VARIABLE,p.label=9;case 9:return c||(n.region_source=B.FAILED_AUTO_DETECTION),[2,c||null]}}))}))},t.prototype.getRegionFromIMDS=function(t,r){var n;return h(this,void 0,void 0,(function(){return p(this,(function(o){return null===(n=this.performanceClient)||void 0===n||n.addQueueMeasurement(e.PerformanceEvents.RegionDiscoveryGetRegionFromIMDS,this.correlationId),[2,this.networkInterface.sendGetRequestAsync(E.IMDS_ENDPOINT+"?api-version="+t+"&format=text",r,E.IMDS_TIMEOUT)]}))}))},t.prototype.getCurrentVersion=function(t){var r;return h(this,void 0,void 0,(function(){var n;return p(this,(function(o){switch(o.label){case 0:null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.RegionDiscoveryGetCurrentVersion,this.correlationId),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.networkInterface.sendGetRequestAsync(E.IMDS_ENDPOINT+"?format=json",t)];case 2:return(n=o.sent()).status===F.httpBadRequest&&n.body&&n.body["newest-versions"]&&n.body["newest-versions"].length>0?[2,n.body["newest-versions"][0]]:[2,null];case 3:return o.sent(),[2,null];case 4:return[2]}}))}))},t.IMDS_OPTIONS={headers:{Metadata:"true"}},t}(),vt=function(){function t(e,t,r,n,o,i,a){this.canonicalAuthority=e,this._canonicalAuthority.validateAsUri(),this.networkInterface=t,this.cacheManager=r,this.authorityOptions=n,this.regionDiscoveryMetadata={region_used:void 0,region_source:void 0,region_outcome:void 0},this.logger=o,this.performanceClient=i,this.correlationId=a,this.regionDiscovery=new mt(t,this.performanceClient,this.correlationId)}return t.prototype.getAuthorityType=function(e){if(e.HostNameAndPort.endsWith(E.CIAM_AUTH_URL))return ce.Ciam;var t=e.PathSegments;if(t.length)switch(t[0].toLowerCase()){case E.ADFS:return ce.Adfs;case E.DSTS:return ce.Dsts}return ce.Default},Object.defineProperty(t.prototype,"authorityType",{get:function(){return this.getAuthorityType(this.canonicalAuthorityUrlComponents)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"protocolMode",{get:function(){return this.authorityOptions.protocolMode},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"options",{get:function(){return this.authorityOptions},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canonicalAuthority",{get:function(){return this._canonicalAuthority.urlString},set:function(e){this._canonicalAuthority=new Ye(e),this._canonicalAuthority.validateAsUri(),this._canonicalAuthorityUrlComponents=null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canonicalAuthorityUrlComponents",{get:function(){return this._canonicalAuthorityUrlComponents||(this._canonicalAuthorityUrlComponents=this._canonicalAuthority.getUrlComponents()),this._canonicalAuthorityUrlComponents},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hostnameAndPort",{get:function(){return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tenant",{get:function(){return this.canonicalAuthorityUrlComponents.PathSegments[0]},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"authorizationEndpoint",{get:function(){if(this.discoveryComplete())return this.replacePath(this.metadata.authorization_endpoint);throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tokenEndpoint",{get:function(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint);throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"deviceCodeEndpoint",{get:function(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint.replace("/token","/devicecode"));throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"endSessionEndpoint",{get:function(){if(this.discoveryComplete()){if(!this.metadata.end_session_endpoint)throw re.createLogoutNotSupportedError();return this.replacePath(this.metadata.end_session_endpoint)}throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selfSignedJwtAudience",{get:function(){if(this.discoveryComplete())return this.replacePath(this.metadata.issuer);throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"jwksUri",{get:function(){if(this.discoveryComplete())return this.replacePath(this.metadata.jwks_uri);throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},enumerable:!1,configurable:!0}),t.prototype.canReplaceTenant=function(r){return 1===r.PathSegments.length&&!t.reservedTenantDomains.has(r.PathSegments[0])&&this.getAuthorityType(r)===ce.Default&&this.protocolMode===e.ProtocolMode.AAD},t.prototype.replaceTenant=function(e){return e.replace(/{tenant}|{tenantid}/g,this.tenant)},t.prototype.replacePath=function(e){var t=this,r=e,n=new Ye(this.metadata.canonical_authority).getUrlComponents(),o=n.PathSegments;return this.canonicalAuthorityUrlComponents.PathSegments.forEach((function(e,i){var a=o[i];if(0===i&&t.canReplaceTenant(n)){var s=new Ye(t.metadata.authorization_endpoint).getUrlComponents().PathSegments[0];a!==s&&(t.logger.verbose("Replacing tenant domain name "+a+" with id "+s),a=s)}e!==a&&(r=r.replace("/"+a+"/","/"+e+"/"))})),this.replaceTenant(r)},Object.defineProperty(t.prototype,"defaultOpenIdConfigurationEndpoint",{get:function(){return this.authorityType===ce.Adfs||this.authorityType===ce.Dsts||this.protocolMode===e.ProtocolMode.OIDC?this.canonicalAuthority+".well-known/openid-configuration":this.canonicalAuthority+"v2.0/.well-known/openid-configuration"},enumerable:!1,configurable:!0}),t.prototype.discoveryComplete=function(){return!!this.metadata},t.prototype.resolveEndpointsAsync=function(){var t,r,n;return h(this,void 0,void 0,(function(){var o,i,a,s;return p(this,(function(c){switch(c.label){case 0:return null===(t=this.performanceClient)||void 0===t||t.addQueueMeasurement(e.PerformanceEvents.AuthorityResolveEndpointsAsync,this.correlationId),(o=this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort))||(o=new at).updateCanonicalAuthority(this.canonicalAuthority),null===(r=this.performanceClient)||void 0===r||r.setPreQueueTime(e.PerformanceEvents.AuthorityUpdateCloudDiscoveryMetadata,this.correlationId),[4,this.updateCloudDiscoveryMetadata(o)];case 1:return i=c.sent(),this.canonicalAuthority=this.canonicalAuthority.replace(this.hostnameAndPort,o.preferred_network),null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.AuthorityUpdateEndpointMetadata,this.correlationId),[4,this.updateEndpointMetadata(o)];case 2:return a=c.sent(),i!==M.CACHE&&a!==M.CACHE&&(o.resetExpiresAt(),o.updateCanonicalAuthority(this.canonicalAuthority)),s=this.cacheManager.generateAuthorityMetadataCacheKey(o.preferred_cache),this.cacheManager.setAuthorityMetadata(s,o),this.metadata=o,[2]}}))}))},t.prototype.updateEndpointMetadata=function(t){var r,n,o,i,a,s;return h(this,void 0,void 0,(function(){var c,u;return p(this,(function(l){switch(l.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.AuthorityUpdateEndpointMetadata,this.correlationId),(c=this.getEndpointMetadataFromConfig())?(t.updateEndpointMetadata(c,!1),[2,M.CONFIG]):this.isAuthoritySameType(t)&&t.endpointsFromNetwork&&!t.isExpired()?[2,M.CACHE]:(null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.AuthorityGetEndpointMetadataFromNetwork,this.correlationId),[4,this.getEndpointMetadataFromNetwork()]);case 1:return(c=l.sent())?(null===(o=this.authorityOptions.azureRegionConfiguration)||void 0===o?void 0:o.azureRegion)?(null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId),[4,this.updateMetadataWithRegionalInformation(c)]):[3,3]:[3,4];case 2:c=l.sent(),l.label=3;case 3:return t.updateEndpointMetadata(c,!0),[2,M.NETWORK];case 4:return!(u=this.getEndpointMetadataFromHardcodedValues())||this.authorityOptions.skipAuthorityMetadataCache?[3,7]:(null===(a=this.authorityOptions.azureRegionConfiguration)||void 0===a?void 0:a.azureRegion)?(null===(s=this.performanceClient)||void 0===s||s.setPreQueueTime(e.PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId),[4,this.updateMetadataWithRegionalInformation(u)]):[3,6];case 5:u=l.sent(),l.label=6;case 6:return t.updateEndpointMetadata(u,!1),[2,M.HARDCODED_VALUES];case 7:throw re.createUnableToGetOpenidConfigError(this.defaultOpenIdConfigurationEndpoint)}}))}))},t.prototype.isAuthoritySameType=function(e){return new Ye(e.canonical_authority).getUrlComponents().PathSegments.length===this.canonicalAuthorityUrlComponents.PathSegments.length},t.prototype.getEndpointMetadataFromConfig=function(){if(this.authorityOptions.authorityMetadata)try{return JSON.parse(this.authorityOptions.authorityMetadata)}catch(e){throw de.createInvalidAuthorityMetadataError()}return null},t.prototype.getEndpointMetadataFromNetwork=function(){var t;return h(this,void 0,void 0,(function(){var r,n;return p(this,(function(o){switch(o.label){case 0:null===(t=this.performanceClient)||void 0===t||t.addQueueMeasurement(e.PerformanceEvents.AuthorityGetEndpointMetadataFromNetwork,this.correlationId),r={},o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.networkInterface.sendGetRequestAsync(this.defaultOpenIdConfigurationEndpoint,r)];case 2:return[2,nt((n=o.sent()).body)?n.body:null];case 3:return o.sent(),[2,null];case 4:return[2]}}))}))},t.prototype.getEndpointMetadataFromHardcodedValues=function(){return this.canonicalAuthority in ot?ot[this.canonicalAuthority]:null},t.prototype.updateMetadataWithRegionalInformation=function(r){var n,o,i,a;return h(this,void 0,void 0,(function(){var s,c;return p(this,(function(u){switch(u.label){case 0:return null===(n=this.performanceClient)||void 0===n||n.addQueueMeasurement(e.PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId),(s=null===(o=this.authorityOptions.azureRegionConfiguration)||void 0===o?void 0:o.azureRegion)?s!==E.AZURE_REGION_AUTO_DISCOVER_FLAG?(this.regionDiscoveryMetadata.region_outcome=x.CONFIGURED_NO_AUTO_DETECTION,this.regionDiscoveryMetadata.region_used=s,[2,t.replaceWithRegionalInformation(r,s)]):(null===(i=this.performanceClient)||void 0===i||i.setPreQueueTime(e.PerformanceEvents.RegionDiscoveryDetectRegion,this.correlationId),[4,this.regionDiscovery.detectRegion(null===(a=this.authorityOptions.azureRegionConfiguration)||void 0===a?void 0:a.environmentRegion,this.regionDiscoveryMetadata)]):[3,2];case 1:if(c=u.sent())return this.regionDiscoveryMetadata.region_outcome=x.AUTO_DETECTION_REQUESTED_SUCCESSFUL,this.regionDiscoveryMetadata.region_used=c,[2,t.replaceWithRegionalInformation(r,c)];this.regionDiscoveryMetadata.region_outcome=x.AUTO_DETECTION_REQUESTED_FAILED,u.label=2;case 2:return[2,r]}}))}))},t.prototype.updateCloudDiscoveryMetadata=function(t){var r,n;return h(this,void 0,void 0,(function(){var o,i,a;return p(this,(function(s){switch(s.label){case 0:return null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.AuthorityUpdateCloudDiscoveryMetadata,this.correlationId),this.logger.verbose("Attempting to get cloud discovery metadata in the config"),this.logger.verbosePii("Known Authorities: "+(this.authorityOptions.knownAuthorities||E.NOT_APPLICABLE)),this.logger.verbosePii("Authority Metadata: "+(this.authorityOptions.authorityMetadata||E.NOT_APPLICABLE)),this.logger.verbosePii("Canonical Authority: "+(t.canonical_authority||E.NOT_APPLICABLE)),(o=this.getCloudDiscoveryMetadataFromConfig())?(this.logger.verbose("Found cloud discovery metadata in the config."),t.updateCloudDiscoveryMetadata(o,!1),[2,M.CONFIG]):(this.logger.verbose("Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the cache."),i=t.isExpired(),this.isAuthoritySameType(t)&&t.aliasesFromNetwork&&!i?(this.logger.verbose("Found metadata in the cache."),[2,M.CACHE]):(i&&this.logger.verbose("The metadata entity is expired."),this.logger.verbose("Did not find cloud discovery metadata in the cache... Attempting to get cloud discovery metadata from the network."),null===(n=this.performanceClient)||void 0===n||n.setPreQueueTime(e.PerformanceEvents.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.correlationId),[4,this.getCloudDiscoveryMetadataFromNetwork()]));case 1:if(o=s.sent())return this.logger.verbose("cloud discovery metadata was successfully returned from getCloudDiscoveryMetadataFromNetwork()"),t.updateCloudDiscoveryMetadata(o,!0),[2,M.NETWORK];if(this.logger.verbose("Did not find cloud discovery metadata from the network... Attempting to get cloud discovery metadata from hardcoded values."),(a=this.getCloudDiscoveryMetadataFromHarcodedValues())&&!this.options.skipAuthorityMetadataCache)return this.logger.verbose("Found cloud discovery metadata from hardcoded values."),t.updateCloudDiscoveryMetadata(a,!1),[2,M.HARDCODED_VALUES];throw this.logger.error("Did not find cloud discovery metadata from hardcoded values... Metadata could not be obtained from config, cache, network or hardcoded values. Throwing Untrusted Authority Error."),de.createUntrustedAuthorityError()}}))}))},t.prototype.getCloudDiscoveryMetadataFromConfig=function(){if(this.authorityType===ce.Ciam)return this.logger.verbose("CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host."),t.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);if(this.authorityOptions.cloudDiscoveryMetadata){this.logger.verbose("The cloud discovery metadata has been provided as a network response, in the config.");try{this.logger.verbose("Attempting to parse the cloud discovery metadata.");var e=JSON.parse(this.authorityOptions.cloudDiscoveryMetadata),r=t.getCloudDiscoveryMetadataFromNetworkResponse(e.metadata,this.hostnameAndPort);if(this.logger.verbose("Parsed the cloud discovery metadata."),r)return this.logger.verbose("There is returnable metadata attached to the parsed cloud discovery metadata."),r;this.logger.verbose("There is no metadata attached to the parsed cloud discovery metadata.")}catch(e){throw this.logger.verbose("Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error."),de.createInvalidCloudDiscoveryMetadataError()}}return this.isInKnownAuthorities()?(this.logger.verbose("The host is included in knownAuthorities. Creating new cloud discovery metadata from the host."),t.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)):null},t.prototype.getCloudDiscoveryMetadataFromNetwork=function(){var r;return h(this,void 0,void 0,(function(){var n,o,i,a,s,c,u,l;return p(this,(function(d){switch(d.label){case 0:null===(r=this.performanceClient)||void 0===r||r.addQueueMeasurement(e.PerformanceEvents.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.correlationId),n=""+E.AAD_INSTANCE_DISCOVERY_ENDPT+this.canonicalAuthority+"oauth2/v2.0/authorize",o={},i=null,d.label=1;case 1:return d.trys.push([1,3,,4]),[4,this.networkInterface.sendGetRequestAsync(n,o)];case 2:if(a=d.sent(),s=void 0,c=void 0,
/*! @azure/msal-common v13.3.3 2024-06-06 */
function(e){return e.hasOwnProperty("tenant_discovery_endpoint")&&e.hasOwnProperty("metadata")}
/*! @azure/msal-common v13.3.3 2024-06-06 */(a.body))s=a.body,c=s.metadata,this.logger.verbosePii("tenant_discovery_endpoint is: "+s.tenant_discovery_endpoint);else{if(!function(e){return e.hasOwnProperty("error")&&e.hasOwnProperty("error_description")}(a.body))return this.logger.error("AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse"),[2,null];if(this.logger.warning("A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: "+a.status),(s=a.body).error===E.INVALID_INSTANCE)return this.logger.error("The CloudInstanceDiscoveryErrorResponse error is invalid_instance."),[2,null];this.logger.warning("The CloudInstanceDiscoveryErrorResponse error is "+s.error),this.logger.warning("The CloudInstanceDiscoveryErrorResponse error description is "+s.error_description),this.logger.warning("Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []"),c=[]}return this.logger.verbose("Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request."),i=t.getCloudDiscoveryMetadataFromNetworkResponse(c,this.hostnameAndPort),[3,4];case 3:return(u=d.sent())instanceof $?this.logger.error("There was a network error while attempting to get the cloud discovery instance metadata.\nError: "+u.errorCode+"\nError Description: "+u.errorMessage):(l=u,this.logger.error("A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.\nError: "+l.name+"\nError Description: "+l.message)),[2,null];case 4:return i||(this.logger.warning("The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request."),this.logger.verbose("Creating custom Authority for custom domain scenario."),i=t.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)),[2,i]}}))}))},t.prototype.getCloudDiscoveryMetadataFromHarcodedValues=function(){return this.canonicalAuthority in it?it[this.canonicalAuthority]:null},t.prototype.isInKnownAuthorities=function(){var e=this;return this.authorityOptions.knownAuthorities.filter((function(t){return Ye.getDomainFromUrl(t).toLowerCase()===e.hostnameAndPort})).length>0},t.generateAuthority=function(t,r){var n;if(r&&r.azureCloudInstance!==e.AzureCloudInstance.None){var o=r.tenant?r.tenant:E.DEFAULT_COMMON_TENANT;n=r.azureCloudInstance+"/"+o+"/"}return n||t},t.createCloudDiscoveryMetadataFromHost=function(e){return{preferred_network:e,preferred_cache:e,aliases:[e]}},t.getCloudDiscoveryMetadataFromNetworkResponse=function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(n.aliases.indexOf(t)>-1)return n}return null},t.prototype.getPreferredCache=function(){if(this.discoveryComplete())return this.metadata.preferred_cache;throw re.createEndpointDiscoveryIncompleteError("Discovery incomplete.")},t.prototype.isAlias=function(e){return this.metadata.aliases.indexOf(e)>-1},t.isPublicCloudAuthority=function(e){return E.KNOWN_PUBLIC_CLOUDS.indexOf(e)>=0},t.buildRegionalAuthorityString=function(e,t,r){var n=new Ye(e);n.validateAsUri();var o=n.getUrlComponents(),i=t+"."+o.HostNameAndPort;this.isPublicCloudAuthority(o.HostNameAndPort)&&(i=t+"."+E.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX);var a=Ye.constructAuthorityUriFromObject(d(d({},n.getUrlComponents()),{HostNameAndPort:i})).urlString;return r?a+"?"+r:a},t.replaceWithRegionalInformation=function(e,r){return e.authorization_endpoint=t.buildRegionalAuthorityString(e.authorization_endpoint,r),e.token_endpoint=t.buildRegionalAuthorityString(e.token_endpoint,r,E.REGIONAL_AUTH_NON_MSI_QUERY_STRING),e.end_session_endpoint&&(e.end_session_endpoint=t.buildRegionalAuthorityString(e.end_session_endpoint,r)),e},t.transformCIAMAuthority=function(e){var t=e.endsWith(E.FORWARD_SLASH)?e:""+e+E.FORWARD_SLASH,r=new Ye(e).getUrlComponents();0===r.PathSegments.length&&r.HostNameAndPort.endsWith(E.CIAM_AUTH_URL)&&(t=""+t+r.HostNameAndPort.split(".")[0]+E.AAD_TENANT_DOMAIN_SUFFIX);return t},t.reservedTenantDomains=new Set(["{tenant}","{tenantid}",v.COMMON,v.CONSUMERS,v.ORGANIZATIONS]),t}(),yt=function(){function t(){}return t.createDiscoveredInstance=function(r,n,o,i,a,s,c){return h(this,void 0,void 0,(function(){var u,l,d;return p(this,(function(h){switch(h.label){case 0:null==s||s.addQueueMeasurement(e.PerformanceEvents.AuthorityFactoryCreateDiscoveredInstance,c),u=vt.transformCIAMAuthority(r),l=t.createInstance(u,n,o,i,a,s,c),h.label=1;case 1:return h.trys.push([1,3,,4]),null==s||s.setPreQueueTime(e.PerformanceEvents.AuthorityResolveEndpointsAsync,c),[4,l.resolveEndpointsAsync()];case 2:return h.sent(),[2,l];case 3:throw d=h.sent(),re.createEndpointDiscoveryIncompleteError(d);case 4:return[2]}}))}))},t.createInstance=function(e,t,r,n,o,i,a){if(ne.isEmpty(e))throw de.createUrlEmptyError();return new vt(e,t,r,n,o,i,a)},t}(),Ct=function(){function e(){this.failedRequests=[],this.errors=[],this.cacheHits=0}return e.isServerTelemetryEntity=function(e,t){var r=0===e.indexOf(D.CACHE_KEY),n=!0;return t&&(n=t.hasOwnProperty("failedRequests")&&t.hasOwnProperty("errors")&&t.hasOwnProperty("cacheHits")),r&&n},e}(),Et=function(){function e(){}return e.isThrottlingEntity=function(e,t){var r=!1;e&&(r=0===e.indexOf(Y));var n=!0;return t&&(n=t.hasOwnProperty("throttleTime")),r&&n},e}(),Tt={sendGetRequestAsync:function(){return Promise.reject($.createUnexpectedError("Network interface - sendGetRequestAsync() has not been implemented for the Network interface."))},sendPostRequestAsync:function(){return Promise.reject($.createUnexpectedError("Network interface - sendPostRequestAsync() has not been implemented for the Network interface."))}},_t={code:"missing_kid_error",desc:"The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided."},It={code:"missing_alg_error",desc:"The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."},wt=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="JoseHeaderError",Object.setPrototypeOf(o,t.prototype),o}return l(t,e),t.createMissingKidError=function(){return new t(_t.code,_t.desc)},t.createMissingAlgError=function(){return new t(It.code,It.desc)},t}($),St=function(){function e(e){this.typ=e.typ,this.alg=e.alg,this.kid=e.kid}return e.getShrHeaderString=function(t){if(!t.kid)throw wt.createMissingKidError();if(!t.alg)throw wt.createMissingAlgError();var r=new e({typ:t.typ||z.Pop,kid:t.kid,alg:t.alg});return JSON.stringify(r)},e}(),At=function(){function e(e){this.headers=e}return e.prototype.getShrNonce=function(){var e=this.headers[f.AuthenticationInfo];if(e){var t=this.parseChallenges(e);if(t.nextnonce)return t.nextnonce;throw de.createInvalidAuthenticationHeaderError(f.AuthenticationInfo,"nextnonce challenge is missing.")}var r=this.headers[f.WWWAuthenticate];if(r){var n=this.parseChallenges(r);if(n.nonce)return n.nonce;throw de.createInvalidAuthenticationHeaderError(f.WWWAuthenticate,"nonce challenge is missing.")}throw de.createMissingNonceAuthenticationHeadersError()},e.prototype.parseChallenges=function(e){var t=e.indexOf(" "),r=e.substr(t+1).split(","),n={};return r.forEach((function(e){var t=e.split("="),r=t[0],o=t[1];n[r]=unescape(o.replace(/['"]+/g,E.EMPTY_STRING))})),n},e}(),kt=function(){function e(e,t){this.cacheOutcome=G.NO_CACHE_HIT,this.cacheManager=t,this.apiId=e.apiId,this.correlationId=e.correlationId,this.wrapperSKU=e.wrapperSKU||E.EMPTY_STRING,this.wrapperVer=e.wrapperVer||E.EMPTY_STRING,this.telemetryCacheKey=D.CACHE_KEY+R.CACHE_KEY_SEPARATOR+e.clientId}return e.prototype.generateCurrentRequestHeaderValue=function(){var e=""+this.apiId+D.VALUE_SEPARATOR+this.cacheOutcome,t=[this.wrapperSKU,this.wrapperVer].join(D.VALUE_SEPARATOR),r=[e,this.getRegionDiscoveryFields()].join(D.VALUE_SEPARATOR);return[D.SCHEMA_VERSION,r,t].join(D.CATEGORY_SEPARATOR)},e.prototype.generateLastRequestHeaderValue=function(){var t=this.getLastRequests(),r=e.maxErrorsToSend(t),n=t.failedRequests.slice(0,2*r).join(D.VALUE_SEPARATOR),o=t.errors.slice(0,r).join(D.VALUE_SEPARATOR),i=t.errors.length,a=[i,r<i?D.OVERFLOW_TRUE:D.OVERFLOW_FALSE].join(D.VALUE_SEPARATOR);return[D.SCHEMA_VERSION,t.cacheHits,n,o,a].join(D.CATEGORY_SEPARATOR)},e.prototype.cacheFailedRequest=function(e){var t=this.getLastRequests();t.errors.length>=D.MAX_CACHED_ERRORS&&(t.failedRequests.shift(),t.failedRequests.shift(),t.errors.shift()),t.failedRequests.push(this.apiId,this.correlationId),ne.isEmpty(e.subError)?ne.isEmpty(e.errorCode)?e&&e.toString()?t.errors.push(e.toString()):t.errors.push(D.UNKNOWN_ERROR):t.errors.push(e.errorCode):t.errors.push(e.subError),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t)},e.prototype.incrementCacheHits=function(){var e=this.getLastRequests();return e.cacheHits+=1,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e),e.cacheHits},e.prototype.getLastRequests=function(){var e=new Ct;return this.cacheManager.getServerTelemetry(this.telemetryCacheKey)||e},e.prototype.clearTelemetryCache=function(){var t=this.getLastRequests(),r=e.maxErrorsToSend(t);if(r===t.errors.length)this.cacheManager.removeItem(this.telemetryCacheKey);else{var n=new Ct;n.failedRequests=t.failedRequests.slice(2*r),n.errors=t.errors.slice(r),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,n)}},e.maxErrorsToSend=function(e){var t,r=0,n=0,o=e.errors.length;for(t=0;t<o;t++){var i=e.failedRequests[2*t]||E.EMPTY_STRING,a=e.failedRequests[2*t+1]||E.EMPTY_STRING,s=e.errors[t]||E.EMPTY_STRING;if(!((n+=i.toString().length+a.toString().length+s.length+3)<D.MAX_LAST_HEADER_BYTES))break;r+=1}return r},e.prototype.getRegionDiscoveryFields=function(){var e=[];return e.push(this.regionUsed||E.EMPTY_STRING),e.push(this.regionSource||E.EMPTY_STRING),e.push(this.regionOutcome||E.EMPTY_STRING),e.join(",")},e.prototype.updateRegionDiscoveryMetadata=function(e){this.regionUsed=e.region_used,this.regionSource=e.region_source,this.regionOutcome=e.region_outcome},e.prototype.setCacheOutcome=function(e){this.cacheOutcome=e},e}(),Rt=function(){function e(e,t,r,n,o,i){this.authority=t,this.libraryName=n,this.libraryVersion=o,this.applicationTelemetry=i,this.clientId=e,this.logger=r,this.callbacks=new Map,this.eventsByCorrelationId=new Map,this.queueMeasurements=new Map,this.preQueueTimeByCorrelationId=new Map}return e.prototype.startPerformanceMeasurement=function(e,t){return{}},e.prototype.startPerformanceMeasuremeant=function(e,t){return{}},e.prototype.getIntFields=function(){return Ve},e.prototype.getPreQueueTime=function(e,t){var r=this.preQueueTimeByCorrelationId.get(t);if(r){if(r.name===e)return r.time;this.logger.trace("PerformanceClient.getPreQueueTime: no pre-queue time found for "+e+", unable to add queue measurement")}else this.logger.trace("PerformanceClient.getPreQueueTime: no pre-queue times found for correlationId: "+t+", unable to add queue measurement")},e.prototype.calculateQueuedTime=function(e,t){return e<1?(this.logger.trace("PerformanceClient: preQueueTime should be a positive integer and not "+e),0):t<1?(this.logger.trace("PerformanceClient: currentTime should be a positive integer and not "+t),0):t<e?(this.logger.trace("PerformanceClient: currentTime is less than preQueueTime, check how time is being retrieved"),0):t-e},e.prototype.addQueueMeasurement=function(e,t,r,n){if(t){if(0===r)this.logger.trace("PerformanceClient.addQueueMeasurement: queue time provided for "+e+" is "+r);else if(!r)return void this.logger.trace("PerformanceClient.addQueueMeasurement: no queue time provided for "+e);var o={eventName:e,queueTime:r,manuallyCompleted:n},i=this.queueMeasurements.get(t);if(i)i.push(o),this.queueMeasurements.set(t,i);else{this.logger.trace("PerformanceClient.addQueueMeasurement: adding correlationId "+t+" to queue measurements");var a=[o];this.queueMeasurements.set(t,a)}this.preQueueTimeByCorrelationId.delete(t)}else this.logger.trace("PerformanceClient.addQueueMeasurement: correlationId not provided for "+e+", cannot add queue measurement")},e.prototype.startMeasurement=function(e,t){var r,n,o=this,i=t||this.generateId();t||this.logger.info("PerformanceClient: No correlation id provided for "+e+", generating",i),this.logger.trace("PerformanceClient: Performance measurement started for "+e,i);var a=this.startPerformanceMeasuremeant(e,i);a.startMeasurement();var s={eventId:this.generateId(),status:Me.InProgress,authority:this.authority,libraryName:this.libraryName,libraryVersion:this.libraryVersion,clientId:this.clientId,name:e,startTimeMs:Date.now(),correlationId:i,appName:null===(r=this.applicationTelemetry)||void 0===r?void 0:r.appName,appVersion:null===(n=this.applicationTelemetry)||void 0===n?void 0:n.appVersion};return this.cacheEventByCorrelationId(s),{endMeasurement:function(e){return o.endMeasurement(d(d({},s),e),a)},discardMeasurement:function(){return o.discardMeasurements(s.correlationId)},addStaticFields:function(e){return o.addStaticFields(e,s.correlationId)},increment:function(e){return o.increment(e,s.correlationId)},measurement:a,event:s}},e.prototype.endMeasurement=function(e,t){var r,n,o=this,i=this.eventsByCorrelationId.get(e.correlationId);if(!i)return this.logger.trace("PerformanceClient: Measurement not found for "+e.eventId,e.correlationId),null;var a=e.eventId===i.eventId,s={totalQueueTime:0,totalQueueCount:0,manuallyCompletedCount:0};a?(s=this.getQueueInfo(e.correlationId),this.discardCache(i.correlationId)):null===(r=i.incompleteSubMeasurements)||void 0===r||r.delete(e.eventId),null==t||t.endMeasurement();var c=null==t?void 0:t.flushMeasurement();if(!c)return this.logger.trace("PerformanceClient: Performance measurement not taken",i.correlationId),null;if(this.logger.trace("PerformanceClient: Performance measurement ended for "+e.name+": "+c+" ms",e.correlationId),!a)return i[e.name+"DurationMs"]=Math.floor(c),d({},i);var u=d(d({},i),e),l=0;return null===(n=u.incompleteSubMeasurements)||void 0===n||n.forEach((function(t){o.logger.trace("PerformanceClient: Incomplete submeasurement "+t.name+" found for "+e.name,u.correlationId),l++})),u.incompleteSubMeasurements=void 0,u=d(d({},u),{durationMs:Math.round(c),queuedTimeMs:s.totalQueueTime,queuedCount:s.totalQueueCount,queuedManuallyCompletedCount:s.manuallyCompletedCount,status:Me.Completed,incompleteSubsCount:l}),this.truncateIntegralFields(u,this.getIntFields()),this.emitEvents([u],e.correlationId),u},e.prototype.addStaticFields=function(e,t){this.logger.trace("PerformanceClient: Updating static fields");var r=this.eventsByCorrelationId.get(t);r?this.eventsByCorrelationId.set(t,d(d({},r),e)):this.logger.trace("PerformanceClient: Event not found for",t)},e.prototype.increment=function(e,t){this.logger.trace("PerformanceClient: Updating counters");var r=this.eventsByCorrelationId.get(t);if(r)for(var n in e)r.hasOwnProperty(n)||(r[n]=0),r[n]+=e[n];else this.logger.trace("PerformanceClient: Event not found for",t)},e.prototype.cacheEventByCorrelationId=function(e){var t=this.eventsByCorrelationId.get(e.correlationId);t?(this.logger.trace("PerformanceClient: Performance measurement for "+e.name+" added/updated",e.correlationId),t.incompleteSubMeasurements=t.incompleteSubMeasurements||new Map,t.incompleteSubMeasurements.set(e.eventId,{name:e.name,startTimeMs:e.startTimeMs})):(this.logger.trace("PerformanceClient: Performance measurement for "+e.name+" started",e.correlationId),this.eventsByCorrelationId.set(e.correlationId,d({},e)))},e.prototype.getQueueInfo=function(e){var t=this.queueMeasurements.get(e);t||this.logger.trace("PerformanceClient: no queue measurements found for for correlationId: "+e);var r=0,n=0,o=0;return null==t||t.forEach((function(e){r+=e.queueTime,n++,o+=e.manuallyCompleted?1:0})),{totalQueueTime:r,totalQueueCount:n,manuallyCompletedCount:o}},e.prototype.discardMeasurements=function(e){this.logger.trace("PerformanceClient: Performance measurements discarded",e),this.eventsByCorrelationId.delete(e)},e.prototype.discardCache=function(e){this.discardMeasurements(e),this.logger.trace("PerformanceClient: QueueMeasurements discarded",e),this.queueMeasurements.delete(e),this.logger.trace("PerformanceClient: Pre-queue times discarded",e),this.preQueueTimeByCorrelationId.delete(e)},e.prototype.addPerformanceCallback=function(e){var t=this.generateId();return this.callbacks.set(t,e),this.logger.verbose("PerformanceClient: Performance callback registered with id: "+t),t},e.prototype.removePerformanceCallback=function(e){var t=this.callbacks.delete(e);return t?this.logger.verbose("PerformanceClient: Performance callback "+e+" removed."):this.logger.verbose("PerformanceClient: Performance callback "+e+" not removed."),t},e.prototype.emitEvents=function(e,t){var r=this;this.logger.verbose("PerformanceClient: Emitting performance events",t),this.callbacks.forEach((function(n,o){r.logger.trace("PerformanceClient: Emitting event to callback "+o,t),n.apply(null,[e])}))},e.prototype.truncateIntegralFields=function(e,t){t.forEach((function(t){t in e&&"number"==typeof e[t]&&(e[t]=Math.floor(e[t]))}))},e}(),bt=function(){function e(){}return e.prototype.startMeasurement=function(){},e.prototype.endMeasurement=function(){},e.prototype.flushMeasurement=function(){return null},e}(),Pt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return l(t,e),t.prototype.generateId=function(){return"callback-id"},t.prototype.startPerformanceMeasuremeant=function(){return new bt},t.prototype.startPerformanceMeasurement=function(){return new bt},t.prototype.calculateQueuedTime=function(e,t){return 0},t.prototype.addQueueMeasurement=function(e,t,r){},t.prototype.setPreQueueTime=function(e,t){},t}(Rt),Nt={pkceNotGenerated:{code:"pkce_not_created",desc:"The PKCE code challenge and verifier could not be generated."},cryptoDoesNotExist:{code:"crypto_nonexistent",desc:"The crypto object or function is not available."},httpMethodNotImplementedError:{code:"http_method_not_implemented",desc:"The HTTP method given has not been implemented in this library."},emptyNavigateUriError:{code:"empty_navigate_uri",desc:"Navigation URI is empty. Please check stack trace for more info."},hashEmptyError:{code:"hash_empty_error",desc:"Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. For more visit: aka.ms/msaljs/browser-errors."},hashDoesNotContainStateError:{code:"no_state_in_hash",desc:"Hash does not contain state. Please verify that the request originated from msal."},hashDoesNotContainKnownPropertiesError:{code:"hash_does_not_contain_known_properties",desc:"Hash does not contain known properites. Please verify that your redirectUri is not changing the hash. For more visit: aka.ms/msaljs/browser-errors."},unableToParseStateError:{code:"unable_to_parse_state",desc:"Unable to parse state. Please verify that the request originated from msal."},stateInteractionTypeMismatchError:{code:"state_interaction_type_mismatch",desc:"Hash contains state but the interaction type does not match the caller."},interactionInProgress:{code:"interaction_in_progress",desc:"Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.  For more visit: aka.ms/msaljs/browser-errors."},popupWindowError:{code:"popup_window_error",desc:"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser."},emptyWindowError:{code:"empty_window_error",desc:"window.open returned null or undefined window object."},userCancelledError:{code:"user_cancelled",desc:"User cancelled the flow."},monitorPopupTimeoutError:{code:"monitor_window_timeout",desc:"Token acquisition in popup failed due to timeout. For more visit: aka.ms/msaljs/browser-errors."},monitorIframeTimeoutError:{code:"monitor_window_timeout",desc:"Token acquisition in iframe failed due to timeout. For more visit: aka.ms/msaljs/browser-errors."},redirectInIframeError:{code:"redirect_in_iframe",desc:"Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs."},blockTokenRequestsInHiddenIframeError:{code:"block_iframe_reload",desc:"Request was blocked inside an iframe because MSAL detected an authentication response. For more visit: aka.ms/msaljs/browser-errors"},blockAcquireTokenInPopupsError:{code:"block_nested_popups",desc:"Request was blocked inside a popup because MSAL detected it was running in a popup."},iframeClosedPrematurelyError:{code:"iframe_closed_prematurely",desc:"The iframe being monitored was closed prematurely."},silentLogoutUnsupportedError:{code:"silent_logout_unsupported",desc:"Silent logout not supported. Please call logoutRedirect or logoutPopup instead."},noAccountError:{code:"no_account_error",desc:"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request."},silentPromptValueError:{code:"silent_prompt_value_error",desc:"The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'."},noTokenRequestCacheError:{code:"no_token_request_cache_error",desc:"No token request found in cache."},unableToParseTokenRequestCacheError:{code:"unable_to_parse_token_request_cache_error",desc:"The cached token request could not be parsed."},noCachedAuthorityError:{code:"no_cached_authority_error",desc:"No cached authority found."},authRequestNotSet:{code:"auth_request_not_set_error",desc:"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler"},invalidCacheType:{code:"invalid_cache_type",desc:"Invalid cache type"},notInBrowserEnvironment:{code:"non_browser_environment",desc:"Login and token requests are not supported in non-browser environments."},databaseNotOpen:{code:"database_not_open",desc:"Database is not open!"},noNetworkConnectivity:{code:"no_network_connectivity",desc:"No network connectivity. Check your internet connection."},postRequestFailed:{code:"post_request_failed",desc:"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'"},getRequestFailed:{code:"get_request_failed",desc:"Network request failed. Please check the network trace to determine root cause."},failedToParseNetworkResponse:{code:"failed_to_parse_response",desc:"Failed to parse network response. Check network trace."},unableToLoadTokenError:{code:"unable_to_load_token",desc:"Error loading token to cache."},signingKeyNotFoundInStorage:{code:"crypto_key_not_found",desc:"Cryptographic Key or Keypair not found in browser storage."},authCodeRequired:{code:"auth_code_required",desc:"An authorization code must be provided (as the `code` property on the request) to this flow."},authCodeOrNativeAccountRequired:{code:"auth_code_or_nativeAccountId_required",desc:"An authorization code or nativeAccountId must be provided to this flow."},spaCodeAndNativeAccountPresent:{code:"spa_code_and_nativeAccountId_present",desc:"Request cannot contain both spa code and native account id."},databaseUnavailable:{code:"database_unavailable",desc:"IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts."},unableToAcquireTokenFromNativePlatform:{code:"unable_to_acquire_token_from_native_platform",desc:"Unable to acquire token from native platform. For a list of possible reasons visit aka.ms/msaljs/browser-errors."},nativeHandshakeTimeout:{code:"native_handshake_timeout",desc:"Timed out while attempting to establish connection to browser extension"},nativeExtensionNotInstalled:{code:"native_extension_not_installed",desc:"Native extension is not installed. If you think this is a mistake call the initialize function."},nativeConnectionNotEstablished:{code:"native_connection_not_established",desc:"Connection to native platform has not been established. Please install a compatible browser extension and run initialize(). For more please visit aka.ms/msaljs/browser-errors."},nativeBrokerCalledBeforeInitialize:{code:"native_broker_called_before_initialize",desc:"You must call and await the initialize function before attempting to call any other MSAL API when native brokering is enabled. For more please visit aka.ms/msaljs/browser-errors."},nativePromptNotSupported:{code:"native_prompt_not_supported",desc:"The provided prompt is not supported by the native platform. This request should be routed to the web based flow."}},Mt=function(e){function t(r,n){var o=e.call(this,r,n)||this;return Object.setPrototypeOf(o,t.prototype),o.name="BrowserAuthError",o}return r(t,e),t.createPkceNotGeneratedError=function(e){return new t(Nt.pkceNotGenerated.code,Nt.pkceNotGenerated.desc+" Detail:"+e)},t.createCryptoNotAvailableError=function(e){return new t(Nt.cryptoDoesNotExist.code,Nt.cryptoDoesNotExist.desc+" Detail:"+e)},t.createHttpMethodNotImplementedError=function(e){return new t(Nt.httpMethodNotImplementedError.code,Nt.httpMethodNotImplementedError.desc+" Given Method: "+e)},t.createEmptyNavigationUriError=function(){return new t(Nt.emptyNavigateUriError.code,Nt.emptyNavigateUriError.desc)},t.createEmptyHashError=function(e){return new t(Nt.hashEmptyError.code,Nt.hashEmptyError.desc+" Given Url: "+e)},t.createHashDoesNotContainStateError=function(){return new t(Nt.hashDoesNotContainStateError.code,Nt.hashDoesNotContainStateError.desc)},t.createHashDoesNotContainKnownPropertiesError=function(){return new t(Nt.hashDoesNotContainKnownPropertiesError.code,Nt.hashDoesNotContainKnownPropertiesError.desc)},t.createUnableToParseStateError=function(){return new t(Nt.unableToParseStateError.code,Nt.unableToParseStateError.desc)},t.createStateInteractionTypeMismatchError=function(){return new t(Nt.stateInteractionTypeMismatchError.code,Nt.stateInteractionTypeMismatchError.desc)},t.createInteractionInProgressError=function(){return new t(Nt.interactionInProgress.code,Nt.interactionInProgress.desc)},t.createPopupWindowError=function(e){var r=Nt.popupWindowError.desc;return r=ne.isEmpty(e)?r:r+" Details: "+e,new t(Nt.popupWindowError.code,r)},t.createEmptyWindowCreatedError=function(){return new t(Nt.emptyWindowError.code,Nt.emptyWindowError.desc)},t.createUserCancelledError=function(){return new t(Nt.userCancelledError.code,Nt.userCancelledError.desc)},t.createMonitorPopupTimeoutError=function(){return new t(Nt.monitorPopupTimeoutError.code,Nt.monitorPopupTimeoutError.desc)},t.createMonitorIframeTimeoutError=function(){return new t(Nt.monitorIframeTimeoutError.code,Nt.monitorIframeTimeoutError.desc)},t.createRedirectInIframeError=function(e){return new t(Nt.redirectInIframeError.code,Nt.redirectInIframeError.desc+" (window.parent !== window) => "+e)},t.createBlockReloadInHiddenIframeError=function(){return new t(Nt.blockTokenRequestsInHiddenIframeError.code,Nt.blockTokenRequestsInHiddenIframeError.desc)},t.createBlockAcquireTokenInPopupsError=function(){return new t(Nt.blockAcquireTokenInPopupsError.code,Nt.blockAcquireTokenInPopupsError.desc)},t.createIframeClosedPrematurelyError=function(){return new t(Nt.iframeClosedPrematurelyError.code,Nt.iframeClosedPrematurelyError.desc)},t.createSilentLogoutUnsupportedError=function(){return new t(Nt.silentLogoutUnsupportedError.code,Nt.silentLogoutUnsupportedError.desc)},t.createNoAccountError=function(){return new t(Nt.noAccountError.code,Nt.noAccountError.desc)},t.createSilentPromptValueError=function(e){return new t(Nt.silentPromptValueError.code,Nt.silentPromptValueError.desc+" Given value: "+e)},t.createUnableToParseTokenRequestCacheError=function(){return new t(Nt.unableToParseTokenRequestCacheError.code,Nt.unableToParseTokenRequestCacheError.desc)},t.createNoTokenRequestCacheError=function(){return new t(Nt.noTokenRequestCacheError.code,Nt.noTokenRequestCacheError.desc)},t.createAuthRequestNotSetError=function(){return new t(Nt.authRequestNotSet.code,Nt.authRequestNotSet.desc)},t.createNoCachedAuthorityError=function(){return new t(Nt.noCachedAuthorityError.code,Nt.noCachedAuthorityError.desc)},t.createInvalidCacheTypeError=function(){return new t(Nt.invalidCacheType.code,""+Nt.invalidCacheType.desc)},t.createNonBrowserEnvironmentError=function(){return new t(Nt.notInBrowserEnvironment.code,Nt.notInBrowserEnvironment.desc)},t.createDatabaseNotOpenError=function(){return new t(Nt.databaseNotOpen.code,Nt.databaseNotOpen.desc)},t.createNoNetworkConnectivityError=function(){return new t(Nt.noNetworkConnectivity.code,Nt.noNetworkConnectivity.desc)},t.createPostRequestFailedError=function(e,r){return new t(Nt.postRequestFailed.code,Nt.postRequestFailed.desc+" | Network client threw: "+e+" | Attempted to reach: "+r.split("?")[0])},t.createGetRequestFailedError=function(e,r){return new t(Nt.getRequestFailed.code,Nt.getRequestFailed.desc+" | Network client threw: "+e+" | Attempted to reach: "+r.split("?")[0])},t.createFailedToParseNetworkResponseError=function(e){return new t(Nt.failedToParseNetworkResponse.code,Nt.failedToParseNetworkResponse.desc+" | Attempted to reach: "+e.split("?")[0])},t.createUnableToLoadTokenError=function(e){return new t(Nt.unableToLoadTokenError.code,Nt.unableToLoadTokenError.desc+" | "+e)},t.createSigningKeyNotFoundInStorageError=function(e){return new t(Nt.signingKeyNotFoundInStorage.code,Nt.signingKeyNotFoundInStorage.desc+" | No match found for KeyId: "+e)},t.createAuthCodeRequiredError=function(){return new t(Nt.authCodeRequired.code,Nt.authCodeRequired.desc)},t.createAuthCodeOrNativeAccountIdRequiredError=function(){return new t(Nt.authCodeOrNativeAccountRequired.code,Nt.authCodeOrNativeAccountRequired.desc)},t.createSpaCodeAndNativeAccountIdPresentError=function(){return new t(Nt.spaCodeAndNativeAccountPresent.code,Nt.spaCodeAndNativeAccountPresent.desc)},t.createDatabaseUnavailableError=function(){return new t(Nt.databaseUnavailable.code,Nt.databaseUnavailable.desc)},t.createUnableToAcquireTokenFromNativePlatformError=function(){return new t(Nt.unableToAcquireTokenFromNativePlatform.code,Nt.unableToAcquireTokenFromNativePlatform.desc)},t.createNativeHandshakeTimeoutError=function(){return new t(Nt.nativeHandshakeTimeout.code,Nt.nativeHandshakeTimeout.desc)},t.createNativeExtensionNotInstalledError=function(){return new t(Nt.nativeExtensionNotInstalled.code,Nt.nativeExtensionNotInstalled.desc)},t.createNativeConnectionNotEstablishedError=function(){return new t(Nt.nativeConnectionNotEstablished.code,Nt.nativeConnectionNotEstablished.desc)},t.createNativeBrokerCalledBeforeInitialize=function(){return new t(Nt.nativeBrokerCalledBeforeInitialize.code,Nt.nativeBrokerCalledBeforeInitialize.desc)},t.createNativePromptParameterNotSupportedError=function(){return new t(Nt.nativePromptNotSupported.code,Nt.nativePromptNotSupported.desc)},t}($),Ot={INTERACTION_IN_PROGRESS_VALUE:"interaction_in_progress",INVALID_GRANT_ERROR:"invalid_grant",POPUP_WIDTH:483,POPUP_HEIGHT:600,POPUP_NAME_PREFIX:"msal",DEFAULT_POLL_INTERVAL_MS:30,MSAL_SKU:"msal.js.browser"},qt="53ee284d-920a-4b59-9d30-a60315b26836",Ut="ppnbnpeolgkicgegkbkbjmhlideopiji",Ht="MATS";
/*! @azure/msal-common v13.3.3 2024-06-06 */!function(e){e.HandshakeRequest="Handshake",e.HandshakeResponse="HandshakeResponse",e.GetToken="GetToken",e.Response="Response"}(st||(st={})),e.BrowserCacheLocation=void 0,(ct=e.BrowserCacheLocation||(e.BrowserCacheLocation={})).LocalStorage="localStorage",ct.SessionStorage="sessionStorage",ct.MemoryStorage="memoryStorage",function(e){e.GET="GET",e.POST="POST"}(ut||(ut={})),function(e){e.AUTHORITY="authority",e.ACQUIRE_TOKEN_ACCOUNT="acquireToken.account",e.SESSION_STATE="session.state",e.REQUEST_STATE="request.state",e.NONCE_IDTOKEN="nonce.id_token",e.ORIGIN_URI="request.origin",e.RENEW_STATUS="token.renew.status",e.URL_HASH="urlHash",e.REQUEST_PARAMS="request.params",e.SCOPES="scopes",e.INTERACTION_STATUS_KEY="interaction.status",e.CCS_CREDENTIAL="ccs.credential",e.CORRELATION_ID="request.correlationId",e.NATIVE_REQUEST="request.native",e.REDIRECT_CONTEXT="request.redirect.context"}(lt||(lt={})),function(e){e.ACCOUNT_KEYS="msal.account.keys",e.TOKEN_KEYS="msal.token.keys"}(dt||(dt={})),function(e){e.WRAPPER_SKU="wrapper.sku",e.WRAPPER_VER="wrapper.version"}(ht||(ht={})),e.ApiId=void 0,(pt=e.ApiId||(e.ApiId={}))[pt.acquireTokenRedirect=861]="acquireTokenRedirect",pt[pt.acquireTokenPopup=862]="acquireTokenPopup",pt[pt.ssoSilent=863]="ssoSilent",pt[pt.acquireTokenSilent_authCode=864]="acquireTokenSilent_authCode",pt[pt.handleRedirectPromise=865]="handleRedirectPromise",pt[pt.acquireTokenByCode=866]="acquireTokenByCode",pt[pt.acquireTokenSilent_silentFlow=61]="acquireTokenSilent_silentFlow",pt[pt.logout=961]="logout",pt[pt.logoutPopup=962]="logoutPopup",e.InteractionType=void 0,(gt=e.InteractionType||(e.InteractionType={})).Redirect="redirect",gt.Popup="popup",gt.Silent="silent",gt.None="none",e.InteractionStatus=void 0,(ft=e.InteractionStatus||(e.InteractionStatus={})).Startup="startup",ft.Login="login",ft.Logout="logout",ft.AcquireToken="acquireToken",ft.SsoSilent="ssoSilent",ft.HandleRedirect="handleRedirect",ft.None="none";var Lt,Dt={scopes:T},Kt="jwk";e.WrapperSKU=void 0,(Lt=e.WrapperSKU||(e.WrapperSKU={})).React="@azure/msal-react",Lt.Angular="@azure/msal-angular";var Ft,Bt="msal.db";e.CacheLookupPolicy=void 0,(Ft=e.CacheLookupPolicy||(e.CacheLookupPolicy={}))[Ft.Default=0]="Default",Ft[Ft.AccessToken=1]="AccessToken",Ft[Ft.AccessTokenAndRefreshToken=2]="AccessTokenAndRefreshToken",Ft[Ft.RefreshToken=3]="RefreshToken",Ft[Ft.RefreshTokenAndNetwork=4]="RefreshTokenAndNetwork",Ft[Ft.Skip=5]="Skip";var xt,Gt,zt={redirectUriNotSet:{code:"redirect_uri_empty",desc:"A redirect URI is required for all calls, and none has been set."},postLogoutUriNotSet:{code:"post_logout_uri_empty",desc:"A post logout redirect has not been set."},storageNotSupportedError:{code:"storage_not_supported",desc:"Given storage configuration option was not supported."},noRedirectCallbacksSet:{code:"no_redirect_callbacks",desc:"No redirect callbacks have been set. Please call setRedirectCallbacks() with the appropriate function arguments before continuing. More information is available here: https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics."},invalidCallbackObject:{code:"invalid_callback_object",desc:"The object passed for the callback was invalid. More information is available here: https://github.com/AzureAD/microsoft-authentication-library-for-js/wiki/MSAL-basics."},stubPcaInstanceCalled:{code:"stubbed_public_client_application_called",desc:"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors"},inMemRedirectUnavailable:{code:"in_mem_redirect_unavailable",desc:"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."},entropyNotProvided:{code:"entropy_not_provided",desc:"The available browser crypto interface requires entropy set via system.cryptoOptions.entropy configuration option."}},Qt=function(e){function t(r,n){var o=e.call(this,r,n)||this;return o.name="BrowserConfigurationAuthError",Object.setPrototypeOf(o,t.prototype),o}return r(t,e),t.createRedirectUriEmptyError=function(){return new t(zt.redirectUriNotSet.code,zt.redirectUriNotSet.desc)},t.createPostLogoutRedirectUriEmptyError=function(){return new t(zt.postLogoutUriNotSet.code,zt.postLogoutUriNotSet.desc)},t.createStorageNotSupportedError=function(e){return new t(zt.storageNotSupportedError.code,zt.storageNotSupportedError.desc+" Given Location: "+e)},t.createRedirectCallbacksNotSetError=function(){return new t(zt.noRedirectCallbacksSet.code,zt.noRedirectCallbacksSet.desc)},t.createStubPcaInstanceCalledError=function(){return new t(zt.stubPcaInstanceCalled.code,zt.stubPcaInstanceCalled.desc)},t.createInMemoryRedirectUnavailableError=function(){return new t(zt.inMemRedirectUnavailable.code,zt.inMemRedirectUnavailable.desc)},t.createEntropyNotProvided=function(){return new t(zt.entropyNotProvided.code,zt.entropyNotProvided.desc)},t}($),jt=function(){function t(e){this.validateWindowStorage(e),this.windowStorage=window[e]}return t.prototype.validateWindowStorage=function(t){if(t!==e.BrowserCacheLocation.LocalStorage&&t!==e.BrowserCacheLocation.SessionStorage)throw Qt.createStorageNotSupportedError(t);if(!!!window[t])throw Qt.createStorageNotSupportedError(t)},t.prototype.getItem=function(e){return this.windowStorage.getItem(e)},t.prototype.setItem=function(e,t){this.windowStorage.setItem(e,t)},t.prototype.removeItem=function(e){this.windowStorage.removeItem(e)},t.prototype.getKeys=function(){return Object.keys(this.windowStorage)},t.prototype.containsKey=function(e){return this.windowStorage.hasOwnProperty(e)},t}(),Yt=function(){function e(){this.cache=new Map}return e.prototype.getItem=function(e){return this.cache.get(e)||null},e.prototype.setItem=function(e,t){this.cache.set(e,t)},e.prototype.removeItem=function(e){this.cache.delete(e)},e.prototype.getKeys=function(){var e=[];return this.cache.forEach((function(t,r){e.push(r)})),e},e.prototype.containsKey=function(e){return this.cache.has(e)},e.prototype.clear=function(){this.cache.clear()},e}(),Wt=function(){function e(){}return e.extractBrowserRequestState=function(e,t){if(ne.isEmpty(t))return null;try{return je.parseRequestState(e,t).libraryState.meta}catch(e){throw re.createInvalidStateError(t,e)}},e.parseServerResponseFromHash=function(e){if(!e)return{};var t=new Ye(e);return Ye.getDeserializedHash(t.getHash())},e}(),Vt=function(t){function n(e,r,n,o){var i=t.call(this,e,n,o)||this;return i.COOKIE_LIFE_MULTIPLIER=864e5,i.cacheConfig=r,i.logger=o,i.internalStorage=new Yt,i.browserStorage=i.setupBrowserStorage(i.cacheConfig.cacheLocation),i.temporaryCacheStorage=i.setupTemporaryCacheStorage(i.cacheConfig.temporaryCacheLocation,i.cacheConfig.cacheLocation),r.cacheMigrationEnabled&&(i.migrateCacheEntries(),i.createKeyMaps()),i}return r(n,t),n.prototype.setupBrowserStorage=function(t){switch(t){case e.BrowserCacheLocation.LocalStorage:case e.BrowserCacheLocation.SessionStorage:try{return new jt(t)}catch(e){this.logger.verbose(e);break}case e.BrowserCacheLocation.MemoryStorage:}return this.cacheConfig.cacheLocation=e.BrowserCacheLocation.MemoryStorage,new Yt},n.prototype.setupTemporaryCacheStorage=function(t,r){switch(r){case e.BrowserCacheLocation.LocalStorage:case e.BrowserCacheLocation.SessionStorage:try{return new jt(t||e.BrowserCacheLocation.SessionStorage)}catch(e){return this.logger.verbose(e),this.internalStorage}case e.BrowserCacheLocation.MemoryStorage:default:return this.internalStorage}},n.prototype.migrateCacheEntries=function(){var e=this,t=E.CACHE_PREFIX+"."+m.ID_TOKEN,r=E.CACHE_PREFIX+"."+m.CLIENT_INFO,n=E.CACHE_PREFIX+"."+m.ERROR,o=E.CACHE_PREFIX+"."+m.ERROR_DESC,i=[this.browserStorage.getItem(t),this.browserStorage.getItem(r),this.browserStorage.getItem(n),this.browserStorage.getItem(o)];[m.ID_TOKEN,m.CLIENT_INFO,m.ERROR,m.ERROR_DESC].forEach((function(t,r){return e.migrateCacheEntry(t,i[r])}))},n.prototype.migrateCacheEntry=function(e,t){t&&this.setTemporaryCache(e,t,!0)},n.prototype.createKeyMaps=function(){var e=this;this.logger.trace("BrowserCacheManager - createKeyMaps called.");var t=this.getItem(dt.ACCOUNT_KEYS),r=this.getItem(dt.TOKEN_KEYS+"."+this.clientId);t&&r?this.logger.verbose("BrowserCacheManager:createKeyMaps - account and token key maps already exist, skipping migration."):this.browserStorage.getKeys().forEach((function(t){var r;if(e.isCredentialKey(t)&&(r=e.getItem(t))){var n=e.validateAndParseJson(r);if(n&&n.hasOwnProperty("credentialType"))switch(n.credentialType){case b.ID_TOKEN:if(Le.isIdTokenEntity(n)){e.logger.trace("BrowserCacheManager:createKeyMaps - idToken found, saving key to token key map"),e.logger.tracePii("BrowserCacheManager:createKeyMaps - idToken with key: "+t+" found, saving key to token key map");var o=ve.toObject(new Le,n),i=e.updateCredentialCacheKey(t,o);return void e.addTokenKey(i,b.ID_TOKEN)}e.logger.trace("BrowserCacheManager:createKeyMaps - key found matching idToken schema with value containing idToken credentialType field but value failed IdTokenEntity validation, skipping."),e.logger.tracePii("BrowserCacheManager:createKeyMaps - failed idToken validation on key: "+t);break;case b.ACCESS_TOKEN:case b.ACCESS_TOKEN_WITH_AUTH_SCHEME:if(Ke.isAccessTokenEntity(n)){e.logger.trace("BrowserCacheManager:createKeyMaps - accessToken found, saving key to token key map"),e.logger.tracePii("BrowserCacheManager:createKeyMaps - accessToken with key: "+t+" found, saving key to token key map");var a=ve.toObject(new Ke,n);i=e.updateCredentialCacheKey(t,a);return void e.addTokenKey(i,b.ACCESS_TOKEN)}e.logger.trace("BrowserCacheManager:createKeyMaps - key found matching accessToken schema with value containing accessToken credentialType field but value failed AccessTokenEntity validation, skipping."),e.logger.tracePii("BrowserCacheManager:createKeyMaps - failed accessToken validation on key: "+t);break;case b.REFRESH_TOKEN:if(Fe.isRefreshTokenEntity(n)){e.logger.trace("BrowserCacheManager:createKeyMaps - refreshToken found, saving key to token key map"),e.logger.tracePii("BrowserCacheManager:createKeyMaps - refreshToken with key: "+t+" found, saving key to token key map");var s=ve.toObject(new Fe,n);i=e.updateCredentialCacheKey(t,s);return void e.addTokenKey(i,b.REFRESH_TOKEN)}e.logger.trace("BrowserCacheManager:createKeyMaps - key found matching refreshToken schema with value containing refreshToken credentialType field but value failed RefreshTokenEntity validation, skipping."),e.logger.tracePii("BrowserCacheManager:createKeyMaps - failed refreshToken validation on key: "+t)}}if(e.isAccountKey(t)&&(r=e.getItem(t))){var c=e.validateAndParseJson(r);c&&fe.isAccountEntity(c)&&(e.logger.trace("BrowserCacheManager:createKeyMaps - account found, saving key to account key map"),e.logger.tracePii("BrowserCacheManager:createKeyMaps - account with key: "+t+" found, saving key to account key map"),e.addAccountKeyToMap(t))}}))},n.prototype.validateAndParseJson=function(e){try{var t=JSON.parse(e);return t&&"object"==typeof t?t:null}catch(e){return null}},n.prototype.getItem=function(e){return this.browserStorage.getItem(e)},n.prototype.setItem=function(e,t){this.browserStorage.setItem(e,t)},n.prototype.getAccount=function(e){this.logger.trace("BrowserCacheManager.getAccount called");var t=this.getItem(e);if(!t)return this.removeAccountKeyFromMap(e),null;var r=this.validateAndParseJson(t);return r&&fe.isAccountEntity(r)?ve.toObject(new fe,r):(this.removeAccountKeyFromMap(e),null)},n.prototype.setAccount=function(e){this.logger.trace("BrowserCacheManager.setAccount called");var t=e.generateAccountKey();this.setItem(t,JSON.stringify(e)),this.addAccountKeyToMap(t)},n.prototype.getAccountKeys=function(){this.logger.trace("BrowserCacheManager.getAccountKeys called");var e=this.getItem(dt.ACCOUNT_KEYS);return e?JSON.parse(e):(this.logger.verbose("BrowserCacheManager.getAccountKeys - No account keys found"),[])},n.prototype.addAccountKeyToMap=function(e){this.logger.trace("BrowserCacheManager.addAccountKeyToMap called"),this.logger.tracePii("BrowserCacheManager.addAccountKeyToMap called with key: "+e);var t=this.getAccountKeys();-1===t.indexOf(e)?(t.push(e),this.setItem(dt.ACCOUNT_KEYS,JSON.stringify(t)),this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key added")):this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key already exists in map")},n.prototype.removeAccountKeyFromMap=function(e){this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap called"),this.logger.tracePii("BrowserCacheManager.removeAccountKeyFromMap called with key: "+e);var t=this.getAccountKeys(),r=t.indexOf(e);r>-1?(t.splice(r,1),this.setItem(dt.ACCOUNT_KEYS,JSON.stringify(t)),this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap account key removed")):this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap key not found in existing map")},n.prototype.removeAccount=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(r){return t.prototype.removeAccount.call(this,e),this.removeAccountKeyFromMap(e),[2]}))}))},n.prototype.removeIdToken=function(e){t.prototype.removeIdToken.call(this,e),this.removeTokenKey(e,b.ID_TOKEN)},n.prototype.removeAccessToken=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(r){return t.prototype.removeAccessToken.call(this,e),this.removeTokenKey(e,b.ACCESS_TOKEN),[2]}))}))},n.prototype.removeRefreshToken=function(e){t.prototype.removeRefreshToken.call(this,e),this.removeTokenKey(e,b.REFRESH_TOKEN)},n.prototype.getTokenKeys=function(){this.logger.trace("BrowserCacheManager.getTokenKeys called");var e=this.getItem(dt.TOKEN_KEYS+"."+this.clientId);if(e){var t=this.validateAndParseJson(e);if(t&&t.hasOwnProperty("idToken")&&t.hasOwnProperty("accessToken")&&t.hasOwnProperty("refreshToken"))return t;this.logger.error("BrowserCacheManager.getTokenKeys - Token keys found but in an unknown format. Returning empty key map.")}else this.logger.verbose("BrowserCacheManager.getTokenKeys - No token keys found");return{idToken:[],accessToken:[],refreshToken:[]}},n.prototype.addTokenKey=function(e,t){this.logger.trace("BrowserCacheManager addTokenKey called");var r=this.getTokenKeys();switch(t){case b.ID_TOKEN:-1===r.idToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - idToken added to map"),r.idToken.push(e));break;case b.ACCESS_TOKEN:-1===r.accessToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - accessToken added to map"),r.accessToken.push(e));break;case b.REFRESH_TOKEN:-1===r.refreshToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - refreshToken added to map"),r.refreshToken.push(e));break;default:this.logger.error("BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: "+t),re.createUnexpectedCredentialTypeError()}this.setItem(dt.TOKEN_KEYS+"."+this.clientId,JSON.stringify(r))},n.prototype.removeTokenKey=function(e,t){this.logger.trace("BrowserCacheManager removeTokenKey called");var r=this.getTokenKeys();switch(t){case b.ID_TOKEN:this.logger.infoPii("BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: "+e+" from map");var n=r.idToken.indexOf(e);n>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - idToken removed from map"),r.idToken.splice(n,1)):this.logger.info("BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.");break;case b.ACCESS_TOKEN:this.logger.infoPii("BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: "+e+" from map");var o=r.accessToken.indexOf(e);o>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - accessToken removed from map"),r.accessToken.splice(o,1)):this.logger.info("BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.");break;case b.REFRESH_TOKEN:this.logger.infoPii("BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: "+e+" from map");var i=r.refreshToken.indexOf(e);i>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken removed from map"),r.refreshToken.splice(i,1)):this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.");break;default:this.logger.error("BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: "+t),re.createUnexpectedCredentialTypeError()}this.setItem(dt.TOKEN_KEYS+"."+this.clientId,JSON.stringify(r))},n.prototype.getIdTokenCredential=function(e){var t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeTokenKey(e,b.ID_TOKEN),null;var r=this.validateAndParseJson(t);return r&&Le.isIdTokenEntity(r)?(this.logger.trace("BrowserCacheManager.getIdTokenCredential: cache hit"),ve.toObject(new Le,r)):(this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeTokenKey(e,b.ID_TOKEN),null)},n.prototype.setIdTokenCredential=function(e){this.logger.trace("BrowserCacheManager.setIdTokenCredential called");var t=e.generateCredentialKey();this.setItem(t,JSON.stringify(e)),this.addTokenKey(t,b.ID_TOKEN)},n.prototype.getAccessTokenCredential=function(e){var t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,b.ACCESS_TOKEN),null;var r=this.validateAndParseJson(t);return r&&Ke.isAccessTokenEntity(r)?(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: cache hit"),ve.toObject(new Ke,r)):(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,b.ACCESS_TOKEN),null)},n.prototype.setAccessTokenCredential=function(e){this.logger.trace("BrowserCacheManager.setAccessTokenCredential called");var t=e.generateCredentialKey();this.setItem(t,JSON.stringify(e)),this.addTokenKey(t,b.ACCESS_TOKEN)},n.prototype.getRefreshTokenCredential=function(e){var t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,b.REFRESH_TOKEN),null;var r=this.validateAndParseJson(t);return r&&Fe.isRefreshTokenEntity(r)?(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: cache hit"),ve.toObject(new Fe,r)):(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,b.REFRESH_TOKEN),null)},n.prototype.setRefreshTokenCredential=function(e){this.logger.trace("BrowserCacheManager.setRefreshTokenCredential called");var t=e.generateCredentialKey();this.setItem(t,JSON.stringify(e)),this.addTokenKey(t,b.REFRESH_TOKEN)},n.prototype.getAppMetadata=function(e){var t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null;var r=this.validateAndParseJson(t);return r&&Xe.isAppMetadataEntity(e,r)?(this.logger.trace("BrowserCacheManager.getAppMetadata: cache hit"),ve.toObject(new Xe,r)):(this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null)},n.prototype.setAppMetadata=function(e){this.logger.trace("BrowserCacheManager.setAppMetadata called");var t=e.generateAppMetadataKey();this.setItem(t,JSON.stringify(e))},n.prototype.getServerTelemetry=function(e){var t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null;var r=this.validateAndParseJson(t);return r&&Ct.isServerTelemetryEntity(e,r)?(this.logger.trace("BrowserCacheManager.getServerTelemetry: cache hit"),ve.toObject(new Ct,r)):(this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null)},n.prototype.setServerTelemetry=function(e,t){this.logger.trace("BrowserCacheManager.setServerTelemetry called"),this.setItem(e,JSON.stringify(t))},n.prototype.getAuthorityMetadata=function(e){var t=this.internalStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAuthorityMetadata: called, no cache hit"),null;var r=this.validateAndParseJson(t);return r&&at.isAuthorityMetadataEntity(e,r)?(this.logger.trace("BrowserCacheManager.getAuthorityMetadata: cache hit"),ve.toObject(new at,r)):null},n.prototype.getAuthorityMetadataKeys=function(){var e=this;return this.internalStorage.getKeys().filter((function(t){return e.isAuthorityMetadata(t)}))},n.prototype.setWrapperMetadata=function(e,t){this.internalStorage.setItem(ht.WRAPPER_SKU,e),this.internalStorage.setItem(ht.WRAPPER_VER,t)},n.prototype.getWrapperMetadata=function(){return[this.internalStorage.getItem(ht.WRAPPER_SKU)||E.EMPTY_STRING,this.internalStorage.getItem(ht.WRAPPER_VER)||E.EMPTY_STRING]},n.prototype.setAuthorityMetadata=function(e,t){this.logger.trace("BrowserCacheManager.setAuthorityMetadata called"),this.internalStorage.setItem(e,JSON.stringify(t))},n.prototype.getActiveAccount=function(){var e=this.generateCacheKey(m.ACTIVE_ACCOUNT_FILTERS),t=this.getItem(e);if(!t){this.logger.trace("BrowserCacheManager.getActiveAccount: No active account filters cache schema found, looking for legacy schema");var r=this.generateCacheKey(m.ACTIVE_ACCOUNT),n=this.getItem(r);if(!n)return this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null;var o=this.getAccountInfoByFilter({localAccountId:n})[0]||null;return o?(this.logger.trace("BrowserCacheManager.getActiveAccount: Legacy active account cache schema found"),this.logger.trace("BrowserCacheManager.getActiveAccount: Adding active account filters cache schema"),this.setActiveAccount(o),o):null}var i=this.validateAndParseJson(t);return i?(this.logger.trace("BrowserCacheManager.getActiveAccount: Active account filters schema found"),this.getAccountInfoByFilter({homeAccountId:i.homeAccountId,localAccountId:i.localAccountId})[0]||null):(this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null)},n.prototype.setActiveAccount=function(e){var t=this.generateCacheKey(m.ACTIVE_ACCOUNT_FILTERS),r=this.generateCacheKey(m.ACTIVE_ACCOUNT);if(e){this.logger.verbose("setActiveAccount: Active account set");var n={homeAccountId:e.homeAccountId,localAccountId:e.localAccountId};this.browserStorage.setItem(t,JSON.stringify(n)),this.browserStorage.setItem(r,e.localAccountId)}else this.logger.verbose("setActiveAccount: No account passed, active account not set"),this.browserStorage.removeItem(t),this.browserStorage.removeItem(r)},n.prototype.getAccountInfoByFilter=function(e){var t=this.getAllAccounts();return this.logger.trace("BrowserCacheManager.getAccountInfoByFilter: total "+t.length+" accounts found"),t.filter((function(t){return(!e.username||e.username.toLowerCase()===t.username.toLowerCase())&&((!e.homeAccountId||e.homeAccountId===t.homeAccountId)&&((!e.localAccountId||e.localAccountId===t.localAccountId)&&((!e.tenantId||e.tenantId===t.tenantId)&&(!e.environment||e.environment===t.environment))))}))},n.prototype.getAccountInfoByHints=function(e,t){var r=this.getAllAccounts().filter((function(r){if(t){var n=r.idTokenClaims&&r.idTokenClaims.sid;return t===n}return!!e&&e===r.username}));if(1===r.length)return r[0];if(r.length>1)throw re.createMultipleMatchingAccountsInCacheError();return null},n.prototype.getThrottlingCache=function(e){var t=this.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null;var r=this.validateAndParseJson(t);return r&&Et.isThrottlingEntity(e,r)?(this.logger.trace("BrowserCacheManager.getThrottlingCache: cache hit"),ve.toObject(new Et,r)):(this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null)},n.prototype.setThrottlingCache=function(e,t){this.logger.trace("BrowserCacheManager.setThrottlingCache called"),this.setItem(e,JSON.stringify(t))},n.prototype.getTemporaryCache=function(t,r){var n=r?this.generateCacheKey(t):t;if(this.cacheConfig.storeAuthStateInCookie){var o=this.getItemCookie(n);if(o)return this.logger.trace("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies"),o}var i=this.temporaryCacheStorage.getItem(n);if(!i){if(this.cacheConfig.cacheLocation===e.BrowserCacheLocation.LocalStorage){var a=this.browserStorage.getItem(n);if(a)return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage"),a}return this.logger.trace("BrowserCacheManager.getTemporaryCache: No cache item found in local storage"),null}return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item returned"),i},n.prototype.setTemporaryCache=function(e,t,r){var n=r?this.generateCacheKey(e):e;this.temporaryCacheStorage.setItem(n,t),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"),this.setItemCookie(n,t))},n.prototype.removeItem=function(e){this.browserStorage.removeItem(e),this.temporaryCacheStorage.removeItem(e),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"),this.clearItemCookie(e))},n.prototype.containsKey=function(e){return this.browserStorage.containsKey(e)||this.temporaryCacheStorage.containsKey(e)},n.prototype.getKeys=function(){return c(this.browserStorage.getKeys(),this.temporaryCacheStorage.getKeys())},n.prototype.clear=function(){return i(this,void 0,void 0,(function(){var e=this;return a(this,(function(t){switch(t.label){case 0:return[4,this.removeAllAccounts()];case 1:return t.sent(),this.removeAppMetadata(),this.getKeys().forEach((function(t){!e.browserStorage.containsKey(t)&&!e.temporaryCacheStorage.containsKey(t)||-1===t.indexOf(E.CACHE_PREFIX)&&-1===t.indexOf(e.clientId)||e.removeItem(t)})),this.internalStorage.clear(),[2]}}))}))},n.prototype.clearTokensAndKeysWithClaims=function(){return i(this,void 0,void 0,(function(){var e,t,r=this;return a(this,(function(n){switch(n.label){case 0:return this.logger.trace("BrowserCacheManager.clearTokensAndKeysWithClaims called"),e=this.getTokenKeys(),t=[],e.accessToken.forEach((function(e){var n=r.getAccessTokenCredential(e);(null==n?void 0:n.requestedClaimsHash)&&e.includes(n.requestedClaimsHash.toLowerCase())&&t.push(r.removeAccessToken(e))})),[4,Promise.all(t)];case 1:return n.sent(),t.length>0&&this.logger.warning(t.length+" access tokens with claims in the cache keys have been removed from the cache."),[2]}}))}))},n.prototype.setItemCookie=function(e,t,r){var n=encodeURIComponent(e)+"="+encodeURIComponent(t)+";path=/;SameSite=Lax;";r&&(n+="expires="+this.getCookieExpirationTime(r)+";");this.cacheConfig.secureCookies&&(n+="Secure;"),document.cookie=n},n.prototype.getItemCookie=function(e){for(var t=encodeURIComponent(e)+"=",r=document.cookie.split(";"),n=0;n<r.length;n++){for(var o=r[n];" "===o.charAt(0);)o=o.substring(1);if(0===o.indexOf(t))return decodeURIComponent(o.substring(t.length,o.length))}return E.EMPTY_STRING},n.prototype.clearMsalCookies=function(){var e=this,t=E.CACHE_PREFIX+"."+this.clientId;document.cookie.split(";").forEach((function(r){for(;" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(t)){var n=r.split("=")[0];e.clearItemCookie(n)}}))},n.prototype.clearItemCookie=function(e){this.setItemCookie(e,E.EMPTY_STRING,-1)},n.prototype.getCookieExpirationTime=function(e){var t=new Date;return new Date(t.getTime()+e*this.COOKIE_LIFE_MULTIPLIER).toUTCString()},n.prototype.getCache=function(){return this.browserStorage},n.prototype.setCache=function(){},n.prototype.generateCacheKey=function(e){return this.validateAndParseJson(e)?JSON.stringify(e):ne.startsWith(e,E.CACHE_PREFIX)||ne.startsWith(e,m.ADAL_ID_TOKEN)?e:E.CACHE_PREFIX+"."+this.clientId+"."+e},n.prototype.generateAuthorityKey=function(e){var t=je.parseRequestState(this.cryptoImpl,e).libraryState.id;return this.generateCacheKey(lt.AUTHORITY+"."+t)},n.prototype.generateNonceKey=function(e){var t=je.parseRequestState(this.cryptoImpl,e).libraryState.id;return this.generateCacheKey(lt.NONCE_IDTOKEN+"."+t)},n.prototype.generateStateKey=function(e){var t=je.parseRequestState(this.cryptoImpl,e).libraryState.id;return this.generateCacheKey(lt.REQUEST_STATE+"."+t)},n.prototype.getCachedAuthority=function(e){var t=this.generateStateKey(e),r=this.getTemporaryCache(t);if(!r)return null;var n=this.generateAuthorityKey(r);return this.getTemporaryCache(n)},n.prototype.updateCacheEntries=function(e,t,r,n,o){this.logger.trace("BrowserCacheManager.updateCacheEntries called");var i=this.generateStateKey(e);this.setTemporaryCache(i,e,!1);var a=this.generateNonceKey(e);this.setTemporaryCache(a,t,!1);var s=this.generateAuthorityKey(e);if(this.setTemporaryCache(s,r,!1),o){var c={credential:o.homeAccountId,type:ke.HOME_ACCOUNT_ID};this.setTemporaryCache(lt.CCS_CREDENTIAL,JSON.stringify(c),!0)}else if(!ne.isEmpty(n)){c={credential:n,type:ke.UPN};this.setTemporaryCache(lt.CCS_CREDENTIAL,JSON.stringify(c),!0)}},n.prototype.resetRequestCache=function(e){var t=this;this.logger.trace("BrowserCacheManager.resetRequestCache called"),ne.isEmpty(e)||this.getKeys().forEach((function(r){-1!==r.indexOf(e)&&t.removeItem(r)})),e&&(this.removeItem(this.generateStateKey(e)),this.removeItem(this.generateNonceKey(e)),this.removeItem(this.generateAuthorityKey(e))),this.removeItem(this.generateCacheKey(lt.REQUEST_PARAMS)),this.removeItem(this.generateCacheKey(lt.ORIGIN_URI)),this.removeItem(this.generateCacheKey(lt.URL_HASH)),this.removeItem(this.generateCacheKey(lt.CORRELATION_ID)),this.removeItem(this.generateCacheKey(lt.CCS_CREDENTIAL)),this.removeItem(this.generateCacheKey(lt.NATIVE_REQUEST)),this.setInteractionInProgress(!1)},n.prototype.cleanRequestByState=function(e){if(this.logger.trace("BrowserCacheManager.cleanRequestByState called"),e){var t=this.generateStateKey(e),r=this.temporaryCacheStorage.getItem(t);this.logger.infoPii("BrowserCacheManager.cleanRequestByState: Removing temporary cache items for state: "+r),this.resetRequestCache(r||E.EMPTY_STRING)}this.clearMsalCookies()},n.prototype.cleanRequestByInteractionType=function(e){var t=this;this.logger.trace("BrowserCacheManager.cleanRequestByInteractionType called"),this.getKeys().forEach((function(r){if(-1!==r.indexOf(lt.REQUEST_STATE)){var n=t.temporaryCacheStorage.getItem(r);if(n){var o=Wt.extractBrowserRequestState(t.cryptoImpl,n);o&&o.interactionType===e&&(t.logger.infoPii("BrowserCacheManager.cleanRequestByInteractionType: Removing temporary cache items for state: "+n),t.resetRequestCache(n))}}})),this.clearMsalCookies(),this.setInteractionInProgress(!1)},n.prototype.cacheCodeRequest=function(e,t){this.logger.trace("BrowserCacheManager.cacheCodeRequest called");var r=t.base64Encode(JSON.stringify(e));this.setTemporaryCache(lt.REQUEST_PARAMS,r,!0)},n.prototype.getCachedRequest=function(e,t){this.logger.trace("BrowserCacheManager.getCachedRequest called");var r=this.getTemporaryCache(lt.REQUEST_PARAMS,!0);if(!r)throw Mt.createNoTokenRequestCacheError();var n=this.validateAndParseJson(t.base64Decode(r));if(!n)throw Mt.createUnableToParseTokenRequestCacheError();if(this.removeItem(this.generateCacheKey(lt.REQUEST_PARAMS)),ne.isEmpty(n.authority)){var o=this.generateAuthorityKey(e),i=this.getTemporaryCache(o);if(!i)throw Mt.createNoCachedAuthorityError();n.authority=i}return n},n.prototype.getCachedNativeRequest=function(){this.logger.trace("BrowserCacheManager.getCachedNativeRequest called");var e=this.getTemporaryCache(lt.NATIVE_REQUEST,!0);if(!e)return this.logger.trace("BrowserCacheManager.getCachedNativeRequest: No cached native request found"),null;var t=this.validateAndParseJson(e);return t||(this.logger.error("BrowserCacheManager.getCachedNativeRequest: Unable to parse native request"),null)},n.prototype.isInteractionInProgress=function(e){var t=this.getInteractionInProgress();return e?t===this.clientId:!!t},n.prototype.getInteractionInProgress=function(){var e=E.CACHE_PREFIX+"."+lt.INTERACTION_STATUS_KEY;return this.getTemporaryCache(e,!1)},n.prototype.setInteractionInProgress=function(e){var t=E.CACHE_PREFIX+"."+lt.INTERACTION_STATUS_KEY;if(e){if(this.getInteractionInProgress())throw Mt.createInteractionInProgressError();this.setTemporaryCache(t,this.clientId,!1)}else e||this.getInteractionInProgress()!==this.clientId||this.removeItem(t)},n.prototype.getLegacyLoginHint=function(){var e=this.getTemporaryCache(m.ADAL_ID_TOKEN);e&&(this.browserStorage.removeItem(m.ADAL_ID_TOKEN),this.logger.verbose("Cached ADAL id token retrieved."));var t=this.getTemporaryCache(m.ID_TOKEN,!0);t&&(this.removeItem(this.generateCacheKey(m.ID_TOKEN)),this.logger.verbose("Cached MSAL.js v1 id token retrieved"));var r=t||e;if(r){var n=new me(r,this.cryptoImpl);if(n.claims&&n.claims.preferred_username)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 preferred_username as loginHint"),n.claims.preferred_username;if(n.claims&&n.claims.upn)return this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, setting ADAL/MSAL v1 upn as loginHint"),n.claims.upn;this.logger.verbose("No SSO params used and ADAL/MSAL v1 token retrieved, however, no account hint claim found. Enable preferred_username or upn id token claim to get SSO.")}return null},n.prototype.updateCredentialCacheKey=function(e,t){var r=t.generateCredentialKey();if(e!==r){var n=this.getItem(e);if(n)return this.removeItem(e),this.setItem(r,n),this.logger.verbose("Updated an outdated "+t.credentialType+" cache key"),r;this.logger.error("Attempted to update an outdated "+t.credentialType+" cache key but no item matching the outdated key was found in storage")}return e},n.prototype.getRedirectRequestContext=function(){return this.getTemporaryCache(lt.REDIRECT_CONTEXT,!0)},n.prototype.setRedirectRequestContext=function(e){this.setTemporaryCache(lt.REDIRECT_CONTEXT,e,!0)},n.prototype.hydrateCache=function(e,t){var r,n,o,s,c,u;return i(this,void 0,void 0,(function(){var i,l,d,h;return a(this,(function(a){switch(a.label){case 0:return i=Le.createIdTokenEntity((null===(r=e.account)||void 0===r?void 0:r.homeAccountId)||"",(null===(n=e.account)||void 0===n?void 0:n.environment)||"",e.idToken,this.clientId,e.tenantId),t.claims?[4,this.cryptoImpl.hashString(t.claims)]:[3,2];case 1:l=a.sent(),a.label=2;case 2:return d=Ke.createAccessTokenEntity((null===(o=e.account)||void 0===o?void 0:o.homeAccountId)||"",(null===(s=e.account)||void 0===s?void 0:s.environment)||"",e.accessToken,this.clientId,e.tenantId,e.scopes.join(" "),(null===(c=e.expiresOn)||void 0===c?void 0:c.getTime())||0,(null===(u=e.extExpiresOn)||void 0===u?void 0:u.getTime())||0,this.cryptoImpl,void 0,e.tokenType,void 0,t.sshKid,t.claims,l),h=new Qe(void 0,i,d),[2,this.saveCacheRecord(h)]}}))}))},n}(ve),Jt="@azure/msal-browser",Xt="2.39.0",Zt=function(){function e(){}return e.prototype.sendGetRequestAsync=function(e,t){return i(this,void 0,void 0,(function(){var r,n,o;return a(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,fetch(e,{method:ut.GET,headers:this.getFetchHeaders(t)})];case 1:return r=i.sent(),[3,3];case 2:throw n=i.sent(),window.navigator.onLine?Mt.createGetRequestFailedError(n,e):Mt.createNoNetworkConnectivityError();case 3:return i.trys.push([3,5,,6]),o={headers:this.getHeaderDict(r.headers)},[4,r.json()];case 4:return[2,(o.body=i.sent(),o.status=r.status,o)];case 5:throw i.sent(),Mt.createFailedToParseNetworkResponseError(e);case 6:return[2]}}))}))},e.prototype.sendPostRequestAsync=function(e,t){return i(this,void 0,void 0,(function(){var r,n,o,i;return a(this,(function(a){switch(a.label){case 0:r=t&&t.body||E.EMPTY_STRING,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,fetch(e,{method:ut.POST,headers:this.getFetchHeaders(t),body:r})];case 2:return n=a.sent(),[3,4];case 3:throw o=a.sent(),window.navigator.onLine?Mt.createPostRequestFailedError(o,e):Mt.createNoNetworkConnectivityError();case 4:return a.trys.push([4,6,,7]),i={headers:this.getHeaderDict(n.headers)},[4,n.json()];case 5:return[2,(i.body=a.sent(),i.status=n.status,i)];case 6:throw a.sent(),Mt.createFailedToParseNetworkResponseError(e);case 7:return[2]}}))}))},e.prototype.getFetchHeaders=function(e){var t=new Headers;if(!e||!e.headers)return t;var r=e.headers;return Object.keys(r).forEach((function(e){t.append(e,r[e])})),t},e.prototype.getHeaderDict=function(e){var t={};return e.forEach((function(e,r){t[r]=e})),t},e}(),$t=function(){function e(){}return e.prototype.sendGetRequestAsync=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,this.sendRequestAsync(e,ut.GET,t)]}))}))},e.prototype.sendPostRequestAsync=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,this.sendRequestAsync(e,ut.POST,t)]}))}))},e.prototype.sendRequestAsync=function(e,t,r){var n=this;return new Promise((function(o,i){var a=new XMLHttpRequest;if(a.open(t,e,!0),n.setXhrHeaders(a,r),a.onload=function(){(a.status<200||a.status>=300)&&(t===ut.POST?i(Mt.createPostRequestFailedError("Failed with status "+a.status,e)):i(Mt.createGetRequestFailedError("Failed with status "+a.status,e)));try{var r=JSON.parse(a.responseText),s={headers:n.getHeaderDict(a),body:r,status:a.status};o(s)}catch(t){i(Mt.createFailedToParseNetworkResponseError(e))}},a.onerror=function(){window.navigator.onLine?t===ut.POST?i(Mt.createPostRequestFailedError("Failed with status "+a.status,e)):i(Mt.createGetRequestFailedError("Failed with status "+a.status,e)):i(Mt.createNoNetworkConnectivityError())},t===ut.POST&&r&&r.body)a.send(r.body);else{if(t!==ut.GET)throw Mt.createHttpMethodNotImplementedError(t);a.send()}}))},e.prototype.setXhrHeaders=function(e,t){if(t&&t.headers){var r=t.headers;Object.keys(r).forEach((function(t){e.setRequestHeader(t,r[t])}))}},e.prototype.getHeaderDict=function(e){var t=e.getAllResponseHeaders().trim().split(/[\r\n]+/),r={};return t.forEach((function(e){var t=e.split(": "),n=t.shift(),o=t.join(": ");n&&o&&(r[n]=o)})),r},e}(),er=function(){function t(){}return t.clearHash=function(e){e.location.hash=E.EMPTY_STRING,"function"==typeof e.history.replaceState&&e.history.replaceState(null,E.EMPTY_STRING,""+e.location.origin+e.location.pathname+e.location.search)},t.replaceHash=function(e){var t=e.split("#");t.shift(),window.location.hash=t.length>0?t.join("#"):E.EMPTY_STRING},t.isInIframe=function(){return window.parent!==window},t.isInPopup=function(){return"undefined"!=typeof window&&!!window.opener&&window.opener!==window&&"string"==typeof window.name&&0===window.name.indexOf(Ot.POPUP_NAME_PREFIX+".")},t.getCurrentUri=function(){return window.location.href.split("?")[0].split("#")[0]},t.getHomepage=function(){var e=new Ye(window.location.href).getUrlComponents();return e.Protocol+"//"+e.HostNameAndPort+"/"},t.getBrowserNetworkClient=function(){return window.fetch&&window.Headers?new Zt:new $t},t.blockReloadInHiddenIframes=function(){if(Ye.hashContainsKnownProperties(window.location.hash)&&t.isInIframe())throw Mt.createBlockReloadInHiddenIframeError()},t.blockRedirectInIframe=function(r,n){var o=t.isInIframe();if(r===e.InteractionType.Redirect&&o&&!n)throw Mt.createRedirectInIframeError(o)},t.blockAcquireTokenInPopups=function(){if(t.isInPopup())throw Mt.createBlockAcquireTokenInPopupsError()},t.blockNonBrowserEnvironment=function(e){if(!e)throw Mt.createNonBrowserEnvironmentError()},t.blockNativeBrokerCalledBeforeInitialized=function(e,t){if(e&&!t)throw Mt.createNativeBrokerCalledBeforeInitialize()},t.detectIEOrEdge=function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE "),r=e.indexOf("Trident/"),n=e.indexOf("Edge/");return t>0||r>0||n>0},t}(),tr=function(){function t(e,t,r,n,o,i,a,s,c){this.config=e,this.browserStorage=t,this.browserCrypto=r,this.networkClient=this.config.system.networkClient,this.eventHandler=o,this.navigationClient=i,this.nativeMessageHandler=s,this.correlationId=c||this.browserCrypto.createNewGuid(),this.logger=n.clone(Ot.MSAL_SKU,Xt,this.correlationId),this.performanceClient=a}return t.prototype.clearCacheOnLogout=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:if(!e)return[3,5];fe.accountInfoIsEqual(e,this.browserStorage.getActiveAccount(),!1)&&(this.logger.verbose("Setting active account to null"),this.browserStorage.setActiveAccount(null)),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.browserStorage.removeAccount(fe.generateAccountCacheKey(e))];case 2:return t.sent(),this.logger.verbose("Cleared cache items belonging to the account provided in the logout request."),[3,4];case 3:return t.sent(),this.logger.error("Account provided in logout request was not found. Local cache unchanged."),[3,4];case 4:return[3,9];case 5:return t.trys.push([5,8,,9]),this.logger.verbose("No account provided in logout request, clearing all cache items.",this.correlationId),[4,this.browserStorage.clear()];case 6:return t.sent(),[4,this.browserCrypto.clearKeystore()];case 7:return t.sent(),[3,9];case 8:return t.sent(),this.logger.error("Attempted to clear all MSAL cache items and failed. Local cache unchanged."),[3,9];case 9:return[2]}}))}))},t.prototype.initializeBaseRequest=function(t,r){return i(this,void 0,void 0,(function(){var o,i,s,u;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.InitializeBaseRequest,t.correlationId),this.logger.verbose("Initializing BaseAuthRequest"),o=t.authority||this.config.auth.authority,r?[4,this.validateRequestAuthority(o,r)]:[3,2];case 1:a.sent(),a.label=2;case 2:if(i=c(t&&t.scopes||[]),(s=n(n({},t),{correlationId:this.correlationId,authority:o,scopes:i})).authenticationScheme){if(s.authenticationScheme===e.AuthenticationScheme.SSH){if(!t.sshJwk)throw de.createMissingSshJwkError();if(!t.sshKid)throw de.createMissingSshKidError()}this.logger.verbose('Authentication Scheme set to "'+s.authenticationScheme+'" as configured in Auth request')}else s.authenticationScheme=e.AuthenticationScheme.BEARER,this.logger.verbose('Authentication Scheme wasn\'t explicitly set in request, defaulting to "Bearer" request');return this.config.cache.claimsBasedCachingEnabled&&t.claims&&!ne.isEmptyObj(t.claims)?(u=s,[4,this.browserCrypto.hashString(t.claims)]):[3,4];case 3:u.requestedClaimsHash=a.sent(),a.label=4;case 4:return[2,s]}}))}))},t.prototype.getRedirectUri=function(e){this.logger.verbose("getRedirectUri called");var t=e||this.config.auth.redirectUri||er.getCurrentUri();return Ye.getAbsoluteUrl(t,er.getCurrentUri())},t.prototype.validateRequestAuthority=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){switch(r.label){case 0:return[4,this.getDiscoveredAuthority(e)];case 1:if(!r.sent().isAlias(t.environment))throw de.createAuthorityMismatchError();return[2]}}))}))},t.prototype.initializeServerTelemetryManager=function(e,t){this.logger.verbose("initializeServerTelemetryManager called");var r={clientId:this.config.auth.clientId,correlationId:this.correlationId,apiId:e,forceRefresh:t||!1,wrapperSKU:this.browserStorage.getWrapperMetadata()[0],wrapperVer:this.browserStorage.getWrapperMetadata()[1]};return new kt(r,this.browserStorage)},t.prototype.getDiscoveredAuthority=function(e){return i(this,void 0,void 0,(function(){var t;return a(this,(function(r){switch(r.label){case 0:return this.logger.verbose("getDiscoveredAuthority called"),t={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata},e?(this.logger.verbose("Creating discovered authority with request authority"),[4,yt.createDiscoveredInstance(e,this.config.system.networkClient,this.browserStorage,t,this.logger)]):[3,2];case 1:case 3:return[2,r.sent()];case 2:return this.logger.verbose("Creating discovered authority with configured authority"),[4,yt.createDiscoveredInstance(this.config.auth.authority,this.config.system.networkClient,this.browserStorage,t,this.logger)]}}))}))},t}(),rr=function(t){function o(){return null!==t&&t.apply(this,arguments)||this}return r(o,t),o.prototype.initializeAuthorizationCodeRequest=function(t){return i(this,void 0,void 0,(function(){var r,o;return a(this,(function(i){switch(i.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest,t.correlationId),this.logger.verbose("initializeAuthorizationRequest called",t.correlationId),[4,this.browserCrypto.generatePkceCodes()];case 1:return r=i.sent(),o=n(n({},t),{redirectUri:t.redirectUri,code:E.EMPTY_STRING,codeVerifier:r.verifier}),t.codeChallenge=r.challenge,t.codeChallengeMethod=E.S256_CODE_CHALLENGE_METHOD,[2,o]}}))}))},o.prototype.initializeLogoutRequest=function(e){this.logger.verbose("initializeLogoutRequest called",null==e?void 0:e.correlationId);var t=n({correlationId:this.correlationId||this.browserCrypto.createNewGuid()},e);if(e)if(e.logoutHint)this.logger.verbose("logoutHint has already been set in logoutRequest");else if(e.account){var r=this.getLogoutHintFromIdTokenClaims(e.account);r&&(this.logger.verbose("Setting logoutHint to login_hint ID Token Claim value for the account provided"),t.logoutHint=r)}else this.logger.verbose("logoutHint was not set and account was not passed into logout request, logoutHint will not be set");else this.logger.verbose("logoutHint will not be set since no logout request was configured");return e&&null===e.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri passed as null, not setting post logout redirect uri",t.correlationId):e&&e.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request",t.correlationId),t.postLogoutRedirectUri=Ye.getAbsoluteUrl(e.postLogoutRedirectUri,er.getCurrentUri())):null===this.config.auth.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect",t.correlationId):this.config.auth.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to configured uri",t.correlationId),t.postLogoutRedirectUri=Ye.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri,er.getCurrentUri())):(this.logger.verbose("Setting postLogoutRedirectUri to current page",t.correlationId),t.postLogoutRedirectUri=Ye.getAbsoluteUrl(er.getCurrentUri(),er.getCurrentUri())),t},o.prototype.getLogoutHintFromIdTokenClaims=function(e){var t=e.idTokenClaims;if(t){if(t.login_hint)return t.login_hint;this.logger.verbose("The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request")}else this.logger.verbose("The provided account does not contain ID Token Claims, logoutHint will not be added to logout request");return null},o.prototype.createAuthCodeClient=function(t,r,n){return i(this,void 0,void 0,(function(){var o;return a(this,(function(i){switch(i.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,this.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientGetClientConfiguration,this.correlationId),[4,this.getClientConfiguration(t,r,n)];case 1:return o=i.sent(),[2,new et(o,this.performanceClient)]}}))}))},o.prototype.getClientConfiguration=function(t,r,n){return i(this,void 0,void 0,(function(){var o,i;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.StandardInteractionClientGetClientConfiguration,this.correlationId),this.logger.verbose("getClientConfiguration called",this.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientGetDiscoveredAuthority,this.correlationId),[4,this.getDiscoveredAuthority(r,n)];case 1:return o=a.sent(),i=this.config.system.loggerOptions,[2,{authOptions:{clientId:this.config.auth.clientId,authority:o,clientCapabilities:this.config.auth.clientCapabilities},systemOptions:{tokenRenewalOffsetSeconds:this.config.system.tokenRenewalOffsetSeconds,preventCorsPreflight:!0},loggerOptions:{loggerCallback:i.loggerCallback,piiLoggingEnabled:i.piiLoggingEnabled,logLevel:i.logLevel,correlationId:this.correlationId},cacheOptions:{claimsBasedCachingEnabled:this.config.cache.claimsBasedCachingEnabled},cryptoInterface:this.browserCrypto,networkInterface:this.networkClient,storageInterface:this.browserStorage,serverTelemetryManager:t,libraryInfo:{sku:Ot.MSAL_SKU,version:Xt,cpu:E.EMPTY_STRING,os:E.EMPTY_STRING},telemetry:this.config.telemetry}]}}))}))},o.prototype.validateAndExtractStateFromHash=function(e,t,r){if(this.logger.verbose("validateAndExtractStateFromHash called",r),!e.state)throw Mt.createHashDoesNotContainStateError();var n=Wt.extractBrowserRequestState(this.browserCrypto,e.state);if(!n)throw Mt.createUnableToParseStateError();if(n.interactionType!==t)throw Mt.createStateInteractionTypeMismatchError();return this.logger.verbose("Returning state from hash",r),e.state},o.prototype.getDiscoveredAuthority=function(t,r){var n;return i(this,void 0,void 0,(function(){var o,i,s,c;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.StandardInteractionClientGetDiscoveredAuthority,this.correlationId),this.logger.verbose("getDiscoveredAuthority called",this.correlationId),o=null===(n=this.performanceClient)||void 0===n?void 0:n.startMeasurement(e.PerformanceEvents.StandardInteractionClientGetDiscoveredAuthority,this.correlationId),i={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},s=t||this.config.auth.authority,c=vt.generateAuthority(s,r||this.config.auth.azureCloudOptions),this.logger.verbose("Creating discovered authority with configured authority",this.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.AuthorityFactoryCreateDiscoveredInstance,this.correlationId),[4,yt.createDiscoveredInstance(c,this.config.system.networkClient,this.browserStorage,i,this.logger,this.performanceClient,this.correlationId).then((function(e){return o.endMeasurement({success:!0}),e})).catch((function(e){throw o.endMeasurement({errorCode:e.errorCode,subErrorCode:e.subError,success:!1}),e}))];case 1:return[2,a.sent()]}}))}))},o.prototype.initializeAuthorizationRequest=function(t,r){return i(this,void 0,void 0,(function(){var o,i,s,c,u,l,d;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest,this.correlationId),this.logger.verbose("initializeAuthorizationRequest called",this.correlationId),o=this.getRedirectUri(t.redirectUri),i={interactionType:r},s=je.setRequestState(this.browserCrypto,t&&t.state||E.EMPTY_STRING,i),this.performanceClient.setPreQueueTime(e.PerformanceEvents.InitializeBaseRequest,this.correlationId),u=[{}],[4,this.initializeBaseRequest(t)];case 1:return c=n.apply(void 0,[n.apply(void 0,u.concat([a.sent()])),{redirectUri:o,state:s,nonce:t.nonce||this.browserCrypto.createNewGuid(),responseMode:S.FRAGMENT}]),(l=t.account||this.browserStorage.getActiveAccount())&&(this.logger.verbose("Setting validated request account",this.correlationId),this.logger.verbosePii("Setting validated request account: "+l.homeAccountId,this.correlationId),c.account=l),ne.isEmpty(c.loginHint)&&!l&&(d=this.browserStorage.getLegacyLoginHint())&&(c.loginHint=d),[2,c]}}))}))},o}(tr),nr=function(){function t(e,t,r,n,o){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r,this.logger=n,this.performanceClient=o}return t.prototype.handleCodeResponseFromHash=function(t,r,n,o){return i(this,void 0,void 0,(function(){var i,s,c;return a(this,(function(a){if(this.performanceClient.addQueueMeasurement(e.PerformanceEvents.HandleCodeResponseFromHash,this.authCodeRequest.correlationId),this.logger.verbose("InteractionHandler.handleCodeResponse called"),ne.isEmpty(t))throw Mt.createEmptyHashError(t);if(i=this.browserStorage.generateStateKey(r),!(s=this.browserStorage.getTemporaryCache(i)))throw re.createStateNotFoundError("Cached State");try{c=this.authModule.handleFragmentResponse(t,s)}catch(e){throw e instanceof Re&&e.subError===Nt.userCancelledError.code?Mt.createUserCancelledError():e}return this.performanceClient.setPreQueueTime(e.PerformanceEvents.HandleCodeResponseFromServer,this.authCodeRequest.correlationId),[2,this.handleCodeResponseFromServer(c,r,n,o)]}))}))},t.prototype.handleCodeResponseFromServer=function(t,r,n,o,s){return void 0===s&&(s=!0),i(this,void 0,void 0,(function(){var i,c,u,l,d,h;return a(this,(function(a){switch(a.label){case 0:if(this.performanceClient.addQueueMeasurement(e.PerformanceEvents.HandleCodeResponseFromServer,this.authCodeRequest.correlationId),this.logger.trace("InteractionHandler.handleCodeResponseFromServer called"),i=this.browserStorage.generateStateKey(r),!(c=this.browserStorage.getTemporaryCache(i)))throw re.createStateNotFoundError("Cached State");return u=this.browserStorage.generateNonceKey(c),l=this.browserStorage.getTemporaryCache(u),this.authCodeRequest.code=t.code,t.cloud_instance_host_name?(this.performanceClient.setPreQueueTime(e.PerformanceEvents.UpdateTokenEndpointAuthority,this.authCodeRequest.correlationId),[4,this.updateTokenEndpointAuthority(t.cloud_instance_host_name,n,o)]):[3,2];case 1:a.sent(),a.label=2;case 2:return s&&(t.nonce=l||void 0),t.state=c,t.client_info?this.authCodeRequest.clientInfo=t.client_info:(d=this.checkCcsCredentials())&&(this.authCodeRequest.ccsCredential=d),this.performanceClient.setPreQueueTime(e.PerformanceEvents.AuthClientAcquireToken,this.authCodeRequest.correlationId),[4,this.authModule.acquireToken(this.authCodeRequest,t)];case 3:return h=a.sent(),this.browserStorage.cleanRequestByState(r),[2,h]}}))}))},t.prototype.updateTokenEndpointAuthority=function(t,r,n){return i(this,void 0,void 0,(function(){var o,i;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.UpdateTokenEndpointAuthority,this.authCodeRequest.correlationId),o="https://"+t+"/"+r.tenant+"/",[4,yt.createDiscoveredInstance(o,n,this.browserStorage,r.options,this.logger,this.performanceClient,this.authCodeRequest.correlationId)];case 1:return i=a.sent(),this.authModule.updateAuthority(i),[2]}}))}))},t.prototype.checkCcsCredentials=function(){var e=this.browserStorage.getTemporaryCache(lt.CCS_CREDENTIAL,!0);if(e)try{return JSON.parse(e)}catch(t){this.authModule.logger.error("Cache credential could not be parsed"),this.authModule.logger.errorPii("Cache credential could not be parsed: "+e)}return null},t}(),or=function(t){function n(e,r,n,o,i,a){var s=t.call(this,e,r,n,o,a)||this;return s.browserCrypto=i,s}return r(n,t),n.prototype.initiateAuthRequest=function(t,r){return i(this,void 0,void 0,(function(){var n;return a(this,(function(o){switch(o.label){case 0:return this.logger.verbose("RedirectHandler.initiateAuthRequest called"),ne.isEmpty(t)?[3,7]:(r.redirectStartPage&&(this.logger.verbose("RedirectHandler.initiateAuthRequest: redirectStartPage set, caching start page"),this.browserStorage.setTemporaryCache(lt.ORIGIN_URI,r.redirectStartPage,!0)),this.browserStorage.setTemporaryCache(lt.CORRELATION_ID,this.authCodeRequest.correlationId,!0),this.browserStorage.cacheCodeRequest(this.authCodeRequest,this.browserCrypto),this.logger.infoPii("RedirectHandler.initiateAuthRequest: Navigate to: "+t),n={apiId:e.ApiId.acquireTokenRedirect,timeout:r.redirectTimeout,noHistory:!1},"function"!=typeof r.onRedirectNavigate?[3,4]:(this.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback"),!1===r.onRedirectNavigate(t)?[3,2]:(this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"),[4,r.navigationClient.navigateExternal(t,n)])));case 1:case 5:return o.sent(),[2];case 2:return this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation"),[2];case 3:return[3,6];case 4:return this.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"),[4,r.navigationClient.navigateExternal(t,n)];case 6:return[3,8];case 7:throw this.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"),Mt.createEmptyNavigationUriError();case 8:return[2]}}))}))},n.prototype.handleCodeResponseFromHash=function(e,t,r,n){return i(this,void 0,void 0,(function(){var o,i,s,c,u,l,d;return a(this,(function(a){switch(a.label){case 0:if(this.logger.verbose("RedirectHandler.handleCodeResponse called"),ne.isEmpty(e))throw Mt.createEmptyHashError(e);if(this.browserStorage.setInteractionInProgress(!1),o=this.browserStorage.generateStateKey(t),!(i=this.browserStorage.getTemporaryCache(o)))throw re.createStateNotFoundError("Cached State");try{s=this.authModule.handleFragmentResponse(e,i)}catch(e){throw e instanceof Re&&e.subError===Nt.userCancelledError.code?Mt.createUserCancelledError():e}return c=this.browserStorage.generateNonceKey(i),u=this.browserStorage.getTemporaryCache(c),this.authCodeRequest.code=s.code,s.cloud_instance_host_name?[4,this.updateTokenEndpointAuthority(s.cloud_instance_host_name,r,n)]:[3,2];case 1:a.sent(),a.label=2;case 2:return s.nonce=u||void 0,s.state=i,s.client_info?this.authCodeRequest.clientInfo=s.client_info:(l=this.checkCcsCredentials())&&(this.authCodeRequest.ccsCredential=l),[4,this.authModule.acquireToken(this.authCodeRequest,s)];case 3:return d=a.sent(),this.browserStorage.cleanRequestByState(t),[2,d]}}))}))},n}(nr);e.EventType=void 0,(xt=e.EventType||(e.EventType={})).INITIALIZE_START="msal:initializeStart",xt.INITIALIZE_END="msal:initializeEnd",xt.ACCOUNT_ADDED="msal:accountAdded",xt.ACCOUNT_REMOVED="msal:accountRemoved",xt.LOGIN_START="msal:loginStart",xt.LOGIN_SUCCESS="msal:loginSuccess",xt.LOGIN_FAILURE="msal:loginFailure",xt.ACQUIRE_TOKEN_START="msal:acquireTokenStart",xt.ACQUIRE_TOKEN_SUCCESS="msal:acquireTokenSuccess",xt.ACQUIRE_TOKEN_FAILURE="msal:acquireTokenFailure",xt.ACQUIRE_TOKEN_NETWORK_START="msal:acquireTokenFromNetworkStart",xt.SSO_SILENT_START="msal:ssoSilentStart",xt.SSO_SILENT_SUCCESS="msal:ssoSilentSuccess",xt.SSO_SILENT_FAILURE="msal:ssoSilentFailure",xt.ACQUIRE_TOKEN_BY_CODE_START="msal:acquireTokenByCodeStart",xt.ACQUIRE_TOKEN_BY_CODE_SUCCESS="msal:acquireTokenByCodeSuccess",xt.ACQUIRE_TOKEN_BY_CODE_FAILURE="msal:acquireTokenByCodeFailure",xt.HANDLE_REDIRECT_START="msal:handleRedirectStart",xt.HANDLE_REDIRECT_END="msal:handleRedirectEnd",xt.POPUP_OPENED="msal:popupOpened",xt.LOGOUT_START="msal:logoutStart",xt.LOGOUT_SUCCESS="msal:logoutSuccess",xt.LOGOUT_FAILURE="msal:logoutFailure",xt.LOGOUT_END="msal:logoutEnd",xt.RESTORE_FROM_BFCACHE="msal:restoreFromBFCache",function(e){e.USER_INTERACTION_REQUIRED="USER_INTERACTION_REQUIRED",e.USER_CANCEL="USER_CANCEL",e.NO_NETWORK="NO_NETWORK",e.TRANSIENT_ERROR="TRANSIENT_ERROR",e.PERSISTENT_ERROR="PERSISTENT_ERROR",e.DISABLED="DISABLED",e.ACCOUNT_UNAVAILABLE="ACCOUNT_UNAVAILABLE"}(Gt||(Gt={}));var ir={code:"ContentError"},ar={code:"user_switch",desc:"User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."},sr={code:"tokens_not_found_in_internal_memory_cache",desc:"Tokens not cached in MSAL JS internal memory, please make the WAM request"},cr=function(e){function t(r,n,o){var i=e.call(this,r,n)||this;return Object.setPrototypeOf(i,t.prototype),i.name="NativeAuthError",i.ext=o,i}return r(t,e),t.prototype.isFatal=function(){return!(!this.ext||!this.ext.status||this.ext.status!==Gt.PERSISTENT_ERROR&&this.ext.status!==Gt.DISABLED)||this.errorCode===ir.code},t.createError=function(e,r,n){if(n&&n.status)switch(n.status){case Gt.ACCOUNT_UNAVAILABLE:return ze.createNativeAccountUnavailableError();case Gt.USER_INTERACTION_REQUIRED:return new ze(e,r);case Gt.USER_CANCEL:return Mt.createUserCancelledError();case Gt.NO_NETWORK:return Mt.createNoNetworkConnectivityError()}return new t(e,r,n)},t.createUserSwitchError=function(){return new t(ar.code,ar.desc)},t.createTokensNotFoundInCacheError=function(){return new t(sr.code,sr.desc)},t}($),ur=function(t){function o(){return null!==t&&t.apply(this,arguments)||this}return r(o,t),o.prototype.acquireToken=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s;return a(this,(function(a){switch(a.label){case 0:return r=this.performanceClient.startMeasurement(e.PerformanceEvents.SilentCacheClientAcquireToken,t.correlationId),n=this.initializeServerTelemetryManager(e.ApiId.acquireTokenSilent_silentFlow),[4,this.createSilentFlowClient(n,t.authority,t.azureCloudOptions)];case 1:o=a.sent(),this.logger.verbose("Silent auth client created"),a.label=2;case 2:return a.trys.push([2,4,,5]),[4,o.acquireCachedToken(t)];case 3:return i=a.sent(),r.endMeasurement({success:!0,fromCache:!0}),[2,i];case 4:throw(s=a.sent())instanceof Mt&&s.errorCode===Nt.signingKeyNotFoundInStorage.code&&this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair."),r.endMeasurement({errorCode:s instanceof $&&s.errorCode||void 0,subErrorCode:s instanceof $&&s.subError||void 0,success:!1}),s;case 5:return[2]}}))}))},o.prototype.logout=function(){return Promise.reject(Mt.createSilentLogoutUnsupportedError())},o.prototype.createSilentFlowClient=function(t,r,n){return i(this,void 0,void 0,(function(){var o;return a(this,(function(i){switch(i.label){case 0:return this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientGetClientConfiguration,this.correlationId),[4,this.getClientConfiguration(t,r,n)];case 1:return o=i.sent(),[2,new rt(o,this.performanceClient)]}}))}))},o.prototype.initializeSilentRequest=function(t,r){return i(this,void 0,void 0,(function(){var o;return a(this,(function(i){switch(i.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.InitializeSilentRequest,this.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.InitializeBaseRequest,this.correlationId),o=[n({},t)],[4,this.initializeBaseRequest(t,r)];case 1:return[2,n.apply(void 0,[n.apply(void 0,o.concat([i.sent()])),{account:r,forceRefresh:t.forceRefresh||!1}])]}}))}))},o}(rr),lr=function(t){function s(e,r,n,o,i,a,s,c,u,l,d,h){var p=t.call(this,e,r,n,o,i,a,c,u,h)||this;return p.apiId=s,p.accountId=l,p.nativeMessageHandler=u,p.nativeStorageManager=d,p.silentCacheClient=new ur(e,p.nativeStorageManager,n,o,i,a,c,u,h),p}return r(s,t),s.prototype.acquireToken=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s,c,u;return a(this,(function(a){switch(a.label){case 0:return this.logger.trace("NativeInteractionClient - acquireToken called."),r=this.performanceClient.startMeasurement(e.PerformanceEvents.NativeInteractionClientAcquireToken,t.correlationId),n=De.nowSeconds(),[4,this.initializeNativeRequest(t)];case 1:o=a.sent(),a.label=2;case 2:return a.trys.push([2,4,,5]),[4,this.acquireTokensFromCache(this.accountId,o)];case 3:return i=a.sent(),r.endMeasurement({success:!0,isNativeBroker:!1,fromCache:!0}),[2,i];case 4:return a.sent(),this.logger.info("MSAL internal Cache does not contain tokens, proceed to make a native call"),[3,5];case 5:return s={method:st.GetToken,request:o},[4,this.nativeMessageHandler.sendMessage(s)];case 6:return c=a.sent(),u=this.validateNativeResponse(c),[2,this.handleNativeResponse(u,o,n).then((function(e){return r.endMeasurement({success:!0,isNativeBroker:!0,requestId:e.requestId}),e})).catch((function(e){throw r.endMeasurement({success:!1,errorCode:e.errorCode,subErrorCode:e.subError,isNativeBroker:!0}),e}))]}}))}))},s.prototype.createSilentCacheRequest=function(e,t){return{authority:e.authority,correlationId:this.correlationId,scopes:he.fromString(e.scope).asArray(),account:t,forceRefresh:!1}},s.prototype.acquireTokensFromCache=function(e,t){return i(this,void 0,void 0,(function(){var r,o,i,s;return a(this,(function(a){switch(a.label){case 0:if(!e)throw this.logger.warning("NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided"),re.createNoAccountFoundError();if(!(r=this.browserStorage.getAccountInfoFilteredBy({nativeAccountId:e})))throw re.createNoAccountFoundError();a.label=1;case 1:return a.trys.push([1,3,,4]),o=this.createSilentCacheRequest(t,r),[4,this.silentCacheClient.acquireToken(o)];case 2:return i=a.sent(),s=n(n({},r),{idTokenClaims:i.idTokenClaims}),[2,n(n({},i),{account:s})];case 3:throw a.sent();case 4:return[2]}}))}))},s.prototype.acquireTokenRedirect=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s,c;return a(this,(function(a){switch(a.label){case 0:return this.logger.trace("NativeInteractionClient - acquireTokenRedirect called."),[4,this.initializeNativeRequest(t)];case 1:r=a.sent(),n={method:st.GetToken,request:r},a.label=2;case 2:return a.trys.push([2,4,,5]),[4,this.nativeMessageHandler.sendMessage(n)];case 3:return o=a.sent(),this.validateNativeResponse(o),[3,5];case 4:if((i=a.sent())instanceof cr&&i.isFatal())throw i;return[3,5];case 5:return this.browserStorage.setTemporaryCache(lt.NATIVE_REQUEST,JSON.stringify(r),!0),s={apiId:e.ApiId.acquireTokenRedirect,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},c=this.config.auth.navigateToLoginRequestUrl?window.location.href:this.getRedirectUri(t.redirectUri),[4,this.navigationClient.navigateExternal(c,s)];case 6:return a.sent(),[2]}}))}))},s.prototype.handleRedirectPromise=function(){return i(this,void 0,void 0,(function(){var e,t,r,n,i,s,c,u;return a(this,(function(a){switch(a.label){case 0:if(this.logger.trace("NativeInteractionClient - handleRedirectPromise called."),!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),[2,null];if(!(e=this.browserStorage.getCachedNativeRequest()))return this.logger.verbose("NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null."),[2,null];t=e.prompt,r=o(e,["prompt"]),t&&this.logger.verbose("NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window."),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(lt.NATIVE_REQUEST)),n={method:st.GetToken,request:r},i=De.nowSeconds(),a.label=1;case 1:return a.trys.push([1,3,,4]),this.logger.verbose("NativeInteractionClient - handleRedirectPromise sending message to native broker."),[4,this.nativeMessageHandler.sendMessage(n)];case 2:return s=a.sent(),this.validateNativeResponse(s),c=this.handleNativeResponse(s,r,i),this.browserStorage.setInteractionInProgress(!1),[2,c];case 3:throw u=a.sent(),this.browserStorage.setInteractionInProgress(!1),u;case 4:return[2]}}))}))},s.prototype.logout=function(){return this.logger.trace("NativeInteractionClient - logout called."),Promise.reject("Logout not implemented yet")},s.prototype.handleNativeResponse=function(e,t,r){return i(this,void 0,void 0,(function(){var n,o,i,s,c;return a(this,(function(a){switch(a.label){case 0:if(this.logger.trace("NativeInteractionClient - handleNativeResponse called."),e.account.id!==t.accountId)throw cr.createUserSwitchError();return[4,this.getDiscoveredAuthority(t.authority)];case 1:return n=a.sent(),o=this.createIdTokenObj(e),i=this.createHomeAccountIdentifier(e,o),s=fe.createAccount({homeAccountId:i,idTokenClaims:o.claims,clientInfo:e.client_info,nativeAccountId:e.account.id},n),[4,this.generateAuthenticationResult(e,t,o,s,n.canonicalAuthority,r)];case 2:return c=a.sent(),this.cacheAccount(s),this.cacheNativeTokens(e,t,i,o,c.accessToken,c.tenantId,r),[2,c]}}))}))},s.prototype.createIdTokenObj=function(e){return new me(e.id_token||E.EMPTY_STRING,this.browserCrypto)},s.prototype.createHomeAccountIdentifier=function(e,t){return fe.generateHomeAccountId(e.client_info||E.EMPTY_STRING,ce.Default,this.logger,this.browserCrypto,t.claims)},s.prototype.generateScopes=function(e,t){return e.scope?he.fromString(e.scope):he.fromString(t.scope)},s.prototype.generatePopAccessToken=function(t,r){return i(this,void 0,void 0,(function(){var n,o;return a(this,(function(i){switch(i.label){case 0:if(r.tokenType!==e.AuthenticationScheme.POP)return[3,2];if(t.shr)return this.logger.trace("handleNativeServerResponse: SHR is enabled in native layer"),[2,t.shr];if(n=new Je(this.browserCrypto),o={resourceRequestMethod:r.resourceRequestMethod,resourceRequestUri:r.resourceRequestUri,shrClaims:r.shrClaims,shrNonce:r.shrNonce},!r.keyId)throw re.createKeyIdMissingError();return[4,n.signPopToken(t.access_token,r.keyId,o)];case 1:return[2,i.sent()];case 2:return[2,t.access_token]}}))}))},s.prototype.generateAuthenticationResult=function(t,r,n,o,s,c){return i(this,void 0,void 0,(function(){var i,u,l,d,h,p,g;return a(this,(function(a){switch(a.label){case 0:return i=this.addTelemetryFromNativeResponse(t),u=t.scope?he.fromString(t.scope):he.fromString(r.scope),l=t.account.properties||{},d=l.UID||n.claims.oid||n.claims.sub||E.EMPTY_STRING,h=l.TenantId||n.claims.tid||E.EMPTY_STRING,[4,this.generatePopAccessToken(t,r)];case 1:return p=a.sent(),g=r.tokenType===e.AuthenticationScheme.POP?e.AuthenticationScheme.POP:e.AuthenticationScheme.BEARER,[2,{authority:s,uniqueId:d,tenantId:h,scopes:u.asArray(),account:o.getAccountInfo(),idToken:t.id_token,idTokenClaims:n.claims,accessToken:p,fromCache:!!i&&this.isResponseFromCache(i),expiresOn:new Date(1e3*Number(c+t.expires_in)),tokenType:g,correlationId:this.correlationId,state:t.state,fromNativeBroker:!0}]}}))}))},s.prototype.cacheAccount=function(e){var t=this;this.browserStorage.setAccount(e),this.browserStorage.removeAccountContext(e).catch((function(e){t.logger.error("Error occurred while removing account context from browser storage. "+e)}))},s.prototype.cacheNativeTokens=function(t,r,n,o,i,a,s){var c=Le.createIdTokenEntity(n,r.authority,t.id_token||E.EMPTY_STRING,r.clientId,o.claims.tid||E.EMPTY_STRING),u=s+(r.tokenType===e.AuthenticationScheme.POP?E.SHR_NONCE_VALIDITY:("string"==typeof t.expires_in?parseInt(t.expires_in,10):t.expires_in)||0),l=this.generateScopes(t,r),d=Ke.createAccessTokenEntity(n,r.authority,i,r.clientId,o?o.claims.tid||E.EMPTY_STRING:a,l.printScopes(),u,0,this.browserCrypto),h=new Qe(void 0,c,d);this.nativeStorageManager.saveCacheRecord(h)},s.prototype.addTelemetryFromNativeResponse=function(e){var t=this.getMATSFromResponse(e);return t?(this.performanceClient.addStaticFields({extensionId:this.nativeMessageHandler.getExtensionId(),extensionVersion:this.nativeMessageHandler.getExtensionVersion(),matsBrokerVersion:t.broker_version,matsAccountJoinOnStart:t.account_join_on_start,matsAccountJoinOnEnd:t.account_join_on_end,matsDeviceJoin:t.device_join,matsPromptBehavior:t.prompt_behavior,matsApiErrorCode:t.api_error_code,matsUiVisible:t.ui_visible,matsSilentCode:t.silent_code,matsSilentBiSubCode:t.silent_bi_sub_code,matsSilentMessage:t.silent_message,matsSilentStatus:t.silent_status,matsHttpStatus:t.http_status,matsHttpEventCount:t.http_event_count},this.correlationId),t):null},s.prototype.validateNativeResponse=function(e){if(e.hasOwnProperty("access_token")&&e.hasOwnProperty("id_token")&&e.hasOwnProperty("client_info")&&e.hasOwnProperty("account")&&e.hasOwnProperty("scope")&&e.hasOwnProperty("expires_in"))return e;throw cr.createUnexpectedError("Response missing expected properties.")},s.prototype.getMATSFromResponse=function(e){if(e.properties.MATS)try{return JSON.parse(e.properties.MATS)}catch(e){this.logger.error("NativeInteractionClient - Error parsing MATS telemetry, returning null instead")}return null},s.prototype.isResponseFromCache=function(e){return void 0===e.is_cached?(this.logger.verbose("NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false."),!1):!!e.is_cached},s.prototype.initializeNativeRequest=function(t){return i(this,void 0,void 0,(function(){var r,i,s,c,u,l,d,h,p,g=this;return a(this,(function(a){switch(a.label){case 0:return this.logger.trace("NativeInteractionClient - initializeNativeRequest called"),r=t.authority||this.config.auth.authority,t.account?[4,this.validateRequestAuthority(r,t.account)]:[3,2];case 1:a.sent(),a.label=2;case 2:return(i=new Ye(r)).validateAsUri(),s=t.scopes,c=o(t,["scopes"]),(u=new he(s||[])).appendScopes(T),l=function(){switch(g.apiId){case e.ApiId.ssoSilent:case e.ApiId.acquireTokenSilent_silentFlow:return g.logger.trace("initializeNativeRequest: silent request sets prompt to none"),w.NONE}if(t.prompt)switch(t.prompt){case w.NONE:case w.CONSENT:case w.LOGIN:return g.logger.trace("initializeNativeRequest: prompt is compatible with native flow"),t.prompt;default:throw g.logger.trace("initializeNativeRequest: prompt = "+t.prompt+" is not compatible with native flow"),Mt.createNativePromptParameterNotSupportedError()}else g.logger.trace("initializeNativeRequest: prompt was not provided")},d=n(n({},c),{accountId:this.accountId,clientId:this.config.auth.clientId,authority:i.urlString,scope:u.printScopes(),redirectUri:this.getRedirectUri(t.redirectUri),prompt:l(),correlationId:this.correlationId,tokenType:t.authenticationScheme,windowTitleSubstring:document.title,extraParameters:n(n(n({},t.extraQueryParameters),t.tokenQueryParameters),{telemetry:Ht}),extendedExpiryToken:!1}),t.authenticationScheme!==e.AuthenticationScheme.POP?[3,4]:(h={resourceRequestUri:t.resourceRequestUri,resourceRequestMethod:t.resourceRequestMethod,shrClaims:t.shrClaims,shrNonce:t.shrNonce},[4,new Je(this.browserCrypto).generateCnf(h)]);case 3:p=a.sent(),d.reqCnf=p.reqCnfString,d.keyId=p.kid,a.label=4;case 4:return[2,d]}}))}))},s}(tr),dr=function(){function t(t,r,n,o){this.logger=t,this.handshakeTimeoutMs=r,this.extensionId=o,this.resolvers=new Map,this.handshakeResolvers=new Map,this.responseId=0,this.messageChannel=new MessageChannel,this.windowListener=this.onWindowMessage.bind(this),this.performanceClient=n,this.handshakeEvent=n.startMeasurement(e.PerformanceEvents.NativeMessageHandlerHandshake)}return t.prototype.sendMessage=function(e){return i(this,void 0,void 0,(function(){var t,r=this;return a(this,(function(n){return this.logger.trace("NativeMessageHandler - sendMessage called."),t={channel:qt,extensionId:this.extensionId,responseId:this.responseId++,body:e},this.logger.trace("NativeMessageHandler - Sending request to browser extension"),this.logger.tracePii("NativeMessageHandler - Sending request to browser extension: "+JSON.stringify(t)),this.messageChannel.port1.postMessage(t),[2,new Promise((function(e,n){r.resolvers.set(t.responseId,{resolve:e,reject:n})}))]}))}))},t.createProvider=function(e,r,n){return i(this,void 0,void 0,(function(){var o,i;return a(this,(function(a){switch(a.label){case 0:e.trace("NativeMessageHandler - createProvider called."),a.label=1;case 1:return a.trys.push([1,3,,5]),[4,(o=new t(e,r,n,Ut)).sendHandshakeRequest()];case 2:return a.sent(),[2,o];case 3:return a.sent(),[4,(i=new t(e,r,n)).sendHandshakeRequest()];case 4:return a.sent(),[2,i];case 5:return[2]}}))}))},t.prototype.sendHandshakeRequest=function(){return i(this,void 0,void 0,(function(){var e,t=this;return a(this,(function(r){return this.logger.trace("NativeMessageHandler - sendHandshakeRequest called."),window.addEventListener("message",this.windowListener,!1),e={channel:qt,extensionId:this.extensionId,responseId:this.responseId++,body:{method:st.HandshakeRequest}},this.handshakeEvent.addStaticFields({extensionId:this.extensionId,extensionHandshakeTimeoutMs:this.handshakeTimeoutMs}),this.messageChannel.port1.onmessage=function(e){t.onChannelMessage(e)},window.postMessage(e,window.origin,[this.messageChannel.port2]),[2,new Promise((function(r,n){t.handshakeResolvers.set(e.responseId,{resolve:r,reject:n}),t.timeoutId=window.setTimeout((function(){window.removeEventListener("message",t.windowListener,!1),t.messageChannel.port1.close(),t.messageChannel.port2.close(),t.handshakeEvent.endMeasurement({extensionHandshakeTimedOut:!0,success:!1}),n(Mt.createNativeHandshakeTimeoutError()),t.handshakeResolvers.delete(e.responseId)}),t.handshakeTimeoutMs)}))]}))}))},t.prototype.onWindowMessage=function(e){if(this.logger.trace("NativeMessageHandler - onWindowMessage called"),e.source===window){var t=e.data;if(t.channel&&t.channel===qt&&(!t.extensionId||t.extensionId===this.extensionId)&&t.body.method===st.HandshakeRequest){this.logger.verbose(t.extensionId?"Extension with id: "+t.extensionId+" not installed":"No extension installed"),clearTimeout(this.timeoutId),this.messageChannel.port1.close(),this.messageChannel.port2.close(),window.removeEventListener("message",this.windowListener,!1);var r=this.handshakeResolvers.get(t.responseId);r&&(this.handshakeEvent.endMeasurement({success:!1,extensionInstalled:!1}),r.reject(Mt.createNativeExtensionNotInstalledError()))}}},t.prototype.onChannelMessage=function(e){this.logger.trace("NativeMessageHandler - onChannelMessage called.");var t=e.data,r=this.resolvers.get(t.responseId),n=this.handshakeResolvers.get(t.responseId);try{var o=t.body.method;if(o===st.Response){if(!r)return;var i=t.body.response;if(this.logger.trace("NativeMessageHandler - Received response from browser extension"),this.logger.tracePii("NativeMessageHandler - Received response from browser extension: "+JSON.stringify(i)),"Success"!==i.status)r.reject(cr.createError(i.code,i.description,i.ext));else{if(!i.result)throw $.createUnexpectedError("Event does not contain result.");i.result.code&&i.result.description?r.reject(cr.createError(i.result.code,i.result.description,i.result.ext)):r.resolve(i.result)}this.resolvers.delete(t.responseId)}else if(o===st.HandshakeResponse){if(!n)return;clearTimeout(this.timeoutId),window.removeEventListener("message",this.windowListener,!1),this.extensionId=t.extensionId,this.extensionVersion=t.body.version,this.logger.verbose("NativeMessageHandler - Received HandshakeResponse from extension: "+this.extensionId),this.handshakeEvent.endMeasurement({extensionInstalled:!0,success:!0}),n.resolve(),this.handshakeResolvers.delete(t.responseId)}}catch(t){this.logger.error("Error parsing response from WAM Extension"),this.logger.errorPii("Error parsing response from WAM Extension: "+t.toString()),this.logger.errorPii("Unable to parse "+e),r?r.reject(t):n&&n.reject(t)}},t.prototype.getExtensionId=function(){return this.extensionId},t.prototype.getExtensionVersion=function(){return this.extensionVersion},t.isNativeAvailable=function(t,r,n,o){if(r.trace("isNativeAvailable called"),!t.system.allowNativeBroker)return r.trace("isNativeAvailable: allowNativeBroker is not enabled, returning false"),!1;if(!n)return r.trace("isNativeAvailable: WAM extension provider is not initialized, returning false"),!1;if(o)switch(o){case e.AuthenticationScheme.BEARER:case e.AuthenticationScheme.POP:return r.trace("isNativeAvailable: authenticationScheme is supported, returning true"),!0;default:return r.trace("isNativeAvailable: authenticationScheme is not supported, returning false"),!1}return!0},t}(),hr=function(t){function o(e,r,n,o,i,a,s,c,u,l){var d=t.call(this,e,r,n,o,i,a,s,u,l)||this;return d.nativeStorage=c,d}return r(o,t),o.prototype.acquireToken=function(t){return i(this,void 0,void 0,(function(){var r,o,i,s,c,u,l,d,h,p=this;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest,t.correlationId),[4,this.initializeAuthorizationRequest(t,e.InteractionType.Redirect)];case 1:r=a.sent(),this.browserStorage.updateCacheEntries(r.state,r.nonce,r.authority,r.loginHint||E.EMPTY_STRING,r.account||null),o=this.initializeServerTelemetryManager(e.ApiId.acquireTokenRedirect),i=function(t){t.persisted&&(p.logger.verbose("Page was restored from back/forward cache. Clearing temporary cache."),p.browserStorage.cleanRequestByState(r.state),p.eventHandler.emitEvent(e.EventType.RESTORE_FROM_BFCACHE,e.InteractionType.Redirect))},a.label=2;case 2:return a.trys.push([2,7,,8]),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest,t.correlationId),[4,this.initializeAuthorizationCodeRequest(r)];case 3:return s=a.sent(),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,t.correlationId),[4,this.createAuthCodeClient(o,r.authority,r.azureCloudOptions)];case 4:return c=a.sent(),this.logger.verbose("Auth code client created"),u=new or(c,this.browserStorage,s,this.logger,this.browserCrypto,this.performanceClient),[4,c.getAuthCodeUrl(n(n({},r),{nativeBroker:dr.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme)}))];case 5:return l=a.sent(),d=this.getRedirectStartPage(t.redirectStartPage),this.logger.verbosePii("Redirect start page: "+d),window.addEventListener("pageshow",i),[4,u.initiateAuthRequest(l,{navigationClient:this.navigationClient,redirectTimeout:this.config.system.redirectNavigationTimeout,redirectStartPage:d,onRedirectNavigate:t.onRedirectNavigate})];case 6:return[2,a.sent()];case 7:throw(h=a.sent())instanceof $&&h.setCorrelationId(this.correlationId),window.removeEventListener("pageshow",i),o.cacheFailedRequest(h),this.browserStorage.cleanRequestByState(r.state),h;case 8:return[2]}}))}))},o.prototype.handleRedirectPromise=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s,c,u,l,d,h,p,g;return a(this,(function(a){switch(a.label){case 0:r=this.initializeServerTelemetryManager(e.ApiId.handleRedirectPromise),a.label=1;case 1:if(a.trys.push([1,10,,11]),!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),[2,null];if(!(n=this.getRedirectResponseHash(t||window.location.hash)))return this.logger.info("handleRedirectPromise did not detect a response hash as a result of a redirect. Cleaning temporary cache."),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),[2,null];o=void 0;try{i=Ye.getDeserializedHash(n),o=this.validateAndExtractStateFromHash(i,e.InteractionType.Redirect),this.logger.verbose("State extracted from hash")}catch(t){return this.logger.info("handleRedirectPromise was unable to extract state due to: "+t),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),[2,null]}return s=this.browserStorage.getTemporaryCache(lt.ORIGIN_URI,!0)||E.EMPTY_STRING,c=Ye.removeHashFromUrl(s),u=Ye.removeHashFromUrl(window.location.href),c===u&&this.config.auth.navigateToLoginRequestUrl?(this.logger.verbose("Current page is loginRequestUrl, handling hash"),[4,this.handleHash(n,o,r)]):[3,3];case 2:return l=a.sent(),s.indexOf("#")>-1&&er.replaceHash(s),[2,l];case 3:return this.config.auth.navigateToLoginRequestUrl?[3,4]:(this.logger.verbose("NavigateToLoginRequestUrl set to false, handling hash"),[2,this.handleHash(n,o,r)]);case 4:return er.isInIframe()&&!this.config.system.allowRedirectInIframe?[3,9]:(this.browserStorage.setTemporaryCache(lt.URL_HASH,n,!0),d={apiId:e.ApiId.handleRedirectPromise,timeout:this.config.system.redirectNavigationTimeout,noHistory:!0},h=!0,s&&"null"!==s?[3,6]:(p=er.getHomepage(),this.browserStorage.setTemporaryCache(lt.ORIGIN_URI,p,!0),this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"),[4,this.navigationClient.navigateInternal(p,d)]));case 5:return h=a.sent(),[3,8];case 6:return this.logger.verbose("Navigating to loginRequestUrl: "+s),[4,this.navigationClient.navigateInternal(s,d)];case 7:h=a.sent(),a.label=8;case 8:if(!h)return[2,this.handleHash(n,o,r)];a.label=9;case 9:return[2,null];case 10:throw(g=a.sent())instanceof $&&g.setCorrelationId(this.correlationId),r.cacheFailedRequest(g),this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Redirect),g;case 11:return[2]}}))}))},o.prototype.getRedirectResponseHash=function(e){if(this.logger.verbose("getRedirectResponseHash called"),Ye.hashContainsKnownProperties(e))return er.clearHash(window),this.logger.verbose("Hash contains known properties, returning response hash"),e;var t=this.browserStorage.getTemporaryCache(lt.URL_HASH,!0);return this.browserStorage.removeItem(this.browserStorage.generateCacheKey(lt.URL_HASH)),this.logger.verbose("Hash does not contain known properties, returning cached hash"),t},o.prototype.handleHash=function(t,r,o){return i(this,void 0,void 0,(function(){var i,s,c,u,l,d,h=this;return a(this,(function(a){switch(a.label){case 0:if(i=this.browserStorage.getCachedRequest(r,this.browserCrypto),this.logger.verbose("handleHash called, retrieved cached request"),(s=Ye.getDeserializedHash(t)).accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw Mt.createNativeConnectionNotEstablishedError();return c=new lr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,e.ApiId.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,s.accountId,this.nativeStorage,i.correlationId),u=je.parseRequestState(this.browserCrypto,r).userRequestState,[2,c.acquireToken(n(n({},i),{state:u,prompt:void 0})).finally((function(){h.browserStorage.cleanRequestByState(r)}))]}if(!(l=this.browserStorage.getCachedAuthority(r)))throw Mt.createNoCachedAuthorityError();return this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,i.correlationId),[4,this.createAuthCodeClient(o,l)];case 1:return d=a.sent(),this.logger.verbose("Auth code client created"),be.removeThrottle(this.browserStorage,this.config.auth.clientId,i),[4,new or(d,this.browserStorage,i,this.logger,this.browserCrypto,this.performanceClient).handleCodeResponseFromHash(t,r,d.authority,this.networkClient)];case 2:return[2,a.sent()]}}))}))},o.prototype.logout=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s,c;return a(this,(function(a){switch(a.label){case 0:this.logger.verbose("logoutRedirect called"),r=this.initializeLogoutRequest(t),n=this.initializeServerTelemetryManager(e.ApiId.logout),a.label=1;case 1:return a.trys.push([1,10,,11]),this.eventHandler.emitEvent(e.EventType.LOGOUT_START,e.InteractionType.Redirect,t),[4,this.clearCacheOnLogout(r.account)];case 2:return a.sent(),o={apiId:e.ApiId.logout,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,r.correlationId),[4,this.createAuthCodeClient(n,t&&t.authority)];case 3:return i=a.sent(),this.logger.verbose("Auth code client created"),s=i.getLogoutUri(r),this.eventHandler.emitEvent(e.EventType.LOGOUT_SUCCESS,e.InteractionType.Redirect,r),t&&"function"==typeof t.onRedirectNavigate?!1===t.onRedirectNavigate(s)?[3,5]:(this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"),this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),[4,this.navigationClient.navigateExternal(s,o)]):[3,7];case 4:return a.sent(),[2];case 5:this.browserStorage.setInteractionInProgress(!1),this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation"),a.label=6;case 6:return[3,9];case 7:return this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0),[4,this.navigationClient.navigateExternal(s,o)];case 8:return a.sent(),[2];case 9:return[3,11];case 10:throw(c=a.sent())instanceof $&&c.setCorrelationId(this.correlationId),n.cacheFailedRequest(c),this.eventHandler.emitEvent(e.EventType.LOGOUT_FAILURE,e.InteractionType.Redirect,null,c),this.eventHandler.emitEvent(e.EventType.LOGOUT_END,e.InteractionType.Redirect),c;case 11:return this.eventHandler.emitEvent(e.EventType.LOGOUT_END,e.InteractionType.Redirect),[2]}}))}))},o.prototype.getRedirectStartPage=function(e){var t=e||window.location.href;return Ye.getAbsoluteUrl(t,er.getCurrentUri())},o}(rr),pr=function(t){function o(e,r,n,o,i,a,s,c,u,l){var d=t.call(this,e,r,n,o,i,a,s,u,l)||this;return d.unloadWindow=d.unloadWindow.bind(d),d.nativeStorage=c,d}return r(o,t),o.prototype.acquireToken=function(e){try{var t=this.generatePopupName(e.scopes||T,e.authority||this.config.auth.authority),r=e.popupWindowAttributes||{};if(this.config.system.asyncPopups)return this.logger.verbose("asyncPopups set to true, acquiring token"),this.acquireTokenPopupAsync(e,t,r);this.logger.verbose("asyncPopup set to false, opening popup before acquiring token");var n=this.openSizedPopup("about:blank",t,r);return this.acquireTokenPopupAsync(e,t,r,n)}catch(e){return Promise.reject(e)}},o.prototype.logout=function(e){try{this.logger.verbose("logoutPopup called");var t=this.initializeLogoutRequest(e),r=this.generateLogoutPopupName(t),n=e&&e.authority,o=e&&e.mainWindowRedirectUri,i=(null==e?void 0:e.popupWindowAttributes)||{};if(this.config.system.asyncPopups)return this.logger.verbose("asyncPopups set to true"),this.logoutPopupAsync(t,r,i,n,void 0,o);this.logger.verbose("asyncPopup set to false, opening popup");var a=this.openSizedPopup("about:blank",r,i);return this.logoutPopupAsync(t,r,i,n,a,o)}catch(e){return Promise.reject(e)}},o.prototype.acquireTokenPopupAsync=function(t,r,o,s){return i(this,void 0,void 0,(function(){var i,c,u,l,d,h,p,g,f,m,v,y,C,T,_,I,w=this;return a(this,(function(a){switch(a.label){case 0:return this.logger.verbose("acquireTokenPopupAsync called"),i=this.initializeServerTelemetryManager(e.ApiId.acquireTokenPopup),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest,t.correlationId),[4,this.initializeAuthorizationRequest(t,e.InteractionType.Popup)];case 1:c=a.sent(),this.browserStorage.updateCacheEntries(c.state,c.nonce,c.authority,c.loginHint||E.EMPTY_STRING,c.account||null),a.label=2;case 2:return a.trys.push([2,8,,9]),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest,t.correlationId),[4,this.initializeAuthorizationCodeRequest(c)];case 3:return u=a.sent(),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,t.correlationId),[4,this.createAuthCodeClient(i,c.authority,c.azureCloudOptions)];case 4:return l=a.sent(),this.logger.verbose("Auth code client created"),d=dr.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,t.authenticationScheme),h=void 0,d&&(h=this.performanceClient.startMeasurement(e.PerformanceEvents.FetchAccountIdWithNativeBroker,t.correlationId)),[4,l.getAuthCodeUrl(n(n({},c),{nativeBroker:d}))];case 5:return p=a.sent(),g=new nr(l,this.browserStorage,u,this.logger,this.performanceClient),f={popup:s,popupName:r,popupWindowAttributes:o},m=this.initiateAuthRequest(p,f),this.eventHandler.emitEvent(e.EventType.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:m},null),[4,this.monitorPopupForHash(m)];case 6:if(v=a.sent(),y=Ye.getDeserializedHash(v),C=this.validateAndExtractStateFromHash(y,e.InteractionType.Popup,c.correlationId),be.removeThrottle(this.browserStorage,this.config.auth.clientId,u),y.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),h&&h.endMeasurement({success:!0,isNativeBroker:!0}),!this.nativeMessageHandler)throw Mt.createNativeConnectionNotEstablishedError();return T=new lr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,e.ApiId.acquireTokenPopup,this.performanceClient,this.nativeMessageHandler,y.accountId,this.nativeStorage,c.correlationId),_=je.parseRequestState(this.browserCrypto,C).userRequestState,[2,T.acquireToken(n(n({},c),{state:_,prompt:void 0})).finally((function(){w.browserStorage.cleanRequestByState(C)}))]}return[4,g.handleCodeResponseFromHash(v,C,l.authority,this.networkClient)];case 7:return[2,a.sent()];case 8:throw I=a.sent(),s&&s.close(),I instanceof $&&I.setCorrelationId(this.correlationId),i.cacheFailedRequest(I),this.browserStorage.cleanRequestByState(c.state),I;case 9:return[2]}}))}))},o.prototype.logoutPopupAsync=function(t,r,n,o,s,c){return i(this,void 0,void 0,(function(){var i,u,l,d,h,p,g;return a(this,(function(a){switch(a.label){case 0:this.logger.verbose("logoutPopupAsync called"),this.eventHandler.emitEvent(e.EventType.LOGOUT_START,e.InteractionType.Popup,t),i=this.initializeServerTelemetryManager(e.ApiId.logoutPopup),a.label=1;case 1:return a.trys.push([1,5,,6]),[4,this.clearCacheOnLogout(t.account)];case 2:return a.sent(),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,t.correlationId),[4,this.createAuthCodeClient(i,o)];case 3:return u=a.sent(),this.logger.verbose("Auth code client created"),l=u.getLogoutUri(t),this.eventHandler.emitEvent(e.EventType.LOGOUT_SUCCESS,e.InteractionType.Popup,t),d=this.openPopup(l,{popupName:r,popupWindowAttributes:n,popup:s}),this.eventHandler.emitEvent(e.EventType.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:d},null),[4,this.waitForLogoutPopup(d)];case 4:return a.sent(),c?(h={apiId:e.ApiId.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},p=Ye.getAbsoluteUrl(c,er.getCurrentUri()),this.logger.verbose("Redirecting main window to url specified in the request"),this.logger.verbosePii("Redirecting main window to: "+p),this.navigationClient.navigateInternal(p,h)):this.logger.verbose("No main window navigation requested"),[3,6];case 5:throw g=a.sent(),s&&s.close(),g instanceof $&&g.setCorrelationId(this.correlationId),this.browserStorage.setInteractionInProgress(!1),this.eventHandler.emitEvent(e.EventType.LOGOUT_FAILURE,e.InteractionType.Popup,null,g),this.eventHandler.emitEvent(e.EventType.LOGOUT_END,e.InteractionType.Popup),i.cacheFailedRequest(g),g;case 6:return this.eventHandler.emitEvent(e.EventType.LOGOUT_END,e.InteractionType.Popup),[2]}}))}))},o.prototype.initiateAuthRequest=function(e,t){if(ne.isEmpty(e))throw this.logger.error("Navigate url is empty"),Mt.createEmptyNavigationUriError();return this.logger.infoPii("Navigate to: "+e),this.openPopup(e,t)},o.prototype.monitorPopupForHash=function(e){var t=this;return new Promise((function(r,n){var o=t.config.system.windowHashTimeout/t.config.system.pollIntervalMilliseconds,i=0;t.logger.verbose("PopupHandler.monitorPopupForHash - polling started");var a=setInterval((function(){if(e.closed)return t.logger.error("PopupHandler.monitorPopupForHash - window closed"),t.cleanPopup(),clearInterval(a),void n(Mt.createUserCancelledError());var s=E.EMPTY_STRING,c=E.EMPTY_STRING;try{s=e.location.href,c=e.location.hash}catch(e){}ne.isEmpty(s)||"about:blank"===s||(t.logger.verbose("PopupHandler.monitorPopupForHash - popup window is on same origin as caller"),i++,c?(t.logger.verbose("PopupHandler.monitorPopupForHash - found hash in url"),clearInterval(a),t.cleanPopup(e),Ye.hashContainsKnownProperties(c)?(t.logger.verbose("PopupHandler.monitorPopupForHash - hash contains known properties, returning."),r(c)):(t.logger.error("PopupHandler.monitorPopupForHash - found hash in url but it does not contain known properties. Check that your router is not changing the hash prematurely."),t.logger.errorPii("PopupHandler.monitorPopupForHash - hash found: "+c),n(Mt.createHashDoesNotContainKnownPropertiesError()))):i>o&&(t.logger.error("PopupHandler.monitorPopupForHash - unable to find hash in url, timing out"),clearInterval(a),n(Mt.createMonitorPopupTimeoutError())))}),t.config.system.pollIntervalMilliseconds)}))},o.prototype.waitForLogoutPopup=function(e){var t=this;return new Promise((function(r){t.logger.verbose("PopupHandler.waitForLogoutPopup - polling started");var n=setInterval((function(){e.closed&&(t.logger.error("PopupHandler.waitForLogoutPopup - window closed"),t.cleanPopup(),clearInterval(n),r());var o=E.EMPTY_STRING;try{o=e.location.href}catch(e){}ne.isEmpty(o)||"about:blank"===o||(t.logger.verbose("PopupHandler.waitForLogoutPopup - popup window is on same origin as caller, closing."),clearInterval(n),t.cleanPopup(e),r())}),t.config.system.pollIntervalMilliseconds)}))},o.prototype.openPopup=function(e,t){try{var r=void 0;if(t.popup?(r=t.popup,this.logger.verbosePii("Navigating popup window to: "+e),r.location.assign(e)):void 0===t.popup&&(this.logger.verbosePii("Opening popup window to: "+e),r=this.openSizedPopup(e,t.popupName,t.popupWindowAttributes)),!r)throw Mt.createEmptyWindowCreatedError();return r.focus&&r.focus(),this.currentWindow=r,window.addEventListener("beforeunload",this.unloadWindow),r}catch(e){throw this.logger.error("error opening popup "+e.message),this.browserStorage.setInteractionInProgress(!1),Mt.createPopupWindowError(e.toString())}},o.prototype.openSizedPopup=function(e,t,r){var n,o,i,a,s=window.screenLeft?window.screenLeft:window.screenX,c=window.screenTop?window.screenTop:window.screenY,u=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,l=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight,d=null===(n=r.popupSize)||void 0===n?void 0:n.width,h=null===(o=r.popupSize)||void 0===o?void 0:o.height,p=null===(i=r.popupPosition)||void 0===i?void 0:i.top,g=null===(a=r.popupPosition)||void 0===a?void 0:a.left;return(!d||d<0||d>u)&&(this.logger.verbose("Default popup window width used. Window width not configured or invalid."),d=Ot.POPUP_WIDTH),(!h||h<0||h>l)&&(this.logger.verbose("Default popup window height used. Window height not configured or invalid."),h=Ot.POPUP_HEIGHT),(!p||p<0||p>l)&&(this.logger.verbose("Default popup window top position used. Window top not configured or invalid."),p=Math.max(0,l/2-Ot.POPUP_HEIGHT/2+c)),(!g||g<0||g>u)&&(this.logger.verbose("Default popup window left position used. Window left not configured or invalid."),g=Math.max(0,u/2-Ot.POPUP_WIDTH/2+s)),window.open(e,t,"width="+d+", height="+h+", top="+p+", left="+g+", scrollbars=yes")},o.prototype.unloadWindow=function(t){this.browserStorage.cleanRequestByInteractionType(e.InteractionType.Popup),this.currentWindow&&this.currentWindow.close(),t.preventDefault()},o.prototype.cleanPopup=function(e){e&&e.close(),window.removeEventListener("beforeunload",this.unloadWindow),this.browserStorage.setInteractionInProgress(!1)},o.prototype.generatePopupName=function(e,t){return Ot.POPUP_NAME_PREFIX+"."+this.config.auth.clientId+"."+e.join("-")+"."+t+"."+this.correlationId},o.prototype.generateLogoutPopupName=function(e){var t=e.account&&e.account.homeAccountId;return Ot.POPUP_NAME_PREFIX+"."+this.config.auth.clientId+"."+t+"."+this.correlationId},o}(rr),gr=function(){function e(){}return e.prototype.navigateInternal=function(t,r){return e.defaultNavigateWindow(t,r)},e.prototype.navigateExternal=function(t,r){return e.defaultNavigateWindow(t,r)},e.defaultNavigateWindow=function(e,t){return t.noHistory?window.location.replace(e):window.location.assign(e),new Promise((function(e){setTimeout((function(){e(!0)}),t.timeout)}))},e}(),fr=6e3;var mr,vr=function(t){function n(e,r,n,o,i,a){var s=t.call(this,e,r,n,o,a)||this;return s.navigateFrameWait=i.navigateFrameWait,s.pollIntervalMilliseconds=i.pollIntervalMilliseconds,s}return r(n,t),n.prototype.initiateAuthRequest=function(t){return i(this,void 0,void 0,(function(){return a(this,(function(r){switch(r.label){case 0:if(this.performanceClient.addQueueMeasurement(e.PerformanceEvents.SilentHandlerInitiateAuthRequest,this.authCodeRequest.correlationId),ne.isEmpty(t))throw this.logger.info("Navigate url is empty"),Mt.createEmptyNavigationUriError();return this.navigateFrameWait?(this.performanceClient.setPreQueueTime(e.PerformanceEvents.SilentHandlerLoadFrame,this.authCodeRequest.correlationId),[4,this.loadFrame(t)]):[3,2];case 1:return[2,r.sent()];case 2:return[2,this.loadFrameSync(t)]}}))}))},n.prototype.monitorIframeForHash=function(t,r){var n=this;return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.SilentHandlerMonitorIframeForHash,this.authCodeRequest.correlationId),new Promise((function(e,o){r<fr&&n.logger.warning("system.loadFrameTimeout or system.iframeHashTimeout set to lower ("+r+"ms) than the default ("+"6000ms). This may result in timeouts.");var i=window.performance.now()+r,a=setInterval((function(){if(window.performance.now()>i)return n.removeHiddenIframe(t),clearInterval(a),void o(Mt.createMonitorIframeTimeoutError());var r=E.EMPTY_STRING,s=t.contentWindow;try{r=s?s.location.href:E.EMPTY_STRING}catch(e){}if(!ne.isEmpty(r)){var c=s?s.location.hash:E.EMPTY_STRING;return Ye.hashContainsKnownProperties(c)?(n.removeHiddenIframe(t),clearInterval(a),void e(c)):void 0}}),n.pollIntervalMilliseconds)}))},n.prototype.loadFrame=function(t){var r=this;return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.SilentHandlerLoadFrame,this.authCodeRequest.correlationId),new Promise((function(e,n){var o=r.createHiddenIframe();setTimeout((function(){o?(o.src=t,e(o)):n("Unable to load iframe")}),r.navigateFrameWait)}))},n.prototype.loadFrameSync=function(e){var t=this.createHiddenIframe();return t.src=e,t},n.prototype.createHiddenIframe=function(){var e=document.createElement("iframe");return e.className="msalSilentIframe",e.style.visibility="hidden",e.style.position="absolute",e.style.width=e.style.height="0",e.style.border="0",e.setAttribute("sandbox","allow-scripts allow-same-origin allow-forms"),document.getElementsByTagName("body")[0].appendChild(e),e},n.prototype.removeHiddenIframe=function(e){document.body===e.parentNode&&document.body.removeChild(e)},n}(nr),yr=function(t){function o(e,r,n,o,i,a,s,c,u,l,d){var h=t.call(this,e,r,n,o,i,a,c,l,d)||this;return h.apiId=s,h.nativeStorage=u,h}return r(o,t),o.prototype.acquireToken=function(t){return i(this,void 0,void 0,(function(){var r,o,i,s,c;return a(this,(function(a){switch(a.label){case 0:if(this.performanceClient.addQueueMeasurement(e.PerformanceEvents.SilentIframeClientAcquireToken,t.correlationId),this.logger.verbose("acquireTokenByIframe called"),r=this.performanceClient.startMeasurement(e.PerformanceEvents.SilentIframeClientAcquireToken,t.correlationId),ne.isEmpty(t.loginHint)&&ne.isEmpty(t.sid)&&(!t.account||ne.isEmpty(t.account.username))&&this.logger.warning("No user hint provided. The authorization server may need more information to complete this request."),t.prompt&&t.prompt!==w.NONE&&t.prompt!==w.NO_SESSION)throw r.endMeasurement({success:!1}),Mt.createSilentPromptValueError(t.prompt);return this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest,t.correlationId),[4,this.initializeAuthorizationRequest(n(n({},t),{prompt:t.prompt||w.NONE}),e.InteractionType.Silent)];case 1:o=a.sent(),this.browserStorage.updateCacheEntries(o.state,o.nonce,o.authority,o.loginHint||E.EMPTY_STRING,o.account||null),i=this.initializeServerTelemetryManager(this.apiId),a.label=2;case 2:return a.trys.push([2,5,,6]),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientCreateAuthCodeClient,t.correlationId),[4,this.createAuthCodeClient(i,o.authority,o.azureCloudOptions)];case 3:return s=a.sent(),this.logger.verbose("Auth code client created"),this.performanceClient.setPreQueueTime(e.PerformanceEvents.SilentIframeClientTokenHelper,t.correlationId),[4,this.silentTokenHelper(s,o).then((function(e){return r.endMeasurement({success:!0,fromCache:!1,requestId:e.requestId}),e}))];case 4:return[2,a.sent()];case 5:throw(c=a.sent())instanceof $&&c.setCorrelationId(this.correlationId),i.cacheFailedRequest(c),this.browserStorage.cleanRequestByState(o.state),r.endMeasurement({errorCode:c instanceof $&&c.errorCode||void 0,subErrorCode:c instanceof $&&c.subError||void 0,success:!1}),c;case 6:return[2]}}))}))},o.prototype.logout=function(){return Promise.reject(Mt.createSilentLogoutUnsupportedError())},o.prototype.silentTokenHelper=function(t,r){return i(this,void 0,void 0,(function(){var o,i,s,c,u,l,d,h,p,g=this;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.SilentIframeClientTokenHelper,r.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationCodeRequest,r.correlationId),[4,this.initializeAuthorizationCodeRequest(r)];case 1:return o=a.sent(),this.performanceClient.setPreQueueTime(e.PerformanceEvents.GetAuthCodeUrl,r.correlationId),[4,t.getAuthCodeUrl(n(n({},r),{nativeBroker:dr.isNativeAvailable(this.config,this.logger,this.nativeMessageHandler,r.authenticationScheme)}))];case 2:return i=a.sent(),s=new vr(t,this.browserStorage,o,this.logger,this.config.system,this.performanceClient),this.performanceClient.setPreQueueTime(e.PerformanceEvents.SilentHandlerInitiateAuthRequest,r.correlationId),[4,s.initiateAuthRequest(i)];case 3:return c=a.sent(),this.performanceClient.setPreQueueTime(e.PerformanceEvents.SilentHandlerMonitorIframeForHash,r.correlationId),[4,s.monitorIframeForHash(c,this.config.system.iframeHashTimeout)];case 4:if(u=a.sent(),l=Ye.getDeserializedHash(u),d=this.validateAndExtractStateFromHash(l,e.InteractionType.Silent,o.correlationId),l.accountId){if(this.logger.verbose("Account id found in hash, calling WAM for token"),!this.nativeMessageHandler)throw Mt.createNativeConnectionNotEstablishedError();return h=new lr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.apiId,this.performanceClient,this.nativeMessageHandler,l.accountId,this.browserStorage,this.correlationId),p=je.parseRequestState(this.browserCrypto,d).userRequestState,[2,h.acquireToken(n(n({},r),{state:p,prompt:r.prompt||w.NONE})).finally((function(){g.browserStorage.cleanRequestByState(d)}))]}return this.performanceClient.setPreQueueTime(e.PerformanceEvents.HandleCodeResponseFromHash,r.correlationId),[2,s.handleCodeResponseFromHash(u,d,t.authority,this.networkClient)]}}))}))},o}(rr),Cr=function(t){function o(){return null!==t&&t.apply(this,arguments)||this}return r(o,t),o.prototype.acquireToken=function(t){return i(this,void 0,void 0,(function(){var r,o,i,s,c,u=this;return a(this,(function(a){switch(a.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.SilentRefreshClientAcquireToken,t.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.InitializeBaseRequest,t.correlationId),o=[n({},t)],[4,this.initializeBaseRequest(t,t.account)];case 1:return r=n.apply(void 0,o.concat([a.sent()])),i=this.performanceClient.startMeasurement(e.PerformanceEvents.SilentRefreshClientAcquireToken,r.correlationId),s=this.initializeServerTelemetryManager(e.ApiId.acquireTokenSilent_silentFlow),[4,this.createRefreshTokenClient(s,r.authority,r.azureCloudOptions)];case 2:return c=a.sent(),this.logger.verbose("Refresh token client created"),this.performanceClient.setPreQueueTime(e.PerformanceEvents.RefreshTokenClientAcquireTokenByRefreshToken,t.correlationId),[2,c.acquireTokenByRefreshToken(r).then((function(e){return i.endMeasurement({success:!0,fromCache:e.fromCache,requestId:e.requestId}),e})).catch((function(e){throw e instanceof $&&e.setCorrelationId(u.correlationId),s.cacheFailedRequest(e),i.endMeasurement({errorCode:e.errorCode,subErrorCode:e.subError,success:!1}),e}))]}}))}))},o.prototype.logout=function(){return Promise.reject(Mt.createSilentLogoutUnsupportedError())},o.prototype.createRefreshTokenClient=function(t,r,n){return i(this,void 0,void 0,(function(){var o;return a(this,(function(i){switch(i.label){case 0:return this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientGetClientConfiguration,this.correlationId),[4,this.getClientConfiguration(t,r,n)];case 1:return o=i.sent(),[2,new tt(o,this.performanceClient)]}}))}))},o}(rr),Er=function(){function t(e,t){this.eventCallbacks=new Map,this.logger=e,this.browserCrypto=t,this.listeningToStorageEvents=!1,this.handleAccountCacheChange=this.handleAccountCacheChange.bind(this)}return t.prototype.addEventCallback=function(e){if("undefined"!=typeof window){var t=this.browserCrypto.createNewGuid();return this.eventCallbacks.set(t,e),this.logger.verbose("Event callback registered with id: "+t),t}return null},t.prototype.removeEventCallback=function(e){this.eventCallbacks.delete(e),this.logger.verbose("Event callback "+e+" removed.")},t.prototype.enableAccountStorageEvents=function(){"undefined"!=typeof window&&(this.listeningToStorageEvents?this.logger.verbose("Account storage listener already registered."):(this.logger.verbose("Adding account storage listener."),this.listeningToStorageEvents=!0,window.addEventListener("storage",this.handleAccountCacheChange)))},t.prototype.disableAccountStorageEvents=function(){"undefined"!=typeof window&&(this.listeningToStorageEvents?(this.logger.verbose("Removing account storage listener."),window.removeEventListener("storage",this.handleAccountCacheChange),this.listeningToStorageEvents=!1):this.logger.verbose("No account storage listener registered."))},t.prototype.emitEvent=function(e,t,r,n){var o=this;if("undefined"!=typeof window){var i={eventType:e,interactionType:t||null,payload:r||null,error:n||null,timestamp:Date.now()};this.logger.info("Emitting event: "+e),this.eventCallbacks.forEach((function(t,r){o.logger.verbose("Emitting event to callback "+r+": "+e),t.apply(null,[i])}))}},t.prototype.handleAccountCacheChange=function(t){try{var r=t.newValue||t.oldValue;if(!r)return;var n=JSON.parse(r);if("object"!=typeof n||!fe.isAccountEntity(n))return;var o=ve.toObject(new fe,n).getAccountInfo();!t.oldValue&&t.newValue?(this.logger.info("Account was added to cache in a different window"),this.emitEvent(e.EventType.ACCOUNT_ADDED,void 0,o)):!t.newValue&&t.oldValue&&(this.logger.info("Account was removed from cache in a different window"),this.emitEvent(e.EventType.ACCOUNT_REMOVED,void 0,o))}catch(t){return}},t}(),Tr=function(){function e(){}return e.decimalToHex=function(e){for(var t=e.toString(16);t.length<2;)t="0"+t;return t},e}(),_r=function(){function e(e){this.cryptoObj=e}return e.prototype.generateGuid=function(){try{var e=new Uint8Array(16);return this.cryptoObj.getRandomValues(e),e[6]|=64,e[6]&=79,e[8]|=128,e[8]&=191,Tr.decimalToHex(e[0])+Tr.decimalToHex(e[1])+Tr.decimalToHex(e[2])+Tr.decimalToHex(e[3])+"-"+Tr.decimalToHex(e[4])+Tr.decimalToHex(e[5])+"-"+Tr.decimalToHex(e[6])+Tr.decimalToHex(e[7])+"-"+Tr.decimalToHex(e[8])+Tr.decimalToHex(e[9])+"-"+Tr.decimalToHex(e[10])+Tr.decimalToHex(e[11])+Tr.decimalToHex(e[12])+Tr.decimalToHex(e[13])+Tr.decimalToHex(e[14])+Tr.decimalToHex(e[15])}catch(e){for(var t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",r="0123456789abcdef",n=0,o=E.EMPTY_STRING,i=0;i<36;i++)"-"!==t[i]&&"4"!==t[i]&&(n=16*Math.random()|0),"x"===t[i]?o+=r[n]:"y"===t[i]?(n&=3,o+=r[n|=8]):o+=t[i];return o}},e.prototype.isGuid=function(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)},e}(),Ir=function(){function e(){}return e.stringToUtf8Arr=function(e){for(var t,r=0,n=e.length,o=0;o<n;o++)r+=(t=e.charCodeAt(o))<128?1:t<2048?2:t<65536?3:t<2097152?4:t<67108864?5:6;for(var i=new Uint8Array(r),a=0,s=0;a<r;s++)(t=e.charCodeAt(s))<128?i[a++]=t:t<2048?(i[a++]=192+(t>>>6),i[a++]=128+(63&t)):t<65536?(i[a++]=224+(t>>>12),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t)):t<2097152?(i[a++]=240+(t>>>18),i[a++]=128+(t>>>12&63),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t)):t<67108864?(i[a++]=248+(t>>>24),i[a++]=128+(t>>>18&63),i[a++]=128+(t>>>12&63),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t)):(i[a++]=252+(t>>>30),i[a++]=128+(t>>>24&63),i[a++]=128+(t>>>18&63),i[a++]=128+(t>>>12&63),i[a++]=128+(t>>>6&63),i[a++]=128+(63&t));return i},e.stringToArrayBuffer=function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return t},e.utf8ArrToString=function(e){for(var t=E.EMPTY_STRING,r=void 0,n=e.length,o=0;o<n;o++)r=e[o],t+=String.fromCharCode(r>251&&r<254&&o+5<n?1073741824*(r-252)+(e[++o]-128<<24)+(e[++o]-128<<18)+(e[++o]-128<<12)+(e[++o]-128<<6)+e[++o]-128:r>247&&r<252&&o+4<n?(r-248<<24)+(e[++o]-128<<18)+(e[++o]-128<<12)+(e[++o]-128<<6)+e[++o]-128:r>239&&r<248&&o+3<n?(r-240<<18)+(e[++o]-128<<12)+(e[++o]-128<<6)+e[++o]-128:r>223&&r<240&&o+2<n?(r-224<<12)+(e[++o]-128<<6)+e[++o]-128:r>191&&r<224&&o+1<n?(r-192<<6)+e[++o]-128:r);return t},e.getSortedObjectString=function(e){return JSON.stringify(e,Object.keys(e).sort())},e}(),wr=function(){function e(){}return e.prototype.urlEncode=function(e){return encodeURIComponent(this.encode(e).replace(/=/g,E.EMPTY_STRING).replace(/\+/g,"-").replace(/\//g,"_"))},e.prototype.urlEncodeArr=function(e){return this.base64EncArr(e).replace(/=/g,E.EMPTY_STRING).replace(/\+/g,"-").replace(/\//g,"_")},e.prototype.encode=function(e){var t=Ir.stringToUtf8Arr(e);return this.base64EncArr(t)},e.prototype.base64EncArr=function(e){for(var t=(3-e.length%3)%3,r=E.EMPTY_STRING,n=void 0,o=e.length,i=0,a=0;a<o;a++)n=a%3,i|=e[a]<<(16>>>n&24),2!==n&&e.length-a!=1||(r+=String.fromCharCode(this.uint6ToB64(i>>>18&63),this.uint6ToB64(i>>>12&63),this.uint6ToB64(i>>>6&63),this.uint6ToB64(63&i)),i=0);return 0===t?r:r.substring(0,r.length-t)+(1===t?"=":"==")},e.prototype.uint6ToB64=function(e){return e<26?e+65:e<52?e+71:e<62?e-4:62===e?43:63===e?47:65},e}(),Sr=function(){function e(){}return e.prototype.decode=function(e){var t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw new Error("Invalid base64 string")}var r=this.base64DecToArr(t);return Ir.utf8ArrToString(r)},e.prototype.base64DecToArr=function(e,t){for(var r=e.replace(/[^A-Za-z0-9\+\/]/g,E.EMPTY_STRING),n=r.length,o=t?Math.ceil((3*n+1>>>2)/t)*t:3*n+1>>>2,i=new Uint8Array(o),a=void 0,s=void 0,c=0,u=0,l=0;l<n;l++)if(s=3&l,c|=this.b64ToUint6(r.charCodeAt(l))<<18-6*s,3===s||n-l==1){for(a=0;a<3&&u<o;a++,u++)i[u]=c>>>(16>>>a&24)&255;c=0}return i},e.prototype.b64ToUint6=function(e){return e>64&&e<91?e-65:e>96&&e<123?e-71:e>47&&e<58?e+4:43===e?62:47===e?63:0},e}(),Ar=function(){function e(e){this.base64Encode=new wr,this.cryptoObj=e}return e.prototype.generateCodes=function(){return i(this,void 0,void 0,(function(){var e,t;return a(this,(function(r){switch(r.label){case 0:return e=this.generateCodeVerifier(),[4,this.generateCodeChallengeFromVerifier(e)];case 1:return t=r.sent(),[2,{verifier:e,challenge:t}]}}))}))},e.prototype.generateCodeVerifier=function(){try{var e=new Uint8Array(32);return this.cryptoObj.getRandomValues(e),this.base64Encode.urlEncodeArr(e)}catch(e){throw Mt.createPkceNotGeneratedError(e)}},e.prototype.generateCodeChallengeFromVerifier=function(e){return i(this,void 0,void 0,(function(){var t,r;return a(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.cryptoObj.sha256Digest(e)];case 1:return t=n.sent(),[2,this.base64Encode.urlEncodeArr(new Uint8Array(t))];case 2:throw r=n.sent(),Mt.createPkceNotGeneratedError(r);case 3:return[2]}}))}))},e}(),kr=function(){function e(){}return e.prototype.getRandomValues=function(e){return window.crypto.getRandomValues(e)},e.prototype.generateKey=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,window.crypto.subtle.generateKey(e,t,r)]}))}))},e.prototype.exportKey=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){return[2,window.crypto.subtle.exportKey(Kt,e)]}))}))},e.prototype.importKey=function(e,t,r,n){return i(this,void 0,void 0,(function(){return a(this,(function(o){return[2,window.crypto.subtle.importKey(Kt,e,t,r,n)]}))}))},e.prototype.sign=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,window.crypto.subtle.sign(e,t,r)]}))}))},e.prototype.digest=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,window.crypto.subtle.digest(e,t)]}))}))},e}(),Rr=function(){function e(){}return e.prototype.initPrng=function(e){return window.msrCrypto.initPrng(c(e))},e.prototype.getRandomValues=function(e){return window.msrCrypto.getRandomValues(e)},e.prototype.generateKey=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,window.msrCrypto.subtle.generateKey(e,t,r)]}))}))},e.prototype.exportKey=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){return[2,window.msrCrypto.subtle.exportKey(Kt,e)]}))}))},e.prototype.importKey=function(e,t,r,n){return i(this,void 0,void 0,(function(){return a(this,(function(o){return[2,window.msrCrypto.subtle.importKey(Kt,e,t,r,n)]}))}))},e.prototype.sign=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,window.msrCrypto.subtle.sign(e,t,r)]}))}))},e.prototype.digest=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,window.msrCrypto.subtle.digest(e,t)]}))}))},e}(),br=function(){function e(){}return e.prototype.getRandomValues=function(e){return window.msCrypto.getRandomValues(e)},e.prototype.generateKey=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,new Promise((function(n,o){var i=window.msCrypto.subtle.generateKey(e,t,r);i.addEventListener("complete",(function(e){n(e.target.result)})),i.addEventListener("error",(function(e){o(e)}))}))]}))}))},e.prototype.exportKey=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){return[2,new Promise((function(t,r){var n=window.msCrypto.subtle.exportKey(Kt,e);n.addEventListener("complete",(function(e){var n=e.target.result,o=Ir.utf8ArrToString(new Uint8Array(n)).replace(/\r/g,E.EMPTY_STRING).replace(/\n/g,E.EMPTY_STRING).replace(/\t/g,E.EMPTY_STRING).split(" ").join(E.EMPTY_STRING).replace("\0",E.EMPTY_STRING);try{t(JSON.parse(o))}catch(e){r(e)}})),n.addEventListener("error",(function(e){r(e)}))}))]}))}))},e.prototype.importKey=function(e,t,r,n){return i(this,void 0,void 0,(function(){var o,i;return a(this,(function(a){return o=Ir.getSortedObjectString(e),i=Ir.stringToArrayBuffer(o),[2,new Promise((function(e,o){var a=window.msCrypto.subtle.importKey(Kt,i,t,r,n);a.addEventListener("complete",(function(t){e(t.target.result)})),a.addEventListener("error",(function(e){o(e)}))}))]}))}))},e.prototype.sign=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,new Promise((function(n,o){var i=window.msCrypto.subtle.sign(e,t,r);i.addEventListener("complete",(function(e){n(e.target.result)})),i.addEventListener("error",(function(e){o(e)}))}))]}))}))},e.prototype.digest=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,new Promise((function(r,n){var o=window.msCrypto.subtle.digest(e,t.buffer);o.addEventListener("complete",(function(e){r(e.target.result)})),o.addEventListener("error",(function(e){n(e)}))}))]}))}))},e}(),Pr="SHA-256",Nr=new Uint8Array([1,0,1]),Mr=function(){function e(e,t){var r,n;if(this.logger=e,this.cryptoOptions=t,this.hasBrowserCrypto())this.logger.verbose("BrowserCrypto: modern crypto interface available"),this.subtleCrypto=new kr;else if(this.hasIECrypto())this.logger.verbose("BrowserCrypto: MS crypto interface available"),this.subtleCrypto=new br;else{if(!this.hasMsrCrypto()||!(null===(r=this.cryptoOptions)||void 0===r?void 0:r.useMsrCrypto))throw this.hasMsrCrypto()&&this.logger.info("BrowserCrypto: MSR Crypto interface available but system.cryptoOptions.useMsrCrypto not enabled"),this.logger.error("BrowserCrypto: No crypto interfaces available."),Mt.createCryptoNotAvailableError("Browser crypto, msCrypto, or msrCrypto interfaces not available.");this.logger.verbose("BrowserCrypto: MSR crypto interface available"),this.subtleCrypto=new Rr}if(this.subtleCrypto.initPrng){if(this.logger.verbose("BrowserCrypto: Interface requires entropy"),!(null===(n=this.cryptoOptions)||void 0===n?void 0:n.entropy))throw this.logger.error("BrowserCrypto: Interface requires entropy but none provided."),Qt.createEntropyNotProvided();this.logger.verbose("BrowserCrypto: Entropy provided"),this.subtleCrypto.initPrng(this.cryptoOptions.entropy)}this.keygenAlgorithmOptions={name:"RSASSA-PKCS1-v1_5",hash:Pr,modulusLength:2048,publicExponent:Nr}}return e.prototype.hasIECrypto=function(){return"msCrypto"in window},e.prototype.hasBrowserCrypto=function(){return"crypto"in window},e.prototype.hasMsrCrypto=function(){return"msrCrypto"in window},e.prototype.sha256Digest=function(e){return i(this,void 0,void 0,(function(){var t;return a(this,(function(r){return t=Ir.stringToUtf8Arr(e),[2,this.subtleCrypto.digest({name:Pr},t)]}))}))},e.prototype.getRandomValues=function(e){return this.subtleCrypto.getRandomValues(e)},e.prototype.generateKeyPair=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,this.subtleCrypto.generateKey(this.keygenAlgorithmOptions,e,t)]}))}))},e.prototype.exportJwk=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){return[2,this.subtleCrypto.exportKey(e)]}))}))},e.prototype.importJwk=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,this.subtleCrypto.importKey(e,this.keygenAlgorithmOptions,t,r)]}))}))},e.prototype.sign=function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(r){return[2,this.subtleCrypto.sign(this.keygenAlgorithmOptions,e,t)]}))}))},e}(),Or=function(){function e(){this.dbName=Bt,this.version=1,this.tableName="msal.db.keys",this.dbOpen=!1}return e.prototype.open=function(){return i(this,void 0,void 0,(function(){var e=this;return a(this,(function(t){return[2,new Promise((function(t,r){var n=window.indexedDB.open(e.dbName,e.version);n.addEventListener("upgradeneeded",(function(t){t.target.result.createObjectStore(e.tableName)})),n.addEventListener("success",(function(r){var n=r;e.db=n.target.result,e.dbOpen=!0,t()})),n.addEventListener("error",(function(){return r(Mt.createDatabaseUnavailableError())}))}))]}))}))},e.prototype.closeConnection=function(){var e=this.db;e&&this.dbOpen&&(e.close(),this.dbOpen=!1)},e.prototype.validateDbIsOpen=function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return this.dbOpen?[3,2]:[4,this.open()];case 1:return[2,e.sent()];case 2:return[2]}}))}))},e.prototype.getItem=function(e){return i(this,void 0,void 0,(function(){var t=this;return a(this,(function(r){switch(r.label){case 0:return[4,this.validateDbIsOpen()];case 1:return r.sent(),[2,new Promise((function(r,n){if(!t.db)return n(Mt.createDatabaseNotOpenError());var o=t.db.transaction([t.tableName],"readonly").objectStore(t.tableName).get(e);o.addEventListener("success",(function(e){var n=e;t.closeConnection(),r(n.target.result)})),o.addEventListener("error",(function(e){t.closeConnection(),n(e)}))}))]}}))}))},e.prototype.setItem=function(e,t){return i(this,void 0,void 0,(function(){var r=this;return a(this,(function(n){switch(n.label){case 0:return[4,this.validateDbIsOpen()];case 1:return n.sent(),[2,new Promise((function(n,o){if(!r.db)return o(Mt.createDatabaseNotOpenError());var i=r.db.transaction([r.tableName],"readwrite").objectStore(r.tableName).put(t,e);i.addEventListener("success",(function(){r.closeConnection(),n()})),i.addEventListener("error",(function(e){r.closeConnection(),o(e)}))}))]}}))}))},e.prototype.removeItem=function(e){return i(this,void 0,void 0,(function(){var t=this;return a(this,(function(r){switch(r.label){case 0:return[4,this.validateDbIsOpen()];case 1:return r.sent(),[2,new Promise((function(r,n){if(!t.db)return n(Mt.createDatabaseNotOpenError());var o=t.db.transaction([t.tableName],"readwrite").objectStore(t.tableName).delete(e);o.addEventListener("success",(function(){t.closeConnection(),r()})),o.addEventListener("error",(function(e){t.closeConnection(),n(e)}))}))]}}))}))},e.prototype.getKeys=function(){return i(this,void 0,void 0,(function(){var e=this;return a(this,(function(t){switch(t.label){case 0:return[4,this.validateDbIsOpen()];case 1:return t.sent(),[2,new Promise((function(t,r){if(!e.db)return r(Mt.createDatabaseNotOpenError());var n=e.db.transaction([e.tableName],"readonly").objectStore(e.tableName).getAllKeys();n.addEventListener("success",(function(r){var n=r;e.closeConnection(),t(n.target.result)})),n.addEventListener("error",(function(t){e.closeConnection(),r(t)}))}))]}}))}))},e.prototype.containsKey=function(e){return i(this,void 0,void 0,(function(){var t=this;return a(this,(function(r){switch(r.label){case 0:return[4,this.validateDbIsOpen()];case 1:return r.sent(),[2,new Promise((function(r,n){if(!t.db)return n(Mt.createDatabaseNotOpenError());var o=t.db.transaction([t.tableName],"readonly").objectStore(t.tableName).count(e);o.addEventListener("success",(function(e){var n=e;t.closeConnection(),r(1===n.target.result)})),o.addEventListener("error",(function(e){t.closeConnection(),n(e)}))}))]}}))}))},e.prototype.deleteDatabase=function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){return this.db&&this.dbOpen&&this.closeConnection(),[2,new Promise((function(e,t){var r=window.indexedDB.deleteDatabase(Bt);r.addEventListener("success",(function(){return e(!0)})),r.addEventListener("blocked",(function(){return e(!0)})),r.addEventListener("error",(function(){return t(!1)}))}))]}))}))},e}(),qr=function(){function e(e,t){this.inMemoryCache=new Yt,this.indexedDBCache=new Or,this.logger=e,this.storeName=t}return e.prototype.handleDatabaseAccessError=function(e){if(!(e instanceof Mt&&e.errorCode===Nt.databaseUnavailable.code))throw e;this.logger.error("Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.")},e.prototype.getItem=function(e){return i(this,void 0,void 0,(function(){var t,r;return a(this,(function(n){switch(n.label){case 0:if(t=this.inMemoryCache.getItem(e))return[3,4];n.label=1;case 1:return n.trys.push([1,3,,4]),this.logger.verbose("Queried item not found in in-memory cache, now querying persistent storage."),[4,this.indexedDBCache.getItem(e)];case 2:return[2,n.sent()];case 3:return r=n.sent(),this.handleDatabaseAccessError(r),[3,4];case 4:return[2,t]}}))}))},e.prototype.setItem=function(e,t){return i(this,void 0,void 0,(function(){var r;return a(this,(function(n){switch(n.label){case 0:this.inMemoryCache.setItem(e,t),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.indexedDBCache.setItem(e,t)];case 2:return n.sent(),[3,4];case 3:return r=n.sent(),this.handleDatabaseAccessError(r),[3,4];case 4:return[2]}}))}))},e.prototype.removeItem=function(e){return i(this,void 0,void 0,(function(){var t;return a(this,(function(r){switch(r.label){case 0:this.inMemoryCache.removeItem(e),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.indexedDBCache.removeItem(e)];case 2:return r.sent(),[3,4];case 3:return t=r.sent(),this.handleDatabaseAccessError(t),[3,4];case 4:return[2]}}))}))},e.prototype.getKeys=function(){return i(this,void 0,void 0,(function(){var e,t;return a(this,(function(r){switch(r.label){case 0:if(0!==(e=this.inMemoryCache.getKeys()).length)return[3,4];r.label=1;case 1:return r.trys.push([1,3,,4]),this.logger.verbose("In-memory cache is empty, now querying persistent storage."),[4,this.indexedDBCache.getKeys()];case 2:return[2,r.sent()];case 3:return t=r.sent(),this.handleDatabaseAccessError(t),[3,4];case 4:return[2,e]}}))}))},e.prototype.containsKey=function(e){return i(this,void 0,void 0,(function(){var t,r;return a(this,(function(n){switch(n.label){case 0:if(t=this.inMemoryCache.containsKey(e))return[3,4];n.label=1;case 1:return n.trys.push([1,3,,4]),this.logger.verbose("Key not found in in-memory cache, now querying persistent storage."),[4,this.indexedDBCache.containsKey(e)];case 2:return[2,n.sent()];case 3:return r=n.sent(),this.handleDatabaseAccessError(r),[3,4];case 4:return[2,t]}}))}))},e.prototype.clearInMemory=function(){this.logger.verbose("Deleting in-memory keystore "+this.storeName),this.inMemoryCache.clear(),this.logger.verbose("In-memory keystore "+this.storeName+" deleted")},e.prototype.clearPersistent=function(){return i(this,void 0,void 0,(function(){var e,t;return a(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),this.logger.verbose("Deleting persistent keystore"),[4,this.indexedDBCache.deleteDatabase()];case 1:return(e=r.sent())&&this.logger.verbose("Persistent keystore deleted"),[2,e];case 2:return t=r.sent(),this.handleDatabaseAccessError(t),[2,!1];case 3:return[2]}}))}))},e}();!function(e){e.asymmetricKeys="asymmetricKeys",e.symmetricKeys="symmetricKeys"}(mr||(mr={}));var Ur=function(){function e(e){this.logger=e,this.asymmetricKeys=new qr(this.logger,mr.asymmetricKeys),this.symmetricKeys=new qr(this.logger,mr.symmetricKeys)}return e.prototype.clear=function(){return i(this,void 0,void 0,(function(){var e;return a(this,(function(t){switch(t.label){case 0:this.asymmetricKeys.clearInMemory(),this.symmetricKeys.clearInMemory(),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.asymmetricKeys.clearPersistent()];case 2:return t.sent(),[2,!0];case 3:return(e=t.sent())instanceof Error?this.logger.error("Clearing keystore failed with error: "+e.message):this.logger.error("Clearing keystore failed with unknown error"),[2,!1];case 4:return[2]}}))}))},e}(),Hr=function(){function t(e,t,r){this.logger=e,this.browserCrypto=new Mr(this.logger,r),this.b64Encode=new wr,this.b64Decode=new Sr,this.guidGenerator=new _r(this.browserCrypto),this.pkceGenerator=new Ar(this.browserCrypto),this.cache=new Ur(this.logger),this.performanceClient=t}return t.prototype.createNewGuid=function(){return this.guidGenerator.generateGuid()},t.prototype.base64Encode=function(e){return this.b64Encode.encode(e)},t.prototype.base64Decode=function(e){return this.b64Decode.decode(e)},t.prototype.generatePkceCodes=function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){return[2,this.pkceGenerator.generateCodes()]}))}))},t.prototype.getPublicKeyThumbprint=function(r){var n;return i(this,void 0,void 0,(function(){var o,i,s,c,u,l,d,h;return a(this,(function(a){switch(a.label){case 0:return o=null===(n=this.performanceClient)||void 0===n?void 0:n.startMeasurement(e.PerformanceEvents.CryptoOptsGetPublicKeyThumbprint,r.correlationId),[4,this.browserCrypto.generateKeyPair(t.EXTRACTABLE,t.POP_KEY_USAGES)];case 1:return i=a.sent(),[4,this.browserCrypto.exportJwk(i.publicKey)];case 2:return s=a.sent(),c={e:s.e,kty:s.kty,n:s.n},u=Ir.getSortedObjectString(c),[4,this.hashString(u)];case 3:return l=a.sent(),[4,this.browserCrypto.exportJwk(i.privateKey)];case 4:return d=a.sent(),[4,this.browserCrypto.importJwk(d,!1,["sign"])];case 5:return h=a.sent(),[4,this.cache.asymmetricKeys.setItem(l,{privateKey:h,publicKey:i.publicKey,requestMethod:r.resourceRequestMethod,requestUri:r.resourceRequestUri})];case 6:return a.sent(),o&&o.endMeasurement({success:!0}),[2,l]}}))}))},t.prototype.removeTokenBindingKey=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,this.cache.asymmetricKeys.removeItem(e)];case 1:return t.sent(),[4,this.cache.asymmetricKeys.containsKey(e)];case 2:return[2,!t.sent()]}}))}))},t.prototype.clearKeystore=function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,this.cache.clear()];case 1:return[2,e.sent()]}}))}))},t.prototype.signJwt=function(t,r,n){var o;return i(this,void 0,void 0,(function(){var i,s,c,u,l,d,h,p,g,f,m,v,y;return a(this,(function(a){switch(a.label){case 0:return i=null===(o=this.performanceClient)||void 0===o?void 0:o.startMeasurement(e.PerformanceEvents.CryptoOptsSignJwt,n),[4,this.cache.asymmetricKeys.getItem(r)];case 1:if(!(s=a.sent()))throw Mt.createSigningKeyNotFoundInStorageError(r);return[4,this.browserCrypto.exportJwk(s.publicKey)];case 2:return c=a.sent(),u=Ir.getSortedObjectString(c),l=this.b64Encode.urlEncode(JSON.stringify({kid:r})),d=St.getShrHeaderString({kid:l,alg:c.alg}),h=this.b64Encode.urlEncode(d),t.cnf={jwk:JSON.parse(u)},p=this.b64Encode.urlEncode(JSON.stringify(t)),g=h+"."+p,f=Ir.stringToArrayBuffer(g),[4,this.browserCrypto.sign(s.privateKey,f)];case 3:return m=a.sent(),v=this.b64Encode.urlEncodeArr(new Uint8Array(m)),y=g+"."+v,i&&i.endMeasurement({success:!0}),[2,y]}}))}))},t.prototype.hashString=function(e){return i(this,void 0,void 0,(function(){var t,r;return a(this,(function(n){switch(n.label){case 0:return[4,this.browserCrypto.sha256Digest(e)];case 1:return t=n.sent(),r=new Uint8Array(t),[2,this.b64Encode.urlEncodeArr(r)]}}))}))},t.POP_KEY_USAGES=["sign","verify"],t.EXTRACTABLE=!0,t}(),Lr=function(){function e(t,r){this.correlationId=r,this.measureName=e.makeMeasureName(t,r),this.startMark=e.makeStartMark(t,r),this.endMark=e.makeEndMark(t,r)}return e.makeMeasureName=function(e,t){return"msal.measure."+e+"."+t},e.makeStartMark=function(e,t){return"msal.start."+e+"."+t},e.makeEndMark=function(e,t){return"msal.end."+e+"."+t},e.supportsBrowserPerformance=function(){return"undefined"!=typeof window&&void 0!==window.performance&&"function"==typeof window.performance.mark&&"function"==typeof window.performance.measure&&"function"==typeof window.performance.clearMarks&&"function"==typeof window.performance.clearMeasures&&"function"==typeof window.performance.getEntriesByName},e.flushMeasurements=function(t,r){if(e.supportsBrowserPerformance())try{r.forEach((function(r){var n=e.makeMeasureName(r.name,t);window.performance.getEntriesByName(n,"measure").length>0&&(window.performance.clearMeasures(n),window.performance.clearMarks(e.makeStartMark(n,t)),window.performance.clearMarks(e.makeEndMark(n,t)))}))}catch(e){}},e.prototype.startMeasurement=function(){if(e.supportsBrowserPerformance())try{window.performance.mark(this.startMark)}catch(e){}},e.prototype.endMeasurement=function(){if(e.supportsBrowserPerformance())try{window.performance.mark(this.endMark),window.performance.measure(this.measureName,this.startMark,this.endMark)}catch(e){}},e.prototype.flushMeasurement=function(){if(e.supportsBrowserPerformance())try{var t=window.performance.getEntriesByName(this.measureName,"measure");if(t.length>0){var r=t[0].duration;return window.performance.clearMeasures(this.measureName),window.performance.clearMarks(this.startMark),window.performance.clearMarks(this.endMark),r}}catch(e){}return null},e}(),Dr=function(e){function t(t,r,n,o,i,a,s){var c=e.call(this,t,r,n,o,i,a)||this;return c.browserCrypto=new Mr(c.logger,s),c.guidGenerator=new _r(c.browserCrypto),c}return r(t,e),t.prototype.startPerformanceMeasuremeant=function(e,t){return new Lr(e,t)},t.prototype.generateId=function(){return this.guidGenerator.generateGuid()},t.prototype.getPageVisibility=function(){var e;return(null===(e=document.visibilityState)||void 0===e?void 0:e.toString())||null},t.prototype.deleteIncompleteSubMeasurements=function(e){var t=this.eventsByCorrelationId.get(e.event.correlationId),r=t&&t.eventId===e.event.eventId,o=[];r&&(null==t?void 0:t.incompleteSubMeasurements)&&t.incompleteSubMeasurements.forEach((function(e){o.push(n({},e))})),o.length>0&&Lr.flushMeasurements(e.event.correlationId,o)},t.prototype.supportsBrowserPerformanceNow=function(){return"undefined"!=typeof window&&void 0!==window.performance&&"function"==typeof window.performance.now},t.prototype.startMeasurement=function(t,r){var o=this,i=this.getPageVisibility(),a=e.prototype.startMeasurement.call(this,t,r);return n(n({},a),{endMeasurement:function(e){var t=a.endMeasurement(n({startPageVisibility:i,endPageVisibility:o.getPageVisibility()},e));return o.deleteIncompleteSubMeasurements(a),t},discardMeasurement:function(){a.discardMeasurement(),o.deleteIncompleteSubMeasurements(a),a.measurement.flushMeasurement()}})},t.prototype.setPreQueueTime=function(e,t){if(this.supportsBrowserPerformanceNow())if(t){var r=this.preQueueTimeByCorrelationId.get(t);r&&(this.logger.trace("BrowserPerformanceClient: Incomplete pre-queue "+r.name+" found",t),this.addQueueMeasurement(r.name,t,void 0,!0)),this.preQueueTimeByCorrelationId.set(t,{name:e,time:window.performance.now()})}else this.logger.trace("BrowserPerformanceClient: correlationId for "+e+" not provided, unable to set telemetry queue time");else this.logger.trace("BrowserPerformanceClient: window performance API not available, unable to set telemetry queue time for "+e)},t.prototype.addQueueMeasurement=function(t,r,n,o){if(this.supportsBrowserPerformanceNow())if(r){var i=e.prototype.getPreQueueTime.call(this,t,r);if(i){var a=window.performance.now(),s=n||e.prototype.calculateQueuedTime.call(this,i,a);return e.prototype.addQueueMeasurement.call(this,t,r,s,o)}}else this.logger.trace("BrowserPerformanceClient: correlationId for "+t+" not provided, unable to add queue measurement");else this.logger.trace("BrowserPerformanceClient: window performance API not available, unable to add queue measurement for "+t)},t}(Rt),Kr=Object.freeze({__proto__:null,BrowserCacheManager:Vt,StandardInteractionClient:rr,RedirectClient:hr,PopupClient:pr,SilentIframeClient:yr,SilentCacheClient:ur,SilentRefreshClient:Cr,NativeInteractionClient:lr,RedirectHandler:or,EventHandler:Er,NativeMessageHandler:dr,BrowserConstants:Ot,get TemporaryCacheKeys(){return lt},CryptoOps:Hr,NativeAuthError:cr,BrowserPerformanceClient:Dr,BrowserPerformanceMeasurement:Lr}),Fr=function(){function e(e,t,r,n){this.isBrowserEnvironment="undefined"!=typeof window,this.config=e,this.storage=t,this.logger=r,this.cryptoObj=n}return e.prototype.loadExternalTokens=function(e,t,r){if(this.logger.info("TokenCache - loadExternalTokens called"),!t.id_token)throw Mt.createUnableToLoadTokenError("Please ensure server response includes id token.");var n,o,i,a=new me(t.id_token,this.cryptoObj);if(e.account)i=fe.createFromAccountInfo(e.account),n=new Qe(i,this.loadIdToken(a,i.homeAccountId,e.account.environment,e.account.tenantId),this.loadAccessToken(e,t,i.homeAccountId,e.account.environment,e.account.tenantId,r),this.loadRefreshToken(e,t,i.homeAccountId,e.account.environment));else{if(!e.authority)throw Mt.createUnableToLoadTokenError("Please provide a request with an account or a request with authority.");var s=vt.generateAuthority(e.authority,e.azureCloudOptions),c={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache};if(o=new vt(s,this.config.system.networkClient,this.storage,c,this.logger),r.clientInfo)this.logger.trace("TokenCache - homeAccountId from options"),i=this.loadAccount(a,o,r.clientInfo),n=new Qe(i,this.loadIdToken(a,i.homeAccountId,o.hostnameAndPort,o.tenant),this.loadAccessToken(e,t,i.homeAccountId,o.hostnameAndPort,o.tenant,r),this.loadRefreshToken(e,t,i.homeAccountId,o.hostnameAndPort));else{if(!t.client_info)throw Mt.createUnableToLoadTokenError("Please provide clientInfo in the response or options.");this.logger.trace("TokenCache - homeAccountId from response"),i=this.loadAccount(a,o,t.client_info),n=new Qe(i,this.loadIdToken(a,i.homeAccountId,o.hostnameAndPort,o.tenant),this.loadAccessToken(e,t,i.homeAccountId,o.hostnameAndPort,o.tenant,r),this.loadRefreshToken(e,t,i.homeAccountId,o.hostnameAndPort))}}return this.generateAuthenticationResult(e,a,n,i,o)},e.prototype.loadAccount=function(e,t,r,n){var o;if(n?o=n:void 0!==t.authorityType&&r&&(o=fe.generateHomeAccountId(r,t.authorityType,this.logger,this.cryptoObj,e.claims)),!o)throw Mt.createUnableToLoadTokenError("Unexpected missing homeAccountId");var i=fe.createAccount({homeAccountId:o,idTokenClaims:e.claims,clientInfo:r,environment:t.hostnameAndPort},t);if(this.isBrowserEnvironment)return this.logger.verbose("TokenCache - loading account"),this.storage.setAccount(i),i;throw Mt.createUnableToLoadTokenError("loadExternalTokens is designed to work in browser environments only.")},e.prototype.loadIdToken=function(e,t,r,n){var o=Le.createIdTokenEntity(t,r,e.rawToken,this.config.auth.clientId,n);if(this.isBrowserEnvironment)return this.logger.verbose("TokenCache - loading id token"),this.storage.setIdTokenCredential(o),o;throw Mt.createUnableToLoadTokenError("loadExternalTokens is designed to work in browser environments only.")},e.prototype.loadAccessToken=function(e,t,r,n,o,i){if(!t.access_token)return this.logger.verbose("TokenCache - No access token provided for caching"),null;if(!t.expires_in)throw Mt.createUnableToLoadTokenError("Please ensure server response includes expires_in value.");if(!i.extendedExpiresOn)throw Mt.createUnableToLoadTokenError("Please provide an extendedExpiresOn value in the options.");var a=new he(e.scopes).printScopes(),s=i.expiresOn||t.expires_in+(new Date).getTime()/1e3,c=i.extendedExpiresOn,u=Ke.createAccessTokenEntity(r,n,t.access_token,this.config.auth.clientId,o,a,s,c,this.cryptoObj);if(this.isBrowserEnvironment)return this.logger.verbose("TokenCache - loading access token"),this.storage.setAccessTokenCredential(u),u;throw Mt.createUnableToLoadTokenError("loadExternalTokens is designed to work in browser environments only.")},e.prototype.loadRefreshToken=function(e,t,r,n){if(!t.refresh_token)return this.logger.verbose("TokenCache - No refresh token provided for caching"),null;var o=Fe.createRefreshTokenEntity(r,n,t.refresh_token,this.config.auth.clientId);if(this.isBrowserEnvironment)return this.logger.verbose("TokenCache - loading refresh token"),this.storage.setRefreshTokenCredential(o),o;throw Mt.createUnableToLoadTokenError("loadExternalTokens is designed to work in browser environments only.")},e.prototype.generateAuthenticationResult=function(e,t,r,n,o){var i,a,s=E.EMPTY_STRING,c=[],u=null;r.accessToken&&(s=r.accessToken.secret,c=he.fromString(r.accessToken.target).asArray(),u=new Date(1e3*Number(r.accessToken.expiresOn)),a=new Date(1e3*Number(r.accessToken.extendedExpiresOn)));var l=(null==t?void 0:t.claims.oid)||(null==t?void 0:t.claims.sub)||E.EMPTY_STRING,d=(null==t?void 0:t.claims.tid)||E.EMPTY_STRING;return{authority:o?o.canonicalAuthority:E.EMPTY_STRING,uniqueId:l,tenantId:d,scopes:c,account:n?n.getAccountInfo():null,idToken:t?t.rawToken:E.EMPTY_STRING,idTokenClaims:t?t.claims:{},accessToken:s,fromCache:!0,expiresOn:u,correlationId:e.correlationId||E.EMPTY_STRING,requestId:E.EMPTY_STRING,extExpiresOn:a,familyId:E.EMPTY_STRING,tokenType:(null===(i=null==r?void 0:r.accessToken)||void 0===i?void 0:i.tokenType)||E.EMPTY_STRING,state:E.EMPTY_STRING,cloudGraphHostName:n.cloudGraphHostName||E.EMPTY_STRING,msGraphHost:n.msGraphHost||E.EMPTY_STRING,code:void 0,fromNativeBroker:!1}},e}(),Br=function(e){function t(t){var r=e.call(this,t)||this;return r.includeRedirectUri=!1,r}return r(t,e),t}(et),xr=function(t){function o(e,r,n,o,i,a,s,c,u,l){var d=t.call(this,e,r,n,o,i,a,c,u,l)||this;return d.apiId=s,d}return r(o,t),o.prototype.acquireToken=function(t){return i(this,void 0,void 0,(function(){var r,o,i,s,c,u;return a(this,(function(a){switch(a.label){case 0:if(this.logger.trace("SilentAuthCodeClient.acquireToken called"),!t.code)throw Mt.createAuthCodeRequiredError();return this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientInitializeAuthorizationRequest,t.correlationId),[4,this.initializeAuthorizationRequest(t,e.InteractionType.Silent)];case 1:r=a.sent(),this.browserStorage.updateCacheEntries(r.state,r.nonce,r.authority,r.loginHint||E.EMPTY_STRING,r.account||null),o=this.initializeServerTelemetryManager(this.apiId),a.label=2;case 2:return a.trys.push([2,4,,5]),i=n(n({},r),{code:t.code}),this.performanceClient.setPreQueueTime(e.PerformanceEvents.StandardInteractionClientGetClientConfiguration,t.correlationId),[4,this.getClientConfiguration(o,r.authority)];case 3:return s=a.sent(),c=new Br(s),this.logger.verbose("Auth code client created"),[2,new vr(c,this.browserStorage,i,this.logger,this.config.system,this.performanceClient).handleCodeResponseFromServer({code:t.code,msgraph_host:t.msGraphHost,cloud_graph_host_name:t.cloudGraphHostName,cloud_instance_host_name:t.cloudInstanceHostName},r.state,c.authority,this.networkClient,!1)];case 4:throw(u=a.sent())instanceof $&&u.setCorrelationId(this.correlationId),o.cacheFailedRequest(u),this.browserStorage.cleanRequestByState(r.state),u;case 5:return[2]}}))}))},o.prototype.logout=function(){return Promise.reject(Mt.createSilentLogoutUnsupportedError())},o}(rr),Gr=function(){function t(t){var r,o,i;this.isBrowserEnvironment="undefined"!=typeof window,this.config=function(t,r){var o=t.auth,i=t.cache,a=t.system,s=t.telemetry,c={clientId:E.EMPTY_STRING,authority:""+E.DEFAULT_AUTHORITY,knownAuthorities:[],cloudDiscoveryMetadata:E.EMPTY_STRING,authorityMetadata:E.EMPTY_STRING,redirectUri:E.EMPTY_STRING,postLogoutRedirectUri:E.EMPTY_STRING,navigateToLoginRequestUrl:!0,clientCapabilities:[],protocolMode:e.ProtocolMode.AAD,azureCloudOptions:{azureCloudInstance:e.AzureCloudInstance.None,tenant:E.EMPTY_STRING},skipAuthorityMetadataCache:!1},u={cacheLocation:e.BrowserCacheLocation.SessionStorage,temporaryCacheLocation:e.BrowserCacheLocation.SessionStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!(!i||i.cacheLocation!==e.BrowserCacheLocation.LocalStorage),claimsBasedCachingEnabled:!0},l={loggerCallback:function(){},logLevel:e.LogLevel.Info,piiLoggingEnabled:!1},d=n(n({},Ce),{loggerOptions:l,networkClient:r?er.getBrowserNetworkClient():Tt,navigationClient:new gr,loadFrameTimeout:0,windowHashTimeout:(null==a?void 0:a.loadFrameTimeout)||6e4,iframeHashTimeout:(null==a?void 0:a.loadFrameTimeout)||fr,navigateFrameWait:r&&er.detectIEOrEdge()?500:0,redirectNavigationTimeout:3e4,asyncPopups:!1,allowRedirectInIframe:!1,allowNativeBroker:!1,nativeBrokerHandshakeTimeout:(null==a?void 0:a.nativeBrokerHandshakeTimeout)||2e3,pollIntervalMilliseconds:Ot.DEFAULT_POLL_INTERVAL_MS,cryptoOptions:{useMsrCrypto:!1,entropy:void 0}}),h=n(n({},a),{loggerOptions:(null==a?void 0:a.loggerOptions)||l}),p={application:{appName:E.EMPTY_STRING,appVersion:E.EMPTY_STRING}};return{auth:n(n({},c),o),cache:n(n({},u),i),system:n(n({},d),h),telemetry:n(n({},p),s)}}(t,this.isBrowserEnvironment),this.initialized=!1,this.logger=new ie(this.config.system.loggerOptions,Jt,Xt),this.networkClient=this.config.system.networkClient,this.navigationClient=this.config.system.navigationClient,this.redirectResponse=new Map,this.hybridAuthCodeResponses=new Map,this.performanceClient=this.isBrowserEnvironment?new Dr(this.config.auth.clientId,this.config.auth.authority,this.logger,Jt,Xt,this.config.telemetry.application,this.config.system.cryptoOptions):new Pt(this.config.auth.clientId,this.config.auth.authority,this.logger,Jt,Xt,this.config.telemetry.application),this.browserCrypto=this.isBrowserEnvironment?new Hr(this.logger,this.performanceClient,this.config.system.cryptoOptions):ee,this.eventHandler=new Er(this.logger,this.browserCrypto),this.browserStorage=this.isBrowserEnvironment?new Vt(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger):(r=this.config.auth.clientId,o=this.logger,i={cacheLocation:e.BrowserCacheLocation.MemoryStorage,temporaryCacheLocation:e.BrowserCacheLocation.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!0},new Vt(r,i,ee,o));var a={cacheLocation:e.BrowserCacheLocation.MemoryStorage,temporaryCacheLocation:e.BrowserCacheLocation.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!0};this.nativeInternalStorage=new Vt(this.config.auth.clientId,a,this.browserCrypto,this.logger),this.tokenCache=new Fr(this.config,this.browserStorage,this.logger,this.browserCrypto),this.trackPageVisibilityWithMeasurement=this.trackPageVisibilityWithMeasurement.bind(this)}return t.prototype.initialize=function(){return i(this,void 0,void 0,(function(){var t,r,n,o,i;return a(this,(function(a){switch(a.label){case 0:if(this.logger.trace("initialize called"),this.initialized)return this.logger.info("initialize has already been called, exiting early."),[2];if(t=this.config.system.allowNativeBroker,r=this.performanceClient.startMeasurement(e.PerformanceEvents.InitializeClientApplication),this.eventHandler.emitEvent(e.EventType.INITIALIZE_START),!t)return[3,4];a.label=1;case 1:return a.trys.push([1,3,,4]),n=this,[4,dr.createProvider(this.logger,this.config.system.nativeBrokerHandshakeTimeout,this.performanceClient)];case 2:return n.nativeExtensionProvider=a.sent(),[3,4];case 3:return o=a.sent(),this.logger.verbose(o),[3,4];case 4:return this.config.cache.claimsBasedCachingEnabled?[3,6]:(this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims"),i=this.performanceClient.startMeasurement(e.PerformanceEvents.ClearTokensAndKeysWithClaims),[4,this.browserStorage.clearTokensAndKeysWithClaims()]);case 5:a.sent(),i.endMeasurement({success:!0}),a.label=6;case 6:return this.initialized=!0,this.eventHandler.emitEvent(e.EventType.INITIALIZE_END),r.endMeasurement({allowNativeBroker:t,success:!0}),[2]}}))}))},t.prototype.handleRedirectPromise=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s,c,u,l,d=this;return a(this,(function(a){return this.logger.verbose("handleRedirectPromise called"),er.blockNativeBrokerCalledBeforeInitialized(this.config.system.allowNativeBroker,this.initialized),r=this.getAllAccounts(),this.isBrowserEnvironment?(n=t||E.EMPTY_STRING,void 0===(o=this.redirectResponse.get(n))?(this.eventHandler.emitEvent(e.EventType.HANDLE_REDIRECT_START,e.InteractionType.Redirect),this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise"),i=this.browserStorage.getCachedNativeRequest(),s=void 0,i&&dr.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider)&&this.nativeExtensionProvider&&!t?(this.logger.trace("handleRedirectPromise - acquiring token from native platform"),c=new lr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,e.ApiId.handleRedirectPromise,this.performanceClient,this.nativeExtensionProvider,i.accountId,this.nativeInternalStorage,i.correlationId),s=c.handleRedirectPromise()):(this.logger.trace("handleRedirectPromise - acquiring token from web flow"),u=this.browserStorage.getTemporaryCache(lt.CORRELATION_ID,!0)||E.EMPTY_STRING,l=this.createRedirectClient(u),s=l.handleRedirectPromise(t)),o=s.then((function(t){t&&(r.length<d.getAllAccounts().length?(d.eventHandler.emitEvent(e.EventType.LOGIN_SUCCESS,e.InteractionType.Redirect,t),d.logger.verbose("handleRedirectResponse returned result, login success")):(d.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Redirect,t),d.logger.verbose("handleRedirectResponse returned result, acquire token success")));return d.eventHandler.emitEvent(e.EventType.HANDLE_REDIRECT_END,e.InteractionType.Redirect),t})).catch((function(t){throw r.length>0?d.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,t):d.eventHandler.emitEvent(e.EventType.LOGIN_FAILURE,e.InteractionType.Redirect,null,t),d.eventHandler.emitEvent(e.EventType.HANDLE_REDIRECT_END,e.InteractionType.Redirect),t})),this.redirectResponse.set(n,o)):this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"),[2,o]):(this.logger.verbose("handleRedirectPromise returns null, not browser environment"),[2,null])}))}))},t.prototype.acquireTokenRedirect=function(t){return i(this,void 0,void 0,(function(){var r,n,o,i,s,c=this;return a(this,(function(a){return r=this.getRequestCorrelationId(t),this.logger.verbose("acquireTokenRedirect called",r),this.preflightBrowserEnvironmentCheck(e.InteractionType.Redirect),(n=this.getAllAccounts().length>0)?this.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_START,e.InteractionType.Redirect,t):this.eventHandler.emitEvent(e.EventType.LOGIN_START,e.InteractionType.Redirect,t),this.nativeExtensionProvider&&this.canUseNative(t)?(i=new lr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,e.ApiId.acquireTokenRedirect,this.performanceClient,this.nativeExtensionProvider,this.getNativeAccountId(t),this.nativeInternalStorage,t.correlationId),o=i.acquireTokenRedirect(t).catch((function(e){if(e instanceof cr&&e.isFatal())return c.nativeExtensionProvider=void 0,c.createRedirectClient(t.correlationId).acquireToken(t);if(e instanceof ze)return c.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow"),c.createRedirectClient(t.correlationId).acquireToken(t);throw c.browserStorage.setInteractionInProgress(!1),e}))):(s=this.createRedirectClient(t.correlationId),o=s.acquireToken(t)),[2,o.catch((function(t){throw n?c.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,t):c.eventHandler.emitEvent(e.EventType.LOGIN_FAILURE,e.InteractionType.Redirect,null,t),t}))]}))}))},t.prototype.acquireTokenPopup=function(t){var r=this,n=this.getRequestCorrelationId(t),o=this.performanceClient.startMeasurement(e.PerformanceEvents.AcquireTokenPopup,n);try{this.logger.verbose("acquireTokenPopup called",n),this.preflightBrowserEnvironmentCheck(e.InteractionType.Popup)}catch(e){return Promise.reject(e)}var i,a=this.getAllAccounts();(a.length>0?this.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_START,e.InteractionType.Popup,t):this.eventHandler.emitEvent(e.EventType.LOGIN_START,e.InteractionType.Popup,t),this.canUseNative(t))?i=this.acquireTokenNative(t,e.ApiId.acquireTokenPopup).then((function(e){return r.browserStorage.setInteractionInProgress(!1),o.endMeasurement({success:!0,isNativeBroker:!0,requestId:e.requestId}),e})).catch((function(e){if(e instanceof cr&&e.isFatal())return r.nativeExtensionProvider=void 0,r.createPopupClient(t.correlationId).acquireToken(t);if(e instanceof ze)return r.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow"),r.createPopupClient(t.correlationId).acquireToken(t);throw r.browserStorage.setInteractionInProgress(!1),e})):i=this.createPopupClient(t.correlationId).acquireToken(t);return i.then((function(t){return a.length<r.getAllAccounts().length?r.eventHandler.emitEvent(e.EventType.LOGIN_SUCCESS,e.InteractionType.Popup,t):r.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Popup,t),o.addStaticFields({accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length}),o.endMeasurement({success:!0,requestId:t.requestId}),t})).catch((function(t){return a.length>0?r.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Popup,null,t):r.eventHandler.emitEvent(e.EventType.LOGIN_FAILURE,e.InteractionType.Popup,null,t),o.endMeasurement({errorCode:t.errorCode,subErrorCode:t.subError,success:!1}),Promise.reject(t)}))},t.prototype.trackPageVisibilityWithMeasurement=function(){var e=this.ssoSilentMeasurement||this.acquireTokenByCodeAsyncMeasurement;e&&(this.logger.info("Perf: Visibility change detected in ",e.event.name),e.increment({visibilityChangeCount:1}))},t.prototype.ssoSilent=function(t){var r;return i(this,void 0,void 0,(function(){var o,i,s,c,u=this;return a(this,(function(a){return o=this.getRequestCorrelationId(t),i=n(n({},t),{prompt:t.prompt,correlationId:o}),this.preflightBrowserEnvironmentCheck(e.InteractionType.Silent),this.ssoSilentMeasurement=this.performanceClient.startMeasurement(e.PerformanceEvents.SsoSilent,o),null===(r=this.ssoSilentMeasurement)||void 0===r||r.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),this.logger.verbose("ssoSilent called",o),this.eventHandler.emitEvent(e.EventType.SSO_SILENT_START,e.InteractionType.Silent,i),this.canUseNative(i)?s=this.acquireTokenNative(i,e.ApiId.ssoSilent).catch((function(e){if(e instanceof cr&&e.isFatal())return u.nativeExtensionProvider=void 0,u.createSilentIframeClient(i.correlationId).acquireToken(i);throw e})):(c=this.createSilentIframeClient(i.correlationId),s=c.acquireToken(i)),[2,s.then((function(t){var r,n;return u.eventHandler.emitEvent(e.EventType.SSO_SILENT_SUCCESS,e.InteractionType.Silent,t),null===(r=u.ssoSilentMeasurement)||void 0===r||r.addStaticFields({accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length}),null===(n=u.ssoSilentMeasurement)||void 0===n||n.endMeasurement({success:!0,isNativeBroker:t.fromNativeBroker,requestId:t.requestId}),t})).catch((function(t){var r;throw u.eventHandler.emitEvent(e.EventType.SSO_SILENT_FAILURE,e.InteractionType.Silent,null,t),null===(r=u.ssoSilentMeasurement)||void 0===r||r.endMeasurement({errorCode:t.errorCode,subErrorCode:t.subError,success:!1}),t})).finally((function(){document.removeEventListener("visibilitychange",u.trackPageVisibilityWithMeasurement)}))]}))}))},t.prototype.acquireTokenByCode=function(t){return i(this,void 0,void 0,(function(){var r,o,i,s,c=this;return a(this,(function(a){r=this.getRequestCorrelationId(t),this.preflightBrowserEnvironmentCheck(e.InteractionType.Silent),this.logger.trace("acquireTokenByCode called",r),this.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_BY_CODE_START,e.InteractionType.Silent,t),o=this.performanceClient.startMeasurement(e.PerformanceEvents.AcquireTokenByCode,t.correlationId);try{if(t.code&&t.nativeAccountId)throw Mt.createSpaCodeAndNativeAccountIdPresentError();if(t.code)return i=t.code,(s=this.hybridAuthCodeResponses.get(i))?(this.logger.verbose("Existing acquireTokenByCode request found",t.correlationId),o.discardMeasurement()):(this.logger.verbose("Initiating new acquireTokenByCode request",r),s=this.acquireTokenByCodeAsync(n(n({},t),{correlationId:r})).then((function(t){return c.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_BY_CODE_SUCCESS,e.InteractionType.Silent,t),c.hybridAuthCodeResponses.delete(i),o.addStaticFields({accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length}),o.endMeasurement({success:!0,isNativeBroker:t.fromNativeBroker,requestId:t.requestId}),t})).catch((function(t){throw c.hybridAuthCodeResponses.delete(i),c.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_BY_CODE_FAILURE,e.InteractionType.Silent,null,t),o.endMeasurement({errorCode:t.errorCode,subErrorCode:t.subError,success:!1}),t})),this.hybridAuthCodeResponses.set(i,s)),[2,s];if(t.nativeAccountId){if(this.canUseNative(t,t.nativeAccountId))return[2,this.acquireTokenNative(t,e.ApiId.acquireTokenByCode,t.nativeAccountId).catch((function(e){throw e instanceof cr&&e.isFatal()&&(c.nativeExtensionProvider=void 0),e}))];throw Mt.createUnableToAcquireTokenFromNativePlatformError()}throw Mt.createAuthCodeOrNativeAccountIdRequiredError()}catch(t){throw this.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_BY_CODE_FAILURE,e.InteractionType.Silent,null,t),o.endMeasurement({errorCode:t instanceof $&&t.errorCode||void 0,subErrorCode:t instanceof $&&t.subError||void 0,success:!1}),t}return[2]}))}))},t.prototype.acquireTokenByCodeAsync=function(t){var r;return i(this,void 0,void 0,(function(){var n=this;return a(this,(function(o){switch(o.label){case 0:return this.logger.trace("acquireTokenByCodeAsync called",t.correlationId),this.acquireTokenByCodeAsyncMeasurement=this.performanceClient.startMeasurement(e.PerformanceEvents.AcquireTokenByCodeAsync,t.correlationId),null===(r=this.acquireTokenByCodeAsyncMeasurement)||void 0===r||r.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),[4,this.createSilentAuthCodeClient(t.correlationId).acquireToken(t).then((function(e){var t;return null===(t=n.acquireTokenByCodeAsyncMeasurement)||void 0===t||t.endMeasurement({success:!0,fromCache:e.fromCache,isNativeBroker:e.fromNativeBroker,requestId:e.requestId}),e})).catch((function(e){var t;throw null===(t=n.acquireTokenByCodeAsyncMeasurement)||void 0===t||t.endMeasurement({errorCode:e.errorCode,subErrorCode:e.subError,success:!1}),e})).finally((function(){document.removeEventListener("visibilitychange",n.trackPageVisibilityWithMeasurement)}))];case 1:return[2,o.sent()]}}))}))},t.prototype.acquireTokenFromCache=function(t,r,n){return i(this,void 0,void 0,(function(){return a(this,(function(o){switch(this.performanceClient.addQueueMeasurement(e.PerformanceEvents.AcquireTokenFromCache,r.correlationId),n.cacheLookupPolicy){case e.CacheLookupPolicy.Default:case e.CacheLookupPolicy.AccessToken:case e.CacheLookupPolicy.AccessTokenAndRefreshToken:return[2,t.acquireToken(r)];default:throw re.createRefreshRequiredError()}return[2]}))}))},t.prototype.acquireTokenByRefreshToken=function(t,r){return i(this,void 0,void 0,(function(){var n;return a(this,(function(o){switch(this.performanceClient.addQueueMeasurement(e.PerformanceEvents.AcquireTokenByRefreshToken,t.correlationId),r.cacheLookupPolicy){case e.CacheLookupPolicy.Default:case e.CacheLookupPolicy.AccessTokenAndRefreshToken:case e.CacheLookupPolicy.RefreshToken:case e.CacheLookupPolicy.RefreshTokenAndNetwork:return n=this.createSilentRefreshClient(t.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.SilentRefreshClientAcquireToken,t.correlationId),[2,n.acquireToken(t)];default:throw re.createRefreshRequiredError()}return[2]}))}))},t.prototype.acquireTokenBySilentIframe=function(t){return i(this,void 0,void 0,(function(){var r;return a(this,(function(n){return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.AcquireTokenBySilentIframe,t.correlationId),r=this.createSilentIframeClient(t.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.SilentIframeClientAcquireToken,t.correlationId),[2,r.acquireToken(t)]}))}))},t.prototype.logout=function(e){return i(this,void 0,void 0,(function(){var t;return a(this,(function(r){return t=this.getRequestCorrelationId(e),this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.",t),[2,this.logoutRedirect(n({correlationId:t},e))]}))}))},t.prototype.logoutRedirect=function(t){return i(this,void 0,void 0,(function(){var r;return a(this,(function(n){return r=this.getRequestCorrelationId(t),this.preflightBrowserEnvironmentCheck(e.InteractionType.Redirect),[2,this.createRedirectClient(r).logout(t)]}))}))},t.prototype.logoutPopup=function(t){try{var r=this.getRequestCorrelationId(t);return this.preflightBrowserEnvironmentCheck(e.InteractionType.Popup),this.createPopupClient(r).logout(t)}catch(e){return Promise.reject(e)}},t.prototype.getAllAccounts=function(){return this.logger.verbose("getAllAccounts called"),this.isBrowserEnvironment?this.browserStorage.getAllAccounts():[]},t.prototype.getAccountByUsername=function(e){if(this.logger.trace("getAccountByUsername called"),!e)return this.logger.warning("getAccountByUsername: No username provided"),null;var t=this.browserStorage.getAccountInfoFilteredBy({username:e});return t?(this.logger.verbose("getAccountByUsername: Account matching username found, returning"),this.logger.verbosePii("getAccountByUsername: Returning signed-in accounts matching username: "+e),t):(this.logger.verbose("getAccountByUsername: No matching account found, returning null"),null)},t.prototype.getAccountByHomeId=function(e){if(this.logger.trace("getAccountByHomeId called"),!e)return this.logger.warning("getAccountByHomeId: No homeAccountId provided"),null;var t=this.browserStorage.getAccountInfoFilteredBy({homeAccountId:e});return t?(this.logger.verbose("getAccountByHomeId: Account matching homeAccountId found, returning"),this.logger.verbosePii("getAccountByHomeId: Returning signed-in accounts matching homeAccountId: "+e),t):(this.logger.verbose("getAccountByHomeId: No matching account found, returning null"),null)},t.prototype.getAccountByLocalId=function(e){if(this.logger.trace("getAccountByLocalId called"),!e)return this.logger.warning("getAccountByLocalId: No localAccountId provided"),null;var t=this.browserStorage.getAccountInfoFilteredBy({localAccountId:e});return t?(this.logger.verbose("getAccountByLocalId: Account matching localAccountId found, returning"),this.logger.verbosePii("getAccountByLocalId: Returning signed-in accounts matching localAccountId: "+e),t):(this.logger.verbose("getAccountByLocalId: No matching account found, returning null"),null)},t.prototype.setActiveAccount=function(e){this.browserStorage.setActiveAccount(e)},t.prototype.getActiveAccount=function(){return this.browserStorage.getActiveAccount()},t.prototype.preflightBrowserEnvironmentCheck=function(t,r){if(void 0===r&&(r=!0),this.logger.verbose("preflightBrowserEnvironmentCheck started"),er.blockNonBrowserEnvironment(this.isBrowserEnvironment),er.blockRedirectInIframe(t,this.config.system.allowRedirectInIframe),er.blockReloadInHiddenIframes(),er.blockAcquireTokenInPopups(),er.blockNativeBrokerCalledBeforeInitialized(this.config.system.allowNativeBroker,this.initialized),t===e.InteractionType.Redirect&&this.config.cache.cacheLocation===e.BrowserCacheLocation.MemoryStorage&&!this.config.cache.storeAuthStateInCookie)throw Qt.createInMemoryRedirectUnavailableError();t!==e.InteractionType.Redirect&&t!==e.InteractionType.Popup||this.preflightInteractiveRequest(r)},t.prototype.preflightInteractiveRequest=function(e){this.logger.verbose("preflightInteractiveRequest called, validating app environment"),er.blockReloadInHiddenIframes(),e&&this.browserStorage.setInteractionInProgress(!0)},t.prototype.acquireTokenNative=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){if(this.logger.trace("acquireTokenNative called"),!this.nativeExtensionProvider)throw Mt.createNativeConnectionNotEstablishedError();return[2,new lr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,t,this.performanceClient,this.nativeExtensionProvider,r||this.getNativeAccountId(e),this.nativeInternalStorage,e.correlationId).acquireToken(e)]}))}))},t.prototype.canUseNative=function(e,t){if(this.logger.trace("canUseNative called"),!dr.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,e.authenticationScheme))return this.logger.trace("canUseNative: isNativeAvailable returned false, returning false"),!1;if(e.prompt)switch(e.prompt){case w.NONE:case w.CONSENT:case w.LOGIN:this.logger.trace("canUseNative: prompt is compatible with native flow");break;default:return this.logger.trace("canUseNative: prompt = "+e.prompt+" is not compatible with native flow, returning false"),!1}return!(!t&&!this.getNativeAccountId(e))||(this.logger.trace("canUseNative: nativeAccountId is not available, returning false"),!1)},t.prototype.getNativeAccountId=function(e){var t=e.account||this.browserStorage.getAccountInfoByHints(e.loginHint,e.sid)||this.getActiveAccount();return t&&t.nativeAccountId||""},t.prototype.createPopupClient=function(e){return new pr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)},t.prototype.createRedirectClient=function(e){return new hr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,e)},t.prototype.createSilentIframeClient=function(t){return new yr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,e.ApiId.ssoSilent,this.performanceClient,this.nativeInternalStorage,this.nativeExtensionProvider,t)},t.prototype.createSilentCacheClient=function(e){return new ur(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)},t.prototype.createSilentRefreshClient=function(e){return new Cr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeExtensionProvider,e)},t.prototype.createSilentAuthCodeClient=function(t){return new xr(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,e.ApiId.acquireTokenByCode,this.performanceClient,this.nativeExtensionProvider,t)},t.prototype.addEventCallback=function(e){return this.eventHandler.addEventCallback(e)},t.prototype.removeEventCallback=function(e){this.eventHandler.removeEventCallback(e)},t.prototype.addPerformanceCallback=function(e){return this.performanceClient.addPerformanceCallback(e)},t.prototype.removePerformanceCallback=function(e){return this.performanceClient.removePerformanceCallback(e)},t.prototype.enableAccountStorageEvents=function(){this.eventHandler.enableAccountStorageEvents()},t.prototype.disableAccountStorageEvents=function(){this.eventHandler.disableAccountStorageEvents()},t.prototype.getTokenCache=function(){return this.tokenCache},t.prototype.getLogger=function(){return this.logger},t.prototype.setLogger=function(e){this.logger=e},t.prototype.initializeWrapperLibrary=function(e,t){this.browserStorage.setWrapperMetadata(e,t)},t.prototype.setNavigationClient=function(e){this.navigationClient=e},t.prototype.getConfiguration=function(){return this.config},t.prototype.getRequestCorrelationId=function(e){return(null==e?void 0:e.correlationId)?e.correlationId:this.isBrowserEnvironment?this.browserCrypto.createNewGuid():E.EMPTY_STRING},t}(),zr=function(t){function o(e){var r=t.call(this,e)||this;return r.astsAsyncMeasurement=void 0,r.activeSilentTokenRequests=new Map,r.trackPageVisibility=r.trackPageVisibility.bind(r),r}return r(o,t),o.prototype.loginRedirect=function(e){return i(this,void 0,void 0,(function(){var t;return a(this,(function(r){return t=this.getRequestCorrelationId(e),this.logger.verbose("loginRedirect called",t),[2,this.acquireTokenRedirect(n({correlationId:t},e||Dt))]}))}))},o.prototype.loginPopup=function(e){var t=this.getRequestCorrelationId(e);return this.logger.verbose("loginPopup called",t),this.acquireTokenPopup(n({correlationId:t},e||Dt))},o.prototype.acquireTokenSilent=function(t){return i(this,void 0,void 0,(function(){var r,o,i,s,c,u,l,d=this;return a(this,(function(a){if(r=this.getRequestCorrelationId(t),(o=this.performanceClient.startMeasurement(e.PerformanceEvents.AcquireTokenSilent,r)).addStaticFields({cacheLookupPolicy:t.cacheLookupPolicy}),this.preflightBrowserEnvironmentCheck(e.InteractionType.Silent),this.logger.verbose("acquireTokenSilent called",r),!(i=t.account||this.getActiveAccount()))throw Mt.createNoAccountError();return s={clientId:this.config.auth.clientId,authority:t.authority||E.EMPTY_STRING,scopes:t.scopes,homeAccountIdentifier:i.homeAccountId,claims:t.claims,authenticationScheme:t.authenticationScheme,resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,sshKid:t.sshKid},c=JSON.stringify(s),void 0===(u=this.activeSilentTokenRequests.get(c))?(this.logger.verbose("acquireTokenSilent called for the first time, storing active request",r),this.performanceClient.setPreQueueTime(e.PerformanceEvents.AcquireTokenSilentAsync,r),l=this.acquireTokenSilentAsync(n(n({},t),{correlationId:r}),i).then((function(e){return d.activeSilentTokenRequests.delete(c),o.addStaticFields({accessTokenSize:e.accessToken.length,idTokenSize:e.idToken.length}),o.endMeasurement({success:!0,fromCache:e.fromCache,isNativeBroker:e.fromNativeBroker,cacheLookupPolicy:t.cacheLookupPolicy,requestId:e.requestId}),e})).catch((function(e){throw d.activeSilentTokenRequests.delete(c),o.endMeasurement({errorCode:e.errorCode,subErrorCode:e.subError,success:!1}),e})),this.activeSilentTokenRequests.set(c,l),[2,l]):(this.logger.verbose("acquireTokenSilent has been called previously, returning the result from the first call",r),o.discardMeasurement(),[2,u])}))}))},o.prototype.trackPageVisibility=function(){this.astsAsyncMeasurement&&(this.logger.info("Perf: Visibility change detected"),this.astsAsyncMeasurement.increment({visibilityChangeCount:1}))},o.prototype.acquireTokenSilentAsync=function(t,r){var o;return i(this,void 0,void 0,(function(){var s,c,u,l,d,h=this;return a(this,(function(p){switch(p.label){case 0:return this.performanceClient.addQueueMeasurement(e.PerformanceEvents.AcquireTokenSilentAsync,t.correlationId),this.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_START,e.InteractionType.Silent,t),this.astsAsyncMeasurement=this.performanceClient.startMeasurement(e.PerformanceEvents.AcquireTokenSilentAsync,t.correlationId),null===(o=this.astsAsyncMeasurement)||void 0===o||o.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibility),dr.isNativeAvailable(this.config,this.logger,this.nativeExtensionProvider,t.authenticationScheme)&&r.nativeAccountId?(this.logger.verbose("acquireTokenSilent - attempting to acquire token from native platform"),c=n(n({},t),{account:r}),s=this.acquireTokenNative(c,e.ApiId.acquireTokenSilent_silentFlow).catch((function(e){return i(h,void 0,void 0,(function(){return a(this,(function(r){if(e instanceof cr&&e.isFatal())return this.logger.verbose("acquireTokenSilent - native platform unavailable, falling back to web flow"),this.nativeExtensionProvider=void 0,[2,this.createSilentIframeClient(t.correlationId).acquireToken(t)];throw e}))}))})),[3,3]):[3,1];case 1:return this.logger.verbose("acquireTokenSilent - attempting to acquire token from web flow"),u=this.createSilentCacheClient(t.correlationId),this.performanceClient.setPreQueueTime(e.PerformanceEvents.InitializeSilentRequest,t.correlationId),[4,u.initializeSilentRequest(t,r)];case 2:l=p.sent(),d=n(n({},t),{cacheLookupPolicy:t.cacheLookupPolicy||e.CacheLookupPolicy.Default}),this.performanceClient.setPreQueueTime(e.PerformanceEvents.AcquireTokenFromCache,l.correlationId),s=this.acquireTokenFromCache(u,l,d).catch((function(r){if(d.cacheLookupPolicy===e.CacheLookupPolicy.AccessToken)throw r;return er.blockReloadInHiddenIframes(),h.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_NETWORK_START,e.InteractionType.Silent,l),h.performanceClient.setPreQueueTime(e.PerformanceEvents.AcquireTokenByRefreshToken,l.correlationId),h.acquireTokenByRefreshToken(l,d).catch((function(r){var n=r instanceof Re,o=r instanceof ze,i=r.errorCode===Ge.noTokensFoundError.code,a=r.errorCode===Ot.INVALID_GRANT_ERROR;if((!n||!a||o||d.cacheLookupPolicy===e.CacheLookupPolicy.AccessTokenAndRefreshToken||d.cacheLookupPolicy===e.CacheLookupPolicy.RefreshToken)&&d.cacheLookupPolicy!==e.CacheLookupPolicy.Skip&&!i)throw r;return h.logger.verbose("Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.",t.correlationId),h.performanceClient.setPreQueueTime(e.PerformanceEvents.AcquireTokenBySilentIframe,l.correlationId),h.acquireTokenBySilentIframe(l)}))})),p.label=3;case 3:return[2,s.then((function(t){var r;return h.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,t),null===(r=h.astsAsyncMeasurement)||void 0===r||r.endMeasurement({success:!0,fromCache:t.fromCache,isNativeBroker:t.fromNativeBroker,requestId:t.requestId}),t})).catch((function(t){var r;throw h.eventHandler.emitEvent(e.EventType.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null,t),null===(r=h.astsAsyncMeasurement)||void 0===r||r.endMeasurement({errorCode:t.errorCode,subErrorCode:t.subError,success:!1}),t})).finally((function(){document.removeEventListener("visibilitychange",h.trackPageVisibility)}))]}}))}))},o.prototype.hydrateCache=function(e,t){return i(this,void 0,void 0,(function(){var r;return a(this,(function(n){return this.logger.verbose("hydrateCache called"),e.account?(r=fe.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost),this.browserStorage.setAccount(r),e.fromNativeBroker?(this.logger.verbose("Response was from native broker, storing in-memory"),[2,this.nativeInternalStorage.hydrateCache(e,t)]):[2,this.browserStorage.hydrateCache(e,t)]):[2]}))}))},o}(Gr),Qr={initialize:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},acquireTokenPopup:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},acquireTokenRedirect:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},acquireTokenSilent:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},acquireTokenByCode:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},getAllAccounts:function(){return[]},getAccountByHomeId:function(){return null},getAccountByUsername:function(){return null},getAccountByLocalId:function(){return null},handleRedirectPromise:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},loginPopup:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},loginRedirect:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},logout:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},logoutRedirect:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},logoutPopup:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},ssoSilent:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())},addEventCallback:function(){return null},removeEventCallback:function(){},addPerformanceCallback:function(){return""},removePerformanceCallback:function(){return!1},enableAccountStorageEvents:function(){},disableAccountStorageEvents:function(){},getTokenCache:function(){throw Qt.createStubPcaInstanceCalledError()},getLogger:function(){throw Qt.createStubPcaInstanceCalledError()},setLogger:function(){},setActiveAccount:function(){},getActiveAccount:function(){return null},initializeWrapperLibrary:function(){},setNavigationClient:function(){},getConfiguration:function(){throw Qt.createStubPcaInstanceCalledError()},hydrateCache:function(){return Promise.reject(Qt.createStubPcaInstanceCalledError())}},jr=function(){function t(){}return t.getInteractionStatusFromEvent=function(t,r){switch(t.eventType){case e.EventType.LOGIN_START:return e.InteractionStatus.Login;case e.EventType.SSO_SILENT_START:return e.InteractionStatus.SsoSilent;case e.EventType.ACQUIRE_TOKEN_START:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup)return e.InteractionStatus.AcquireToken;break;case e.EventType.HANDLE_REDIRECT_START:return e.InteractionStatus.HandleRedirect;case e.EventType.LOGOUT_START:return e.InteractionStatus.Logout;case e.EventType.SSO_SILENT_SUCCESS:case e.EventType.SSO_SILENT_FAILURE:if(r&&r!==e.InteractionStatus.SsoSilent)break;return e.InteractionStatus.None;case e.EventType.LOGOUT_END:if(r&&r!==e.InteractionStatus.Logout)break;return e.InteractionStatus.None;case e.EventType.HANDLE_REDIRECT_END:if(r&&r!==e.InteractionStatus.HandleRedirect)break;return e.InteractionStatus.None;case e.EventType.LOGIN_SUCCESS:case e.EventType.LOGIN_FAILURE:case e.EventType.ACQUIRE_TOKEN_SUCCESS:case e.EventType.ACQUIRE_TOKEN_FAILURE:case e.EventType.RESTORE_FROM_BFCACHE:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup){if(r&&r!==e.InteractionStatus.Login&&r!==e.InteractionStatus.AcquireToken)break;return e.InteractionStatus.None}}return null},t}(),Yr=function(){function e(e,t){var r=t&&t.loggerOptions||{};this.logger=new ie(r,Jt,Xt),this.cryptoOps=new Hr(this.logger),this.popTokenGenerator=new Je(this.cryptoOps),this.shrParameters=e}return e.prototype.generatePublicKeyThumbprint=function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,this.popTokenGenerator.generateKid(this.shrParameters)];case 1:return[2,e.sent().kid]}}))}))},e.prototype.signRequest=function(e,t,r){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,this.popTokenGenerator.signPayload(e,t,this.shrParameters,r)]}))}))},e.prototype.removeKeys=function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,this.cryptoOps.removeTokenBindingKey(e)];case 1:return[2,t.sent()]}}))}))},e}();e.AccountEntity=fe,e.AuthError=$,e.AuthErrorMessage=Z,e.AuthenticationHeaderParser=At,e.BrowserAuthError=Mt,e.BrowserAuthErrorMessage=Nt,e.BrowserConfigurationAuthError=Qt,e.BrowserConfigurationAuthErrorMessage=zt,e.BrowserUtils=er,e.ClientAuthError=re,e.ClientAuthErrorMessage=te,e.ClientConfigurationError=de,e.ClientConfigurationErrorMessage=le,e.DEFAULT_IFRAME_TIMEOUT_MS=fr,e.EventMessageUtils=jr,e.InteractionRequiredAuthError=ze,e.InteractionRequiredAuthErrorMessage=Ge,e.Logger=ie,e.NavigationClient=gr,e.OIDC_DEFAULT_SCOPES=T,e.PublicClientApplication=zr,e.ServerError=Re,e.SignedHttpRequest=Yr,e.StringUtils=ne,e.UrlString=Ye,e.internals=Kr,e.stubbedPublicClientApplication=Qr,e.version=Xt,Object.defineProperty(e,"__esModule",{value:!0})}));
