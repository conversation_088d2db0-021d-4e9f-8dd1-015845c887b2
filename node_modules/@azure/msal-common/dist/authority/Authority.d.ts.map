{"version": 3, "file": "Authority.d.ts", "sourceRoot": "", "sources": ["../../src/authority/Authority.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAA0B,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAEtF,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAEnC,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAI3D,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,kCAAkC,CAAC;AAEjE,OAAO,EAAE,gBAAgB,EAAsB,MAAM,oBAAoB,CAAC;AAM1E,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAElE,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,OAAO,EAAE,kBAAkB,EAAE,MAAM,6CAA6C,CAAC;AAGjF;;;GAGG;AACH,qBAAa,SAAS;IAElB,OAAO,CAAC,mBAAmB,CAAY;IAEvC,OAAO,CAAC,gCAAgC,CAAc;IAEtD,SAAS,CAAC,gBAAgB,EAAE,cAAc,CAAC;IAE3C,SAAS,CAAC,YAAY,EAAE,aAAa,CAAC;IAEtC,OAAO,CAAC,gBAAgB,CAAmB;IAE3C,OAAO,CAAC,QAAQ,CAA0B;IAE1C,OAAO,CAAC,eAAe,CAAkB;IAElC,uBAAuB,EAAE,uBAAuB,CAAC;IAExD,OAAO,CAAC,MAAM,CAAS;IAEvB,SAAS,CAAC,iBAAiB,EAAE,kBAAkB,GAAG,SAAS,CAAC;IAE5D,SAAS,CAAC,aAAa,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5C,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAMhC;gBAGA,SAAS,EAAE,MAAM,EACjB,gBAAgB,EAAE,cAAc,EAChC,YAAY,EAAE,aAAa,EAC3B,gBAAgB,EAAE,gBAAgB,EAClC,MAAM,EAAE,MAAM,EACd,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM;IAc1B;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IAqBxB,IAAW,aAAa,IAAI,aAAa,CAExC;IAED;;OAEG;IACH,IAAW,YAAY,IAAI,YAAY,CAEtC;IAED;;OAEG;IACH,IAAW,OAAO,IAAI,gBAAgB,CAErC;IAED;;OAEG;IACH,IAAW,kBAAkB,IAAI,MAAM,CAEtC;IAED;;OAEG;IACH,IAAW,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAIxC;IAED;;OAEG;IACH,IAAW,+BAA+B,IAAI,IAAI,CAMjD;IAED;;OAEG;IACH,IAAW,eAAe,IAAI,MAAM,CAEnC;IAED;;OAEG;IACH,IAAW,MAAM,IAAI,MAAM,CAE1B;IAED;;OAEG;IACH,IAAW,qBAAqB,IAAI,MAAM,CAMzC;IAED;;OAEG;IACH,IAAW,aAAa,IAAI,MAAM,CAMjC;IAED,IAAW,kBAAkB,IAAI,MAAM,CAMtC;IAED;;OAEG;IACH,IAAW,kBAAkB,IAAI,MAAM,CAUtC;IAED;;OAEG;IACH,IAAW,qBAAqB,IAAI,MAAM,CAMzC;IAED;;OAEG;IACH,IAAW,OAAO,IAAI,MAAM,CAM3B;IAED;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IAOxB;;;OAGG;IACH,OAAO,CAAC,aAAa;IAIrB;;;OAGG;IACH,OAAO,CAAC,WAAW;IA8BnB;;OAEG;IACH,SAAS,KAAK,kCAAkC,IAAI,MAAM,CASzD;IAED;;OAEG;IACH,iBAAiB,IAAI,OAAO;IAI5B;;;OAGG;IACU,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IA4BnD;;;OAGG;YACW,sBAAsB;IA8CpC;;;;;OAKG;IACH,OAAO,CAAC,mBAAmB;IAO3B;;OAEG;IACH,OAAO,CAAC,6BAA6B;IAYrC;;;;OAIG;YACW,8BAA8B;IAmB5C;;OAEG;IACH,OAAO,CAAC,sCAAsC;IAQ9C;;;OAGG;YACW,qCAAqC;IA6CnD;;;;;OAKG;YACW,4BAA4B;IAiD1C;;OAEG;IACH,OAAO,CAAC,mCAAmC;IAwC3C;;;;OAIG;YACW,oCAAoC;IAyElD;;OAEG;IACH,OAAO,CAAC,2CAA2C;IAQnD;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAQ5B;;;;OAIG;IACH,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,MAAM;IAWhG;;;OAGG;IACH,MAAM,CAAC,oCAAoC,CAAC,IAAI,EAAE,MAAM,GAAG,sBAAsB;IAQjF;;;;OAIG;IACH,MAAM,CAAC,4CAA4C,CAC/C,QAAQ,EAAE,sBAAsB,EAAE,EAClC,SAAS,EAAE,MAAM,GAClB,sBAAsB,GAAG,IAAI;IAWhC;;OAEG;IACH,iBAAiB,IAAI,MAAM;IAQ3B;;;OAGG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAI9B;;;;;OAKG;IACH,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAIpD;;;;;OAKG;IACH,MAAM,CAAC,4BAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM;IAyB/F;;;;;OAKG;IACH,MAAM,CAAC,8BAA8B,CAAC,QAAQ,EAAE,oBAAoB,EAAE,WAAW,EAAE,MAAM,GAAG,oBAAoB;IAchH;;;;;;;;OAQG;IACH,MAAM,CAAC,sBAAsB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;CAa3D"}