{"version": 3, "file": "AuthorityFactory.js", "sources": ["../../src/authority/AuthorityFactory.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Authority } from \"./Authority\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { INetworkModule } from \"../network/INetworkModule\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { ICacheManager } from \"../cache/interface/ICacheManager\";\r\nimport { AuthorityOptions } from \"./AuthorityOptions\";\r\nimport { Logger } from \"../logger/Logger\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\n\r\nexport class AuthorityFactory {\r\n\r\n    /**\r\n     * Create an authority object of the correct type based on the url\r\n     * Performs basic authority validation - checks to see if the authority is of a valid type (i.e. aad, b2c, adfs)\r\n     *\r\n     * Also performs endpoint discovery.\r\n     *\r\n     * @param authorityUri\r\n     * @param networkClient\r\n     * @param protocolMode\r\n     */\r\n    static async createDiscoveredInstance(\r\n        authorityUri: string,\r\n        networkClient: INetworkModule,\r\n        cacheManager: ICacheManager,\r\n        authorityOptions: AuthorityOptions,\r\n        logger: Logger,\r\n        performanceClient?: IPerformanceClient,\r\n        correlationId?: string\r\n    ): Promise<Authority> {\r\n        performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityFactoryCreateDiscoveredInstance, correlationId);\r\n        \r\n        const authorityUriFinal = Authority.transformCIAMAuthority(authorityUri);\r\n\r\n        // Initialize authority and perform discovery endpoint check.\r\n        const acquireTokenAuthority: Authority = AuthorityFactory.createInstance(\r\n            authorityUriFinal,\r\n            networkClient,\r\n            cacheManager,\r\n            authorityOptions,\r\n            logger,\r\n            performanceClient,\r\n            correlationId\r\n        );\r\n\r\n        try {\r\n            performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityResolveEndpointsAsync, correlationId);\r\n            await acquireTokenAuthority.resolveEndpointsAsync();\r\n            return acquireTokenAuthority;\r\n        } catch (e) {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create an authority object of the correct type based on the url\r\n     * Performs basic authority validation - checks to see if the authority is of a valid type (i.e. aad, b2c, adfs)\r\n     *\r\n     * Does not perform endpoint discovery.\r\n     *\r\n     * @param authorityUrl\r\n     * @param networkInterface\r\n     * @param protocolMode\r\n     */\r\n    static createInstance(\r\n        authorityUrl: string,\r\n        networkInterface: INetworkModule,\r\n        cacheManager: ICacheManager,\r\n        authorityOptions: AuthorityOptions,\r\n        logger: Logger,\r\n        performanceClient?: IPerformanceClient,\r\n        correlationId?: string\r\n    ): Authority {\r\n        // Throw error if authority url is empty\r\n        if (StringUtils.isEmpty(authorityUrl)) {\r\n            throw ClientConfigurationError.createUrlEmptyError();\r\n        }\r\n\r\n        return new Authority(authorityUrl, networkInterface, cacheManager, authorityOptions, logger, performanceClient, correlationId);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAaH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KAuEC;AArEG;;;;;;;;;AASG;AACU,IAAA,gBAAA,CAAA,wBAAwB,GAArC,UACI,YAAoB,EACpB,aAA6B,EAC7B,YAA2B,EAC3B,gBAAkC,EAClC,MAAc,EACd,iBAAsC,EACtC,aAAsB,EAAA;;;;;;AAEtB,wBAAA,iBAAiB,KAAjB,IAAA,IAAA,iBAAiB,KAAjB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,iBAAiB,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,wCAAwC,EAAE,aAAa,CAAE,CAAA;AAE5G,wBAAA,iBAAiB,GAAG,SAAS,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;AAGnE,wBAAA,qBAAqB,GAAc,gBAAgB,CAAC,cAAc,CACpE,iBAAiB,EACjB,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC;;;;AAGE,wBAAA,iBAAiB,KAAjB,IAAA,IAAA,iBAAiB,KAAjB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,iBAAiB,CAAE,eAAe,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,aAAa,CAAE,CAAA;AACpG,wBAAA,OAAA,CAAA,CAAA,YAAM,qBAAqB,CAAC,qBAAqB,EAAE,CAAA,CAAA;;AAAnD,wBAAA,EAAA,CAAA,IAAA,EAAmD,CAAC;AACpD,wBAAA,OAAA,CAAA,CAAA,aAAO,qBAAqB,CAAC,CAAA;;;AAE7B,wBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,GAAC,CAAC,CAAC;;;;;AAEvE,KAAA,CAAA;AAED;;;;;;;;;AASG;AACI,IAAA,gBAAA,CAAA,cAAc,GAArB,UACI,YAAoB,EACpB,gBAAgC,EAChC,YAA2B,EAC3B,gBAAkC,EAClC,MAAc,EACd,iBAAsC,EACtC,aAAsB,EAAA;;AAGtB,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACnC,YAAA,MAAM,wBAAwB,CAAC,mBAAmB,EAAE,CAAC;AACxD,SAAA;AAED,QAAA,OAAO,IAAI,SAAS,CAAC,YAAY,EAAE,gBAAgB,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;KAClI,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}