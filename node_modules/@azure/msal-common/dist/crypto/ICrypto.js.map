{"version": 3, "file": "ICrypto.js", "sources": ["../../src/crypto/ICrypto.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"../error/AuthError\";\r\nimport { BaseAuthRequest } from \"../request/BaseAuthRequest\";\r\nimport { SignedHttpRequest } from \"./SignedHttpRequest\";\r\n\r\n/**\r\n * The PkceCodes type describes the structure\r\n * of objects that contain PKCE code\r\n * challenge and verifier pairs\r\n */\r\nexport type PkceCodes = {\r\n    verifier: string,\r\n    challenge: string\r\n};\r\n\r\nexport type SignedHttpRequestParameters = Pick<BaseAuthRequest, \"resourceRequestMethod\" | \"resourceRequestUri\" | \"shrClaims\" | \"shrNonce\" > & {\r\n    correlationId?: string\r\n};\r\n\r\n/**\r\n * Interface for crypto functions used by library\r\n */\r\nexport interface ICrypto {\r\n    /**\r\n     * Creates a guid randomly.\r\n     */\r\n    createNewGuid(): string;\r\n    /**\r\n     * base64 Encode string\r\n     * @param input \r\n     */\r\n    base64Encode(input: string): string;\r\n    /**\r\n     * base64 decode string\r\n     * @param input \r\n     */\r\n    base64Decode(input: string): string;\r\n    /**\r\n     * Generate PKCE codes for OAuth. See RFC here: https://tools.ietf.org/html/rfc7636\r\n     */\r\n    generatePkceCodes(): Promise<PkceCodes>;\r\n    /**\r\n     * Generates an JWK RSA S256 Thumbprint\r\n     * @param request\r\n     */\r\n    getPublicKeyThumbprint(request: SignedHttpRequestParameters): Promise<string>;\r\n    /**\r\n     * Removes cryptographic keypair from key store matching the keyId passed in\r\n     * @param kid \r\n     */\r\n    removeTokenBindingKey(kid: string): Promise<boolean>;\r\n    /**\r\n     * Removes all cryptographic keys from IndexedDB storage\r\n     */\r\n    clearKeystore(): Promise<boolean>;\r\n    /** \r\n     * Returns a signed proof-of-possession token with a given acces token that contains a cnf claim with the required kid.\r\n     * @param accessToken \r\n     */\r\n    signJwt(payload: SignedHttpRequest, kid: string, correlationId?: string): Promise<string>;\r\n    /**\r\n     * Returns the SHA-256 hash of an input string\r\n     * @param plainText\r\n     */\r\n    hashString(plainText: string): Promise<string>;\r\n}\r\n\r\nexport const DEFAULT_CRYPTO_IMPLEMENTATION: ICrypto = {\r\n    createNewGuid: (): string => {\r\n        const notImplErr = \"Crypto interface - createNewGuid() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    base64Decode: (): string => {\r\n        const notImplErr = \"Crypto interface - base64Decode() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    base64Encode: (): string => {\r\n        const notImplErr = \"Crypto interface - base64Encode() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async generatePkceCodes(): Promise<PkceCodes> {\r\n        const notImplErr = \"Crypto interface - generatePkceCodes() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async getPublicKeyThumbprint(): Promise<string> {\r\n        const notImplErr = \"Crypto interface - getPublicKeyThumbprint() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async removeTokenBindingKey(): Promise<boolean> {\r\n        const notImplErr = \"Crypto interface - removeTokenBindingKey() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async clearKeystore(): Promise<boolean> {\r\n        const notImplErr = \"Crypto interface - clearKeystore() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async signJwt(): Promise<string> {\r\n        const notImplErr = \"Crypto interface - signJwt() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async hashString(): Promise<string> {\r\n        const notImplErr = \"Crypto interface - hashString() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAoEU,IAAA,6BAA6B,GAAY;AAClD,IAAA,aAAa,EAAE,YAAA;QACX,IAAM,UAAU,GAAG,6DAA6D,CAAC;AACjF,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD;AACD,IAAA,YAAY,EAAE,YAAA;QACV,IAAM,UAAU,GAAG,4DAA4D,CAAC;AAChF,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD;AACD,IAAA,YAAY,EAAE,YAAA;QACV,IAAM,UAAU,GAAG,4DAA4D,CAAC;AAChF,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD;AACK,IAAA,iBAAiB,EAAvB,YAAA;;;;gBACU,UAAU,GAAG,iEAAiE,CAAC;AACrF,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;AACK,IAAA,sBAAsB,EAA5B,YAAA;;;;gBACU,UAAU,GAAG,sEAAsE,CAAC;AAC1F,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;AACK,IAAA,qBAAqB,EAA3B,YAAA;;;;gBACU,UAAU,GAAG,qEAAqE,CAAC;AACzF,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;AACK,IAAA,aAAa,EAAnB,YAAA;;;;gBACU,UAAU,GAAG,6DAA6D,CAAC;AACjF,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;AACK,IAAA,OAAO,EAAb,YAAA;;;;gBACU,UAAU,GAAG,uDAAuD,CAAC;AAC3E,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;AACK,IAAA,UAAU,EAAhB,YAAA;;;;gBACU,UAAU,GAAG,0DAA0D,CAAC;AAC9E,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;;;;;"}