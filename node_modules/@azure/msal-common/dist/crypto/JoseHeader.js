/*! @azure/msal-common v13.3.3 2024-06-06 */
'use strict';
import { JoseHeaderError } from '../error/JoseHeaderError.js';
import { JsonTypes } from '../utils/Constants.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var JoseHeader = /** @class */ (function () {
    function JoseHeader(options) {
        this.typ = options.typ;
        this.alg = options.alg;
        this.kid = options.kid;
    }
    /**
     * Builds SignedHttpRequest formatted JOSE Header from the
     * JOSE Header options provided or previously set on the object and returns
     * the stringified header object.
     * Throws if keyId or algorithm aren't provided since they are required for Access Token Binding.
     * @param shrHeaderOptions
     * @returns
     */
    JoseHeader.getShrHeaderString = function (shrHeaderOptions) {
        // KeyID is required on the SHR header
        if (!shrHeaderOptions.kid) {
            throw JoseHeaderError.createMissingKidError();
        }
        // Alg is required on the SHR header
        if (!shrHeaderOptions.alg) {
            throw JoseHeaderError.createMissingAlgError();
        }
        var shrHeader = new JoseHeader({
            // Access Token PoP headers must have type pop, but the type header can be overriden for special cases
            typ: shrHeaderOptions.typ || JsonTypes.Pop,
            kid: shrHeaderOptions.kid,
            alg: shrHeaderOptions.alg
        });
        return JSON.stringify(shrHeader);
    };
    return JoseHeader;
}());

export { JoseHeader };
//# sourceMappingURL=JoseHeader.js.map
