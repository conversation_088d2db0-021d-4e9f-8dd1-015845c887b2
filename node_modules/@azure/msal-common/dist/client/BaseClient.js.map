{"version": 3, "file": "BaseClient.js", "sources": ["../../src/client/BaseClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientConfiguration, buildClientConfiguration, CommonClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { INetworkModule } from \"../network/INetworkModule\";\r\nimport { NetworkManager, NetworkResponse } from \"../network/NetworkManager\";\r\nimport { ICrypto } from \"../crypto/ICrypto\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { Logger } from \"../logger/Logger\";\r\nimport { Constants, HeaderNames } from \"../utils/Constants\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { CacheManager } from \"../cache/CacheManager\";\r\nimport { ServerTelemetryManager } from \"../telemetry/server/ServerTelemetryManager\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { version, name } from \"../packageMetadata\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { CcsCredential, CcsCredentialType } from \"../account/CcsCredential\";\r\nimport { buildClientInfoFromHomeAccountId } from \"../account/ClientInfo\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { BaseAuthRequest } from \"../request/BaseAuthRequest\";\r\n\r\n/**\r\n * Base application class which will construct requests to send to and handle responses from the Microsoft STS using the authorization code flow.\r\n */\r\nexport abstract class BaseClient {\r\n    // Logger object\r\n    public logger: Logger;\r\n\r\n    // Application config\r\n    protected config: CommonClientConfiguration;\r\n\r\n    // Crypto Interface\r\n    protected cryptoUtils: ICrypto;\r\n\r\n    // Storage Interface\r\n    protected cacheManager: CacheManager;\r\n\r\n    // Network Interface\r\n    protected networkClient: INetworkModule;\r\n\r\n    // Server Telemetry Manager\r\n    protected serverTelemetryManager: ServerTelemetryManager | null;\r\n\r\n    // Network Manager\r\n    protected networkManager: NetworkManager;\r\n\r\n    // Default authority object\r\n    public authority: Authority;\r\n\r\n    // Performance telemetry client\r\n    protected performanceClient?: IPerformanceClient;\r\n\r\n    protected constructor(configuration: ClientConfiguration, performanceClient?: IPerformanceClient) {\r\n        // Set the configuration\r\n        this.config = buildClientConfiguration(configuration);\r\n\r\n        // Initialize the logger\r\n        this.logger = new Logger(this.config.loggerOptions, name, version);\r\n\r\n        // Initialize crypto\r\n        this.cryptoUtils = this.config.cryptoInterface;\r\n\r\n        // Initialize storage interface\r\n        this.cacheManager = this.config.storageInterface;\r\n\r\n        // Set the network interface\r\n        this.networkClient = this.config.networkInterface;\r\n\r\n        // Set the NetworkManager\r\n        this.networkManager = new NetworkManager(this.networkClient, this.cacheManager);\r\n\r\n        // Set TelemetryManager\r\n        this.serverTelemetryManager = this.config.serverTelemetryManager;\r\n\r\n        // set Authority\r\n        this.authority = this.config.authOptions.authority;\r\n\r\n        // set performance telemetry client\r\n        this.performanceClient = performanceClient;\r\n    }\r\n\r\n    /**\r\n     * Creates default headers for requests to token endpoint\r\n     */\r\n    protected createTokenRequestHeaders(ccsCred?: CcsCredential): Record<string, string> {   \r\n        const headers: Record<string, string> = {};\r\n        headers[HeaderNames.CONTENT_TYPE] = Constants.URL_FORM_CONTENT_TYPE;\r\n        if (!this.config.systemOptions.preventCorsPreflight && ccsCred) {\r\n            switch (ccsCred.type) {\r\n                case CcsCredentialType.HOME_ACCOUNT_ID:\r\n                    try {\r\n                        const clientInfo = buildClientInfoFromHomeAccountId(ccsCred.credential);\r\n                        headers[HeaderNames.CCS_HEADER] = `Oid:${clientInfo.uid}@${clientInfo.utid}`;\r\n                    } catch (e) {\r\n                        this.logger.verbose(\"Could not parse home account ID for CCS Header: \" + e);\r\n                    }\r\n                    break;\r\n                case CcsCredentialType.UPN:\r\n                    headers[HeaderNames.CCS_HEADER] = `UPN: ${ccsCred.credential}`;\r\n                    break;\r\n            }\r\n        }   \r\n        return headers;\r\n    }\r\n\r\n    /**\r\n     * Http post to token endpoint\r\n     * @param tokenEndpoint\r\n     * @param queryString\r\n     * @param headers\r\n     * @param thumbprint\r\n     */\r\n    protected async executePostToTokenEndpoint(tokenEndpoint: string, queryString: string, headers: Record<string, string>, thumbprint: RequestThumbprint): Promise<NetworkResponse<ServerAuthorizationTokenResponse>> {\r\n        const response = await this.networkManager.sendPostRequest<ServerAuthorizationTokenResponse>(\r\n            thumbprint,\r\n            tokenEndpoint,\r\n            { body: queryString, headers: headers }\r\n        );\r\n\r\n        if (this.config.serverTelemetryManager && response.status < 500 && response.status !== 429) {\r\n            // Telemetry data successfully logged by server, clear Telemetry cache\r\n            this.config.serverTelemetryManager.clearTelemetryCache();\r\n        }\r\n\r\n        return response;\r\n    }\r\n\r\n    /**\r\n     * Updates the authority object of the client. Endpoint discovery must be completed.\r\n     * @param updatedAuthority\r\n     */\r\n    updateAuthority(updatedAuthority: Authority): void {\r\n        if (!updatedAuthority.discoveryComplete()) {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Updated authority has not completed endpoint discovery.\");\r\n        }\r\n        this.authority = updatedAuthority;\r\n    }\r\n\r\n    /**\r\n     * Creates query string for the /token request\r\n     * @param request\r\n     */\r\n    createTokenQueryParameters(request: BaseAuthRequest): string {\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        if (request.tokenQueryParameters) {\r\n            parameterBuilder.addExtraQueryParameters(request.tokenQueryParameters);\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAqBH;;AAEG;AACH,IAAA,UAAA,kBAAA,YAAA;IA4BI,SAAsB,UAAA,CAAA,aAAkC,EAAE,iBAAsC,EAAA;;AAE5F,QAAA,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAC;;AAGtD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;;QAGnE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;;QAG/C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;QAGjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;AAGlD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;;QAGhF,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;;QAGjE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;;AAGnD,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;AAEG;IACO,UAAyB,CAAA,SAAA,CAAA,yBAAA,GAAnC,UAAoC,OAAuB,EAAA;QACvD,IAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,qBAAqB,CAAC;QACpE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,EAAE;YAC5D,QAAQ,OAAO,CAAC,IAAI;gBAChB,KAAK,iBAAiB,CAAC,eAAe;oBAClC,IAAI;wBACA,IAAM,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACxE,wBAAA,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,MAAA,GAAO,UAAU,CAAC,GAAG,GAAA,GAAA,GAAI,UAAU,CAAC,IAAM,CAAC;AAChF,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;wBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kDAAkD,GAAG,CAAC,CAAC,CAAC;AAC/E,qBAAA;oBACD,MAAM;gBACV,KAAK,iBAAiB,CAAC,GAAG;oBACtB,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,OAAQ,GAAA,OAAO,CAAC,UAAY,CAAC;oBAC/D,MAAM;AACb,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;AAED;;;;;;AAMG;IACa,UAA0B,CAAA,SAAA,CAAA,0BAAA,GAA1C,UAA2C,aAAqB,EAAE,WAAmB,EAAE,OAA+B,EAAE,UAA6B,EAAA;;;;;4BAChI,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,cAAc,CAAC,eAAe,CACtD,UAAU,EACV,aAAa,EACb,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,CAC1C,CAAA,CAAA;;AAJK,wBAAA,QAAQ,GAAG,EAIhB,CAAA,IAAA,EAAA,CAAA;AAED,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;;AAExF,4BAAA,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,CAAC;AAC5D,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;;;;AACnB,KAAA,CAAA;AAED;;;AAGG;IACH,UAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,gBAA2B,EAAA;AACvC,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,EAAE;AACvC,YAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,yDAAyD,CAAC,CAAC;AAC3H,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;KACrC,CAAA;AAED;;;AAGG;IACH,UAA0B,CAAA,SAAA,CAAA,0BAAA,GAA1B,UAA2B,OAAwB,EAAA;AAC/C,QAAA,IAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAC9B,YAAA,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;IACL,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA;;;;"}