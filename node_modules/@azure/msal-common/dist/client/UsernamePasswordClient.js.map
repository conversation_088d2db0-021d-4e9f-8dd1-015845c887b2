{"version": 3, "file": "UsernamePasswordClient.js", "sources": ["../../src/client/UsernamePasswordClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { CommonUsernamePasswordRequest } from \"../request/CommonUsernamePasswordRequest\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { NetworkResponse } from \"../network/NetworkManager\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { GrantType, HeaderNames } from \"../utils/Constants\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { CcsCredentialType } from \"../account/CcsCredential\";\r\nimport { UrlString } from \"../url/UrlString\";\r\n\r\n/**\r\n * Oauth2.0 Password grant client\r\n * Note: We are only supporting public clients for password grant and for purely testing purposes\r\n */\r\nexport class UsernamePasswordClient extends BaseClient {\r\n\r\n    constructor(configuration: ClientConfiguration) {\r\n        super(configuration);\r\n    }\r\n\r\n    /**\r\n     * API to acquire a token by passing the username and password to the service in exchage of credentials\r\n     * password_grant\r\n     * @param request\r\n     */\r\n    async acquireToken(request: CommonUsernamePasswordRequest): Promise<AuthenticationResult | null> {\r\n        // @ts-ignore\r\n        const atsMeasurement = this.performanceClient?.startMeasurement(\"UsernamePasswordClientAcquireToken\", request.correlationId);\r\n        this.logger.info(\"in acquireToken call in username-password client\");\r\n\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n        const response = await this.executeTokenRequest(this.authority, request);\r\n\r\n        const httpVerToken = response.headers?.[HeaderNames.X_MS_HTTP_VERSION];\r\n        atsMeasurement?.addStaticFields({\r\n            httpVerToken\r\n        });\r\n    \r\n        const responseHandler = new ResponseHandler(\r\n            this.config.authOptions.clientId,\r\n            this.cacheManager,\r\n            this.cryptoUtils,\r\n            this.logger,\r\n            this.config.serializableCache,\r\n            this.config.persistencePlugin\r\n        );\r\n\r\n        // Validate response. This function throws a server error if an error is returned by the server.\r\n        responseHandler.validateTokenResponse(response.body);\r\n        const tokenResponse = responseHandler.handleServerTokenResponse(response.body, this.authority, reqTimestamp, request);\r\n\r\n        return tokenResponse;\r\n    }\r\n\r\n    /**\r\n     * Executes POST request to token endpoint\r\n     * @param authority\r\n     * @param request\r\n     */\r\n    private async executeTokenRequest(authority: Authority, request: CommonUsernamePasswordRequest): Promise<NetworkResponse<ServerAuthorizationTokenResponse>> {\r\n        const queryParametersString = this.createTokenQueryParameters(request);\r\n        const endpoint = UrlString.appendQueryString(authority.tokenEndpoint, queryParametersString);\r\n        const requestBody = this.createTokenRequestBody(request);\r\n        const headers: Record<string, string> = this.createTokenRequestHeaders({\r\n            credential: request.username,\r\n            type: CcsCredentialType.UPN\r\n        });\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: this.config.authOptions.clientId,\r\n            authority: authority.canonicalAuthority,\r\n            scopes: request.scopes,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n\r\n        return this.executePostToTokenEndpoint(endpoint, requestBody, headers, thumbprint);\r\n    }\r\n\r\n    /**\r\n     * Generates a map for all the params to be sent to the service\r\n     * @param request\r\n     */\r\n    private createTokenRequestBody(request: CommonUsernamePasswordRequest): string {\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n        parameterBuilder.addUsername(request.username);\r\n        parameterBuilder.addPassword(request.password);\r\n\r\n        parameterBuilder.addScopes(request.scopes);\r\n\r\n        parameterBuilder.addResponseTypeForTokenAndIdToken();\r\n\r\n        parameterBuilder.addGrantType(GrantType.RESOURCE_OWNER_PASSWORD_GRANT);\r\n        parameterBuilder.addClientInfo();\r\n\r\n        parameterBuilder.addLibraryInfo(this.config.libraryInfo);\r\n        parameterBuilder.addApplicationTelemetry(this.config.telemetry.application);\r\n        parameterBuilder.addThrottling();\r\n\r\n        if (this.serverTelemetryManager) {\r\n            parameterBuilder.addServerTelemetry(this.serverTelemetryManager);\r\n        }\r\n\r\n        const correlationId = request.correlationId || this.config.cryptoInterface.createNewGuid();\r\n        parameterBuilder.addCorrelationId(correlationId);\r\n\r\n        if (this.config.clientCredentials.clientSecret) {\r\n            parameterBuilder.addClientSecret(this.config.clientCredentials.clientSecret);\r\n        }\r\n\r\n        if (this.config.clientCredentials.clientAssertion) {\r\n            const clientAssertion = this.config.clientCredentials.clientAssertion;\r\n            parameterBuilder.addClientAssertion(clientAssertion.assertion);\r\n            parameterBuilder.addClientAssertionType(clientAssertion.assertionType);\r\n        }\r\n\r\n        if (!StringUtils.isEmptyObj(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n\r\n        if (this.config.systemOptions.preventCorsPreflight && request.username) {\r\n            parameterBuilder.addCcsUpn(request.username);\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;AAkBH;;;AAGG;AACH,IAAA,sBAAA,kBAAA,UAAA,MAAA,EAAA;IAA4C,SAAU,CAAA,sBAAA,EAAA,MAAA,CAAA,CAAA;AAElD,IAAA,SAAA,sBAAA,CAAY,aAAkC,EAAA;AAC1C,QAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,aAAa,CAAC,IAAA,IAAA,CAAA;KACvB;AAED;;;;AAIG;IACG,sBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAsC,EAAA;;;;;;;AAE/C,wBAAA,cAAc,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC,oCAAoC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7H,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;AAE/D,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;wBAC3B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAlE,wBAAA,QAAQ,GAAG,EAAuD,CAAA,IAAA,EAAA,CAAA;wBAElE,YAAY,GAAA,CAAA,EAAA,GAAG,QAAQ,CAAC,OAAO,0CAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACvE,wBAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,eAAe,CAAC;AAC5B,4BAAA,YAAY,EAAA,YAAA;yBACf,CAAE,CAAA;AAEG,wBAAA,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;;AAGF,wBAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC/C,wBAAA,aAAa,GAAG,eAAe,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAEtH,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,CAAA;;;;AACxB,KAAA,CAAA;AAED;;;;AAIG;AACW,IAAA,sBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAjC,UAAkC,SAAoB,EAAE,OAAsC,EAAA;;;;AACpF,gBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;gBACjE,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AACvF,gBAAA,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AACnD,gBAAA,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAAC;oBACnE,UAAU,EAAE,OAAO,CAAC,QAAQ;oBAC5B,IAAI,EAAE,iBAAiB,CAAC,GAAG;AAC9B,iBAAA,CAAC,CAAC;AACG,gBAAA,UAAU,GAAsB;AAClC,oBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;oBAC1C,SAAS,EAAE,SAAS,CAAC,kBAAkB;oBACvC,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;oBAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;oBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;oBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;iBACzB,CAAC;AAEF,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAA;;;AACtF,KAAA,CAAA;AAED;;;AAGG;IACK,sBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,OAAsC,EAAA;AACjE,QAAA,IAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/D,QAAA,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAA,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAE/C,QAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE3C,gBAAgB,CAAC,iCAAiC,EAAE,CAAC;AAErD,QAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;QACvE,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACzD,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5E,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAC3F,QAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAEjD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;YAC5C,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAChF,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;YAC/C,IAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AACtE,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC/D,YAAA,gBAAgB,CAAC,sBAAsB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAChJ,YAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,SAAA;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpE,YAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAChD,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;IACL,OAAC,sBAAA,CAAA;AAAD,CArHA,CAA4C,UAAU,CAqHrD;;;;"}