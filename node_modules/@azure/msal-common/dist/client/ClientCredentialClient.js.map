{"version": 3, "file": "ClientCredentialClient.js", "sources": ["../../src/client/ClientCredentialClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { ScopeSet } from \"../request/ScopeSet\";\r\nimport { GrantType , CredentialType, CacheOutcome, Constants, AuthenticationScheme } from \"../utils/Constants\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { CommonClientCredentialRequest } from \"../request/CommonClientCredentialRequest\";\r\nimport { CredentialFilter } from \"../cache/utils/CacheTypes\";\r\nimport { AccessTokenEntity } from \"../cache/entities/AccessTokenEntity\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { IAppTokenProvider } from \"../config/AppTokenProvider\";\r\nimport { UrlString } from \"../url/UrlString\";\r\n\r\n/**\r\n * OAuth2.0 client credential grant\r\n */\r\nexport class ClientCredentialClient extends BaseClient {\r\n\r\n    private scopeSet: ScopeSet;\r\n    private readonly appTokenProvider?: IAppTokenProvider;\r\n\r\n    constructor(configuration: ClientConfiguration, appTokenProvider?: IAppTokenProvider) {\r\n        super(configuration);\r\n        this.appTokenProvider = appTokenProvider;\r\n    }\r\n\r\n    /**\r\n     * Public API to acquire a token with ClientCredential Flow for Confidential clients\r\n     * @param request\r\n     */\r\n    public async acquireToken(request: CommonClientCredentialRequest): Promise<AuthenticationResult | null> {\r\n\r\n        this.scopeSet = new ScopeSet(request.scopes || []);\r\n\r\n        if (request.skipCache) {\r\n            return await this.executeTokenRequest(request, this.authority);\r\n        }\r\n\r\n        const cachedAuthenticationResult = await this.getCachedAuthenticationResult(request);\r\n        if (cachedAuthenticationResult) {\r\n            return cachedAuthenticationResult;\r\n        } else {\r\n            return await this.executeTokenRequest(request, this.authority);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * looks up cache if the tokens are cached already\r\n     */\r\n    private async getCachedAuthenticationResult(request: CommonClientCredentialRequest): Promise<AuthenticationResult | null> {\r\n        \r\n        const cachedAccessToken = this.readAccessTokenFromCache();\r\n\r\n        if (!cachedAccessToken) {\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.NO_CACHED_ACCESS_TOKEN);\r\n            return null;\r\n        }\r\n\r\n        if (TimeUtils.isTokenExpired(cachedAccessToken.expiresOn, this.config.systemOptions.tokenRenewalOffsetSeconds)) {\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.CACHED_ACCESS_TOKEN_EXPIRED);\r\n            return null;\r\n        }\r\n\r\n        return await ResponseHandler.generateAuthenticationResult(\r\n            this.cryptoUtils,\r\n            this.authority,\r\n            {\r\n                account: null,\r\n                idToken: null,\r\n                accessToken: cachedAccessToken,\r\n                refreshToken: null,\r\n                appMetadata: null\r\n            },\r\n            true,\r\n            request\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Reads access token from the cache\r\n     */\r\n    private readAccessTokenFromCache(): AccessTokenEntity | null {\r\n        const accessTokenFilter: CredentialFilter = {\r\n            homeAccountId: Constants.EMPTY_STRING,\r\n            environment: this.authority.canonicalAuthorityUrlComponents.HostNameAndPort,\r\n            credentialType: CredentialType.ACCESS_TOKEN,\r\n            clientId: this.config.authOptions.clientId,\r\n            realm: this.authority.tenant,\r\n            target: ScopeSet.createSearchScopes(this.scopeSet.asArray())\r\n        };\r\n\r\n        const accessTokens = this.cacheManager.getAccessTokensByFilter(accessTokenFilter);\r\n        if (accessTokens.length < 1) {\r\n            return null;\r\n        } else if (accessTokens.length > 1) {\r\n            throw ClientAuthError.createMultipleMatchingTokensInCacheError();\r\n        }\r\n        return accessTokens[0] as AccessTokenEntity;\r\n    }\r\n\r\n    /**\r\n     * Makes a network call to request the token from the service\r\n     * @param request\r\n     * @param authority\r\n     */\r\n    private async executeTokenRequest(request: CommonClientCredentialRequest, authority: Authority)\r\n        : Promise<AuthenticationResult | null> {\r\n        \r\n        let serverTokenResponse: ServerAuthorizationTokenResponse;\r\n        let reqTimestamp: number;\r\n\r\n        if (this.appTokenProvider) {\r\n            this.logger.info(\"Using appTokenProvider extensibility.\");\r\n\r\n            const appTokenPropviderParameters = {\r\n                correlationId: request.correlationId,\r\n                tenantId: this.config.authOptions.authority.tenant,\r\n                scopes: request.scopes,\r\n                claims: request.claims,\r\n            };\r\n\r\n            reqTimestamp = TimeUtils.nowSeconds();\r\n            const appTokenProviderResult = await this.appTokenProvider(appTokenPropviderParameters);\r\n\r\n            serverTokenResponse = {\r\n                access_token: appTokenProviderResult.accessToken, \r\n                expires_in: appTokenProviderResult.expiresInSeconds,\r\n                refresh_in: appTokenProviderResult.refreshInSeconds,\r\n                token_type : AuthenticationScheme.BEARER\r\n            };\r\n        } else {\r\n            const queryParametersString = this.createTokenQueryParameters(request);\r\n            const endpoint = UrlString.appendQueryString(authority.tokenEndpoint, queryParametersString);\r\n            const requestBody = this.createTokenRequestBody(request);\r\n            const headers: Record<string, string> = this.createTokenRequestHeaders();\r\n            const thumbprint: RequestThumbprint = {\r\n                clientId: this.config.authOptions.clientId,\r\n                authority: request.authority,\r\n                scopes: request.scopes,\r\n                claims: request.claims,\r\n                authenticationScheme: request.authenticationScheme,\r\n                resourceRequestMethod: request.resourceRequestMethod,\r\n                resourceRequestUri: request.resourceRequestUri,\r\n                shrClaims: request.shrClaims,\r\n                sshKid: request.sshKid\r\n            };\r\n    \r\n            reqTimestamp = TimeUtils.nowSeconds();\r\n            const response = await this.executePostToTokenEndpoint(endpoint, requestBody, headers, thumbprint);\r\n            serverTokenResponse = response.body;\r\n        }\r\n\r\n        const responseHandler = new ResponseHandler(\r\n            this.config.authOptions.clientId,\r\n            this.cacheManager,\r\n            this.cryptoUtils,\r\n            this.logger,\r\n            this.config.serializableCache,\r\n            this.config.persistencePlugin\r\n        );\r\n\r\n        responseHandler.validateTokenResponse(serverTokenResponse);\r\n       \r\n        const tokenResponse = await responseHandler.handleServerTokenResponse(\r\n            serverTokenResponse,\r\n            this.authority,\r\n            reqTimestamp,\r\n            request\r\n        );\r\n\r\n        return tokenResponse;\r\n    }\r\n\r\n    /**\r\n     * generate the request to the server in the acceptable format\r\n     * @param request\r\n     */\r\n    private createTokenRequestBody(request: CommonClientCredentialRequest): string {\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n\r\n        parameterBuilder.addScopes(request.scopes, false);\r\n\r\n        parameterBuilder.addGrantType(GrantType.CLIENT_CREDENTIALS_GRANT);\r\n\r\n        parameterBuilder.addLibraryInfo(this.config.libraryInfo);\r\n        parameterBuilder.addApplicationTelemetry(this.config.telemetry.application);\r\n\r\n        parameterBuilder.addThrottling();\r\n        \r\n        if (this.serverTelemetryManager) {\r\n            parameterBuilder.addServerTelemetry(this.serverTelemetryManager);\r\n        }\r\n\r\n        const correlationId = request.correlationId || this.config.cryptoInterface.createNewGuid();\r\n        parameterBuilder.addCorrelationId(correlationId);\r\n\r\n        if (this.config.clientCredentials.clientSecret) {\r\n            parameterBuilder.addClientSecret(this.config.clientCredentials.clientSecret);\r\n        }\r\n\r\n        // Use clientAssertion from request, fallback to client assertion in base configuration\r\n        const clientAssertion = request.clientAssertion || this.config.clientCredentials.clientAssertion;\r\n\r\n        if (clientAssertion) {\r\n            parameterBuilder.addClientAssertion(clientAssertion.assertion);\r\n            parameterBuilder.addClientAssertionType(clientAssertion.assertionType);\r\n        }\r\n\r\n        if (!StringUtils.isEmptyObj(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAqBH;;AAEG;AACH,IAAA,sBAAA,kBAAA,UAAA,MAAA,EAAA;IAA4C,SAAU,CAAA,sBAAA,EAAA,MAAA,CAAA,CAAA;IAKlD,SAAY,sBAAA,CAAA,aAAkC,EAAE,gBAAoC,EAAA;QAApF,IACI,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,aAAa,CAAC,IAEvB,IAAA,CAAA;AADG,QAAA,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;;KAC5C;AAED;;;AAGG;IACU,sBAAY,CAAA,SAAA,CAAA,YAAA,GAAzB,UAA0B,OAAsC,EAAA;;;;;;AAE5D,wBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;6BAE/C,OAAO,CAAC,SAAS,EAAjB,OAAiB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACV,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA,CAAA;AAA9D,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAuD,CAAC,CAAA;AAGhC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA9E,wBAAA,0BAA0B,GAAG,EAAiD,CAAA,IAAA,EAAA,CAAA;AAChF,wBAAA,IAAA,CAAA,0BAA0B,EAA1B,OAA0B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC1B,wBAAA,OAAA,CAAA,CAAA,aAAO,0BAA0B,CAAC,CAAA;4BAE3B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA,CAAA;AAA9D,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAuD,CAAC,CAAA;;;;AAEtE,KAAA,CAAA;AAED;;AAEG;IACW,sBAA6B,CAAA,SAAA,CAAA,6BAAA,GAA3C,UAA4C,OAAsC,EAAA;;;;;;;AAExE,wBAAA,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;wBAE1D,IAAI,CAAC,iBAAiB,EAAE;4BACpB,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,sBAAsB,CAAE,CAAA;AAClF,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAED,wBAAA,IAAI,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,CAAC,EAAE;4BAC5G,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,2BAA2B,CAAE,CAAA;AACvF,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;wBAEM,OAAM,CAAA,CAAA,YAAA,eAAe,CAAC,4BAA4B,CACrD,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd;AACI,gCAAA,OAAO,EAAE,IAAI;AACb,gCAAA,OAAO,EAAE,IAAI;AACb,gCAAA,WAAW,EAAE,iBAAiB;AAC9B,gCAAA,YAAY,EAAE,IAAI;AAClB,gCAAA,WAAW,EAAE,IAAI;AACpB,6BAAA,EACD,IAAI,EACJ,OAAO,CACV,CAAA,CAAA;AAZD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAYN,CAAC,CAAA;;;;AACL,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,sBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAhC,YAAA;AACI,QAAA,IAAM,iBAAiB,GAAqB;YACxC,aAAa,EAAE,SAAS,CAAC,YAAY;AACrC,YAAA,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,+BAA+B,CAAC,eAAe;YAC3E,cAAc,EAAE,cAAc,CAAC,YAAY;AAC3C,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;AAC1C,YAAA,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;YAC5B,MAAM,EAAE,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;SAC/D,CAAC;QAEF,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;AAClF,QAAA,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAM,aAAA,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,YAAA,MAAM,eAAe,CAAC,wCAAwC,EAAE,CAAC;AACpE,SAAA;AACD,QAAA,OAAO,YAAY,CAAC,CAAC,CAAsB,CAAC;KAC/C,CAAA;AAED;;;;AAIG;AACW,IAAA,sBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAjC,UAAkC,OAAsC,EAAE,SAAoB,EAAA;;;;;;6BAMtF,IAAI,CAAC,gBAAgB,EAArB,OAAqB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACrB,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;AAEpD,wBAAA,2BAA2B,GAAG;4BAChC,aAAa,EAAE,OAAO,CAAC,aAAa;4BACpC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM;4BAClD,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,EAAE,OAAO,CAAC,MAAM;yBACzB,CAAC;AAEF,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACP,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAA,CAAA;;AAAjF,wBAAA,sBAAsB,GAAG,EAAwD,CAAA,IAAA,EAAA,CAAA;AAEvF,wBAAA,mBAAmB,GAAG;4BAClB,YAAY,EAAE,sBAAsB,CAAC,WAAW;4BAChD,UAAU,EAAE,sBAAsB,CAAC,gBAAgB;4BACnD,UAAU,EAAE,sBAAsB,CAAC,gBAAgB;4BACnD,UAAU,EAAG,oBAAoB,CAAC,MAAM;yBAC3C,CAAC;;;AAEI,wBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;wBACjE,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AACvF,wBAAA,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AACnD,wBAAA,OAAO,GAA2B,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACnE,wBAAA,UAAU,GAAsB;AAClC,4BAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;4BAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;4BAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;yBACzB,CAAC;AAEF,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACrB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA,CAAA;;AAA5F,wBAAA,QAAQ,GAAG,EAAiF,CAAA,IAAA,EAAA,CAAA;AAClG,wBAAA,mBAAmB,GAAG,QAAQ,CAAC,IAAI,CAAC;;;AAGlC,wBAAA,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AAEF,wBAAA,eAAe,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;AAErC,wBAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CAAC,yBAAyB,CACjE,mBAAmB,EACnB,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,CACV,CAAA,CAAA;;AALK,wBAAA,aAAa,GAAG,EAKrB,CAAA,IAAA,EAAA,CAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,CAAA;;;;AACxB,KAAA,CAAA;AAED;;;AAGG;IACK,sBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,OAAsC,EAAA;AACjE,QAAA,IAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/D,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAElD,QAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAElE,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACzD,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAE5E,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAC3F,QAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAEjD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;YAC5C,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAChF,SAAA;;AAGD,QAAA,IAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AAEjG,QAAA,IAAI,eAAe,EAAE;AACjB,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC/D,YAAA,gBAAgB,CAAC,sBAAsB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAChJ,YAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;IACL,OAAC,sBAAA,CAAA;AAAD,CAxMA,CAA4C,UAAU,CAwMrD;;;;"}