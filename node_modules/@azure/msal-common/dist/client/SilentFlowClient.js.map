{"version": 3, "file": "SilentFlowClient.js", "sources": ["../../src/client/SilentFlowClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { CommonSilentFlowRequest } from \"../request/CommonSilentFlowRequest\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { AuthToken } from \"../account/AuthToken\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { RefreshTokenClient } from \"./RefreshTokenClient\";\r\nimport { ClientAuthError, ClientAuthErrorMessage } from \"../error/ClientAuthError\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { CacheRecord } from \"../cache/entities/CacheRecord\";\r\nimport { CacheOutcome } from \"../utils/Constants\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\n\r\nexport class SilentFlowClient extends BaseClient {\r\n    \r\n    constructor(configuration: ClientConfiguration, performanceClient?: IPerformanceClient) {\r\n        super(configuration,performanceClient);\r\n    }\r\n    \r\n    /**\r\n     * Retrieves a token from cache if it is still valid, or uses the cached refresh token to renew\r\n     * the given token and returns the renewed token\r\n     * @param request\r\n     */\r\n    async acquireToken(request: CommonSilentFlowRequest): Promise<AuthenticationResult> {\r\n        try {\r\n            return await this.acquireCachedToken(request);\r\n        } catch (e) {\r\n            if (e instanceof ClientAuthError && e.errorCode === ClientAuthErrorMessage.tokenRefreshRequired.code) {\r\n                const refreshTokenClient = new RefreshTokenClient(this.config, this.performanceClient);\r\n                return refreshTokenClient.acquireTokenByRefreshToken(request);\r\n            } else {\r\n                throw e;\r\n            }\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Retrieves token from cache or throws an error if it must be refreshed.\r\n     * @param request\r\n     */\r\n    async acquireCachedToken(request: CommonSilentFlowRequest): Promise<AuthenticationResult> {\r\n        // Cannot renew token if no request object is given.\r\n        if (!request) {\r\n            throw ClientConfigurationError.createEmptyTokenRequestError();\r\n        }\r\n\r\n        if (request.forceRefresh) {\r\n            // Must refresh due to present force_refresh flag.\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.FORCE_REFRESH);\r\n            this.logger.info(\"SilentFlowClient:acquireCachedToken - Skipping cache because forceRefresh is true.\");\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        } else if (!this.config.cacheOptions.claimsBasedCachingEnabled && !StringUtils.isEmptyObj(request.claims)) {\r\n            // Must refresh due to presence of claims in request preventing cache lookup\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.CLAIMS_REQUESTED_CACHE_SKIPPED);\r\n            this.logger.info(\"SilentFlowClient:acquireCachedToken - Skipping cache because claims-based caching is disabled and claims were requested.\");\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        }\r\n\r\n        // We currently do not support silent flow for account === null use cases; This will be revisited for confidential flow usecases\r\n        if (!request.account) {\r\n            throw ClientAuthError.createNoAccountInSilentRequestError();\r\n        }\r\n\r\n        const environment = request.authority || this.authority.getPreferredCache();\r\n\r\n        const cacheRecord = this.cacheManager.readCacheRecord(request.account, request, environment);\r\n\r\n        if (!cacheRecord.accessToken) {\r\n            // Must refresh due to non-existent access_token.\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.NO_CACHED_ACCESS_TOKEN);\r\n            this.logger.info(\"SilentFlowClient:acquireCachedToken - No access token found in cache for the given properties.\");\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        } else if (\r\n            TimeUtils.wasClockTurnedBack(cacheRecord.accessToken.cachedAt) ||\r\n            TimeUtils.isTokenExpired(cacheRecord.accessToken.expiresOn, this.config.systemOptions.tokenRenewalOffsetSeconds)\r\n        ) {\r\n            // Must refresh due to expired access_token.\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.CACHED_ACCESS_TOKEN_EXPIRED);\r\n            this.logger.info(`SilentFlowClient:acquireCachedToken - Cached access token is expired or will expire within ${this.config.systemOptions.tokenRenewalOffsetSeconds} seconds.`);\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        } else if (cacheRecord.accessToken.refreshOn && TimeUtils.isTokenExpired(cacheRecord.accessToken.refreshOn, 0)) {\r\n            // Must refresh due to the refresh_in value.\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.REFRESH_CACHED_ACCESS_TOKEN);\r\n            this.logger.info(\"SilentFlowClient:acquireCachedToken - Cached access token's refreshOn property has been exceeded'.\");\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        }\r\n\r\n        if (this.config.serverTelemetryManager) {\r\n            this.config.serverTelemetryManager.incrementCacheHits();\r\n        }\r\n\r\n        return await this.generateResultFromCacheRecord(cacheRecord, request);\r\n    }\r\n\r\n    /**\r\n     * Helper function to build response object from the CacheRecord\r\n     * @param cacheRecord\r\n     */\r\n    private async generateResultFromCacheRecord(cacheRecord: CacheRecord, request: CommonSilentFlowRequest): Promise<AuthenticationResult> {\r\n        let idTokenObj: AuthToken | undefined;\r\n        if (cacheRecord.idToken) {\r\n            idTokenObj = new AuthToken(cacheRecord.idToken.secret, this.config.cryptoInterface);\r\n        }\r\n\r\n        // token max_age check\r\n        if (request.maxAge || (request.maxAge === 0)) {\r\n            const authTime = idTokenObj?.claims.auth_time;\r\n            if (!authTime) {\r\n                throw ClientAuthError.createAuthTimeNotFoundError();\r\n            }\r\n\r\n            AuthToken.checkMaxAge(authTime, request.maxAge);\r\n        }\r\n\r\n        return await ResponseHandler.generateAuthenticationResult(\r\n            this.cryptoUtils,\r\n            this.authority,\r\n            cacheRecord,\r\n            true,\r\n            request,\r\n            idTokenObj\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAiBH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAU,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;IAE5C,SAAY,gBAAA,CAAA,aAAkC,EAAE,iBAAsC,EAAA;eAClF,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,aAAa,EAAC,iBAAiB,CAAC,IAAA,IAAA,CAAA;KACzC;AAED;;;;AAIG;IACG,gBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,OAAgC,EAAA;;;;;;;AAEpC,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA,CAAA;AAA7C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAsC,CAAC,CAAA;;;AAE9C,wBAAA,IAAI,GAAC,YAAY,eAAe,IAAI,GAAC,CAAC,SAAS,KAAK,sBAAsB,CAAC,oBAAoB,CAAC,IAAI,EAAE;AAC5F,4BAAA,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACvF,4BAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAA;AACjE,yBAAA;AAAM,6BAAA;AACH,4BAAA,MAAM,GAAC,CAAC;AACX,yBAAA;;;;;AAER,KAAA,CAAA;AAED;;;AAGG;IACG,gBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAxB,UAAyB,OAAgC,EAAA;;;;;;;;wBAErD,IAAI,CAAC,OAAO,EAAE;AACV,4BAAA,MAAM,wBAAwB,CAAC,4BAA4B,EAAE,CAAC;AACjE,yBAAA;wBAED,IAAI,OAAO,CAAC,YAAY,EAAE;;4BAEtB,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,aAAa,CAAE,CAAA;AACzE,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;AACvG,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;AAAM,6BAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,yBAAyB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;4BAEvG,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,8BAA8B,CAAE,CAAA;AAC1F,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0HAA0H,CAAC,CAAC;AAC7I,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;;AAGD,wBAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAClB,4BAAA,MAAM,eAAe,CAAC,mCAAmC,EAAE,CAAC;AAC/D,yBAAA;wBAEK,WAAW,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;AAEtE,wBAAA,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AAE7F,wBAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;;4BAE1B,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,sBAAsB,CAAE,CAAA;AAClF,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;AACnH,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;6BAAM,IACH,SAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC9D,4BAAA,SAAS,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,CAAC,EAClH;;4BAEE,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,2BAA2B,CAAE,CAAA;AACvF,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6FAA8F,GAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,GAAA,WAAW,CAAC,CAAC;AAC/K,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;AAAM,6BAAA,IAAI,WAAW,CAAC,WAAW,CAAC,SAAS,IAAI,SAAS,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE;;4BAE5G,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,2BAA2B,CAAE,CAAA;AACvF,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oGAAoG,CAAC,CAAC;AACvH,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;AAED,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;AACpC,4BAAA,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;AAC3D,yBAAA;wBAEM,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA,CAAA;AAArE,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA8D,CAAC,CAAA;;;;AACzE,KAAA,CAAA;AAED;;;AAGG;AACW,IAAA,gBAAA,CAAA,SAAA,CAAA,6BAA6B,GAA3C,UAA4C,WAAwB,EAAE,OAAgC,EAAA;;;;;;wBAElG,IAAI,WAAW,CAAC,OAAO,EAAE;AACrB,4BAAA,UAAU,GAAG,IAAI,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AACvF,yBAAA;;wBAGD,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;4BACpC,QAAQ,GAAG,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,MAAM,CAAC,SAAS,CAAC;4BAC9C,IAAI,CAAC,QAAQ,EAAE;AACX,gCAAA,MAAM,eAAe,CAAC,2BAA2B,EAAE,CAAC;AACvD,6BAAA;4BAED,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,yBAAA;wBAEM,OAAM,CAAA,CAAA,YAAA,eAAe,CAAC,4BAA4B,CACrD,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,WAAW,EACX,IAAI,EACJ,OAAO,EACP,UAAU,CACb,CAAA,CAAA;AAPD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAON,CAAC,CAAA;;;;AACL,KAAA,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CA/GA,CAAsC,UAAU,CA+G/C;;;;"}