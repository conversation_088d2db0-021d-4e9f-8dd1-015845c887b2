{"version": 3, "file": "IPerformanceClient.d.ts", "sourceRoot": "", "sources": ["../../../src/telemetry/performance/IPerformanceClient.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACjG,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,oBAAY,2BAA2B,GAAG,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,IAAI,CAAC;AAE/E,oBAAY,0BAA0B,GAAG;IACrC,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,KAAK,gBAAgB,GAAG,IAAI,CAAC;IAC/E,kBAAkB,EAAE,MAAM,IAAI,CAAC;IAC/B,eAAe,EAAE,CAAC,YAAY,EAAE,YAAY,KAAK,IAAI,CAAC;IACtD,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;IACxC,KAAK,EAAE,gBAAgB,CAAC;IACxB,WAAW,EAAE,uBAAuB,CAAA;CACvC,CAAC;AAEF,MAAM,WAAW,kBAAkB;IAC/B,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,0BAA0B,CAAC;IACrG,cAAc,CAAC,KAAK,EAAE,gBAAgB,GAAG,gBAAgB,GAAG,IAAI,CAAC;IACjE,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACjD,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACzE,yBAAyB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC;IACvD,sBAAsB,CAAC,QAAQ,EAAE,2BAA2B,GAAG,MAAM,CAAC;IACtE,UAAU,CAAC,MAAM,EAAE,gBAAgB,EAAE,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpE,4BAA4B,CAAC,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,uBAAuB,CAAC;IAClG,2BAA2B,CAAC,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,uBAAuB,CAAC;IACjG,UAAU,IAAI,MAAM,CAAC;IACrB,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;IACvE,mBAAmB,CAAC,SAAS,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACjI,eAAe,CAAC,SAAS,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC/E;AAED;;GAEG;AACH,oBAAY,gBAAgB,GAAG;IAC3B;;OAEG;IACH,SAAS,EAAE,iBAAiB,CAAC;IAE7B;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC/B,CAAC"}