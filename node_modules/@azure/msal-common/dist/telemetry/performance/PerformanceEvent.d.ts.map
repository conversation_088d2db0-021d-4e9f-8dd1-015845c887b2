{"version": 3, "file": "PerformanceEvent.d.ts", "sourceRoot": "", "sources": ["../../../src/telemetry/performance/PerformanceEvent.ts"], "names": [], "mappings": "AAKA;;;;;GAKG;AACH,oBAAY,iBAAiB;IAEzB;;;OAGG;IACH,kBAAkB,uBAAuB;IAEzC;;;OAGG;IACH,0BAA0B,+BAA+B;IAEzD;;;OAGG;IACH,kBAAkB,uBAAuB;IAEzC;;;OAGG;IACH,uBAAuB,4BAA4B;IAEnD;;;OAGG;IACH,iBAAiB,sBAAsB;IAEvC;;;OAGG;IACH,gCAAgC,qCAAqC;IAErE;;;OAGG;IACH,iBAAiB,sBAAsB;IAEvC;;;OAGG;IACH,6BAA6B,kCAAkC;IAE/D;;;OAGG;IACH,8BAA8B,mCAAmC;IAEjE;;;OAGG;IACH,+BAA+B,oCAAoC;IAEnE;;;OAGG;IACH,SAAS,cAAc;IAEvB;;;OAGG;IACH,+CAA+C,oDAAoD;IAEnG;;;OAGG;IACH,8BAA8B,mCAAmC;IAEjE;;;OAGG;IACH,mCAAmC,wCAAwC;IAC3E;;OAEG;IACH,mCAAmC,wCAAwC;IAC3E;;OAEG;IACH,gBAAgB,oBAAoB;IACpC;;OAEG;IACH,kCAAkC,uCAAuC;IACzE;;OAEG;IACH,oBAAoB,yBAAyB;IAE7C;;OAEG;IACH,qCAAqC,0CAA0C;IAE/E;;OAEG;IACH,8BAA8B,mCAAmC;IAEjE;;OAEG;IACH,oDAAoD,yDAAyD;IAE7G;;OAEG;IACH,4CAA4C,iDAAiD;IAE7F;;OAEG;IACH,wCAAwC,6CAA6C;IAErF;;;OAGG;IACH,qBAAqB,0BAA0B;IAE/C;;;OAGG;IACH,0BAA0B,+BAA+B;IAEzD;;OAEG;IACH,qBAAqB,0BAA0B;IAE/C;;OAEG;IACH,uBAAuB,4BAA4B;IAEnD,2BAA2B,gCAAgC;IAE3D;;OAEG;IACH,6BAA6B,kCAAkC;IAE/D;;OAEG;IACH,gCAAgC,qCAAqC;IACrE,iCAAiC,sCAAsC;IACvE,sBAAsB,2BAA2B;IAEjD;;OAEG;IACH,6CAA6C,kDAAkD;IAC/F,+CAA+C,oDAAoD;IACnG,uDAAuD,4DAA4D;IACnH,2DAA2D,gEAAgE;IAE3H;;OAEG;IACH,cAAc,mBAAmB;IAEjC;;OAEG;IACH,4BAA4B,iCAAiC;IAC7D,0BAA0B,+BAA+B;IACzD,4BAA4B,iCAAiC;IAE7D;;OAEG;IACH,sBAAsB,2BAA2B;IACjD,6BAA6B,kCAAkC;IAC/D,gCAAgC,qCAAqC;IACrE,2BAA2B,gCAAgC;IAE3D;;OAEG;IACH,mBAAmB,wBAAwB;IAC3C,mBAAmB,wBAAwB;IAE3C;;OAEG;IACH,yBAAyB,8BAA8B;IAEvD;;OAEG;IACH,wCAAwC,6CAA6C;IACrF,8BAA8B,mCAAmC;IACjE,6CAA6C,kDAAkD;IAC/F,qCAAqC,0CAA0C;IAC/E,uCAAuC,4CAA4C;IACnF,+BAA+B,oCAAoC;IACnE,8CAA8C,mDAAmD;IAEjG;;OAEG;IACH,2BAA2B,gCAAgC;IAC3D,gCAAgC,qCAAqC;IACrE,gCAAgC,qCAAqC;IAErE,uBAAuB,4BAA4B;IAEnD,8BAA8B,mCAAmC;IACjE,+CAA+C,oDAAoD;IAEnG,gCAAgC,qCAAoC;IAEpE,uCAAuC,4CAA4C;IAEnF,kCAAkC,uCAAuC;IAEzE,6BAA6B,kCAAkC;IAE/D;;OAEG;IACH,4BAA4B,iCAAiC;CAChE;AAED;;;;;GAKG;AACH,oBAAY,sBAAsB;IAC9B,UAAU,IAAA;IACV,UAAU,IAAA;IACV,SAAS,IAAA;CACZ;AAED;;GAEG;AACH,oBAAY,YAAY,GAAG;IACvB;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEvC;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;;;OAKG;IAEH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;;;;OAKG;IAEH,gBAAgB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEtC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,gBAAgB,CAAC,EAAE,MAAM,CAAA;IACzB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,cAAc,CAAC,EAAE,MAAM,CAAA;IACvB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,2BAA2B,CAAC,EAAE,MAAM,CAAC;IACrC,0BAA0B,CAAC,EAAE,OAAO,CAAC;CACxC,CAAC;AAEF;;GAEG;AACH,oBAAY,QAAQ,GAAG;IACnB,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB;;;;OAIG;IACH,4BAA4B,CAAC,EAAE,MAAM,CAAC;CACzC,CAAC;AAEF,oBAAY,cAAc,GAAG;IACzB,IAAI,EAAE,iBAAiB,CAAC;IACxB,WAAW,EAAE,MAAM,CAAA;CACtB,CAAC;AAEF;;;;;GAKG;AACH,oBAAY,gBAAgB,GAAG,YAAY,GAAG,QAAQ,GAAG;IACrD;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,MAAM,EAAE,sBAAsB,CAAC;IAE/B;;;;OAIG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;;OAIG;IACH,QAAQ,EAAE,MAAM,CAAA;IAEhB;;;;OAIG;IACH,aAAa,EAAE,MAAM,CAAC;IAEtB;;;;;OAKG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElC;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAE3B;;;;OAIG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;;;;OAKG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;;;OAIG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEzB;;;;OAIG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;OAIG;IACH,cAAc,EAAE,MAAM,CAAC;IAEvB;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;;;OAIG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAElB;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEvC;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,yBAAyB,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;CAC1D,CAAC;AAEF,eAAO,MAAM,SAAS,EAAE,WAAW,CAAC,MAAM,CAUxC,CAAC"}