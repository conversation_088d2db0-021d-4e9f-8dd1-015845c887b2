{"version": 3, "file": "CacheRecord.js", "sources": ["../../../src/cache/entities/CacheRecord.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { IdTokenEntity } from \"./IdTokenEntity\";\r\nimport { AccessTokenEntity } from \"./AccessTokenEntity\";\r\nimport { RefreshTokenEntity } from \"./RefreshTokenEntity\";\r\nimport { AccountEntity } from \"./AccountEntity\";\r\nimport { AppMetadataEntity } from \"./AppMetadataEntity\";\r\n\r\nexport class CacheRecord {\r\n    account: AccountEntity | null;\r\n    idToken: IdTokenEntity | null;\r\n    accessToken: AccessTokenEntity | null;\r\n    refreshToken: RefreshTokenEntity | null;\r\n    appMetadata: AppMetadataEntity | null;\r\n\r\n    constructor(accountEntity?: AccountEntity | null, idTokenEntity?: IdTokenEntity | null, accessTokenEntity?: AccessTokenEntity | null, refreshTokenEntity?: RefreshTokenEntity | null, appMetadataEntity?: AppMetadataEntity | null) {\r\n        this.account = accountEntity || null;\r\n        this.idToken = idTokenEntity || null;\r\n        this.accessToken = accessTokenEntity || null;\r\n        this.refreshToken = refreshTokenEntity || null;\r\n        this.appMetadata = appMetadataEntity || null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAQH,IAAA,WAAA,kBAAA,YAAA;IAOI,SAAY,WAAA,CAAA,aAAoC,EAAE,aAAoC,EAAE,iBAA4C,EAAE,kBAA8C,EAAE,iBAA4C,EAAA;AAC9N,QAAA,IAAI,CAAC,OAAO,GAAG,aAAa,IAAI,IAAI,CAAC;AACrC,QAAA,IAAI,CAAC,OAAO,GAAG,aAAa,IAAI,IAAI,CAAC;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,iBAAiB,IAAI,IAAI,CAAC;AAC7C,QAAA,IAAI,CAAC,YAAY,GAAG,kBAAkB,IAAI,IAAI,CAAC;AAC/C,QAAA,IAAI,CAAC,WAAW,GAAG,iBAAiB,IAAI,IAAI,CAAC;KAChD;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA;;;;"}