{"version": 3, "file": "AuthorityMetadataEntity.js", "sources": ["../../../src/cache/entities/AuthorityMetadataEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CloudDiscoveryMetadata } from \"../../authority/CloudDiscoveryMetadata\";\r\nimport { OpenIdConfigResponse } from \"../../authority/OpenIdConfigResponse\";\r\nimport { AUTHORITY_METADATA_CONSTANTS } from \"../../utils/Constants\";\r\nimport { TimeUtils } from \"../../utils/TimeUtils\";\r\n\r\nexport class AuthorityMetadataEntity {\r\n    aliases: Array<string>;\r\n    preferred_cache: string;\r\n    preferred_network: string;\r\n    canonical_authority: string;\r\n    authorization_endpoint: string;\r\n    token_endpoint: string;\r\n    end_session_endpoint?: string;\r\n    issuer: string;\r\n    aliasesFromNetwork: boolean;\r\n    endpointsFromNetwork: boolean;\r\n    expiresAt: number;\r\n    jwks_uri: string;\r\n\r\n    constructor() {\r\n        this.expiresAt = TimeUtils.nowSeconds() + AUTHORITY_METADATA_CONSTANTS.REFRESH_TIME_SECONDS;\r\n    }\r\n\r\n    /**\r\n     * Update the entity with new aliases, preferred_cache and preferred_network values\r\n     * @param metadata \r\n     * @param fromNetwork \r\n     */\r\n    updateCloudDiscoveryMetadata(metadata: CloudDiscoveryMetadata, fromNetwork: boolean): void {\r\n        this.aliases = metadata.aliases;\r\n        this.preferred_cache = metadata.preferred_cache;\r\n        this.preferred_network = metadata.preferred_network;\r\n        this.aliasesFromNetwork = fromNetwork;\r\n    }\r\n\r\n    /**\r\n     * Update the entity with new endpoints\r\n     * @param metadata \r\n     * @param fromNetwork \r\n     */\r\n    updateEndpointMetadata(metadata: OpenIdConfigResponse, fromNetwork: boolean): void {\r\n        this.authorization_endpoint = metadata.authorization_endpoint;\r\n        this.token_endpoint = metadata.token_endpoint;\r\n        this.end_session_endpoint = metadata.end_session_endpoint;\r\n        this.issuer = metadata.issuer;\r\n        this.endpointsFromNetwork = fromNetwork;\r\n        this.jwks_uri = metadata.jwks_uri;\r\n    }\r\n\r\n    /**\r\n     * Save the authority that was used to create this cache entry\r\n     * @param authority \r\n     */\r\n    updateCanonicalAuthority(authority: string): void {\r\n        this.canonical_authority = authority;\r\n    }\r\n\r\n    /**\r\n     * Reset the exiresAt value\r\n     */\r\n    resetExpiresAt(): void {\r\n        this.expiresAt = TimeUtils.nowSeconds() + AUTHORITY_METADATA_CONSTANTS.REFRESH_TIME_SECONDS;\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the data needs to be refreshed\r\n     */\r\n    isExpired(): boolean {\r\n        return this.expiresAt <= TimeUtils.nowSeconds();\r\n    }\r\n\r\n    /**\r\n     * Validates an entity: checks for all expected params\r\n     * @param entity\r\n     */\r\n    static isAuthorityMetadataEntity(key: string, entity: object): boolean {\r\n\r\n        if (!entity) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            key.indexOf(AUTHORITY_METADATA_CONSTANTS.CACHE_KEY) === 0 &&\r\n            entity.hasOwnProperty(\"aliases\") &&\r\n            entity.hasOwnProperty(\"preferred_cache\") &&\r\n            entity.hasOwnProperty(\"preferred_network\") &&\r\n            entity.hasOwnProperty(\"canonical_authority\") &&\r\n            entity.hasOwnProperty(\"authorization_endpoint\") &&\r\n            entity.hasOwnProperty(\"token_endpoint\") &&\r\n            entity.hasOwnProperty(\"issuer\") &&\r\n            entity.hasOwnProperty(\"aliasesFromNetwork\") &&\r\n            entity.hasOwnProperty(\"endpointsFromNetwork\") &&\r\n            entity.hasOwnProperty(\"expiresAt\") &&\r\n            entity.hasOwnProperty(\"jwks_uri\")\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAOH,IAAA,uBAAA,kBAAA,YAAA;AAcI,IAAA,SAAA,uBAAA,GAAA;QACI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,UAAU,EAAE,GAAG,4BAA4B,CAAC,oBAAoB,CAAC;KAC/F;AAED;;;;AAIG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UAA6B,QAAgC,EAAE,WAAoB,EAAA;AAC/E,QAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AAChC,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;AAChD,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;AACpD,QAAA,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;KACzC,CAAA;AAED;;;;AAIG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,UAAuB,QAA8B,EAAE,WAAoB,EAAA;AACvE,QAAA,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,sBAAsB,CAAC;AAC9D,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;AAC9C,QAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;AAC1D,QAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC9B,QAAA,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;KACrC,CAAA;AAED;;;AAGG;IACH,uBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,SAAiB,EAAA;AACtC,QAAA,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;KACxC,CAAA;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,UAAU,EAAE,GAAG,4BAA4B,CAAC,oBAAoB,CAAC;KAC/F,CAAA;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QACI,OAAO,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;KACnD,CAAA;AAED;;;AAGG;AACI,IAAA,uBAAA,CAAA,yBAAyB,GAAhC,UAAiC,GAAW,EAAE,MAAc,EAAA;QAExD,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,QACI,GAAG,CAAC,OAAO,CAAC,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC;AACzD,YAAA,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC;AAChC,YAAA,MAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC;AACxC,YAAA,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC;AAC1C,YAAA,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC;AAC5C,YAAA,MAAM,CAAC,cAAc,CAAC,wBAAwB,CAAC;AAC/C,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,YAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,YAAA,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC;AAC3C,YAAA,MAAM,CAAC,cAAc,CAAC,sBAAsB,CAAC;AAC7C,YAAA,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC;AAClC,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EACnC;KACL,CAAA;IACL,OAAC,uBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}