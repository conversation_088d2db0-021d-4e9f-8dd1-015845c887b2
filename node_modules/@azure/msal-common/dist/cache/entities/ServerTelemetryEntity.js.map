{"version": 3, "file": "ServerTelemetryEntity.js", "sources": ["../../../src/cache/entities/ServerTelemetryEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { SERVER_TELEM_CONSTANTS } from \"../../utils/Constants\";\r\n\r\nexport class ServerTelemetryEntity {\r\n    failedRequests: Array<string|number>;\r\n    errors: string[];\r\n    cacheHits: number;\r\n\r\n    constructor() {\r\n        this.failedRequests = [];\r\n        this.errors = [];\r\n        this.cacheHits = 0;\r\n    }\r\n\r\n    /**\r\n     * validates if a given cache entry is \"Telemetry\", parses <key,value>\r\n     * @param key\r\n     * @param entity\r\n     */\r\n    static isServerTelemetryEntity(key: string, entity?: object): boolean {\r\n\r\n        const validateKey: boolean = key.indexOf(SERVER_TELEM_CONSTANTS.CACHE_KEY) === 0;\r\n        let validateEntity: boolean = true;\r\n\r\n        if (entity) {\r\n            validateEntity =\r\n                entity.hasOwnProperty(\"failedRequests\") &&\r\n                entity.hasOwnProperty(\"errors\") &&\r\n                entity.hasOwnProperty(\"cacheHits\");\r\n        }\r\n\r\n        return validateKey && validateEntity;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAIH,IAAA,qBAAA,kBAAA,YAAA;AAKI,IAAA,SAAA,qBAAA,GAAA;AACI,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;KACtB;AAED;;;;AAIG;AACI,IAAA,qBAAA,CAAA,uBAAuB,GAA9B,UAA+B,GAAW,EAAE,MAAe,EAAA;AAEvD,QAAA,IAAM,WAAW,GAAY,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjF,IAAI,cAAc,GAAY,IAAI,CAAC;AAEnC,QAAA,IAAI,MAAM,EAAE;YACR,cAAc;AACV,gBAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,oBAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,oBAAA,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC1C,SAAA;QAED,OAAO,WAAW,IAAI,cAAc,CAAC;KACxC,CAAA;IACL,OAAC,qBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}