{"version": 3, "file": "AccountEntity.js", "sources": ["../../../src/cache/entities/AccountEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport {\r\n    Separators,\r\n    CacheAccountType,\r\n    Constants,\r\n} from \"../../utils/Constants\";\r\nimport { Authority } from \"../../authority/Authority\";\r\nimport { ICrypto } from \"../../crypto/ICrypto\";\r\nimport { buildClientInfo } from \"../../account/ClientInfo\";\r\nimport { StringUtils } from \"../../utils/StringUtils\";\r\nimport { AccountInfo } from \"../../account/AccountInfo\";\r\nimport { ClientAuthError } from \"../../error/ClientAuthError\";\r\nimport { AuthorityType } from \"../../authority/AuthorityType\";\r\nimport { Logger } from \"../../logger/Logger\";\r\nimport { TokenClaims } from \"../../account/TokenClaims\";\r\nimport { ProtocolMode } from \"../../authority/ProtocolMode\";\r\n\r\n/**\r\n * Type that defines required and optional parameters for an Account field (based on universal cache schema implemented by all MSALs).\r\n *\r\n * Key : Value Schema\r\n *\r\n * Key: <home_account_id>-<environment>-<realm*>\r\n *\r\n * Value Schema:\r\n * {\r\n *      homeAccountId: home account identifier for the auth scheme,\r\n *      environment: entity that issued the token, represented as a full host\r\n *      realm: Full tenant or organizational identifier that the account belongs to\r\n *      localAccountId: Original tenant-specific accountID, usually used for legacy cases\r\n *      username: primary username that represents the user, usually corresponds to preferred_username in the v2 endpt\r\n *      authorityType: Accounts authority type as a string\r\n *      clientInfo: Full base64 encoded client info received from ESTS\r\n *      name: Full name for the account, including given name and family name\r\n *      lastModificationTime: last time this entity was modified in the cache\r\n *      lastModificationApp:\r\n *      idTokenClaims: Object containing claims parsed from ID token\r\n *      nativeAccountId: Account identifier on the native device\r\n * }\r\n */\r\nexport class AccountEntity {\r\n    homeAccountId: string;\r\n    environment: string;\r\n    realm: string;\r\n    localAccountId: string;\r\n    username: string;\r\n    authorityType: string;\r\n    clientInfo?: string;\r\n    name?: string;\r\n    lastModificationTime?: string;\r\n    lastModificationApp?: string;\r\n    cloudGraphHostName?: string;\r\n    msGraphHost?: string;\r\n    idTokenClaims?: TokenClaims;\r\n    nativeAccountId?: string;\r\n\r\n    /**\r\n     * Generate Account Id key component as per the schema: <home_account_id>-<environment>\r\n     */\r\n    generateAccountId(): string {\r\n        const accountId: Array<string> = [this.homeAccountId, this.environment];\r\n        return accountId.join(Separators.CACHE_KEY_SEPARATOR).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Generate Account Cache Key as per the schema: <home_account_id>-<environment>-<realm*>\r\n     */\r\n    generateAccountKey(): string {\r\n        return AccountEntity.generateAccountCacheKey({\r\n            homeAccountId: this.homeAccountId,\r\n            environment: this.environment,\r\n            tenantId: this.realm,\r\n            username: this.username,\r\n            localAccountId: this.localAccountId\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Returns the AccountInfo interface for this account.\r\n     */\r\n    getAccountInfo(): AccountInfo {\r\n        return {\r\n            homeAccountId: this.homeAccountId,\r\n            environment: this.environment,\r\n            tenantId: this.realm,\r\n            username: this.username,\r\n            localAccountId: this.localAccountId,\r\n            name: this.name,\r\n            idTokenClaims: this.idTokenClaims,\r\n            nativeAccountId: this.nativeAccountId,\r\n            authorityType: this.authorityType\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Generates account key from interface\r\n     * @param accountInterface\r\n     */\r\n    static generateAccountCacheKey(accountInterface: AccountInfo): string {\r\n        const accountKey = [\r\n            accountInterface.homeAccountId,\r\n            accountInterface.environment || Constants.EMPTY_STRING,\r\n            accountInterface.tenantId || Constants.EMPTY_STRING,\r\n        ];\r\n\r\n        return accountKey.join(Separators.CACHE_KEY_SEPARATOR).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Build Account cache from account properties.\r\n     * @param accountDetails\r\n     * @param authority\r\n     */\r\n    static createAccount(\r\n        accountDetails: {\r\n            homeAccountId: string,\r\n            idTokenClaims: TokenClaims,\r\n            clientInfo?: string,\r\n            cloudGraphHostName?: string,\r\n            msGraphHost?: string,\r\n            environment?: string,\r\n            nativeAccountId?: string\r\n        },\r\n        authority: Authority\r\n    ): AccountEntity {\r\n        const account: AccountEntity = new AccountEntity();\r\n\r\n        if (authority.authorityType === AuthorityType.Adfs) {\r\n            account.authorityType = CacheAccountType.ADFS_ACCOUNT_TYPE;\r\n        } else if (authority.protocolMode === ProtocolMode.AAD) {\r\n            account.authorityType = CacheAccountType.MSSTS_ACCOUNT_TYPE;\r\n        } else {\r\n            account.authorityType = CacheAccountType.GENERIC_ACCOUNT_TYPE;\r\n        }\r\n\r\n        account.clientInfo = accountDetails.clientInfo;\r\n        account.homeAccountId = accountDetails.homeAccountId;\r\n        account.nativeAccountId = accountDetails.nativeAccountId;\r\n\r\n        const env = accountDetails.environment || (authority && authority.getPreferredCache());\r\n\r\n        if (!env) {\r\n            throw ClientAuthError.createInvalidCacheEnvironmentError();\r\n        }\r\n\r\n        account.environment = env;\r\n        // non AAD scenarios can have empty realm\r\n        account.realm = accountDetails.idTokenClaims.tid || Constants.EMPTY_STRING;\r\n\r\n        account.idTokenClaims = accountDetails.idTokenClaims;\r\n\r\n        // How do you account for MSA CID here?\r\n        account.localAccountId = accountDetails.idTokenClaims.oid ||  accountDetails.idTokenClaims.sub || Constants.EMPTY_STRING;\r\n\r\n        /*\r\n         * In B2C scenarios the emails claim is used instead of preferred_username and it is an array.\r\n         * In most cases it will contain a single email. This field should not be relied upon if a custom \r\n         * policy is configured to return more than 1 email.\r\n         */\r\n        if (account.authorityType === CacheAccountType.MSSTS_ACCOUNT_TYPE) {\r\n            const preferredUsername = accountDetails.idTokenClaims.preferred_username;\r\n            const email = (accountDetails.idTokenClaims.emails) ? accountDetails.idTokenClaims.emails[0] : null;\r\n                \r\n            account.username = preferredUsername || email || \"\";\r\n        } else {\r\n            account.username = accountDetails.idTokenClaims.upn || \"\";\r\n        }\r\n        account.name = accountDetails.idTokenClaims.name;\r\n\r\n        account.cloudGraphHostName = accountDetails.cloudGraphHostName;\r\n        account.msGraphHost = accountDetails.msGraphHost;\r\n\r\n        return account;\r\n    }\r\n\r\n    /**\r\n     * Creates an account object from AccountInfo\r\n     * @param accountInfo\r\n     * @param cloudGraphHostName\r\n     * @param msGraphHost\r\n     * @returns AccountEntity\r\n     */\r\n    static createFromAccountInfo(\r\n        accountInfo: AccountInfo,\r\n        cloudGraphHostName?: string,\r\n        msGraphHost?: string\r\n    ): AccountEntity {\r\n        const account: AccountEntity = new AccountEntity();\r\n\r\n        account.authorityType =\r\n            accountInfo.authorityType || CacheAccountType.GENERIC_ACCOUNT_TYPE;\r\n        account.homeAccountId = accountInfo.homeAccountId;\r\n        account.localAccountId = accountInfo.localAccountId;\r\n        account.nativeAccountId = accountInfo.nativeAccountId;\r\n        \r\n        account.realm = accountInfo.tenantId;\r\n        account.environment = accountInfo.environment;\r\n\r\n        account.username = accountInfo.username;\r\n        account.name = accountInfo.name;\r\n        account.idTokenClaims = accountInfo.idTokenClaims;\r\n\r\n        account.cloudGraphHostName = cloudGraphHostName;\r\n        account.msGraphHost = msGraphHost;\r\n\r\n        return account;\r\n    }\r\n\r\n    /**\r\n     * Generate HomeAccountId from server response\r\n     * @param serverClientInfo\r\n     * @param authType\r\n     */\r\n    static generateHomeAccountId(\r\n        serverClientInfo: string,\r\n        authType: AuthorityType,\r\n        logger: Logger,\r\n        cryptoObj: ICrypto,\r\n        idTokenClaims?: TokenClaims\r\n    ): string {\r\n\r\n        const accountId = idTokenClaims?.sub ? idTokenClaims.sub : Constants.EMPTY_STRING;\r\n\r\n        // since ADFS does not have tid and does not set client_info\r\n        if (authType === AuthorityType.Adfs || authType === AuthorityType.Dsts) {\r\n            return accountId;\r\n        }\r\n\r\n        // for cases where there is clientInfo\r\n        if (serverClientInfo) {\r\n            try {\r\n                const clientInfo = buildClientInfo(serverClientInfo, cryptoObj);\r\n                if (!StringUtils.isEmpty(clientInfo.uid) && !StringUtils.isEmpty(clientInfo.utid)) {\r\n                    return `${clientInfo.uid}${Separators.CLIENT_INFO_SEPARATOR}${clientInfo.utid}`;\r\n                }\r\n            } catch (e) {}\r\n        }\r\n\r\n        // default to \"sub\" claim\r\n        logger.verbose(\"No client info in response\");\r\n        return accountId;\r\n    }\r\n\r\n    /**\r\n     * Validates an entity: checks for all expected params\r\n     * @param entity\r\n     */\r\n    static isAccountEntity(entity: object): boolean {\r\n\r\n        if (!entity) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            entity.hasOwnProperty(\"homeAccountId\") &&\r\n            entity.hasOwnProperty(\"environment\") &&\r\n            entity.hasOwnProperty(\"realm\") &&\r\n            entity.hasOwnProperty(\"localAccountId\") &&\r\n            entity.hasOwnProperty(\"username\") &&\r\n            entity.hasOwnProperty(\"authorityType\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Helper function to determine whether 2 accountInfo objects represent the same account\r\n     * @param accountA\r\n     * @param accountB\r\n     * @param compareClaims - If set to true idTokenClaims will also be compared to determine account equality\r\n     */\r\n    static accountInfoIsEqual(accountA: AccountInfo | null, accountB: AccountInfo | null, compareClaims?: boolean): boolean {\r\n        if (!accountA || !accountB) {\r\n            return false;\r\n        }\r\n\r\n        let claimsMatch = true; // default to true so as to not fail comparison below if compareClaims: false\r\n        if (compareClaims) {\r\n            const accountAClaims = (accountA.idTokenClaims || {}) as TokenClaims;\r\n            const accountBClaims = (accountB.idTokenClaims || {}) as TokenClaims;\r\n\r\n            // issued at timestamp and nonce are expected to change each time a new id token is acquired\r\n            claimsMatch = (accountAClaims.iat === accountBClaims.iat) &&\r\n            (accountAClaims.nonce === accountBClaims.nonce);\r\n        }\r\n\r\n        return (accountA.homeAccountId === accountB.homeAccountId) &&\r\n            (accountA.localAccountId === accountB.localAccountId) &&\r\n            (accountA.username === accountB.username) &&\r\n            (accountA.tenantId === accountB.tenantId) &&\r\n            (accountA.environment === accountB.environment) &&\r\n            (accountA.nativeAccountId === accountB.nativeAccountId) &&\r\n            claimsMatch;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAkBH;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACH,IAAA,aAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,aAAA,GAAA;KA4PC;AA5OG;;AAEG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;QACI,IAAM,SAAS,GAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KACvE,CAAA;AAED;;AAEG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;QACI,OAAO,aAAa,CAAC,uBAAuB,CAAC;YACzC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;AACtC,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACI,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK;YACpB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,aAAa,EAAE,IAAI,CAAC,aAAa;SACpC,CAAC;KACL,CAAA;AAED;;;AAGG;IACI,aAAuB,CAAA,uBAAA,GAA9B,UAA+B,gBAA6B,EAAA;AACxD,QAAA,IAAM,UAAU,GAAG;AACf,YAAA,gBAAgB,CAAC,aAAa;AAC9B,YAAA,gBAAgB,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY;AACtD,YAAA,gBAAgB,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY;SACtD,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KACxE,CAAA;AAED;;;;AAIG;AACI,IAAA,aAAA,CAAA,aAAa,GAApB,UACI,cAQC,EACD,SAAoB,EAAA;AAEpB,QAAA,IAAM,OAAO,GAAkB,IAAI,aAAa,EAAE,CAAC;AAEnD,QAAA,IAAI,SAAS,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI,EAAE;AAChD,YAAA,OAAO,CAAC,aAAa,GAAG,gBAAgB,CAAC,iBAAiB,CAAC;AAC9D,SAAA;AAAM,aAAA,IAAI,SAAS,CAAC,YAAY,KAAK,YAAY,CAAC,GAAG,EAAE;AACpD,YAAA,OAAO,CAAC,aAAa,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;AAC/D,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,CAAC,aAAa,GAAG,gBAAgB,CAAC,oBAAoB,CAAC;AACjE,SAAA;AAED,QAAA,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC/C,QAAA,OAAO,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;AACrD,QAAA,OAAO,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;AAEzD,QAAA,IAAM,GAAG,GAAG,cAAc,CAAC,WAAW,KAAK,SAAS,IAAI,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEvF,IAAI,CAAC,GAAG,EAAE;AACN,YAAA,MAAM,eAAe,CAAC,kCAAkC,EAAE,CAAC;AAC9D,SAAA;AAED,QAAA,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;;AAE1B,QAAA,OAAO,CAAC,KAAK,GAAG,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC;AAE3E,QAAA,OAAO,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;;AAGrD,QAAA,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC,GAAG,IAAK,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC;AAEzH;;;;AAIG;AACH,QAAA,IAAI,OAAO,CAAC,aAAa,KAAK,gBAAgB,CAAC,kBAAkB,EAAE;AAC/D,YAAA,IAAM,iBAAiB,GAAG,cAAc,CAAC,aAAa,CAAC,kBAAkB,CAAC;YAC1E,IAAM,KAAK,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,IAAI,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAEpG,OAAO,CAAC,QAAQ,GAAG,iBAAiB,IAAI,KAAK,IAAI,EAAE,CAAC;AACvD,SAAA;AAAM,aAAA;YACH,OAAO,CAAC,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,EAAE,CAAC;AAC7D,SAAA;QACD,OAAO,CAAC,IAAI,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC;AAEjD,QAAA,OAAO,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;AAC/D,QAAA,OAAO,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;AAEjD,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;AAED;;;;;;AAMG;AACI,IAAA,aAAA,CAAA,qBAAqB,GAA5B,UACI,WAAwB,EACxB,kBAA2B,EAC3B,WAAoB,EAAA;AAEpB,QAAA,IAAM,OAAO,GAAkB,IAAI,aAAa,EAAE,CAAC;AAEnD,QAAA,OAAO,CAAC,aAAa;AACjB,YAAA,WAAW,CAAC,aAAa,IAAI,gBAAgB,CAAC,oBAAoB,CAAC;AACvE,QAAA,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;AAClD,QAAA,OAAO,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACpD,QAAA,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;AAEtD,QAAA,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAA,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;AAE9C,QAAA,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AACxC,QAAA,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;AAChC,QAAA,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;AAElD,QAAA,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAChD,QAAA,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;AAElC,QAAA,OAAO,OAAO,CAAC;KAClB,CAAA;AAED;;;;AAIG;IACI,aAAqB,CAAA,qBAAA,GAA5B,UACI,gBAAwB,EACxB,QAAuB,EACvB,MAAc,EACd,SAAkB,EAClB,aAA2B,EAAA;QAG3B,IAAM,SAAS,GAAG,CAAA,aAAa,aAAb,aAAa,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAb,aAAa,CAAE,GAAG,IAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC;;QAGlF,IAAI,QAAQ,KAAK,aAAa,CAAC,IAAI,IAAI,QAAQ,KAAK,aAAa,CAAC,IAAI,EAAE;AACpE,YAAA,OAAO,SAAS,CAAC;AACpB,SAAA;;AAGD,QAAA,IAAI,gBAAgB,EAAE;YAClB,IAAI;gBACA,IAAM,UAAU,GAAG,eAAe,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAChE,gBAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AAC/E,oBAAA,OAAO,EAAG,GAAA,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,qBAAqB,GAAG,UAAU,CAAC,IAAM,CAAC;AACnF,iBAAA;AACJ,aAAA;YAAC,OAAO,CAAC,EAAE,GAAE;AACjB,SAAA;;AAGD,QAAA,MAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAC7C,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;AAGG;IACI,aAAe,CAAA,eAAA,GAAtB,UAAuB,MAAc,EAAA;QAEjC,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QACI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;AACtC,YAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;AACpC,YAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;AAC9B,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,YAAA,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,EACxC;KACL,CAAA;AAED;;;;;AAKG;AACI,IAAA,aAAA,CAAA,kBAAkB,GAAzB,UAA0B,QAA4B,EAAE,QAA4B,EAAE,aAAuB,EAAA;AACzG,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACxB,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,aAAa,EAAE;YACf,IAAM,cAAc,IAAI,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAgB,CAAC;YACrE,IAAM,cAAc,IAAI,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAgB,CAAC;;YAGrE,WAAW,GAAG,CAAC,cAAc,CAAC,GAAG,KAAK,cAAc,CAAC,GAAG;iBACvD,cAAc,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,CAAC,CAAC;AACnD,SAAA;QAED,OAAO,CAAC,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,aAAa;AACrD,aAAC,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,cAAc,CAAC;AACrD,aAAC,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC;AACzC,aAAC,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC;AACzC,aAAC,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW,CAAC;AAC/C,aAAC,QAAQ,CAAC,eAAe,KAAK,QAAQ,CAAC,eAAe,CAAC;AACvD,YAAA,WAAW,CAAC;KACnB,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA;;;;"}