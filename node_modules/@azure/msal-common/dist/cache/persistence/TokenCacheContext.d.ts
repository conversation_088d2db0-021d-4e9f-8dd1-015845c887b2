import { ISerializableTokenCache } from "../interface/ISerializableTokenCache";
/**
 * This class instance helps track the memory changes facilitating
 * decisions to read from and write to the persistent cache
 */ export declare class TokenCacheContext {
    /**
     * boolean indicating cache change
     */
    hasChanged: boolean;
    /**
     * serializable token cache interface
     */
    cache: ISerializableTokenCache;
    constructor(tokenCache: ISerializableTokenCache, hasChanged: boolean);
    /**
     * boolean which indicates the changes in cache
     */
    get cacheHasChanged(): boolean;
    /**
     * function to retrieve the token cache
     */
    get tokenCache(): ISerializableTokenCache;
}
//# sourceMappingURL=TokenCacheContext.d.ts.map