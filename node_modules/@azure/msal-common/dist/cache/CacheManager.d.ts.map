{"version": 3, "file": "CacheManager.d.ts", "sourceRoot": "", "sources": ["../../src/cache/CacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC1I,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAIrD,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AAEnE,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAE1D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAE/D,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAC5C,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAG1C;;GAEG;AACH,8BAAsB,YAAa,YAAW,aAAa;IACvD,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;IAE9B,OAAO,CAAC,YAAY,CAAS;gBAEjB,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;IAMjE;;;OAGG;IACH,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAE7D;;;OAGG;IACH,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAEjD;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAEvE;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAE3D;;;OAGG;IACH,QAAQ,CAAC,wBAAwB,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAEnF;;;OAGG;IACH,QAAQ,CAAC,wBAAwB,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IAEvE;;;OAGG;IACH,QAAQ,CAAC,yBAAyB,CAAC,eAAe,EAAE,MAAM,GAAG,kBAAkB,GAAG,IAAI;IAEtF;;;OAGG;IACH,QAAQ,CAAC,yBAAyB,CAAC,YAAY,EAAE,kBAAkB,GAAG,IAAI;IAE1E;;;OAGG;IACH,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAEzE;;;OAGG;IACH,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IAE7D;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,qBAAqB,GAAG,IAAI;IAErF;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,qBAAqB,GAAG,IAAI;IAErG;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IAE1E;;OAEG;IACH,QAAQ,CAAC,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAElD;;;;OAIG;IACH,QAAQ,CAAC,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,uBAAuB,GAAG,IAAI;IAEhF;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IAEhF;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,GAAG,IAAI;IAEhG;;;OAGG;IACH,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAEtC;;;OAGG;IACH,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO;IAEzD;;OAEG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,EAAE;IAE5B;;OAEG;IACH,QAAQ,CAAC,cAAc,IAAI,MAAM,EAAE;IAEnC;;OAEG;IACH,QAAQ,CAAC,YAAY,IAAI,SAAS;IAElC;;OAEG;IACH,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAE/B;;OAEG;IACH,QAAQ,CAAC,wBAAwB,CAAC,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,mBAAmB,GAAG,MAAM;IAEnG;;OAEG;IACH,cAAc,IAAI,WAAW,EAAE;IA0B/B;;OAEG;IACH,wBAAwB,CAAC,aAAa,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI;IAS1E,OAAO,CAAC,wBAAwB;IAUhC;;;OAGG;IACG,eAAe,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IA0B9D;;;OAGG;YACW,eAAe;IAiC7B;;;;;;OAMG;IACH,qBAAqB,CAAC,aAAa,EAAE,aAAa,GAAG,aAAa,EAAE;IA8CpE;;;;;;OAMG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO;IAmB7E;;;OAGG;IACH,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IA+BrC;;;;;OAKG;IACH,uBAAuB,CAAC,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO;IAkEvF;;;OAGG;IACH,wBAAwB,CAAC,MAAM,EAAE,iBAAiB,GAAG,gBAAgB;IAOrE;;;;OAIG;IACH,OAAO,CAAC,gCAAgC;IAoCxC;;;OAGG;IACH,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IA4BzE;;OAEG;IACG,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IAWxC;;;OAGG;IACG,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAStD;;;OAGG;IACG,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IA0BjE;;;OAGG;IACG,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyBnD;;OAEG;IACH,iBAAiB,IAAI,OAAO;IAW5B;;;;;;;OAOG;IACH,eAAe,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,GAAG,WAAW;IAqBjG;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,WAAW,GAAG,aAAa,GAAG,IAAI;IAKhE;;;;;OAKG;IACH,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,SAAS,GAAG,aAAa,GAAG,IAAI;IA8B7E;;;;OAIG;IACH,mBAAmB,CAAC,MAAM,EAAE,gBAAgB,EAAE,SAAS,CAAC,EAAE,SAAS,GAAG,aAAa,EAAE;IAkBrF;;;;;OAKG;IACH,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO;IAa5E;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAIhC;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAIrC;;;;;;OAMG;IACH,cAAc,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,CAAC,EAAE,SAAS,GAAG,iBAAiB,GAAG,IAAI;IAuD/G;;;;;;OAMG;IACH,2BAA2B,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,OAAO,GAAG,OAAO;IAkClH;;;;OAIG;IACH,uBAAuB,CAAC,MAAM,EAAE,gBAAgB,GAAG,iBAAiB,EAAE;IAkBtE;;;;;OAKG;IACH,eAAe,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,SAAS,GAAG,kBAAkB,GAAG,IAAI;IAoC1G;;;;OAIG;IACH,4BAA4B,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO;IAkBjF;;OAEG;IACH,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAmBvE;;;;OAIG;IACH,iBAAiB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAK/C;;;;OAIG;IACH,OAAO,CAAC,kBAAkB;IAI1B;;;;;OAKG;IACH,OAAO,CAAC,mBAAmB;IAI3B;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAIrB;;;;OAIG;IACH,OAAO,CAAC,sBAAsB;IAI9B;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IASxB;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAI3B;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAIrB;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAIrB;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAIlB;;;;;OAKG;IACH,OAAO,CAAC,oBAAoB;IAI5B;;;;OAIG;IACH,OAAO,CAAC,WAAW;IAYnB;;;;OAIG;IACH,OAAO,CAAC,cAAc;IAItB;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAIlB;;;OAGG;IACH,OAAO,CAAC,aAAa;IAIrB;;;OAGG;IACH,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAInD;;OAEG;IACH,iCAAiC,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAI5D;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;CAM9C;AAED,qBAAa,mBAAoB,SAAQ,YAAY;IACjD,UAAU,IAAI,IAAI;IAIlB,UAAU,IAAI,aAAa;IAI3B,oBAAoB,IAAI,IAAI;IAI5B,oBAAoB,IAAI,aAAa;IAIrC,wBAAwB,IAAI,IAAI;IAIhC,wBAAwB,IAAI,iBAAiB;IAI7C,yBAAyB,IAAI,IAAI;IAIjC,yBAAyB,IAAI,kBAAkB;IAI/C,cAAc,IAAI,IAAI;IAItB,cAAc,IAAI,iBAAiB;IAInC,kBAAkB,IAAI,IAAI;IAI1B,kBAAkB,IAAI,qBAAqB;IAI3C,oBAAoB,IAAI,IAAI;IAI5B,oBAAoB,IAAI,uBAAuB,GAAG,IAAI;IAItD,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAIzC,kBAAkB,IAAI,IAAI;IAI1B,kBAAkB,IAAI,gBAAgB;IAItC,UAAU,IAAI,OAAO;IAIrB,WAAW,IAAI,OAAO;IAItB,OAAO,IAAI,MAAM,EAAE;IAInB,cAAc,IAAI,MAAM,EAAE;IAI1B,YAAY,IAAI,SAAS;IAInB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAI5B,wBAAwB,IAAI,MAAM;CAIrC"}