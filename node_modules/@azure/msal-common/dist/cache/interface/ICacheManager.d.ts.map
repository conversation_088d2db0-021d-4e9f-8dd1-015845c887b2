{"version": 3, "file": "ICacheManager.d.ts", "sourceRoot": "", "sources": ["../../../src/cache/interface/ICacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,qBAAqB,EAAE,MAAM,mCAAmC,CAAC;AAC1E,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AACpE,OAAO,EAAE,uBAAuB,EAAE,MAAM,qCAAqC,CAAC;AAE9E,MAAM,WAAW,aAAa;IAE1B;;;OAGG;IACH,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC;IAErD;;;OAGG;IACH,UAAU,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC;IAEzC;;;;;;OAMG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;IAE7E;;;OAGG;IACH,oBAAoB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC;IAE/D;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC;IAEnD;;;OAGG;IACH,wBAAwB,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC;IAE3E;;;OAGG;IACH,wBAAwB,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAE/D;;;OAGG;IACH,yBAAyB,CAAC,eAAe,EAAE,MAAM,GAAG,kBAAkB,GAAG,IAAI,CAAC;IAE9E;;;OAGG;IACH,yBAAyB,CAAC,YAAY,EAAE,kBAAkB,GAAG,IAAI,CAAC;IAElE;;;OAGG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC;IAEjE;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAErD;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,qBAAqB,GAAG,IAAI,CAAC;IAE7E;;;;OAIG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,qBAAqB,GAAG,IAAI,CAAC;IAE7F;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI,CAAC;IAElE;;OAEG;IACH,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1C;;;;OAIG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,uBAAuB,GAAG,IAAI,CAAC;IAExE;;;OAGG;IACH,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI,CAAC;IAE1E;;;OAGG;IACH,iCAAiC,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;IAE7D;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI,CAAC;IAExE;;;;OAIG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAExF;;OAEG;IACH,cAAc,IAAI,WAAW,EAAE,CAAC;IAEhC;;;OAGG;IACH,eAAe,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEzD;;;;;OAKG;IACH,qBAAqB,CAAC,MAAM,EAAE,aAAa,GAAG,aAAa,EAAE,CAAC;IAE9D;;;OAGG;IACH,wBAAwB,CAAC,MAAM,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI,CAAC;IAEpE;;OAEG;IACH,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAEnC;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjD;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE5D;;OAEG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC;;OAEG;IACH,iBAAiB,CAAC,GAAG,EAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE7C;;OAEG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;CACzC"}