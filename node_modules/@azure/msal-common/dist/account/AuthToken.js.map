{"version": 3, "file": "AuthToken.js", "sources": ["../../src/account/AuthToken.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { TokenClaims } from \"./TokenClaims\";\r\nimport { DecodedAuthToken } from \"./DecodedAuthToken\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ICrypto } from \"../crypto/ICrypto\";\r\n\r\n/**\r\n * JWT Token representation class. Parses token string and generates claims object.\r\n */\r\nexport class AuthToken {\r\n\r\n    // Raw Token string\r\n    rawToken: string;\r\n    // Claims inside token\r\n    claims: TokenClaims;\r\n    constructor(rawToken: string, crypto: ICrypto) {\r\n        if (StringUtils.isEmpty(rawToken)) {\r\n            throw ClientAuthError.createTokenNullOrEmptyError(rawToken);\r\n        }\r\n\r\n        this.rawToken = rawToken;\r\n        this.claims = AuthToken.extractTokenClaims(rawToken, crypto);\r\n    }\r\n\r\n    /**\r\n     * Extract token by decoding the rawToken\r\n     *\r\n     * @param encodedToken\r\n     */\r\n    static extractTokenClaims(encodedToken: string, crypto: ICrypto): TokenClaims {\r\n\r\n        const decodedToken: DecodedAuthToken = StringUtils.decodeAuthToken(encodedToken);\r\n\r\n        // token will be decoded to get the username\r\n        try {\r\n            const base64TokenPayload = decodedToken.JWSPayload;\r\n\r\n            // base64Decode() should throw an error if there is an issue\r\n            const base64Decoded = crypto.base64Decode(base64TokenPayload);\r\n            return JSON.parse(base64Decoded) as TokenClaims;\r\n        } catch (err) {\r\n            throw ClientAuthError.createTokenParsingError(err);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Determine if the token's max_age has transpired\r\n     */\r\n    static checkMaxAge(authTime: number, maxAge: number): void {\r\n        /*\r\n         * per https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest\r\n         * To force an immediate re-authentication: If an app requires that a user re-authenticate prior to access,\r\n         * provide a value of 0 for the max_age parameter and the AS will force a fresh login.\r\n         */\r\n        const fiveMinuteSkew = 300000; // five minutes in milliseconds\r\n        if ((maxAge === 0) || ((Date.now() - fiveMinuteSkew) > (authTime + maxAge))) {\r\n            throw ClientAuthError.createMaxAgeTranspiredError();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAQH;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;IAMI,SAAY,SAAA,CAAA,QAAgB,EAAE,MAAe,EAAA;AACzC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC/B,YAAA,MAAM,eAAe,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;AAC/D,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KAChE;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,kBAAkB,GAAzB,UAA0B,YAAoB,EAAE,MAAe,EAAA;QAE3D,IAAM,YAAY,GAAqB,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;;QAGjF,IAAI;AACA,YAAA,IAAM,kBAAkB,GAAG,YAAY,CAAC,UAAU,CAAC;;YAGnD,IAAM,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;AAC9D,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAgB,CAAC;AACnD,SAAA;AAAC,QAAA,OAAO,GAAG,EAAE;AACV,YAAA,MAAM,eAAe,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;AACtD,SAAA;KACJ,CAAA;AAED;;AAEG;AACI,IAAA,SAAA,CAAA,WAAW,GAAlB,UAAmB,QAAgB,EAAE,MAAc,EAAA;AAC/C;;;;AAIG;AACH,QAAA,IAAM,cAAc,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,KAAK,QAAQ,GAAG,MAAM,CAAC,CAAC,EAAE;AACzE,YAAA,MAAM,eAAe,CAAC,2BAA2B,EAAE,CAAC;AACvD,SAAA;KACJ,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;"}