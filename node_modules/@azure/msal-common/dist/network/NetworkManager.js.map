{"version": 3, "file": "NetworkManager.js", "sources": ["../../src/network/NetworkManager.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { INetworkModule, NetworkRequestOptions } from \"./INetworkModule\";\r\nimport { RequestThumbprint } from \"./RequestThumbprint\";\r\nimport { ThrottlingUtils } from \"./ThrottlingUtils\";\r\nimport { CacheManager } from \"../cache/CacheManager\";\r\nimport { AuthError } from \"../error/AuthError\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\n\r\nexport type NetworkResponse<T> = {\r\n    headers: Record<string, string>;\r\n    body: T;\r\n    status: number;\r\n};\r\n\r\nexport type UrlToHttpRequestOptions = {\r\n    protocol: string;\r\n    hostname: string;\r\n    hash: string;\r\n    search: string;\r\n    pathname: string;\r\n    path: string;\r\n    href: string;\r\n    port?: number;\r\n    auth?: string;\r\n};\r\n\r\nexport class NetworkManager {\r\n    private networkClient: INetworkModule;\r\n    private cacheManager: CacheManager;\r\n\r\n    constructor(networkClient: INetworkModule, cacheManager: CacheManager) {\r\n        this.networkClient = networkClient;\r\n        this.cacheManager = cacheManager;\r\n    }\r\n\r\n    /**\r\n     * Wraps sendPostRequestAsync with necessary preflight and postflight logic\r\n     * @param thumbprint\r\n     * @param tokenEndpoint\r\n     * @param options\r\n     */\r\n    async sendPostRequest<T>(thumbprint: RequestThumbprint, tokenEndpoint: string, options: NetworkRequestOptions): Promise<NetworkResponse<T>> {\r\n        ThrottlingUtils.preProcess(this.cacheManager, thumbprint);\r\n\r\n        let response;\r\n        try {\r\n            response = await this.networkClient.sendPostRequestAsync<T>(tokenEndpoint, options);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                throw e;\r\n            } else {\r\n                throw ClientAuthError.createNetworkError(tokenEndpoint, e);\r\n            }\r\n        }\r\n\r\n        ThrottlingUtils.postProcess(this.cacheManager, thumbprint, response);\r\n\r\n        return response;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AA2BH,IAAA,cAAA,kBAAA,YAAA;IAII,SAAY,cAAA,CAAA,aAA6B,EAAE,YAA0B,EAAA;AACjE,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC;AAED;;;;;AAKG;AACG,IAAA,cAAA,CAAA,SAAA,CAAA,eAAe,GAArB,UAAyB,UAA6B,EAAE,aAAqB,EAAE,OAA8B,EAAA;;;;;;wBACzG,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;;;;wBAI3C,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAI,aAAa,EAAE,OAAO,CAAC,CAAA,CAAA;;wBAAnF,QAAQ,GAAG,SAAwE,CAAC;;;;wBAEpF,IAAI,GAAC,YAAY,SAAS,EAAE;AACxB,4BAAA,MAAM,GAAC,CAAC;AACX,yBAAA;AAAM,6BAAA;4BACH,MAAM,eAAe,CAAC,kBAAkB,CAAC,aAAa,EAAE,GAAC,CAAC,CAAC;AAC9D,yBAAA;;wBAGL,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AAErE,wBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;;;;AACnB,KAAA,CAAA;IACL,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA;;;;"}