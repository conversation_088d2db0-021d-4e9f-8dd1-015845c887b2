{"version": 3, "file": "ProtocolUtils.js", "sources": ["../../src/utils/ProtocolUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { StringUtils } from \"./StringUtils\";\r\nimport { Constants } from \"./Constants\";\r\nimport { ICrypto } from \"../crypto/ICrypto\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\n\r\n/**\r\n * Type which defines the object that is stringified, encoded and sent in the state value.\r\n * Contains the following:\r\n * - id - unique identifier for this request\r\n * - ts - timestamp for the time the request was made. Used to ensure that token expiration is not calculated incorrectly.\r\n * - platformState - string value sent from the platform.\r\n */\r\nexport type LibraryStateObject = {\r\n    id: string,\r\n    meta?: Record<string, string>\r\n};\r\n\r\n/**\r\n * Type which defines the stringified and encoded object sent to the service in the authorize request.\r\n */\r\nexport type RequestStateObject = {\r\n    userRequestState: string,\r\n    libraryState: LibraryStateObject\r\n};\r\n\r\n/**\r\n * Class which provides helpers for OAuth 2.0 protocol specific values\r\n */\r\nexport class ProtocolUtils {\r\n\r\n    /**\r\n     * Appends user state with random guid, or returns random guid.\r\n     * @param userState \r\n     * @param randomGuid \r\n     */\r\n    static setRequestState(cryptoObj: ICrypto, userState?: string, meta?: Record<string, string>): string {\r\n        const libraryState = ProtocolUtils.generateLibraryState(cryptoObj, meta);\r\n        return !StringUtils.isEmpty(userState) ? `${libraryState}${Constants.RESOURCE_DELIM}${userState}` : libraryState;\r\n    }\r\n\r\n    /**\r\n     * Generates the state value used by the common library.\r\n     * @param randomGuid \r\n     * @param cryptoObj \r\n     */\r\n    static generateLibraryState(cryptoObj: ICrypto, meta?: Record<string, string>): string {\r\n        if (!cryptoObj) {\r\n            throw ClientAuthError.createNoCryptoObjectError(\"generateLibraryState\");\r\n        }\r\n\r\n        // Create a state object containing a unique id and the timestamp of the request creation\r\n        const stateObj: LibraryStateObject = {\r\n            id: cryptoObj.createNewGuid()\r\n        };\r\n\r\n        if (meta) {\r\n            stateObj.meta = meta;\r\n        }\r\n\r\n        const stateString = JSON.stringify(stateObj);\r\n\r\n        return cryptoObj.base64Encode(stateString);\r\n    }\r\n\r\n    /**\r\n     * Parses the state into the RequestStateObject, which contains the LibraryState info and the state passed by the user.\r\n     * @param state \r\n     * @param cryptoObj \r\n     */\r\n    static parseRequestState(cryptoObj: ICrypto, state: string): RequestStateObject {\r\n        if (!cryptoObj) {\r\n            throw ClientAuthError.createNoCryptoObjectError(\"parseRequestState\");\r\n        }\r\n\r\n        if (StringUtils.isEmpty(state)) {\r\n            throw ClientAuthError.createInvalidStateError(state, \"Null, undefined or empty state\");\r\n        }\r\n\r\n        try {\r\n            // Split the state between library state and user passed state and decode them separately\r\n            const splitState = state.split(Constants.RESOURCE_DELIM);\r\n            const libraryState = splitState[0];\r\n            const userState = splitState.length > 1 ? splitState.slice(1).join(Constants.RESOURCE_DELIM) : Constants.EMPTY_STRING;\r\n            const libraryStateString = cryptoObj.base64Decode(libraryState);\r\n            const libraryStateObj = JSON.parse(libraryStateString) as LibraryStateObject;\r\n            return {\r\n                userRequestState: !StringUtils.isEmpty(userState) ? userState : Constants.EMPTY_STRING,\r\n                libraryState: libraryStateObj\r\n            };\r\n        } catch(e) {\r\n            throw ClientAuthError.createInvalidStateError(state, e);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AA2BH;;AAEG;AACH,IAAA,aAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,aAAA,GAAA;KAiEC;AA/DG;;;;AAIG;AACI,IAAA,aAAA,CAAA,eAAe,GAAtB,UAAuB,SAAkB,EAAE,SAAkB,EAAE,IAA6B,EAAA;QACxF,IAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAA,GAAG,YAAY,GAAG,SAAS,CAAC,cAAc,GAAG,SAAW,GAAG,YAAY,CAAC;KACpH,CAAA;AAED;;;;AAIG;AACI,IAAA,aAAA,CAAA,oBAAoB,GAA3B,UAA4B,SAAkB,EAAE,IAA6B,EAAA;QACzE,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,MAAM,eAAe,CAAC,yBAAyB,CAAC,sBAAsB,CAAC,CAAC;AAC3E,SAAA;;AAGD,QAAA,IAAM,QAAQ,GAAuB;AACjC,YAAA,EAAE,EAAE,SAAS,CAAC,aAAa,EAAE;SAChC,CAAC;AAEF,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,SAAA;QAED,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAE7C,QAAA,OAAO,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KAC9C,CAAA;AAED;;;;AAIG;AACI,IAAA,aAAA,CAAA,iBAAiB,GAAxB,UAAyB,SAAkB,EAAE,KAAa,EAAA;QACtD,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,MAAM,eAAe,CAAC,yBAAyB,CAAC,mBAAmB,CAAC,CAAC;AACxE,SAAA;AAED,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,eAAe,CAAC,uBAAuB,CAAC,KAAK,EAAE,gCAAgC,CAAC,CAAC;AAC1F,SAAA;QAED,IAAI;;YAEA,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AACzD,YAAA,IAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,YAAA,IAAM,SAAS,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC;YACtH,IAAM,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAChE,IAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAuB,CAAC;YAC7E,OAAO;AACH,gBAAA,gBAAgB,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,YAAY;AACtF,gBAAA,YAAY,EAAE,eAAe;aAChC,CAAC;AACL,SAAA;AAAC,QAAA,OAAM,CAAC,EAAE;YACP,MAAM,eAAe,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3D,SAAA;KACJ,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA;;;;"}