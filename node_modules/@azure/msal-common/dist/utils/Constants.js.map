{"version": 3, "file": "Constants.js", "sources": ["../../src/utils/Constants.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nexport const Constants = {\r\n    LIBRARY_NAME: \"MSAL.JS\",\r\n    SKU: \"msal.js.common\",\r\n    // Prefix for all library cache entries\r\n    CACHE_PREFIX: \"msal\",\r\n    // default authority\r\n    DEFAULT_AUTHORITY: \"https://login.microsoftonline.com/common/\",\r\n    DEFAULT_AUTHORITY_HOST: \"login.microsoftonline.com\",\r\n    DEFAULT_COMMON_TENANT: \"common\",\r\n    // ADFS String\r\n    ADFS: \"adfs\",\r\n    DSTS: \"dstsv2\",\r\n    // Default AAD Instance Discovery Endpoint\r\n    AAD_INSTANCE_DISCOVERY_ENDPT: \"https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=\",\r\n    // CIAM URL\r\n    CIAM_AUTH_URL: \".ciamlogin.com\",\r\n    AAD_TENANT_DOMAIN_SUFFIX: \".onmicrosoft.com\",\r\n    // Resource delimiter - used for certain cache entries\r\n    RESOURCE_DELIM: \"|\",\r\n    // Placeholder for non-existent account ids/objects\r\n    NO_ACCOUNT: \"NO_ACCOUNT\",\r\n    // Claims\r\n    CLAIMS: \"claims\",\r\n    // Consumer UTID\r\n    CONSUMER_UTID: \"9188040d-6c67-4c5b-b112-36a304b66dad\",\r\n    // Default scopes\r\n    OPENID_SCOPE: \"openid\",\r\n    PROFILE_SCOPE: \"profile\",\r\n    OFFLINE_ACCESS_SCOPE: \"offline_access\",\r\n    EMAIL_SCOPE: \"email\",\r\n    // Default response type for authorization code flow\r\n    CODE_RESPONSE_TYPE: \"code\",\r\n    CODE_GRANT_TYPE: \"authorization_code\",\r\n    RT_GRANT_TYPE: \"refresh_token\",\r\n    FRAGMENT_RESPONSE_MODE: \"fragment\",\r\n    S256_CODE_CHALLENGE_METHOD: \"S256\",\r\n    URL_FORM_CONTENT_TYPE: \"application/x-www-form-urlencoded;charset=utf-8\",\r\n    AUTHORIZATION_PENDING: \"authorization_pending\",\r\n    NOT_DEFINED: \"not_defined\",\r\n    EMPTY_STRING: \"\",\r\n    NOT_APPLICABLE: \"N/A\",\r\n    FORWARD_SLASH: \"/\",\r\n    IMDS_ENDPOINT: \"http://169.254.169.254/metadata/instance/compute/location\",\r\n    IMDS_VERSION: \"2020-06-01\",\r\n    IMDS_TIMEOUT: 2000,\r\n    AZURE_REGION_AUTO_DISCOVER_FLAG: \"TryAutoDetect\",\r\n    REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX: \"login.microsoft.com\",\r\n    REGIONAL_AUTH_NON_MSI_QUERY_STRING: \"allowestsrnonmsi=true\",\r\n    KNOWN_PUBLIC_CLOUDS: [\"login.microsoftonline.com\", \"login.windows.net\", \"login.microsoft.com\", \"sts.windows.net\"],\r\n    TOKEN_RESPONSE_TYPE: \"token\",\r\n    ID_TOKEN_RESPONSE_TYPE: \"id_token\",\r\n    SHR_NONCE_VALIDITY: 240,\r\n    INVALID_INSTANCE: \"invalid_instance\",\r\n};\r\n\r\nexport const OIDC_DEFAULT_SCOPES = [\r\n    Constants.OPENID_SCOPE,\r\n    Constants.PROFILE_SCOPE,\r\n    Constants.OFFLINE_ACCESS_SCOPE\r\n];\r\n\r\nexport const OIDC_SCOPES = [\r\n    ...OIDC_DEFAULT_SCOPES,\r\n    Constants.EMAIL_SCOPE\r\n];\r\n\r\n/**\r\n * Request header names\r\n */\r\nexport enum HeaderNames {\r\n    CONTENT_TYPE = \"Content-Type\",\r\n    RETRY_AFTER = \"Retry-After\",\r\n    CCS_HEADER = \"X-AnchorMailbox\",\r\n    WWWAuthenticate = \"WWW-Authenticate\",\r\n    AuthenticationInfo = \"Authentication-Info\",\r\n    X_MS_REQUEST_ID = \"x-ms-request-id\",\r\n    X_MS_HTTP_VERSION= \"x-ms-httpver\"\r\n}\r\n\r\n/**\r\n * Persistent cache keys MSAL which stay while user is logged in.\r\n */\r\nexport enum PersistentCacheKeys {\r\n    ID_TOKEN = \"idtoken\",\r\n    CLIENT_INFO = \"client.info\",\r\n    ADAL_ID_TOKEN = \"adal.idtoken\",\r\n    ERROR = \"error\",\r\n    ERROR_DESC = \"error.description\",\r\n    ACTIVE_ACCOUNT = \"active-account\", // Legacy active-account cache key, use new key instead\r\n    ACTIVE_ACCOUNT_FILTERS = \"active-account-filters\" // new cache entry for active_account for a more robust version for browser\r\n}\r\n\r\n/**\r\n * String constants related to AAD Authority\r\n */\r\nexport enum AADAuthorityConstants {\r\n    COMMON = \"common\",\r\n    ORGANIZATIONS = \"organizations\",\r\n    CONSUMERS = \"consumers\"\r\n}\r\n\r\n/**\r\n * Keys in the hashParams sent by AAD Server\r\n */\r\nexport enum AADServerParamKeys {\r\n    CLIENT_ID = \"client_id\",\r\n    REDIRECT_URI = \"redirect_uri\",\r\n    RESPONSE_TYPE = \"response_type\",\r\n    RESPONSE_MODE = \"response_mode\",\r\n    GRANT_TYPE = \"grant_type\",\r\n    CLAIMS = \"claims\",\r\n    SCOPE = \"scope\",\r\n    ERROR = \"error\",\r\n    ERROR_DESCRIPTION = \"error_description\",\r\n    ACCESS_TOKEN = \"access_token\",\r\n    ID_TOKEN = \"id_token\",\r\n    REFRESH_TOKEN = \"refresh_token\",\r\n    EXPIRES_IN = \"expires_in\",\r\n    STATE = \"state\",\r\n    NONCE = \"nonce\",\r\n    PROMPT = \"prompt\",\r\n    SESSION_STATE = \"session_state\",\r\n    CLIENT_INFO = \"client_info\",\r\n    CODE = \"code\",\r\n    CODE_CHALLENGE = \"code_challenge\",\r\n    CODE_CHALLENGE_METHOD = \"code_challenge_method\",\r\n    CODE_VERIFIER = \"code_verifier\",\r\n    CLIENT_REQUEST_ID = \"client-request-id\",\r\n    X_CLIENT_SKU = \"x-client-SKU\",\r\n    X_CLIENT_VER = \"x-client-VER\",\r\n    X_CLIENT_OS = \"x-client-OS\",\r\n    X_CLIENT_CPU = \"x-client-CPU\",\r\n    X_CLIENT_CURR_TELEM = \"x-client-current-telemetry\",\r\n    X_CLIENT_LAST_TELEM = \"x-client-last-telemetry\",\r\n    X_MS_LIB_CAPABILITY = \"x-ms-lib-capability\",\r\n    X_APP_NAME = \"x-app-name\",\r\n    X_APP_VER = \"x-app-ver\",\r\n    POST_LOGOUT_URI = \"post_logout_redirect_uri\",\r\n    ID_TOKEN_HINT = \"id_token_hint\",\r\n    DEVICE_CODE = \"device_code\",\r\n    CLIENT_SECRET = \"client_secret\",\r\n    CLIENT_ASSERTION = \"client_assertion\",\r\n    CLIENT_ASSERTION_TYPE = \"client_assertion_type\",\r\n    TOKEN_TYPE = \"token_type\",\r\n    REQ_CNF = \"req_cnf\",\r\n    OBO_ASSERTION = \"assertion\",\r\n    REQUESTED_TOKEN_USE = \"requested_token_use\",\r\n    ON_BEHALF_OF = \"on_behalf_of\",\r\n    FOCI = \"foci\",\r\n    CCS_HEADER = \"X-AnchorMailbox\",\r\n    RETURN_SPA_CODE = \"return_spa_code\",\r\n    NATIVE_BROKER = \"nativebroker\",\r\n    LOGOUT_HINT = \"logout_hint\"\r\n}\r\n\r\n/**\r\n * Claims request keys\r\n */\r\nexport enum ClaimsRequestKeys {\r\n    ACCESS_TOKEN = \"access_token\",\r\n    XMS_CC = \"xms_cc\"\r\n}\r\n\r\n/**\r\n * we considered making this \"enum\" in the request instead of string, however it looks like the allowed list of\r\n * prompt values kept changing over past couple of years. There are some undocumented prompt values for some\r\n * internal partners too, hence the choice of generic \"string\" type instead of the \"enum\"\r\n */\r\nexport const PromptValue = {\r\n    LOGIN: \"login\",\r\n    SELECT_ACCOUNT: \"select_account\",\r\n    CONSENT: \"consent\",\r\n    NONE: \"none\",\r\n    CREATE: \"create\",\r\n    NO_SESSION: \"no_session\"\r\n};\r\n\r\n/**\r\n * SSO Types - generated to populate hints\r\n */\r\nexport enum SSOTypes {\r\n    ACCOUNT = \"account\",\r\n    SID = \"sid\",\r\n    LOGIN_HINT = \"login_hint\",\r\n    ID_TOKEN = \"id_token\",\r\n    DOMAIN_HINT = \"domain_hint\",\r\n    ORGANIZATIONS = \"organizations\",\r\n    CONSUMERS = \"consumers\",\r\n    ACCOUNT_ID = \"accountIdentifier\",\r\n    HOMEACCOUNT_ID = \"homeAccountIdentifier\"\r\n}\r\n\r\n/**\r\n * allowed values for codeVerifier\r\n */\r\nexport const CodeChallengeMethodValues = {\r\n    PLAIN: \"plain\",\r\n    S256: \"S256\"\r\n};\r\n\r\n/**\r\n * The method used to encode the code verifier for the code challenge parameter. can be one\r\n * of plain or s256. if excluded, code challenge is assumed to be plaintext. for more\r\n * information, see the pkce rcf: https://tools.ietf.org/html/rfc7636\r\n */\r\nexport const CodeChallengeMethodValuesArray: string[] = [\r\n    CodeChallengeMethodValues.PLAIN,\r\n    CodeChallengeMethodValues.S256\r\n];\r\n\r\n/**\r\n * allowed values for response_mode\r\n */\r\nexport enum ResponseMode {\r\n    QUERY = \"query\",\r\n    FRAGMENT = \"fragment\",\r\n    FORM_POST = \"form_post\"\r\n}\r\n\r\n/**\r\n * allowed grant_type\r\n */\r\nexport enum GrantType {\r\n    IMPLICIT_GRANT = \"implicit\",\r\n    AUTHORIZATION_CODE_GRANT = \"authorization_code\",\r\n    CLIENT_CREDENTIALS_GRANT = \"client_credentials\",\r\n    RESOURCE_OWNER_PASSWORD_GRANT = \"password\",\r\n    REFRESH_TOKEN_GRANT = \"refresh_token\",\r\n    DEVICE_CODE_GRANT = \"device_code\",\r\n    JWT_BEARER = \"urn:ietf:params:oauth:grant-type:jwt-bearer\"\r\n}\r\n\r\n/**\r\n * Account types in Cache\r\n */\r\nexport enum CacheAccountType {\r\n    MSSTS_ACCOUNT_TYPE = \"MSSTS\",\r\n    ADFS_ACCOUNT_TYPE = \"ADFS\",\r\n    MSAV1_ACCOUNT_TYPE = \"MSA\",\r\n    GENERIC_ACCOUNT_TYPE = \"Generic\" // NTLM, Kerberos, FBA, Basic etc\r\n}\r\n\r\n/**\r\n * Separators used in cache\r\n */\r\nexport enum Separators {\r\n    CACHE_KEY_SEPARATOR = \"-\",\r\n    CLIENT_INFO_SEPARATOR = \".\"\r\n}\r\n\r\n/**\r\n * Credential Type stored in the cache\r\n */\r\nexport enum CredentialType {\r\n    ID_TOKEN = \"IdToken\",\r\n    ACCESS_TOKEN = \"AccessToken\",\r\n    ACCESS_TOKEN_WITH_AUTH_SCHEME = \"AccessToken_With_AuthScheme\",\r\n    REFRESH_TOKEN = \"RefreshToken\",\r\n}\r\n\r\n/**\r\n * Combine all cache types\r\n */\r\nexport enum CacheType {\r\n    ADFS = 1001,\r\n    MSA = 1002,\r\n    MSSTS = 1003,\r\n    GENERIC = 1004,\r\n    ACCESS_TOKEN = 2001,\r\n    REFRESH_TOKEN = 2002,\r\n    ID_TOKEN = 2003,\r\n    APP_METADATA = 3001,\r\n    UNDEFINED = 9999\r\n}\r\n\r\n/**\r\n * More Cache related constants\r\n */\r\nexport const APP_METADATA = \"appmetadata\";\r\nexport const CLIENT_INFO = \"client_info\";\r\nexport const THE_FAMILY_ID = \"1\";\r\n\r\nexport const AUTHORITY_METADATA_CONSTANTS = {\r\n    CACHE_KEY: \"authority-metadata\",\r\n    REFRESH_TIME_SECONDS: 3600 * 24 // 24 Hours\r\n};\r\n\r\nexport enum AuthorityMetadataSource {\r\n    CONFIG = \"config\",\r\n    CACHE = \"cache\",\r\n    NETWORK = \"network\",\r\n    HARDCODED_VALUES= \"hardcoded_values\",\r\n}\r\n\r\nexport const SERVER_TELEM_CONSTANTS = {\r\n    SCHEMA_VERSION: 5,\r\n    MAX_CUR_HEADER_BYTES: 80, // ESTS limit is 100B, set to 80 to provide a 20B buffer\r\n    MAX_LAST_HEADER_BYTES: 330, // ESTS limit is 350B, set to 330 to provide a 20B buffer,\r\n    MAX_CACHED_ERRORS: 50, // Limit the number of errors that can be stored to prevent uncontrolled size gains\r\n    CACHE_KEY: \"server-telemetry\",\r\n    CATEGORY_SEPARATOR: \"|\",\r\n    VALUE_SEPARATOR: \",\",\r\n    OVERFLOW_TRUE: \"1\",\r\n    OVERFLOW_FALSE: \"0\",\r\n    UNKNOWN_ERROR: \"unknown_error\"\r\n};\r\n\r\n/**\r\n * Type of the authentication request\r\n */\r\nexport enum AuthenticationScheme {\r\n    BEARER = \"Bearer\",\r\n    POP = \"pop\",\r\n    SSH = \"ssh-cert\"\r\n}\r\n\r\n/**\r\n * Constants related to throttling\r\n */\r\nexport const ThrottlingConstants = {\r\n    // Default time to throttle RequestThumbprint in seconds\r\n    DEFAULT_THROTTLE_TIME_SECONDS: 60,\r\n    // Default maximum time to throttle in seconds, overrides what the server sends back\r\n    DEFAULT_MAX_THROTTLE_TIME_SECONDS: 3600,\r\n    // Prefix for storing throttling entries\r\n    THROTTLING_PREFIX: \"throttling\",\r\n    // Value assigned to the x-ms-lib-capability header to indicate to the server the library supports throttling\r\n    X_MS_LIB_CAPABILITY_VALUE: \"retry-after, h429\"\r\n};\r\n\r\nexport const Errors = {\r\n    INVALID_GRANT_ERROR: \"invalid_grant\",\r\n    CLIENT_MISMATCH_ERROR: \"client_mismatch\",\r\n};\r\n\r\n/**\r\n * Password grant parameters\r\n */\r\nexport enum PasswordGrantConstants {\r\n    username = \"username\",\r\n    password = \"password\"\r\n}\r\n\r\n/**\r\n * Response codes\r\n */\r\nexport enum  ResponseCodes {\r\n    httpSuccess = 200,\r\n    httpBadRequest = 400\r\n}\r\n\r\n/**\r\n * Region Discovery Sources\r\n */\r\nexport enum RegionDiscoverySources {\r\n    FAILED_AUTO_DETECTION = \"1\",\r\n    INTERNAL_CACHE = \"2\",\r\n    ENVIRONMENT_VARIABLE = \"3\",\r\n    IMDS = \"4\",\r\n}\r\n\r\n/**\r\n * Region Discovery Outcomes\r\n */\r\nexport enum RegionDiscoveryOutcomes {\r\n    CONFIGURED_MATCHES_DETECTED = \"1\",\r\n    CONFIGURED_NO_AUTO_DETECTION = \"2\",\r\n    CONFIGURED_NOT_DETECTED = \"3\",\r\n    AUTO_DETECTION_REQUESTED_SUCCESSFUL = \"4\",\r\n    AUTO_DETECTION_REQUESTED_FAILED = \"5\"\r\n}\r\n\r\nexport enum CacheOutcome {\r\n    NO_CACHE_HIT = \"0\",\r\n    FORCE_REFRESH = \"1\",\r\n    NO_CACHED_ACCESS_TOKEN = \"2\",\r\n    CACHED_ACCESS_TOKEN_EXPIRED = \"3\",\r\n    REFRESH_CACHED_ACCESS_TOKEN = \"4\",\r\n    CLAIMS_REQUESTED_CACHE_SKIPPED = \"5\"\r\n}\r\n\r\nexport enum JsonTypes {\r\n    Jwt = \"JWT\",\r\n    Jwk = \"JWK\",\r\n    Pop = \"pop\"\r\n}\r\n\r\nexport const ONE_DAY_IN_MS = 86400000;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAEU,IAAA,SAAS,GAAG;AACrB,IAAA,YAAY,EAAE,SAAS;AACvB,IAAA,GAAG,EAAE,gBAAgB;;AAErB,IAAA,YAAY,EAAE,MAAM;;AAEpB,IAAA,iBAAiB,EAAE,2CAA2C;AAC9D,IAAA,sBAAsB,EAAE,2BAA2B;AACnD,IAAA,qBAAqB,EAAE,QAAQ;;AAE/B,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,IAAI,EAAE,QAAQ;;AAEd,IAAA,4BAA4B,EAAE,qGAAqG;;AAEnI,IAAA,aAAa,EAAE,gBAAgB;AAC/B,IAAA,wBAAwB,EAAE,kBAAkB;;AAE5C,IAAA,cAAc,EAAE,GAAG;;AAEnB,IAAA,UAAU,EAAE,YAAY;;AAExB,IAAA,MAAM,EAAE,QAAQ;;AAEhB,IAAA,aAAa,EAAE,sCAAsC;;AAErD,IAAA,YAAY,EAAE,QAAQ;AACtB,IAAA,aAAa,EAAE,SAAS;AACxB,IAAA,oBAAoB,EAAE,gBAAgB;AACtC,IAAA,WAAW,EAAE,OAAO;;AAEpB,IAAA,kBAAkB,EAAE,MAAM;AAC1B,IAAA,eAAe,EAAE,oBAAoB;AACrC,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,sBAAsB,EAAE,UAAU;AAClC,IAAA,0BAA0B,EAAE,MAAM;AAClC,IAAA,qBAAqB,EAAE,iDAAiD;AACxE,IAAA,qBAAqB,EAAE,uBAAuB;AAC9C,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,cAAc,EAAE,KAAK;AACrB,IAAA,aAAa,EAAE,GAAG;AAClB,IAAA,aAAa,EAAE,2DAA2D;AAC1E,IAAA,YAAY,EAAE,YAAY;AAC1B,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,+BAA+B,EAAE,eAAe;AAChD,IAAA,iCAAiC,EAAE,qBAAqB;AACxD,IAAA,kCAAkC,EAAE,uBAAuB;IAC3D,mBAAmB,EAAE,CAAC,2BAA2B,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,iBAAiB,CAAC;AACjH,IAAA,mBAAmB,EAAE,OAAO;AAC5B,IAAA,sBAAsB,EAAE,UAAU;AAClC,IAAA,kBAAkB,EAAE,GAAG;AACvB,IAAA,gBAAgB,EAAE,kBAAkB;EACtC;AAEW,IAAA,mBAAmB,GAAG;AAC/B,IAAA,SAAS,CAAC,YAAY;AACtB,IAAA,SAAS,CAAC,aAAa;AACvB,IAAA,SAAS,CAAC,oBAAoB;EAChC;AAEK,IAAM,WAAW,GAAA,cAAA,CACjB,mBAAmB,EAAA;AACtB,IAAA,SAAS,CAAC,WAAW;GACvB;AAEF;;AAEG;IACS,YAQX;AARD,CAAA,UAAY,WAAW,EAAA;AACnB,IAAA,WAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,WAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,WAAA,CAAA,YAAA,CAAA,GAAA,iBAA8B,CAAA;AAC9B,IAAA,WAAA,CAAA,iBAAA,CAAA,GAAA,kBAAoC,CAAA;AACpC,IAAA,WAAA,CAAA,oBAAA,CAAA,GAAA,qBAA0C,CAAA;AAC1C,IAAA,WAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,WAAA,CAAA,mBAAA,CAAA,GAAA,cAAiC,CAAA;AACrC,CAAC,EARW,WAAW,KAAX,WAAW,GAQtB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,oBAQX;AARD,CAAA,UAAY,mBAAmB,EAAA;AAC3B,IAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,SAAoB,CAAA;AACpB,IAAA,mBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,cAA8B,CAAA;AAC9B,IAAA,mBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,mBAAgC,CAAA;AAChC,IAAA,mBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;IACjC,mBAAiD,CAAA,wBAAA,CAAA,GAAA,wBAAA,CAAA;AACrD,CAAC,EARW,mBAAmB,KAAnB,mBAAmB,GAQ9B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,sBAIX;AAJD,CAAA,UAAY,qBAAqB,EAAA;AAC7B,IAAA,qBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,qBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,qBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AAC3B,CAAC,EAJW,qBAAqB,KAArB,qBAAqB,GAIhC,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,mBAiDX;AAjDD,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,kBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,kBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,kBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,kBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,kBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,kBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,kBAAA,CAAA,uBAAA,CAAA,GAAA,uBAA+C,CAAA;AAC/C,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,4BAAkD,CAAA;AAClD,IAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,yBAA+C,CAAA;AAC/C,IAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC3C,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,kBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,kBAAA,CAAA,iBAAA,CAAA,GAAA,0BAA4C,CAAA;AAC5C,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,kBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,kBAAA,CAAA,uBAAA,CAAA,GAAA,uBAA+C,CAAA;AAC/C,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,kBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,WAA2B,CAAA;AAC3B,IAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC3C,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,kBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,iBAA8B,CAAA;AAC9B,IAAA,kBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,cAA8B,CAAA;AAC9B,IAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC/B,CAAC,EAjDW,kBAAkB,KAAlB,kBAAkB,GAiD7B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,kBAGX;AAHD,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACrB,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,GAG5B,EAAA,CAAA,CAAA,CAAA;AAED;;;;AAIG;AACU,IAAA,WAAW,GAAG;AACvB,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,UAAU,EAAE,YAAY;EAC1B;AAEF;;AAEG;IACS,SAUX;AAVD,CAAA,UAAY,QAAQ,EAAA;AAChB,IAAA,QAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,QAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,QAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB,IAAA,QAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,QAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,QAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,QAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,QAAA,CAAA,YAAA,CAAA,GAAA,mBAAgC,CAAA;AAChC,IAAA,QAAA,CAAA,gBAAA,CAAA,GAAA,uBAAwC,CAAA;AAC5C,CAAC,EAVW,QAAQ,KAAR,QAAQ,GAUnB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;AACU,IAAA,yBAAyB,GAAG;AACrC,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,IAAI,EAAE,MAAM;EACd;AAYF;;AAEG;IACS,aAIX;AAJD,CAAA,UAAY,YAAY,EAAA;AACpB,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,YAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,YAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AAC3B,CAAC,EAJW,YAAY,KAAZ,YAAY,GAIvB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,UAQX;AARD,CAAA,UAAY,SAAS,EAAA;AACjB,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,UAA2B,CAAA;AAC3B,IAAA,SAAA,CAAA,0BAAA,CAAA,GAAA,oBAA+C,CAAA;AAC/C,IAAA,SAAA,CAAA,0BAAA,CAAA,GAAA,oBAA+C,CAAA;AAC/C,IAAA,SAAA,CAAA,+BAAA,CAAA,GAAA,UAA0C,CAAA;AAC1C,IAAA,SAAA,CAAA,qBAAA,CAAA,GAAA,eAAqC,CAAA;AACrC,IAAA,SAAA,CAAA,mBAAA,CAAA,GAAA,aAAiC,CAAA;AACjC,IAAA,SAAA,CAAA,YAAA,CAAA,GAAA,6CAA0D,CAAA;AAC9D,CAAC,EARW,SAAS,KAAT,SAAS,GAQpB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,iBAKX;AALD,CAAA,UAAY,gBAAgB,EAAA;AACxB,IAAA,gBAAA,CAAA,oBAAA,CAAA,GAAA,OAA4B,CAAA;AAC5B,IAAA,gBAAA,CAAA,mBAAA,CAAA,GAAA,MAA0B,CAAA;AAC1B,IAAA,gBAAA,CAAA,oBAAA,CAAA,GAAA,KAA0B,CAAA;IAC1B,gBAAgC,CAAA,sBAAA,CAAA,GAAA,SAAA,CAAA;AACpC,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,GAK3B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,WAGX;AAHD,CAAA,UAAY,UAAU,EAAA;AAClB,IAAA,UAAA,CAAA,qBAAA,CAAA,GAAA,GAAyB,CAAA;AACzB,IAAA,UAAA,CAAA,uBAAA,CAAA,GAAA,GAA2B,CAAA;AAC/B,CAAC,EAHW,UAAU,KAAV,UAAU,GAGrB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,eAKX;AALD,CAAA,UAAY,cAAc,EAAA;AACtB,IAAA,cAAA,CAAA,UAAA,CAAA,GAAA,SAAoB,CAAA;AACpB,IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,aAA4B,CAAA;AAC5B,IAAA,cAAA,CAAA,+BAAA,CAAA,GAAA,6BAA6D,CAAA;AAC7D,IAAA,cAAA,CAAA,eAAA,CAAA,GAAA,cAA8B,CAAA;AAClC,CAAC,EALW,cAAc,KAAd,cAAc,GAKzB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,UAUX;AAVD,CAAA,UAAY,SAAS,EAAA;AACjB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA,GAAA,MAAW,CAAA;AACX,IAAA,SAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,KAAU,CAAA;AACV,IAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,GAAA,OAAY,CAAA;AACZ,IAAA,SAAA,CAAA,SAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,GAAA,SAAc,CAAA;AACd,IAAA,SAAA,CAAA,SAAA,CAAA,cAAA,CAAA,GAAA,IAAA,CAAA,GAAA,cAAmB,CAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,eAAA,CAAA,GAAA,IAAA,CAAA,GAAA,eAAoB,CAAA;AACpB,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,GAAA,UAAe,CAAA;AACf,IAAA,SAAA,CAAA,SAAA,CAAA,cAAA,CAAA,GAAA,IAAA,CAAA,GAAA,cAAmB,CAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,GAAA,WAAgB,CAAA;AACpB,CAAC,EAVW,SAAS,KAAT,SAAS,GAUpB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;AACI,IAAM,YAAY,GAAG,cAAc;AACnC,IAAM,WAAW,GAAG,cAAc;AAClC,IAAM,aAAa,GAAG,IAAI;AAEpB,IAAA,4BAA4B,GAAG;AACxC,IAAA,SAAS,EAAE,oBAAoB;AAC/B,IAAA,oBAAoB,EAAE,IAAI,GAAG,EAAE;EACjC;IAEU,wBAKX;AALD,CAAA,UAAY,uBAAuB,EAAA;AAC/B,IAAA,uBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,uBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,uBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAoC,CAAA;AACxC,CAAC,EALW,uBAAuB,KAAvB,uBAAuB,GAKlC,EAAA,CAAA,CAAA,CAAA;AAEY,IAAA,sBAAsB,GAAG;AAClC,IAAA,cAAc,EAAE,CAAC;AACjB,IAAA,oBAAoB,EAAE,EAAE;AACxB,IAAA,qBAAqB,EAAE,GAAG;AAC1B,IAAA,iBAAiB,EAAE,EAAE;AACrB,IAAA,SAAS,EAAE,kBAAkB;AAC7B,IAAA,kBAAkB,EAAE,GAAG;AACvB,IAAA,eAAe,EAAE,GAAG;AACpB,IAAA,aAAa,EAAE,GAAG;AAClB,IAAA,cAAc,EAAE,GAAG;AACnB,IAAA,aAAa,EAAE,eAAe;EAChC;AAEF;;AAEG;IACS,qBAIX;AAJD,CAAA,UAAY,oBAAoB,EAAA;AAC5B,IAAA,oBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,oBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,oBAAA,CAAA,KAAA,CAAA,GAAA,UAAgB,CAAA;AACpB,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,GAI/B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;AACU,IAAA,mBAAmB,GAAG;;AAE/B,IAAA,6BAA6B,EAAE,EAAE;;AAEjC,IAAA,iCAAiC,EAAE,IAAI;;AAEvC,IAAA,iBAAiB,EAAE,YAAY;;AAE/B,IAAA,yBAAyB,EAAE,mBAAmB;EAChD;AAEW,IAAA,MAAM,GAAG;AAClB,IAAA,mBAAmB,EAAE,eAAe;AACpC,IAAA,qBAAqB,EAAE,iBAAiB;EAC1C;AAEF;;AAEG;IACS,uBAGX;AAHD,CAAA,UAAY,sBAAsB,EAAA;AAC9B,IAAA,sBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,sBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACzB,CAAC,EAHW,sBAAsB,KAAtB,sBAAsB,GAGjC,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACU,cAGZ;AAHD,CAAA,UAAa,aAAa,EAAA;AACtB,IAAA,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,GAAA,GAAA,CAAA,GAAA,aAAiB,CAAA;AACjB,IAAA,aAAA,CAAA,aAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAoB,CAAA;AACxB,CAAC,EAHY,aAAa,KAAb,aAAa,GAGzB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,uBAKX;AALD,CAAA,UAAY,sBAAsB,EAAA;AAC9B,IAAA,sBAAA,CAAA,uBAAA,CAAA,GAAA,GAA2B,CAAA;AAC3B,IAAA,sBAAA,CAAA,gBAAA,CAAA,GAAA,GAAoB,CAAA;AACpB,IAAA,sBAAA,CAAA,sBAAA,CAAA,GAAA,GAA0B,CAAA;AAC1B,IAAA,sBAAA,CAAA,MAAA,CAAA,GAAA,GAAU,CAAA;AACd,CAAC,EALW,sBAAsB,KAAtB,sBAAsB,GAKjC,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,wBAMX;AAND,CAAA,UAAY,uBAAuB,EAAA;AAC/B,IAAA,uBAAA,CAAA,6BAAA,CAAA,GAAA,GAAiC,CAAA;AACjC,IAAA,uBAAA,CAAA,8BAAA,CAAA,GAAA,GAAkC,CAAA;AAClC,IAAA,uBAAA,CAAA,yBAAA,CAAA,GAAA,GAA6B,CAAA;AAC7B,IAAA,uBAAA,CAAA,qCAAA,CAAA,GAAA,GAAyC,CAAA;AACzC,IAAA,uBAAA,CAAA,iCAAA,CAAA,GAAA,GAAqC,CAAA;AACzC,CAAC,EANW,uBAAuB,KAAvB,uBAAuB,GAMlC,EAAA,CAAA,CAAA,CAAA;IAEW,aAOX;AAPD,CAAA,UAAY,YAAY,EAAA;AACpB,IAAA,YAAA,CAAA,cAAA,CAAA,GAAA,GAAkB,CAAA;AAClB,IAAA,YAAA,CAAA,eAAA,CAAA,GAAA,GAAmB,CAAA;AACnB,IAAA,YAAA,CAAA,wBAAA,CAAA,GAAA,GAA4B,CAAA;AAC5B,IAAA,YAAA,CAAA,6BAAA,CAAA,GAAA,GAAiC,CAAA;AACjC,IAAA,YAAA,CAAA,6BAAA,CAAA,GAAA,GAAiC,CAAA;AACjC,IAAA,YAAA,CAAA,gCAAA,CAAA,GAAA,GAAoC,CAAA;AACxC,CAAC,EAPW,YAAY,KAAZ,YAAY,GAOvB,EAAA,CAAA,CAAA,CAAA;IAEW,UAIX;AAJD,CAAA,UAAY,SAAS,EAAA;AACjB,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACf,CAAC,EAJW,SAAS,KAAT,SAAS,GAIpB,EAAA,CAAA,CAAA,CAAA;AAEM,IAAM,aAAa,GAAG;;;;"}