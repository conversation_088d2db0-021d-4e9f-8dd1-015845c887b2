/*! @azure/msal-common v13.3.3 2024-06-06 */
'use strict';
import { __extends } from '../_virtual/_tslib.js';
import { Constants } from '../utils/Constants.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * AuthErrorMessage class containing string constants used by error codes and messages.
 */
var AuthErrorMessage = {
    unexpectedError: {
        code: "unexpected_error",
        desc: "Unexpected error in authentication."
    },
    postRequestFailed: {
        code: "post_request_failed",
        desc: "Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."
    }
};
/**
 * General error class thrown by the MSAL.js library.
 */
var AuthError = /** @class */ (function (_super) {
    __extends(AuthError, _super);
    function AuthError(errorCode, errorMessage, suberror) {
        var _this = this;
        var errorString = errorMessage ? errorCode + ": " + errorMessage : errorCode;
        _this = _super.call(this, errorString) || this;
        Object.setPrototypeOf(_this, AuthError.prototype);
        _this.errorCode = errorCode || Constants.EMPTY_STRING;
        _this.errorMessage = errorMessage || Constants.EMPTY_STRING;
        _this.subError = suberror || Constants.EMPTY_STRING;
        _this.name = "AuthError";
        return _this;
    }
    AuthError.prototype.setCorrelationId = function (correlationId) {
        this.correlationId = correlationId;
    };
    /**
     * Creates an error that is thrown when something unexpected happens in the library.
     * @param errDesc
     */
    AuthError.createUnexpectedError = function (errDesc) {
        return new AuthError(AuthErrorMessage.unexpectedError.code, AuthErrorMessage.unexpectedError.desc + ": " + errDesc);
    };
    /**
     * Creates an error for post request failures.
     * @param errDesc
     * @returns
     */
    AuthError.createPostRequestFailed = function (errDesc) {
        return new AuthError(AuthErrorMessage.postRequestFailed.code, AuthErrorMessage.postRequestFailed.desc + ": " + errDesc);
    };
    return AuthError;
}(Error));

export { AuthError, AuthErrorMessage };
//# sourceMappingURL=AuthError.js.map
