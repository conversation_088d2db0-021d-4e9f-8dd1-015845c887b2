{"version": 3, "file": "ServerError.js", "sources": ["../../src/error/ServerError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"./AuthError\";\r\n\r\n/**\r\n * Error thrown when there is an error with the server code, for example, unavailability.\r\n */\r\nexport class ServerError extends AuthError {\r\n\r\n    constructor(errorCode?: string, errorMessage?: string, subError?: string) {\r\n        super(errorCode, errorMessage, subError);\r\n        this.name = \"ServerError\";\r\n\r\n        Object.setPrototypeOf(this, ServerError.prototype);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACH,IAAA,WAAA,kBAAA,UAAA,MAAA,EAAA;IAAiC,SAAS,CAAA,WAAA,EAAA,MAAA,CAAA,CAAA;AAEtC,IAAA,SAAA,WAAA,CAAY,SAAkB,EAAE,YAAqB,EAAE,QAAiB,EAAA;AAAxE,QAAA,IAAA,KAAA,GACI,kBAAM,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC,IAI3C,IAAA,CAAA;AAHG,QAAA,KAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAE1B,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;;KACtD;IACL,OAAC,WAAA,CAAA;AAAD,CARA,CAAiC,SAAS,CAQzC;;;;"}