{"version": 3, "file": "AuthError.js", "sources": ["../../src/error/AuthError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"../utils/Constants\";\r\n\r\n/**\r\n * AuthErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const AuthErrorMessage = {\r\n    unexpectedError: {\r\n        code: \"unexpected_error\",\r\n        desc: \"Unexpected error in authentication.\"\r\n    },\r\n    postRequestFailed: {\r\n        code: \"post_request_failed\",\r\n        desc: \"Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details.\"\r\n    }\r\n};\r\n\r\n/**\r\n * General error class thrown by the MSAL.js library.\r\n */\r\nexport class AuthError extends Error {\r\n\r\n    /**\r\n     * Short string denoting error\r\n     */\r\n    errorCode: string;\r\n\r\n    /**\r\n     * Detailed description of error\r\n     */\r\n    errorMessage: string;\r\n\r\n    /**\r\n     * Describes the subclass of an error\r\n     */\r\n    subError: string;\r\n\r\n    /**\r\n     * CorrelationId associated with the error\r\n     */\r\n    correlationId: string;\r\n\r\n    constructor(errorCode?: string, errorMessage?: string, suberror?: string) {\r\n        const errorString = errorMessage ? `${errorCode}: ${errorMessage}` : errorCode;\r\n        super(errorString);\r\n        Object.setPrototypeOf(this, AuthError.prototype);\r\n\r\n        this.errorCode = errorCode || Constants.EMPTY_STRING;\r\n        this.errorMessage = errorMessage || Constants.EMPTY_STRING;\r\n        this.subError = suberror || Constants.EMPTY_STRING;\r\n        this.name = \"AuthError\";\r\n    }\r\n\r\n    setCorrelationId(correlationId: string): void {\r\n        this.correlationId = correlationId;\r\n    }\r\n\r\n    /**\r\n     * Creates an error that is thrown when something unexpected happens in the library.\r\n     * @param errDesc\r\n     */\r\n    static createUnexpectedError(errDesc: string): AuthError {\r\n        return new AuthError(AuthErrorMessage.unexpectedError.code, `${AuthErrorMessage.unexpectedError.desc}: ${errDesc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error for post request failures.\r\n     * @param errDesc \r\n     * @returns \r\n     */\r\n    static createPostRequestFailed(errDesc: string): AuthError {\r\n        return new AuthError(AuthErrorMessage.postRequestFailed.code, `${AuthErrorMessage.postRequestFailed.desc}: ${errDesc}`);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,IAAA,gBAAgB,GAAG;AAC5B,IAAA,eAAe,EAAE;AACb,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,qCAAqC;AAC9C,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,sIAAsI;AAC/I,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,SAAA,kBAAA,UAAA,MAAA,EAAA;IAA+B,SAAK,CAAA,SAAA,EAAA,MAAA,CAAA,CAAA;AAsBhC,IAAA,SAAA,SAAA,CAAY,SAAkB,EAAE,YAAqB,EAAE,QAAiB,EAAA;QAAxE,IASC,KAAA,GAAA,IAAA,CAAA;AARG,QAAA,IAAM,WAAW,GAAG,YAAY,GAAM,SAAS,GAAA,IAAA,GAAK,YAAc,GAAG,SAAS,CAAC;QAC/E,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,WAAW,CAAC,IAAC,IAAA,CAAA;QACnB,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAEjD,KAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC;QACrD,KAAI,CAAC,YAAY,GAAG,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC;QAC3D,KAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,SAAS,CAAC,YAAY,CAAC;AACnD,QAAA,KAAI,CAAC,IAAI,GAAG,WAAW,CAAC;;KAC3B;IAED,SAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,aAAqB,EAAA;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC,CAAA;AAED;;;AAGG;IACI,SAAqB,CAAA,qBAAA,GAA5B,UAA6B,OAAe,EAAA;AACxC,QAAA,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,EAAK,gBAAgB,CAAC,eAAe,CAAC,IAAI,GAAK,IAAA,GAAA,OAAS,CAAC,CAAC;KACvH,CAAA;AAED;;;;AAIG;IACI,SAAuB,CAAA,uBAAA,GAA9B,UAA+B,OAAe,EAAA;AAC1C,QAAA,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,EAAK,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,GAAK,IAAA,GAAA,OAAS,CAAC,CAAC;KAC3H,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CArDA,CAA+B,KAAK,CAqDnC;;;;"}