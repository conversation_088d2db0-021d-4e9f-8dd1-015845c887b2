{"version": 3, "file": "InteractionRequiredAuthError.js", "sources": ["../../src/error/InteractionRequiredAuthError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"../utils/Constants\";\r\nimport { AuthError } from \"./AuthError\";\r\n\r\n/**\r\n * InteractionRequiredServerErrorMessage contains string constants used by error codes and messages returned by the server indicating interaction is required\r\n */\r\nexport const InteractionRequiredServerErrorMessage = [\r\n    \"interaction_required\",\r\n    \"consent_required\",\r\n    \"login_required\"\r\n];\r\n\r\nexport const InteractionRequiredAuthSubErrorMessage = [\r\n    \"message_only\",\r\n    \"additional_action\",\r\n    \"basic_action\",\r\n    \"user_password_expired\",\r\n    \"consent_required\"\r\n];\r\n\r\n/**\r\n * Interaction required errors defined by the SDK\r\n */\r\nexport const InteractionRequiredAuthErrorMessage = {\r\n    noTokensFoundError: {\r\n        code: \"no_tokens_found\",\r\n        desc: \"No refresh token found in the cache. Please sign-in.\"\r\n    },\r\n    native_account_unavailable: {\r\n        code: \"native_account_unavailable\",\r\n        desc: \"The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API.\"\r\n    }\r\n};\r\n\r\n/**\r\n * Error thrown when user interaction is required.\r\n */\r\nexport class InteractionRequiredAuthError extends AuthError {\r\n    /**\r\n     * The time the error occured at\r\n     */\r\n    timestamp: string;\r\n\r\n    /**\r\n     * TraceId associated with the error\r\n     */\r\n    traceId: string;\r\n\r\n    /**\r\n     * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-common/docs/claims-challenge.md\r\n     * \r\n     * A string with extra claims needed for the token request to succeed\r\n     * web site: redirect the user to the authorization page and set the extra claims\r\n     * web api: include the claims in the WWW-Authenticate header that are sent back to the client so that it knows to request a token with the extra claims\r\n     * desktop application or browser context: include the claims when acquiring the token interactively\r\n     * app to app context (client_credentials): include the claims in the AcquireTokenByClientCredential request\r\n     */\r\n    claims: string;\r\n\r\n    constructor(errorCode?: string, errorMessage?: string, subError?: string, timestamp?: string, traceId?: string, correlationId?: string, claims?: string) {\r\n        super(errorCode, errorMessage, subError);\r\n        Object.setPrototypeOf(this, InteractionRequiredAuthError.prototype);\r\n        \r\n        this.timestamp = timestamp || Constants.EMPTY_STRING;\r\n        this.traceId = traceId || Constants.EMPTY_STRING;\r\n        this.correlationId = correlationId || Constants.EMPTY_STRING;\r\n        this.claims = claims || Constants.EMPTY_STRING;\r\n        this.name = \"InteractionRequiredAuthError\";\r\n    }\r\n\r\n    /**\r\n     * Helper function used to determine if an error thrown by the server requires interaction to resolve\r\n     * @param errorCode \r\n     * @param errorString \r\n     * @param subError \r\n     */\r\n    static isInteractionRequiredError(errorCode?: string, errorString?: string, subError?: string): boolean {\r\n        const isInteractionRequiredErrorCode = !!errorCode && InteractionRequiredServerErrorMessage.indexOf(errorCode) > -1;\r\n        const isInteractionRequiredSubError = !!subError && InteractionRequiredAuthSubErrorMessage.indexOf(subError) > -1;\r\n        const isInteractionRequiredErrorDesc = !!errorString && InteractionRequiredServerErrorMessage.some((irErrorCode) => {\r\n            return errorString.indexOf(irErrorCode) > -1;\r\n        });\r\n\r\n        return isInteractionRequiredErrorCode || isInteractionRequiredErrorDesc || isInteractionRequiredSubError;\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the authorization code required for a token request is null or empty.\r\n     */\r\n    static createNoTokensFoundError(): InteractionRequiredAuthError {\r\n        return new InteractionRequiredAuthError(InteractionRequiredAuthErrorMessage.noTokensFoundError.code, InteractionRequiredAuthErrorMessage.noTokensFoundError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the native broker returns ACCOUNT_UNAVAILABLE status, indicating that the account was removed and interactive sign-in is required\r\n     * @returns \r\n     */\r\n    static createNativeAccountUnavailableError(): InteractionRequiredAuthError {\r\n        return new InteractionRequiredAuthError(InteractionRequiredAuthErrorMessage.native_account_unavailable.code, InteractionRequiredAuthErrorMessage.native_account_unavailable.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAKH;;AAEG;AACU,IAAA,qCAAqC,GAAG;IACjD,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;EAClB;AAEW,IAAA,sCAAsC,GAAG;IAClD,cAAc;IACd,mBAAmB;IACnB,cAAc;IACd,uBAAuB;IACvB,kBAAkB;EACpB;AAEF;;AAEG;AACU,IAAA,mCAAmC,GAAG;AAC/C,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,sDAAsD;AAC/D,KAAA;AACD,IAAA,0BAA0B,EAAE;AACxB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,qJAAqJ;AAC9J,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,4BAAA,kBAAA,UAAA,MAAA,EAAA;IAAkD,SAAS,CAAA,4BAAA,EAAA,MAAA,CAAA,CAAA;AAsBvD,IAAA,SAAA,4BAAA,CAAY,SAAkB,EAAE,YAAqB,EAAE,QAAiB,EAAE,SAAkB,EAAE,OAAgB,EAAE,aAAsB,EAAE,MAAe,EAAA;AAAvJ,QAAA,IAAA,KAAA,GACI,kBAAM,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC,IAQ3C,IAAA,CAAA;QAPG,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,4BAA4B,CAAC,SAAS,CAAC,CAAC;QAEpE,KAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC;QACrD,KAAI,CAAC,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC,YAAY,CAAC;QACjD,KAAI,CAAC,aAAa,GAAG,aAAa,IAAI,SAAS,CAAC,YAAY,CAAC;QAC7D,KAAI,CAAC,MAAM,GAAG,MAAM,IAAI,SAAS,CAAC,YAAY,CAAC;AAC/C,QAAA,KAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;;KAC9C;AAED;;;;;AAKG;AACI,IAAA,4BAAA,CAAA,0BAA0B,GAAjC,UAAkC,SAAkB,EAAE,WAAoB,EAAE,QAAiB,EAAA;AACzF,QAAA,IAAM,8BAA8B,GAAG,CAAC,CAAC,SAAS,IAAI,qCAAqC,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AACpH,QAAA,IAAM,6BAA6B,GAAG,CAAC,CAAC,QAAQ,IAAI,sCAAsC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAClH,IAAM,8BAA8B,GAAG,CAAC,CAAC,WAAW,IAAI,qCAAqC,CAAC,IAAI,CAAC,UAAC,WAAW,EAAA;YAC3G,OAAO,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,8BAA8B,IAAI,8BAA8B,IAAI,6BAA6B,CAAC;KAC5G,CAAA;AAED;;AAEG;AACI,IAAA,4BAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,4BAA4B,CAAC,mCAAmC,CAAC,kBAAkB,CAAC,IAAI,EAAE,mCAAmC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACrK,CAAA;AAED;;;AAGG;AACI,IAAA,4BAAA,CAAA,mCAAmC,GAA1C,YAAA;AACI,QAAA,OAAO,IAAI,4BAA4B,CAAC,mCAAmC,CAAC,0BAA0B,CAAC,IAAI,EAAE,mCAAmC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;KACrL,CAAA;IACL,OAAC,4BAAA,CAAA;AAAD,CA/DA,CAAkD,SAAS,CA+D1D;;;;"}