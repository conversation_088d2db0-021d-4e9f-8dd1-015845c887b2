{"version": 3, "file": "ClientAuthError.d.ts", "sourceRoot": "", "sources": ["../../src/error/ClientAuthError.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC;;GAEG;AACH,eAAO,MAAM,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4MlC,CAAC;AAEF;;GAEG;AACH,qBAAa,eAAgB,SAAQ,SAAS;gBAE9B,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM;IAOpD;;;OAGG;IACH,MAAM,CAAC,6BAA6B,CAAC,WAAW,EAAE,MAAM,GAAG,eAAe;IAK1E;;;OAGG;IACH,MAAM,CAAC,0BAA0B,IAAI,eAAe;IAKpD;;;OAGG;IACH,MAAM,CAAC,uBAAuB,CAAC,qBAAqB,EAAE,MAAM,GAAG,eAAe;IAK9E;;;OAGG;IACH,MAAM,CAAC,2BAA2B,CAAC,qBAAqB,EAAE,MAAM,GAAG,eAAe;IAKlF;;OAEG;IACH,MAAM,CAAC,sCAAsC,CAAC,SAAS,EAAE,MAAM,GAAG,eAAe;IAKjF;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,eAAe;IAK/E;;OAEG;IACH,MAAM,CAAC,kCAAkC,CAAC,SAAS,EAAE,MAAM,GAAG,eAAe;IAK7E;;;OAGG;IACH,MAAM,CAAC,8BAA8B,CAAC,YAAY,EAAE,MAAM,GAAG,eAAe;IAK5E;;;OAGG;IACH,MAAM,CAAC,uBAAuB,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,eAAe;IAK3F;;OAEG;IACH,MAAM,CAAC,wBAAwB,IAAI,eAAe;IAKlD;;;OAGG;IACH,MAAM,CAAC,wBAAwB,CAAC,YAAY,EAAE,MAAM,GAAG,eAAe;IAKtE;;OAEG;IACH,MAAM,CAAC,wBAAwB,IAAI,eAAe;IAKlD;;;OAGG;IACH,MAAM,CAAC,2BAA2B,IAAI,eAAe;IAKrD;;OAEG;IACH,MAAM,CAAC,2BAA2B,IAAI,eAAe;IAKrD;;;OAGG;IACH,MAAM,CAAC,wBAAwB,CAAC,YAAY,EAAE,MAAM,GAAG,eAAe;IAKtE;;OAEG;IACH,MAAM,CAAC,wCAAwC,IAAI,eAAe;IAKlE;;OAEG;IACH,MAAM,CAAC,0CAA0C,IAAI,eAAe;IAKpE;;OAEG;IACH,MAAM,CAAC,6CAA6C,IAAI,eAAe;IAKvE;;OAEG;IACH,MAAM,CAAC,mCAAmC,IAAI,eAAe;IAI7D;;;OAGG;IACH,MAAM,CAAC,gCAAgC,CAAC,UAAU,EAAE,MAAM,GAAG,eAAe;IAI5E;;;OAGG;IACH,MAAM,CAAC,kCAAkC,CAAC,UAAU,EAAE,MAAM,GAAG,eAAe;IAI9E;;;OAGG;IACH,MAAM,CAAC,yBAAyB,CAAC,WAAW,EAAE,MAAM,GAAG,eAAe;IAItE;;;OAGG;IACH,MAAM,CAAC,6BAA6B,IAAI,eAAe;IAIvD;;OAEG;IACH,MAAM,CAAC,8BAA8B,IAAI,eAAe;IAIxD;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,eAAe;IAItD;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,eAAe;IAItD;;OAEG;IACH,MAAM,CAAC,mCAAmC,IAAI,eAAe;IAI7D;;OAEG;IACH,MAAM,CAAC,gCAAgC,IAAI,eAAe;IAI1D;;OAEG;IACH,MAAM,CAAC,kCAAkC,IAAI,eAAe;IAI5D;;OAEG;IACH,MAAM,CAAC,yBAAyB,IAAI,eAAe;IAInD;;OAEG;IACH,MAAM,CAAC,sBAAsB,IAAI,eAAe;IAIhD;;;OAGG;IACH,MAAM,CAAC,yBAAyB,CAAC,aAAa,EAAE,MAAM,GAAG,eAAe;IAIxE;;OAEG;IACH,MAAM,CAAC,2BAA2B,IAAI,eAAe;IAIrD;;OAEG;IACH,MAAM,CAAC,gCAAgC,IAAI,eAAe;IAI1D;;OAEG;IACH,MAAM,CAAC,mCAAmC,IAAI,eAAe;IAI7D;;OAEG;IACH,MAAM,CAAC,2BAA2B,IAAI,eAAe;IAIrD;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,eAAe;IAItD;;OAEG;IACH,MAAM,CAAC,0BAA0B,IAAI,eAAe;IAIpD;;OAEG;IACH,MAAM,CAAC,6BAA6B,IAAI,eAAe;IAOvD,MAAM,CAAC,8BAA8B,IAAI,eAAe;IAIxD;;OAEG;IACH,MAAM,CAAC,qCAAqC,IAAI,eAAe;IAI/D,MAAM,CAAC,+BAA+B,IAAI,eAAe;IAIzD;;OAEG;IACH,MAAM,CAAC,6BAA6B,IAAI,eAAe;IAIvD;;OAEG;IACH,MAAM,CAAC,uBAAuB,IAAI,eAAe;IAIjD;;OAEG;IACH,MAAM,CAAC,gCAAgC,IAAI,eAAe;IAI1D;;OAEG;IACH,MAAM,CAAC,uBAAuB,IAAI,eAAe;CAGpD"}