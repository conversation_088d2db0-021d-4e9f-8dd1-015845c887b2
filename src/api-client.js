/**
 * Chat API Client
 * 
 * This module provides functions to interact with the Chat API
 */

const axios = require('axios');

class FileAnalysisApiClient {
  /**
   * Initialize the API client with the base URL
   * @param {string} baseUrl - The base URL of the Chat API
   */
  constructor(baseUrl = 'http://localhost:8000') {
    this.baseUrl = baseUrl;

    // Get access token from cookies or sessionStorage
    const accessToken = this.getAccessToken();

    const headers = {
      'Content-Type': 'application/json'
    };

    // Add Authorization header if token is available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    this.client = axios.create({
      baseURL: baseUrl,
      timeout: 30000, // 30 seconds timeout
      headers: headers
    });
  }

  /**
   * Get access token from cookies or sessionStorage
   * @returns {string|null} The access token or null if not found
   */
  getAccessToken() {
    // First try to get from sessionStorage (as used in the chat popup)
    if (typeof sessionStorage !== 'undefined') {
      const token = sessionStorage.getItem('accessToken');
      if (token) {
        return token;
      }
    }

    // Fallback to cookies if running in browser environment
    if (typeof document !== 'undefined') {
      return this.getCookieValue('accessToken');
    }

    return null;
  }

  /**
   * Get a cookie value by name
   * @param {string} name - The cookie name
   * @returns {string|null} The cookie value or null if not found
   */
  getCookieValue(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop().split(';').shift();
    }
    return null;
  }

  /**
   * Set the base URL for the API
   * @param {string} url - The base URL for the API
   */
  setBaseUrl(url) {
    this.baseUrl = url;

    // Get access token from cookies or sessionStorage
    const accessToken = this.getAccessToken();

    const headers = {
      'Content-Type': 'application/json'
    };

    // Add Authorization header if token is available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    this.client = axios.create({
      baseURL: url,
      timeout: 30000,
      headers: headers
    });
  }

  /**
   * Update the authorization header with a new access token
   * @param {string} accessToken - The new access token
   */
  updateAuthToken(accessToken) {
    if (accessToken) {
      this.client.defaults.headers['Authorization'] = `Bearer ${accessToken}`;
    } else {
      delete this.client.defaults.headers['Authorization'];
    }
  }

  /**
   * Refresh the authorization header from current storage
   */
  refreshAuthToken() {
    const accessToken = this.getAccessToken();
    this.updateAuthToken(accessToken);
  }

  /**
   * Check if the API server is running
   * @returns {Promise<boolean>} True if the server is healthy
   */
  async checkHealth() {
    try {
      const response = await this.client.get('/health');
      return response.data?.status === 'healthy';
    } catch (error) {
      console.error('Health check failed:', error.message);
      return false;
    }
  }

  /**
   * Send a message to the chat API
   * @param {string} message - The message to send
   * @param {string} accessToken - Optional access token (if not provided, will use stored token)
   * @returns {Promise<string>} Chat response
   */
  async chat(message, accessToken = null) {
    // Refresh auth token from storage before making request
    this.refreshAuthToken();

    // If a specific token is provided, use it for this request
    const headers = {
      'Content-Type': 'application/json'
    };

    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    try {
      const payload = { message };
      console.log('Sending chat request:', payload);

      // Use specific headers if accessToken provided, otherwise use default client headers
      const response = accessToken
        ? await this.client.post('/chat', payload, { headers })
        : await this.client.post('/chat', payload);

      console.log('Raw API response:', response.data);

      if (!response.data || response.data.response === undefined) {
        throw new Error('Invalid response format from server');
      }

      return response.data.response;
    } catch (error) {
      console.error('Chat failed:', error.message);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw new Error(`Chat failed: ${error.response?.data?.detail || error.message}`);
    }
  }

  async getLeaveBalance(accessToken = null) {
    // Refresh auth token from storage before making request
    this.refreshAuthToken();

    // Use provided token or get from storage
    const token = accessToken || this.getAccessToken();

    if (!token) {
      throw new Error('Authentication required - no access token available');
    }

    try {
      const response = await this.client.get('/leave-balance', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Get leave balance failed:', error.message);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw new Error(`Get leave balance failed: ${error.response?.data?.detail || error.message}`);
    }
  }
}

module.exports = FileAnalysisApiClient; 