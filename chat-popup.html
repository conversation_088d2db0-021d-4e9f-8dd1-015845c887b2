<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Chat Popup</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>
  <style>
    /* Set dimensions and reset margins/paddings */
    html, body {
      background: transparent;
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    
    /* Main container with custom styling */
    #chat-container {
      position: relative;
      width: 380px;
      height: 480px;
      border-radius: 16px;
      border: 0.5px solid #CAC7FA;
      background: linear-gradient(106deg, rgb(102 104 105 / 68%) 18.29%, rgb(80 99 126 / 69%) 90.96%), rgb(111 108 108 / 60%);
      backdrop-filter: blur(200px);
      -webkit-backdrop-filter: blur(200px);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }
    
    /* Header with title */
    .chat-header {
      padding: 16px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .title-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .mai-logo {
      width: 28px;
      height: 28px;
      object-fit: contain;
    }
    
    .chat-title {
      color: #FFF;
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
      margin: 0;
    }
    
    .close-button {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 4px 8px;
      opacity: 0.7;
      transition: opacity 0.2s;
    }
    
    .close-button:hover {
      opacity: 1;
    }
    
    /* Messages container */
    #chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 20px 25px;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    /* Message with icon */
    .message-with-icon {
      display: flex;
      gap: 10px;
      align-items: flex-start;
    }
    
    /* Individual message */
    .message {
      max-width: 90%;
      padding: 0;
      color: #FFF;
      font-family: 'Poppins', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5;
    }
    
    /* Add styles for user and assistant messages */
    .message.user {
      align-self: flex-end;
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 12px;
      border-radius: 12px 12px 0 12px;
      margin-left: 40px;
      max-width: 80%;
    }

    .message.assistant {
      align-self: flex-start;
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 12px;
      border-radius: 12px 12px 12px 0;
      margin-right: 40px;
      max-width: 80%;
    }

    .timestamp {
      font-size: 10px;
      opacity: 0.6;
      margin-top: 4px;
      text-align: right;
    }
    
    /* Input area container */
    .input-area {
      padding: 12px 20px;
      background: transparent;
      border-top: none;
      display: flex;
      gap: 8px;
      align-items: center;
    }
    
    /* Input container */
    #chat-input-container {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      height: 24px;
    }
    
    /* Paperclip icon */
    .paperclip-icon {
      opacity: 0.7;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
    
    /* Text input */
    #chat-input {
      flex: 1;
      padding: 0;
      border: none;
      outline: none;
      font-family: 'Poppins', sans-serif;
      font-size: 15px;
      font-weight: 400;
      background-color: transparent;
      color: white;
      height: 24px;
      line-height: 24px;
    }

    #chat-input::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
    
    /* Send button */
    #send-btn {
      background: linear-gradient(135deg, #FF6B4A 0%, #FF4D4D 100%);
      color: white;
      border: none;
      border-radius: 12px;
      width: 48px;
      height: 48px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform 0.2s;
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(255, 107, 74, 0.25);
    }

    #send-btn:hover {
      transform: scale(1.05);
    }

    #send-btn svg {
      width: 24px;
      height: 24px;
      margin-left: 2px;
      margin-top: -2px;
    }
    
    /* Add styles for clear history button */
    .clear-history {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      opacity: 0.7;
      transition: opacity 0.2s;
    }

    .clear-history:hover {
      opacity: 1;
    }

    /* Thinking animation */
    .thinking {
      display: flex;
      align-items: center;
      gap: 5px;
      padding: 10px;
      margin-bottom: 5px;
      background-color: rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      align-self: flex-start;
    }
    
    .thinking span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.8);
      display: inline-block;
      animation: pulse 1s infinite ease-in-out;
    }
    
    .thinking span:nth-child(2) {
      animation-delay: 0.15s;
    }
    
    .thinking span:nth-child(3) {
      animation-delay: 0.3s;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(0.8); opacity: 0.5; }
      50% { transform: scale(1.2); opacity: 1; }
    }

    /* Add typing animation styles */
    @keyframes typing {
      from { width: 0 }
      to { width: 100% }
    }

    .message.assistant .message-text {
      white-space: pre-wrap;
      overflow: hidden;
    }

    .message.assistant.typing .message-text {
      border-right: 2px solid rgba(255, 255, 255, 0.7);
      animation: cursor-blink 0.7s infinite;
    }

    @keyframes cursor-blink {
      0%, 100% { border-color: transparent }
      50% { border-color: rgba(255, 255, 255, 0.7) }
    }

    /* Add styles for initial welcome message */
    .welcome-message {
      color: rgba(255, 255, 255, 0.7);
      text-align: left;
      padding: 5px;
      font-size: 15px;
      font-weight: 400;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .welcome-message .sparkle {
      color: #ff6b4a;
      font-size: 14px;
    }

    /* Add styles for suggestion buttons */
    .suggestion-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 12px 25px;
    }

    .suggestion-button {
      background: rgba(255, 255, 255, 0.1);
      border: 1px dashed rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.8);
      padding: 10px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      text-align: left;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .suggestion-button:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
    }

    .suggestion-button .icon {
      color: #FF6B4A;
      font-size: 16px;
    }

    /* Add styles for agent suggestion message */
    .agent-suggestion {
      background: linear-gradient(106deg, rgba(255, 107, 74, 0.1) 0%, rgba(255, 77, 77, 0.1) 100%);
      border: 1px dashed rgba(255, 107, 74, 0.3);
      border-radius: 12px;
      padding: 16px;
      margin: 12px 0;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      line-height: 1.5;
    }

    .agent-button {
      background: linear-gradient(135deg, #FF6B4A 0%, #FF4D4D 100%);
      border: none;
      border-radius: 8px;
      color: white;
      padding: 12px 20px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      width: 100%;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(255, 107, 74, 0.25);
    }

    .agent-button:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(255, 107, 74, 0.35);
    }

    .agent-button .icon {
      font-size: 18px;
    }

    /* Add styles for markdown content */
    .message-text {
      color: #FFF;
    }

    .message-text p {
      margin: 0 0 10px 0;
    }

    .message-text p:last-child {
      margin-bottom: 0;
    }

    .message-text code {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
    }

    .message-text pre {
      background: rgba(255, 255, 255, 0.1);
      padding: 10px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 10px 0;
    }

    .message-text pre code {
      background: none;
      padding: 0;
    }

    .message-text ul, .message-text ol {
      margin: 0;
      padding-left: 20px;
    }

    .message-text table {
      border-collapse: collapse;
      width: 100%;
      margin: 10px 0;
    }

    .message-text th, .message-text td {
      border: 1px solid rgba(255, 255, 255, 0.2);
      padding: 6px 8px;
    }

    .message-text th {
      background: rgba(255, 255, 255, 0.1);
    }

    /* Add styles for login/logout button */
    .auth-button {
      background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
      border: none;
      border-radius: 8px;
      color: white;
      padding: 8px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 8px;
      margin-right: 8px;
    }

    .auth-button:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 120, 212, 0.35);
    }

    .auth-button.logout {
      background: linear-gradient(135deg, #d40000 0%, #9e0000 100%);
    }
  </style>
</head>
<body>
  <div id="chat-container">
    <!-- Chat header -->
    <div class="chat-header">
      <div class="title-container">
        <img src="assets/mai-logo.png" alt="mAI" class="mai-logo">
        <h1 class="chat-title">mAI Assistant</h1>
      </div>
      <div style="display: flex; gap: 8px; align-items: center;">
        <button id="auth-button" class="auth-button" onclick="handleAuth()">Login</button>
        <button class="clear-history" onclick="clearHistory()">Clear History</button>
        <button class="close-button" onclick="closeWindow()">×</button>
      </div>
    </div>
    
    <!-- Chat messages -->
    <div id="chat-messages">
      <!-- Initial welcome message will be added here -->
    </div>
    
    <!-- Input area -->
    <div class="input-area">
      <div id="chat-input-container">
        <input id="chat-input" type="text" placeholder="Ask Anything" onkeypress="if(event.key === 'Enter') { sendMessage(); return false; }" />
        <svg class="paperclip-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66l-9.2 9.19a2 2 0 01-2.83-2.83l8.49-8.48" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <button id="send-btn" onclick="sendMessage(); return false;">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 2L11 13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>
  </div>

  <script>
    const { ipcRenderer } = require('electron');
    const FileAnalysisApiClient = require('./src/api-client');
    
    // Initialize API client
    let apiClient = new FileAnalysisApiClient('http://localhost:8000');

    // Cookie utility functions
    function setCookie(name, value, days) {
      const expires = new Date();
      expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
      document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
    }

    function getCookie(name) {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) {
        return parts.pop().split(';').shift();
      }
      return null;
    }

    function deleteCookie(name) {
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
    }

    // Initialize MSAL configuration
    const msalConfig = {
      auth: {
        clientId: "6c3325df-19ac-45cf-b54b-8a6d2e196fb6",
        authority: "https://login.microsoftonline.com/organizations",
        redirectUri: "http://localhost:8080",
        postLogoutRedirectUri: "http://localhost:8080",
        navigateToLoginRequestUrl: false
      },
      cache: {
        cacheLocation: "sessionStorage",
        storeAuthStateInCookie: false
      },
      system: {
        loggerOptions: {
          loggerCallback: (level, message, containsPii) => {
            if (containsPii) {
              return;
            }
            switch (level) {
              case 0: // Error
                console.error(message);
                break;
              case 1: // Warning
                console.warn(message);
                break;
              case 2: // Info
                console.info(message);
                break;
              case 3: // Verbose
                console.debug(message);
                break;
            }
          },
          piiLoggingEnabled: false,
          logLevel: 3 // Verbose
        }
      }
    };

    // Initialize MSAL instance
    const msalInstance = new msal.PublicClientApplication(msalConfig);

    // Track login state to prevent multiple simultaneous attempts
    let isLoginInProgress = false;

    // Function to handle authentication
    async function handleAuth() {
      const authButton = document.getElementById('auth-button');

      // Prevent multiple simultaneous login attempts
      if (isLoginInProgress) {
        console.log('Login already in progress, ignoring request');
        return;
      }

      if (authButton.textContent === 'Login') {
        isLoginInProgress = true;
        try {
          console.log('Starting login process...');

          // Define the scopes we need
          const loginRequest = {
            scopes: ["openid", "profile", "offline_access", "User.Read"],
            prompt: "select_account"
          };

          // Add timeout for login process
          const loginTimeout = setTimeout(() => {
            console.warn('Login process taking too long, may have been interrupted');
          }, 60000); // 60 seconds timeout
          
          // First, try to get any existing accounts
          const accounts = msalInstance.getAllAccounts();
          console.log('Existing accounts:', accounts);
          
          if (accounts.length > 0) {
            // If we have an account, try to get token silently
            console.log('Found existing account, attempting silent token acquisition...');
            const account = accounts[0];
            try {
              const tokenResponse = await msalInstance.acquireTokenSilent({
                ...loginRequest,
                account: account
              });
              console.log('Successfully acquired token silently');
              
              // Store token in sessionStorage and cookies
              sessionStorage.setItem('accessToken', tokenResponse.accessToken);
              setCookie('accessToken', tokenResponse.accessToken, 7); // Store for 7 days
              console.log('Token stored in sessionStorage and cookies');
              
              // Update UI
              authButton.textContent = 'Logout';
              authButton.classList.add('logout');
              
              // Add welcome message for logged in user
              const welcomeMessage = document.getElementById('welcome-message');
              if (welcomeMessage) {
                welcomeMessage.innerHTML = `<span class="sparkle">✨</span> Welcome ${account.name}! How can I help you today?`;
              }
            } catch (silentError) {
              console.log('Silent token acquisition failed, falling back to popup:', silentError);
              // If silent token acquisition fails, use popup
              try {
                // Calculate popup position more reliably
                const screenWidth = window.screen.availWidth || window.screen.width;
                const screenHeight = window.screen.availHeight || window.screen.height;
                const popupWidth = 600;
                const popupHeight = 700;

                const loginResponse = await msalInstance.loginPopup({
                  ...loginRequest,
                  popupWindowAttributes: {
                    width: popupWidth,
                    height: popupHeight,
                    left: Math.max(0, (screenWidth - popupWidth) / 2),
                    top: Math.max(0, (screenHeight - popupHeight) / 2),
                    scrollbars: 'yes',
                    resizable: 'yes',
                    status: 'no',
                    toolbar: 'no',
                    menubar: 'no',
                    location: 'no'
                  }
                });
                
                console.log('Login popup response:', loginResponse);
                
                if (loginResponse && loginResponse.account) {
                  const tokenResponse = await msalInstance.acquireTokenSilent({
                    ...loginRequest,
                    account: loginResponse.account
                  });
                  
                  console.log('Token response:', tokenResponse);
                  
                  // Store token in sessionStorage and cookies
                  sessionStorage.setItem('accessToken', tokenResponse.accessToken);
                  setCookie('accessToken', tokenResponse.accessToken, 7); // Store for 7 days
                  console.log('Token stored in sessionStorage and cookies');
                  
                  // Update UI
                  authButton.textContent = 'Logout';
                  authButton.classList.add('logout');
                  
                  // Add welcome message for logged in user
                  const welcomeMessage = document.getElementById('welcome-message');
                  if (welcomeMessage) {
                    welcomeMessage.innerHTML = `<span class="sparkle">✨</span> Welcome ${loginResponse.account.name}! How can I help you today?`;
                  }
                } else {
                  throw new Error('No account information received from login response');
                }
              } catch (popupError) {
                console.error('Popup login failed:', popupError);
                throw popupError;
              }
            }
          } else {
            // If no existing account, use popup login
            console.log('No existing account, using popup login...');
            try {
              // Calculate popup position more reliably
              const screenWidth = window.screen.availWidth || window.screen.width;
              const screenHeight = window.screen.availHeight || window.screen.height;
              const popupWidth = 600;
              const popupHeight = 700;

              const loginResponse = await msalInstance.loginPopup({
                ...loginRequest,
                popupWindowAttributes: {
                  width: popupWidth,
                  height: popupHeight,
                  left: Math.max(0, (screenWidth - popupWidth) / 2),
                  top: Math.max(0, (screenHeight - popupHeight) / 2),
                  scrollbars: 'yes',
                  resizable: 'yes',
                  status: 'no',
                  toolbar: 'no',
                  menubar: 'no',
                  location: 'no'
                }
              });
              
              console.log('Login popup response:', loginResponse);
              
              if (loginResponse && loginResponse.account) {
                const tokenResponse = await msalInstance.acquireTokenSilent({
                  ...loginRequest,
                  account: loginResponse.account
                });
                
                console.log('Token response:', tokenResponse);
                
                // Store token in sessionStorage and cookies
                sessionStorage.setItem('accessToken', tokenResponse.accessToken);
                setCookie('accessToken', tokenResponse.accessToken, 7); // Store for 7 days
                console.log('Token stored in sessionStorage and cookies');
                
                // Update UI
                authButton.textContent = 'Logout';
                authButton.classList.add('logout');
                
                // Add welcome message for logged in user
                const welcomeMessage = document.getElementById('welcome-message');
                if (welcomeMessage) {
                  welcomeMessage.innerHTML = `<span class="sparkle">✨</span> Welcome ${loginResponse.account.name}! How can I help you today?`;
                }
              } else {
                throw new Error('No account information received from login response');
              }
            } catch (popupError) {
              console.error('Popup login failed:', popupError);
              throw popupError;
            }
          }
        } catch (error) {
          console.error('Login failed with error:', error);
          console.error('Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack,
            errorCode: error.errorCode,
            errorMessage: error.errorMessage,
            subError: error.subError
          });

          // Show a more user-friendly error message
          let errorMessage = 'Login failed. ';
          if (error.name === 'UserCancelledError' || error.name === 'BrowserAuthError') {
            errorMessage += 'The login window was closed before completing the process. Please try again.';
          } else if (error.message && error.message.includes('cancelled')) {
            errorMessage += 'The login process was interrupted. Please try again.';
          } else if (error.errorCode === 'user_cancelled' || error.errorCode === 'user_canceled') {
            errorMessage += 'The login process was cancelled. Please try again.';
          } else if (error.errorCode === 'popup_window_error' || error.errorCode === 'popup_blocked') {
            errorMessage += 'Could not open the login window. Please check your popup blocker settings and try again.';
          } else if (error.errorCode === 'interaction_in_progress') {
            errorMessage += 'Another login attempt is already in progress. Please wait and try again.';
          } else if (error.errorCode === 'network_error') {
            errorMessage += 'Network connection error. Please check your internet connection and try again.';
          } else {
            errorMessage += error.message || 'An unexpected error occurred. Please try again.';
          }

          // Reset button state on error
          authButton.textContent = 'Login';
          authButton.classList.remove('logout');

          addMessage({
            text: errorMessage,
            timestamp: new Date().toISOString(),
            type: 'assistant'
          });
        } finally {
          // Clear timeout and reset login state
          if (typeof loginTimeout !== 'undefined') {
            clearTimeout(loginTimeout);
          }
          isLoginInProgress = false;
        }
      } else {
        // Handle logout
        try {
          console.log('Starting logout process...');
          await msalInstance.logoutPopup({
            postLogoutRedirectUri: msalConfig.auth.postLogoutRedirectUri,
            popupWindowAttributes: {
              width: 600,
              height: 700,
              left: window.screen.width / 2 - 300,
              top: window.screen.height / 2 - 350
            }
          });
          
          // Clear token from sessionStorage and cookies
          sessionStorage.removeItem('accessToken');
          deleteCookie('accessToken');
          console.log('Token removed from sessionStorage and cookies');
          
          // Update UI
          authButton.textContent = 'Login';
          authButton.classList.remove('logout');
          
          // Reset welcome message
          const welcomeMessage = document.getElementById('welcome-message');
          if (welcomeMessage) {
            welcomeMessage.innerHTML = '<span class="sparkle">✨</span> Ask me anything... I\'ll bring the right tools.';
          }
          
          console.log('Logout process completed successfully');
        } catch (error) {
          console.error('Logout failed with error:', error);
          console.error('Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack,
            errorCode: error.errorCode,
            errorMessage: error.errorMessage,
            subError: error.subError
          });
        }
      }
    }

    // Check if user is already logged in on page load
    window.addEventListener('DOMContentLoaded', async () => {
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        const authButton = document.getElementById('auth-button');
        authButton.textContent = 'Logout';
        authButton.classList.add('logout');
        
        // Add welcome message for logged in user
        const welcomeMessage = document.getElementById('welcome-message');
        if (welcomeMessage) {
          welcomeMessage.innerHTML = `<span class="sparkle">✨</span> Welcome ${accounts[0].name}! How can I help you today?`;
        }
      }
    });

    // Define keywords that trigger agent suggestions
    const defaultKeywords = ['resume', 'resume parser', 'job description', 'JD', 'timesheet', 'time sheet', 'time tracking', 'work hours', 'hours worked', 'time entry', 'working hours', 'report hours', 'reporting', 'time report', 'clock in', 'clock out'];

    // Function to check if text contains any of the keywords
    function containsKeyword(text) {
      return defaultKeywords.some(keyword => 
        text.toLowerCase().includes(keyword.toLowerCase())
      );
    }

    // Function to format timestamp
    function formatTimestamp(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // Function to scroll chat to bottom
    function scrollToBottom() {
      const messagesDiv = document.getElementById('chat-messages');
      messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    // Function to simulate typing effect with markdown
    async function typeMessage(element, text, speed = 30) {
      const textElement = element.querySelector('.message-text');
      element.classList.add('typing');
      
      // First, convert markdown to HTML
      const htmlContent = marked.parse(text);
      
      // Create a temporary div to hold the HTML content
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      
      // Get the text content to type
      const textToType = tempDiv.textContent;
      
      // Clear the text element
      textElement.textContent = '';
      
      // Type out the text character by character
      for (let i = 0; i < textToType.length; i++) {
        textElement.textContent += textToType[i];
        // Scroll to bottom as text is being typed
        scrollToBottom();
        await new Promise(resolve => setTimeout(resolve, speed));
      }
      
      // After typing is complete, set the HTML with markdown formatting
      textElement.innerHTML = htmlContent;
      
      // Scroll one final time to ensure everything is visible
      scrollToBottom();
      element.classList.remove('typing');
    }

    // Function to add welcome message
    function addWelcomeMessage() {
      const messagesDiv = document.getElementById('chat-messages');
      const welcomeDiv = document.createElement('div');
      welcomeDiv.className = 'welcome-message';
      welcomeDiv.innerHTML = '<span class="sparkle">✨</span> Ask me anything... I\'ll bring the right tools.';
      welcomeDiv.id = 'welcome-message';
      messagesDiv.appendChild(welcomeDiv);
    }

    // Function to remove welcome message
    function removeWelcomeMessage() {
      const welcomeMessage = document.getElementById('welcome-message');
      if (welcomeMessage) {
        welcomeMessage.remove();
      }
    }

    // Function to add a message to the chat
    async function addMessage(message) {
      // Remove welcome message and suggestions when conversation starts
      removeWelcomeMessage();
      removeAgentSuggestion();

      const messagesDiv = document.getElementById('chat-messages');
      const messageElement = document.createElement('div');
      messageElement.className = `message ${message.type}`;
      
      const textElement = document.createElement('div');
      textElement.className = 'message-text';
      
      const timestampElement = document.createElement('div');
      timestampElement.className = 'timestamp';
      timestampElement.textContent = formatTimestamp(message.timestamp);
      
      messageElement.appendChild(textElement);
      messageElement.appendChild(timestampElement);
      messagesDiv.appendChild(messageElement);

      // If it's an assistant message, use typing effect with markdown
      if (message.type === 'assistant') {
        const displayText = message.text.replace(/^Echo:\s*/, '');
        await typeMessage(messageElement, displayText);
      } else {
        // For user messages, just set the text content
        textElement.textContent = message.text;
      }

      // Check for keywords and add agent suggestion if found - only for sent messages
      if (message.type === 'user' && containsKeyword(message.text)) {
        addAgentSuggestion(message.text);
      }
      
      // Scroll to bottom
      scrollToBottom();
    }

    // Handle receiving chat history
    ipcRenderer.on('load-chat-history', (event, history) => {
      const messagesDiv = document.getElementById('chat-messages');
      messagesDiv.innerHTML = ''; // Clear existing messages
      
      if (history && history.length > 0) {
        history.forEach(message => {
          addMessage(message);
        });
      } else {
        // If no history, show welcome message
        addWelcomeMessage();
      }
    });

    // Show welcome message when the window loads
    window.addEventListener('DOMContentLoaded', () => {
      const messagesDiv = document.getElementById('chat-messages');
      if (messagesDiv.children.length === 0) {
        addWelcomeMessage();
      }
    });

    // Function to open agent interface
    function openAgentInterface() {
      // Send the menu-item-clicked event with view-recent-tasks action
      ipcRenderer.send('menu-item-clicked', 'view-recent-tasks');
      // Close the chat popup after sending the event
      closeWindow();
    }

    // Function to add agent suggestion
    function addAgentSuggestion(keyword) {
      removeAgentSuggestion(); // Remove any existing suggestion first
      
      const messagesDiv = document.getElementById('chat-messages');
      const suggestionDiv = document.createElement('div');
      suggestionDiv.className = 'agent-suggestion';
      suggestionDiv.id = 'agent-suggestion';
      
      // Customize message based on keyword
      let agentType = '';
      if (['resume', 'resume parser', 'job description', 'JD'].some(k => keyword.toLowerCase().includes(k.toLowerCase()))) {
        agentType = 'Resume Parser';
      } else if (['timesheet', 'time sheet', 'time tracking', 'work hours', 'hours worked', 'time entry', 'working hours', 'report hours', 'reporting', 'time report', 'clock in', 'clock out'].some(k => keyword.toLowerCase().includes(k.toLowerCase()))) {
        agentType = 'Time Tracking';
      }
      
      suggestionDiv.innerHTML = `
        <div>Great! Sounds like you're working on ${agentType} related tasks. Click the button below to open the respective Agent.</div>
        <button class="agent-button" onclick="openAgentInterface()">
          <span class="icon">🚀</span>
          Open ${agentType} Agent
        </button>
      `;
      
      messagesDiv.appendChild(suggestionDiv);
      messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    // Function to remove agent suggestion
    function removeAgentSuggestion() {
      const suggestionDiv = document.getElementById('agent-suggestion');
      if (suggestionDiv) {
        suggestionDiv.remove();
      }
    }

    // Function to send message
    async function sendMessage() {
      const input = document.getElementById('chat-input');
      const message = input.value.trim();
      
      if (message) {
        input.value = '';
        
        // Add user message to chat
        const userMessage = {
          text: message,
          timestamp: new Date().toISOString(),
          type: 'user'
        };
        await addMessage(userMessage);
        
        try {
          // Show thinking animation
          const thinkingDiv = document.createElement('div');
          thinkingDiv.className = 'thinking';
          thinkingDiv.innerHTML = '<span></span><span></span><span></span>';
          document.getElementById('chat-messages').appendChild(thinkingDiv);
          
          // Scroll to bottom to show thinking animation
          scrollToBottom();
          
          // Get response from API (token will be automatically retrieved from sessionStorage)
          const response = await apiClient.chat(message);
          console.log('API Response:', response);
          
          // Remove thinking animation
          thinkingDiv.remove();
          
          // Check if response is valid
          if (response === undefined || response === null) {
            throw new Error('Empty response received from server');
          }
          
          // Add response message
          const responseMessage = {
            text: response,
            timestamp: new Date().toISOString(),
            type: 'assistant'
          };
          await addMessage(responseMessage);

          // Save the chat history after successful message
          ipcRenderer.send('save-chat-history', { message, response });
        } catch (error) {
          console.error('Error sending message:', error);
          const errorMessage = {
            text: `Sorry, there was an error: ${error.message}. Please try again.`,
            timestamp: new Date().toISOString(),
            type: 'assistant'
          };
          await addMessage(errorMessage);
        }
      }
    }

    // Function to clear chat history
    function clearHistory() {
      if (confirm('Are you sure you want to clear the chat history?')) {
        ipcRenderer.send('clear-chat-history');
        const messagesDiv = document.getElementById('chat-messages');
        messagesDiv.innerHTML = '';
        // Add welcome message back
        addWelcomeMessage();
      }
    }

    // Function to close window
    function closeWindow() {
      ipcRenderer.send('close-chat-popup');
    }

    // Prevent window from closing when clicking outside
    document.addEventListener('click', (event) => {
      event.stopPropagation();
    });

    // Remove the input event listener that shows suggestions while typing
    document.getElementById('chat-input').removeEventListener('input', function(e) {
      if (containsKeyword(e.target.value)) {
        addAgentSuggestion(e.target.value);
      } else {
        removeAgentSuggestion();
      }
    });

    // Add a simpler input event listener that only removes suggestions
    document.getElementById('chat-input').addEventListener('input', function(e) {
      removeAgentSuggestion();
    });
  </script>
</body>
</html> 